// 性能监控API路由
import express from 'express';
import { authenticateToken } from '../middleware/auth.js';

const router = express.Router();

interface PerformanceMetric {
  name: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  metadata?: Record<string, any>;
}

interface PerformanceReport {
  sessionId: string;
  totalTime: number;
  phases: {
    warmUp: number;
    preparation: number;
    validation: number;
    activation: number;
  };
  bottlenecks: string[];
  metrics: PerformanceMetric[];
  timestamp: number;
}

// 存储性能报告（生产环境应该使用数据库）
const performanceReports: Map<string, PerformanceReport[]> = new Map();

/**
 * 接收性能报告
 */
router.post('/report', authenticateToken, async (req, res) => {
  try {
    const report: PerformanceReport = req.body;
    const userId = req.user?.userId;
    
    if (!userId) {
      return res.status(401).json({ error: 'User not authenticated' });
    }
    
    // 验证报告数据
    if (!report.sessionId || !report.totalTime || !report.metrics) {
      return res.status(400).json({ error: 'Invalid performance report data' });
    }
    
    // 存储报告
    if (!performanceReports.has(userId)) {
      performanceReports.set(userId, []);
    }
    
    const userReports = performanceReports.get(userId)!;
    userReports.push(report);
    
    // 保持最近100个报告
    if (userReports.length > 100) {
      userReports.splice(0, userReports.length - 100);
    }
    
    // 分析性能数据
    const analysis = analyzePerformance(report);
    
    console.log(`📊 Performance Report received for user ${userId}:`, {
      sessionId: report.sessionId,
      totalTime: report.totalTime,
      bottlenecks: report.bottlenecks,
      analysis
    });
    
    res.json({
      success: true,
      message: 'Performance report received',
      analysis
    });
  } catch (error) {
    console.error('❌ Failed to process performance report:', error);
    res.status(500).json({ error: 'Failed to process performance report' });
  }
});

/**
 * 获取性能统计
 */
router.get('/stats', authenticateToken, async (req, res) => {
  try {
    const userId = req.user?.userId;
    
    if (!userId) {
      return res.status(401).json({ error: 'User not authenticated' });
    }
    
    const userReports = performanceReports.get(userId) || [];
    
    if (userReports.length === 0) {
      return res.json({
        totalReports: 0,
        averageTime: 0,
        commonBottlenecks: [],
        trends: {}
      });
    }
    
    // 计算统计数据
    const totalReports = userReports.length;
    const averageTime = userReports.reduce((sum, r) => sum + r.totalTime, 0) / totalReports;
    
    // 统计常见瓶颈
    const bottleneckCounts: Record<string, number> = {};
    userReports.forEach(report => {
      report.bottlenecks.forEach(bottleneck => {
        bottleneckCounts[bottleneck] = (bottleneckCounts[bottleneck] || 0) + 1;
      });
    });
    
    const commonBottlenecks = Object.entries(bottleneckCounts)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 5)
      .map(([bottleneck, count]) => ({ bottleneck, count }));
    
    // 计算趋势（最近10个报告 vs 之前的报告）
    const recentReports = userReports.slice(-10);
    const olderReports = userReports.slice(0, -10);
    
    const recentAverage = recentReports.reduce((sum, r) => sum + r.totalTime, 0) / recentReports.length;
    const olderAverage = olderReports.length > 0 
      ? olderReports.reduce((sum, r) => sum + r.totalTime, 0) / olderReports.length 
      : recentAverage;
    
    const trend = recentAverage < olderAverage ? 'improving' : 
                  recentAverage > olderAverage ? 'degrading' : 'stable';
    
    res.json({
      totalReports,
      averageTime,
      commonBottlenecks,
      trends: {
        overall: trend,
        recentAverage,
        olderAverage,
        improvement: olderAverage - recentAverage
      }
    });
  } catch (error) {
    console.error('❌ Failed to get performance stats:', error);
    res.status(500).json({ error: 'Failed to get performance stats' });
  }
});

/**
 * 获取全局性能统计（管理员）
 */
router.get('/global-stats', authenticateToken, async (req, res) => {
  try {
    // 这里应该检查管理员权限
    // if (!req.user?.isAdmin) {
    //   return res.status(403).json({ error: 'Admin access required' });
    // }
    
    const allReports: PerformanceReport[] = [];
    performanceReports.forEach(userReports => {
      allReports.push(...userReports);
    });
    
    if (allReports.length === 0) {
      return res.json({
        totalUsers: 0,
        totalReports: 0,
        globalAverageTime: 0,
        globalBottlenecks: []
      });
    }
    
    const totalUsers = performanceReports.size;
    const totalReports = allReports.length;
    const globalAverageTime = allReports.reduce((sum, r) => sum + r.totalTime, 0) / totalReports;
    
    // 全局瓶颈统计
    const globalBottleneckCounts: Record<string, number> = {};
    allReports.forEach(report => {
      report.bottlenecks.forEach(bottleneck => {
        globalBottleneckCounts[bottleneck] = (globalBottleneckCounts[bottleneck] || 0) + 1;
      });
    });
    
    const globalBottlenecks = Object.entries(globalBottleneckCounts)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 10)
      .map(([bottleneck, count]) => ({ 
        bottleneck, 
        count, 
        percentage: (count / totalReports * 100).toFixed(1) 
      }));
    
    res.json({
      totalUsers,
      totalReports,
      globalAverageTime,
      globalBottlenecks
    });
  } catch (error) {
    console.error('❌ Failed to get global performance stats:', error);
    res.status(500).json({ error: 'Failed to get global performance stats' });
  }
});

/**
 * 分析性能报告
 */
function analyzePerformance(report: PerformanceReport): {
  grade: string;
  recommendations: string[];
  score: number;
} {
  let score = 100;
  const recommendations: string[] = [];
  
  // 总时间评分
  if (report.totalTime > 10000) { // 10秒以上
    score -= 30;
    recommendations.push('总启动时间过长，建议优化整体流程');
  } else if (report.totalTime > 5000) { // 5秒以上
    score -= 15;
    recommendations.push('启动时间偏长，可以进一步优化');
  }
  
  // 各阶段评分
  if (report.phases.warmUp > 2000) {
    score -= 10;
    recommendations.push('预热阶段耗时过长，检查网络连接和资源加载');
  }
  
  if (report.phases.preparation > 3000) {
    score -= 15;
    recommendations.push('准备阶段耗时过长，检查音频和WebSocket初始化');
  }
  
  if (report.phases.validation > 1000) {
    score -= 10;
    recommendations.push('验证阶段耗时过长，检查扣费API响应时间');
  }
  
  if (report.phases.activation > 1000) {
    score -= 10;
    recommendations.push('激活阶段耗时过长，检查会话激活逻辑');
  }
  
  // 瓶颈评分
  score -= Math.min(report.bottlenecks.length * 5, 20);
  
  // 确定等级
  let grade: string;
  if (score >= 90) grade = 'A';
  else if (score >= 80) grade = 'B';
  else if (score >= 70) grade = 'C';
  else if (score >= 60) grade = 'D';
  else grade = 'F';
  
  if (recommendations.length === 0) {
    recommendations.push('性能表现良好，无需特别优化');
  }
  
  return { grade, recommendations, score };
}

export default router;
