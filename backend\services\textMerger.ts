import { RecognitionResult } from '../types/asrTypes';

export interface TextSegment {
  text: string;
  confidence: number;
  timestamp: number;
  service: string;
  isPartial: boolean;
  segmentId: string;
  weight?: number;
}

export interface MergedText {
  text: string;
  confidence: number;
  segments: TextSegment[];
  mergeStrategy: string;
  processingTime: number;
  metadata: {
    totalSegments: number;
    averageConfidence: number;
    dominantService: string;
    textLength: number;
    processingSteps: string[];
  };
}

export interface TextMergeConfig {
  similarityThreshold: number;      // 文本相似度阈值
  confidenceWeight: number;         // 置信度权重
  serviceWeight: Record<string, number>; // 服务权重
  timeDecayFactor: number;          // 时间衰减因子
  maxSegments: number;              // 最大段数
  enableSmartPunctuation: boolean;  // 启用智能标点
  enableLanguageDetection: boolean; // 启用语言检测
  enableSemanticMerge: boolean;     // 启用语义合并
}

export const DEFAULT_TEXT_MERGE_CONFIG: TextMergeConfig = {
  similarityThreshold: 0.8,
  confidenceWeight: 0.4,
  serviceWeight: {
    'iflytek': 1.2,
    'alibaba': 1.1,
    'baidu': 1.0,
    'openai': 0.9,
    'mock': 0.8
  },
  timeDecayFactor: 0.1,
  maxSegments: 10,
  enableSmartPunctuation: true,
  enableLanguageDetection: true,
  enableSemanticMerge: true
};

/**
 * 智能文本合并器
 * 将多个识别片段合并为连贯、高质量的文本
 */
export class TextMerger {
  private config: TextMergeConfig;
  private processingSteps: string[] = [];

  constructor(config: Partial<TextMergeConfig> = {}) {
    this.config = { ...DEFAULT_TEXT_MERGE_CONFIG, ...config };
    console.log('TextMerger initialized with config:', this.config);
  }

  /**
   * 合并识别结果
   */
  async mergeResults(results: RecognitionResult[]): Promise<MergedText> {
    const startTime = Date.now();
    this.processingSteps = [];
    
    console.log(`🔄 Starting text merge for ${results.length} results`);
    this.addProcessingStep(`Started with ${results.length} recognition results`);

    if (results.length === 0) {
      return this.createEmptyResult(startTime);
    }

    if (results.length === 1) {
      return this.createSingleResult(results[0], startTime);
    }

    // 1. 预处理和验证
    const validSegments = this.preprocessSegments(results);
    this.addProcessingStep(`Preprocessed to ${validSegments.length} valid segments`);

    // 2. 去重处理
    const deduplicatedSegments = this.removeDuplicates(validSegments);
    this.addProcessingStep(`Deduplicated to ${deduplicatedSegments.length} unique segments`);

    // 3. 时间排序和对齐
    const alignedSegments = this.alignSegments(deduplicatedSegments);
    this.addProcessingStep(`Aligned ${alignedSegments.length} segments by timestamp`);

    // 4. 语义连贯性检查
    const coherentSegments = await this.ensureCoherence(alignedSegments);
    this.addProcessingStep(`Ensured coherence for ${coherentSegments.length} segments`);

    // 5. 智能合并
    const mergedText = this.performIntelligentMerge(coherentSegments);
    this.addProcessingStep(`Merged into text of ${mergedText.length} characters`);

    // 6. 后处理优化
    const optimizedText = this.postProcessText(mergedText);
    this.addProcessingStep(`Post-processed and optimized text`);

    // 7. 计算元数据
    const metadata = this.calculateMetadata(coherentSegments, optimizedText);

    const result: MergedText = {
      text: optimizedText,
      confidence: this.calculateOverallConfidence(coherentSegments),
      segments: coherentSegments,
      mergeStrategy: this.determineMergeStrategy(coherentSegments),
      processingTime: Date.now() - startTime,
      metadata
    };

    console.log(`✅ Text merge completed:`, {
      text: result.text.substring(0, 100) + '...',
      confidence: result.confidence.toFixed(2),
      strategy: result.mergeStrategy,
      processingTime: result.processingTime
    });

    return result;
  }

  /**
   * 预处理段落
   */
  private preprocessSegments(results: RecognitionResult[]): TextSegment[] {
    return results
      .filter(result => result.text && result.text.trim().length > 0)
      .map(result => ({
        text: result.text.trim(),
        confidence: Math.max(0, Math.min(1, result.confidence)),
        timestamp: result.timestamp,
        service: result.service,
        isPartial: result.isPartial,
        segmentId: result.segmentId,
        weight: this.calculateSegmentWeight(result)
      }))
      .sort((a, b) => a.timestamp - b.timestamp);
  }

  /**
   * 计算段落权重
   */
  private calculateSegmentWeight(result: RecognitionResult): number {
    const serviceWeight = this.config.serviceWeight[result.service] || 1.0;
    const confidenceWeight = result.confidence * this.config.confidenceWeight;
    const lengthBonus = Math.min(result.text.length / 50, 1); // 长度奖励，最多1分
    const partialPenalty = result.isPartial ? 0.8 : 1.0; // 部分结果惩罚
    
    return serviceWeight * confidenceWeight * lengthBonus * partialPenalty;
  }

  /**
   * 去除重复段落
   */
  private removeDuplicates(segments: TextSegment[]): TextSegment[] {
    const result: TextSegment[] = [];
    
    for (const segment of segments) {
      const isDuplicate = result.some(existing => 
        this.calculateTextSimilarity(segment.text, existing.text) > this.config.similarityThreshold
      );
      
      if (!isDuplicate) {
        result.push(segment);
      } else {
        // 如果是重复，选择权重更高的
        const existingIndex = result.findIndex(existing => 
          this.calculateTextSimilarity(segment.text, existing.text) > this.config.similarityThreshold
        );
        
        if (existingIndex !== -1 && segment.weight! > result[existingIndex].weight!) {
          console.log(`🔄 Replacing duplicate with higher weight:`, {
            old: result[existingIndex].text.substring(0, 30) + '...',
            new: segment.text.substring(0, 30) + '...',
            oldWeight: result[existingIndex].weight,
            newWeight: segment.weight
          });
          result[existingIndex] = segment;
        }
      }
    }
    
    return result;
  }

  /**
   * 计算文本相似度
   */
  private calculateTextSimilarity(text1: string, text2: string): number {
    // 预处理：去除标点符号和空格，转换为小写
    const clean1 = text1.replace(/[^\w\u4e00-\u9fa5]/g, '').toLowerCase();
    const clean2 = text2.replace(/[^\w\u4e00-\u9fa5]/g, '').toLowerCase();
    
    if (clean1.length === 0 && clean2.length === 0) return 1;
    if (clean1.length === 0 || clean2.length === 0) return 0;
    
    // 使用编辑距离计算相似度
    const distance = this.levenshteinDistance(clean1, clean2);
    const maxLength = Math.max(clean1.length, clean2.length);
    
    return 1 - (distance / maxLength);
  }

  /**
   * 计算编辑距离
   */
  private levenshteinDistance(str1: string, str2: string): number {
    const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));
    
    for (let i = 0; i <= str1.length; i++) matrix[0][i] = i;
    for (let j = 0; j <= str2.length; j++) matrix[j][0] = j;
    
    for (let j = 1; j <= str2.length; j++) {
      for (let i = 1; i <= str1.length; i++) {
        const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
        matrix[j][i] = Math.min(
          matrix[j][i - 1] + 1,     // deletion
          matrix[j - 1][i] + 1,     // insertion
          matrix[j - 1][i - 1] + indicator // substitution
        );
      }
    }
    
    return matrix[str2.length][str1.length];
  }

  /**
   * 对齐段落
   */
  private alignSegments(segments: TextSegment[]): TextSegment[] {
    // 按时间戳排序
    const sorted = segments.sort((a, b) => a.timestamp - b.timestamp);
    
    // 应用时间衰减
    const now = Date.now();
    return sorted.map(segment => ({
      ...segment,
      weight: segment.weight! * Math.exp(-this.config.timeDecayFactor * (now - segment.timestamp) / 1000)
    }));
  }

  /**
   * 确保语义连贯性
   */
  private async ensureCoherence(segments: TextSegment[]): Promise<TextSegment[]> {
    if (!this.config.enableSemanticMerge) {
      return segments;
    }

    const result: TextSegment[] = [];
    
    for (let i = 0; i < segments.length; i++) {
      const current = segments[i];
      const previous = result[result.length - 1];
      
      if (!previous) {
        result.push(current);
        continue;
      }
      
      // 检查时间间隔
      const timeGap = current.timestamp - previous.timestamp;
      if (timeGap > 10000) { // 10秒间隔太大
        console.log(`⚠️ Large time gap detected: ${timeGap}ms`);
      }
      
      // 检查文本连贯性
      const isCoherent = this.checkTextCoherence(previous.text, current.text);
      if (isCoherent) {
        result.push(current);
      } else {
        console.log(`⚠️ Incoherent text detected:`, {
          previous: previous.text.substring(0, 30) + '...',
          current: current.text.substring(0, 30) + '...'
        });
        // 降低置信度但仍然保留
        result.push({
          ...current,
          confidence: current.confidence * 0.7,
          weight: current.weight! * 0.7
        });
      }
    }
    
    return result;
  }

  /**
   * 检查文本连贯性
   */
  private checkTextCoherence(previousText: string, currentText: string): boolean {
    // 1. 检查重复开头
    const words1 = this.tokenize(previousText);
    const words2 = this.tokenize(currentText);
    
    if (words1.length > 0 && words2.length > 0) {
      const lastWord = words1[words1.length - 1];
      const firstWord = words2[0];
      
      // 如果当前文本以上一个文本的最后一个词开头，可能是重复
      if (lastWord === firstWord) {
        return false;
      }
    }
    
    // 2. 检查语言一致性
    const isChinese1 = /[\u4e00-\u9fa5]/.test(previousText);
    const isChinese2 = /[\u4e00-\u9fa5]/.test(currentText);
    
    if (isChinese1 !== isChinese2) {
      return false; // 语言不一致
    }
    
    // 3. 检查语义跳跃（简单实现）
    const similarity = this.calculateTextSimilarity(previousText, currentText);
    if (similarity > 0.9) {
      return false; // 太相似，可能是重复
    }
    
    return true;
  }

  /**
   * 分词
   */
  private tokenize(text: string): string[] {
    return text
      .replace(/[^\w\u4e00-\u9fa5]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 0);
  }

  /**
   * 执行智能合并
   */
  private performIntelligentMerge(segments: TextSegment[]): string {
    if (segments.length === 0) return '';
    if (segments.length === 1) return segments[0].text;

    let mergedText = '';
    
    for (let i = 0; i < segments.length; i++) {
      const current = segments[i];
      const previous = segments[i - 1];
      
      if (i === 0) {
        mergedText = current.text;
        continue;
      }
      
      // 智能连接逻辑
      const connector = this.determineConnector(previous.text, current.text);
      mergedText += connector + current.text;
    }
    
    return mergedText;
  }

  /**
   * 确定连接符
   */
  private determineConnector(previousText: string, currentText: string): string {
    // 如果前一个文本以标点符号结尾，不需要额外连接符
    if (/[。！？，、；：]$/.test(previousText.trim())) {
      return '';
    }
    
    // 如果当前文本以标点符号开头，不需要额外连接符
    if (/^[。！？，、；：]/.test(currentText.trim())) {
      return '';
    }
    
    // 检查是否需要空格（英文）
    const isEnglish1 = /[a-zA-Z]$/.test(previousText.trim());
    const isEnglish2 = /^[a-zA-Z]/.test(currentText.trim());
    
    if (isEnglish1 && isEnglish2) {
      return ' ';
    }
    
    // 中文通常不需要连接符，但可以添加适当的标点
    return '';
  }

  /**
   * 后处理文本
   */
  private postProcessText(text: string): string {
    if (!this.config.enableSmartPunctuation) {
      return text.trim();
    }

    let result = text;
    
    // 1. 去除多余的空格
    result = result.replace(/\s+/g, ' ').trim();
    
    // 2. 修正标点符号
    result = result.replace(/\s+([。！？，、；：])/g, '$1');
    
    // 3. 首字母大写（英文）
    result = result.replace(/^[a-z]/, match => match.toUpperCase());
    
    // 4. 去除重复的标点符号
    result = result.replace(/([。！？])\1+/g, '$1');
    
    // 5. 智能标点添加
    result = this.addSmartPunctuation(result);
    
    return result;
  }

  /**
   * 添加智能标点
   */
  private addSmartPunctuation(text: string): string {
    // 简单的智能标点规则
    let result = text;
    
    // 如果文本较长且没有结尾标点，添加句号
    if (result.length > 10 && !/[。！？]$/.test(result)) {
      // 检查是否是疑问句
      if (/^(什么|怎么|为什么|哪里|谁|何时|如何|是否|能否|可以|会不会)/.test(result) || 
          /\?$/.test(result) || /吗$/.test(result)) {
        result += '？';
      } else {
        result += '。';
      }
    }
    
    return result;
  }

  /**
   * 计算综合置信度
   */
  private calculateOverallConfidence(segments: TextSegment[]): number {
    if (segments.length === 0) return 0;
    
    // 加权平均置信度
    let totalWeight = 0;
    let weightedSum = 0;
    
    for (const segment of segments) {
      const weight = segment.weight || 1;
      weightedSum += segment.confidence * weight;
      totalWeight += weight;
    }
    
    return totalWeight > 0 ? weightedSum / totalWeight : 0;
  }

  /**
   * 确定合并策略
   */
  private determineMergeStrategy(segments: TextSegment[]): string {
    if (segments.length <= 1) return 'single';
    
    const services = new Set(segments.map(s => s.service));
    const hasPartial = segments.some(s => s.isPartial);
    const avgConfidence = this.calculateOverallConfidence(segments);
    
    let strategy = '';
    
    if (services.size === 1) {
      strategy += 'single-service';
    } else {
      strategy += 'multi-service';
    }
    
    if (hasPartial) {
      strategy += '-incremental';
    } else {
      strategy += '-batch';
    }
    
    if (avgConfidence > 0.8) {
      strategy += '-high-confidence';
    } else if (avgConfidence < 0.6) {
      strategy += '-low-confidence';
    }
    
    return strategy;
  }

  /**
   * 计算元数据
   */
  private calculateMetadata(segments: TextSegment[], finalText: string): MergedText['metadata'] {
    const serviceCount: Record<string, number> = {};
    segments.forEach(segment => {
      serviceCount[segment.service] = (serviceCount[segment.service] || 0) + 1;
    });
    
    const dominantService = Object.entries(serviceCount)
      .sort(([,a], [,b]) => b - a)[0]?.[0] || 'unknown';
    
    return {
      totalSegments: segments.length,
      averageConfidence: this.calculateOverallConfidence(segments),
      dominantService,
      textLength: finalText.length,
      processingSteps: [...this.processingSteps]
    };
  }

  /**
   * 创建空结果
   */
  private createEmptyResult(startTime: number): MergedText {
    return {
      text: '',
      confidence: 0,
      segments: [],
      mergeStrategy: 'empty',
      processingTime: Date.now() - startTime,
      metadata: {
        totalSegments: 0,
        averageConfidence: 0,
        dominantService: 'none',
        textLength: 0,
        processingSteps: ['No results to merge']
      }
    };
  }

  /**
   * 创建单一结果
   */
  private createSingleResult(result: RecognitionResult, startTime: number): MergedText {
    const segment: TextSegment = {
      text: result.text,
      confidence: result.confidence,
      timestamp: result.timestamp,
      service: result.service,
      isPartial: result.isPartial,
      segmentId: result.segmentId,
      weight: 1.0
    };
    
    return {
      text: this.postProcessText(result.text),
      confidence: result.confidence,
      segments: [segment],
      mergeStrategy: 'single',
      processingTime: Date.now() - startTime,
      metadata: {
        totalSegments: 1,
        averageConfidence: result.confidence,
        dominantService: result.service,
        textLength: result.text.length,
        processingSteps: ['Single result, no merge needed']
      }
    };
  }

  /**
   * 添加处理步骤
   */
  private addProcessingStep(step: string): void {
    this.processingSteps.push(`${Date.now()}: ${step}`);
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<TextMergeConfig>): void {
    this.config = { ...this.config, ...newConfig };
    console.log('TextMerger config updated:', this.config);
  }

  /**
   * 获取当前配置
   */
  getConfig(): TextMergeConfig {
    return { ...this.config };
  }
}
