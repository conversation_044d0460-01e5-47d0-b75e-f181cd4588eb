import { create } from 'zustand';
import { notificationService, UserNotification } from '../lib/api/notifications';

interface NotificationState {
  // 状态
  notifications: UserNotification[];
  unreadCount: number;
  isLoading: boolean;
  error: string | null;
  
  // 操作方法
  fetchUnreadCount: () => Promise<void>;
  fetchNotifications: (params?: { page?: number; limit?: number; unreadOnly?: boolean }) => Promise<void>;
  markAsRead: (id: string) => Promise<void>;
  markAllAsRead: () => Promise<void>;
  clearError: () => void;
  reset: () => void;
}

const useNotificationStore = create<NotificationState>((set, get) => ({
  // 初始状态
  notifications: [],
  unreadCount: 0,
  isLoading: false,
  error: null,

  // 获取未读通知数量
  fetchUnreadCount: async () => {
    try {
      set({ isLoading: true, error: null });
      const response = await notificationService.getUnreadCount();
      
      if (response.success) {
        set({ unreadCount: response.data.unreadCount });
      } else {
        set({ error: '获取未读通知数量失败' });
      }
    } catch (error: any) {
      console.error('获取未读通知数量失败:', error);
      set({ error: error.message || '获取未读通知数量失败' });
    } finally {
      set({ isLoading: false });
    }
  },

  // 获取通知列表
  fetchNotifications: async (params) => {
    try {
      set({ isLoading: true, error: null });
      const response = await notificationService.getNotifications(params);
      
      if (response.success) {
        set({ notifications: response.data.notifications });
      } else {
        set({ error: '获取通知列表失败' });
      }
    } catch (error: any) {
      console.error('获取通知列表失败:', error);
      set({ error: error.message || '获取通知列表失败' });
    } finally {
      set({ isLoading: false });
    }
  },

  // 标记单个通知为已读
  markAsRead: async (id: string) => {
    try {
      const response = await notificationService.markAsRead(id);
      
      if (response.success) {
        const { notifications, unreadCount } = get();
        
        // 更新通知列表中的已读状态
        const updatedNotifications = notifications.map(notification => 
          notification.id === id 
            ? { ...notification, isRead: true, readAt: new Date().toISOString() }
            : notification
        );
        
        // 更新未读数量
        const wasUnread = notifications.find(n => n.id === id && !n.isRead);
        const newUnreadCount = wasUnread ? Math.max(0, unreadCount - 1) : unreadCount;
        
        set({ 
          notifications: updatedNotifications,
          unreadCount: newUnreadCount
        });
      } else {
        set({ error: '标记已读失败' });
      }
    } catch (error: any) {
      console.error('标记已读失败:', error);
      set({ error: error.message || '标记已读失败' });
    }
  },

  // 标记所有通知为已读
  markAllAsRead: async () => {
    try {
      const response = await notificationService.markAllAsRead();
      
      if (response.success) {
        const { notifications } = get();
        
        // 更新所有通知为已读状态
        const updatedNotifications = notifications.map(notification => ({
          ...notification,
          isRead: true,
          readAt: notification.readAt || new Date().toISOString()
        }));
        
        set({ 
          notifications: updatedNotifications,
          unreadCount: 0
        });
      } else {
        set({ error: '标记全部已读失败' });
      }
    } catch (error: any) {
      console.error('标记全部已读失败:', error);
      set({ error: error.message || '标记全部已读失败' });
    }
  },

  // 清除错误
  clearError: () => {
    set({ error: null });
  },

  // 重置状态
  reset: () => {
    set({
      notifications: [],
      unreadCount: 0,
      isLoading: false,
      error: null
    });
  }
}));

export default useNotificationStore;
