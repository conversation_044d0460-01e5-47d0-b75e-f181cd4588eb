# AI面试系统语音识别技术文档

## 📋 目录
- [1. 技术架构概述](#1-技术架构概述)
- [2. 核心组件详解](#2-核心组件详解)
- [3. 实现代码示例](#3-实现代码示例)
- [4. API接口文档](#4-api接口文档)
- [5. 配置说明](#5-配置说明)
- [6. 部署指南](#6-部署指南)
- [7. 最佳实践](#7-最佳实践)

## 1. 技术架构概述

### 1.1 整体架构
本项目采用**混合语音识别架构**，结合云端高精度识别和本地备用方案，确保系统的可靠性和准确性。

```mermaid
graph TB
    A[前端音频采集] --> B[音频预处理]
    B --> C[格式转换 FFmpeg]
    C --> D[主识别引擎 阿里云DashScope]
    D --> E[识别结果]
    C --> F[备用引擎 Vosk本地模型]
    F --> E
    E --> G[文本后处理]
    G --> H[AI对话系统]
```

### 1.2 技术栈
- **主识别引擎**: 阿里云DashScope Paraformer-v2
- **备用识别**: Vosk离线模型
- **音频处理**: FFmpeg
- **前端采集**: Web Audio API + VAD检测
- **后端框架**: Spring Boot
- **音频格式**: WAV (16kHz, 单声道)

### 1.3 核心优势
- ✅ **高准确率**: 阿里云Paraformer模型，支持中英文混合
- ✅ **实时处理**: 流式音频处理，低延迟响应
- ✅ **离线备用**: Vosk本地模型，网络异常时可用
- ✅ **智能降噪**: VAD语音活动检测，过滤环境噪音
- ✅ **格式兼容**: 自动音频格式转换，支持多种输入格式

## 2. 核心组件详解

### 2.1 语音转文字服务 (SpeechToTextService)

**功能**: 将音频文件转换为文本
**位置**: `consumer/src/main/java/com/guangge/Interview/audio/services/SpeechToTextService.java`

**核心特性**:
- 使用阿里云DashScope API
- 支持中英文混合识别
- 实时语音识别模型
- 自动语言检测

### 2.2 音频格式转换器 (AudioConverter)

**功能**: 统一音频格式，优化识别效果
**位置**: `consumer/src/main/java/com/guangge/Interview/audio/services/AudioConverter.java`

**转换参数**:
- 采样率: 16kHz
- 声道: 单声道
- 格式: WAV
- 编码: PCM 16-bit

### 2.3 前端语音采集 (useVoiceRecorder)

**功能**: 实时音频采集和预处理
**位置**: `frontend/frontend/components/useVoiceRecorder.tsx`

**核心功能**:
- 实时音频流采集
- VAD语音活动检测
- 噪声过滤
- 静音检测和自动分段

### 2.4 语音合成服务 (TextToSpeechService)

**功能**: 文本转语音，完成对话闭环
**位置**: `consumer/src/main/java/com/guangge/Interview/audio/services/TextToSpeechService.java`

## 3. 实现代码示例

### 3.1 语音识别核心实现

```java
@Service
public class SpeechToTextService {
    @Value("${spring.ai.dash-scope.audio.api-key}")
    private String apiKey;

    /**
     * 音频转文字核心方法
     * @param audioFile 音频文件
     * @return 识别文本
     */
    public String transcribeAudio(File audioFile) throws IOException {
        // 创建识别器实例
        Recognition recognizer = new Recognition();
        
        // 配置识别参数
        RecognitionParam param = RecognitionParam.builder()
                .apiKey(apiKey)
                .model("paraformer-realtime-v2")  // 实时识别模型
                .format("wav")                     // 音频格式
                .sampleRate(16000)                 // 采样率
                .parameter("language_hints", new String[]{"zh", "en"}) // 语言提示
                .build();

        try {
            // 执行识别
            String result = recognizer.call(param, audioFile);
            
            // 解析结果
            Map<String, Object> resultMap = JacksonMapperUtils.json2map(result);
            List<Map<String, Object>> sentences = 
                (List<Map<String, Object>>) resultMap.get("sentences");
            
            return (String) sentences.get(0).get("text");
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }
}
```

### 3.2 音频格式转换实现

```java
public class AudioConverter {
    
    /**
     * 音频格式转换为WAV
     * @param inputFile 输入音频文件
     * @return 转换后的WAV文件
     */
    public static File convertToWav(File inputFile) throws IOException, InterruptedException {
        String ffmpegPath = getFFmpegPath();
        File outputFile = File.createTempFile("converted-", ".wav");
        
        // FFmpeg转换命令
        ProcessBuilder processBuilder = new ProcessBuilder(
                ffmpegPath,
                "-y",                              // 强制覆盖
                "-i", inputFile.getAbsolutePath(), // 输入文件
                "-ar", "16000",                    // 采样率16kHz
                "-ac", "1",                        // 单声道
                "-sample_fmt", "s16",              // 16位PCM
                "-fflags", "+genpts",              // 修复时间戳
                "-avoid_negative_ts", "make_zero", // 避免负时间戳
                outputFile.getAbsolutePath()       // 输出文件
        );

        Process process = processBuilder.start();
        
        // 等待转换完成（超时10秒）
        boolean finished = process.waitFor(10, TimeUnit.SECONDS);
        if (!finished) {
            process.destroy();
            throw new IOException("FFmpeg 处理超时");
        }

        if (process.exitValue() != 0) {
            throw new IOException("FFmpeg 转换失败，退出码: " + process.exitValue());
        }

        return outputFile;
    }
    
    private static String getFFmpegPath() {
        String os = System.getProperty("os.name").toLowerCase();
        if (os.contains("win")) {
            return "C:\\software\\ffmpeg-7.1-full_build\\bin\\ffmpeg.exe";
        } else {
            return "/usr/bin/ffmpeg";
        }
    }
}
```

### 3.3 前端VAD语音检测

```typescript
// 语音活动检测算法
const vadDetection = (buffer: Float32Array) => {
  const energy = calculateEnergy(buffer);      // 计算音频能量
  const zeroCross = calculateZeroCross(buffer); // 计算过零率

  // 动态阈值
  const energyThreshold = noiseLevelRef.current * 1.8;
  const zcrThreshold = 60;

  return {
    isVoice: energy > energyThreshold && zeroCross < zcrThreshold,
    score: energy
  };
};

// 能量计算
const calculateEnergy = (buffer: Float32Array): number => {
  let sum = 0;
  for (let i = 0; i < buffer.length; i++) {
    sum += buffer[i] * buffer[i];
  }
  return Math.sqrt(sum / buffer.length);
};

// 过零率计算
const calculateZeroCross = (buffer: Float32Array): number => {
  let crossings = 0;
  for (let i = 1; i < buffer.length; i++) {
    if ((buffer[i] >= 0) !== (buffer[i - 1] >= 0)) {
      crossings++;
    }
  }
  return crossings;
};
```

### 3.4 完整语音处理流程

```java
@PostMapping(value="/face2faceChat", produces = "audio/wav")
public ResponseEntity<byte[]> face2faceChat(
        @RequestParam("chatId") String chatId,
        @RequestParam(value ="userName", required = false) String userName,
        @RequestPart(value = "audio", required = false) MultipartFile audio) 
        throws IOException, InterruptedException, UnsupportedAudioFileException {
    
    String text = "";
    String completed = "";
    
    if (StringUtils.hasLength(userName)) {
        // 使用用户名作为输入
        text = userName;
    } else if (audio != null) {
        // 处理音频输入
        // 1. 保存临时文件
        File tempFile = File.createTempFile("audio-", ".opus");
        audio.transferTo(tempFile);
        
        // 2. 格式转换
        File convertFile = AudioConverter.convertToWav(tempFile);
        
        // 3. 语音识别
        text = speechToTextService.transcribeAudio(convertFile);
        
        // 4. 清理临时文件
        tempFile.delete();
        convertFile.delete();
    }

    // 5. AI对话处理
    String response = interviewAssistant.chat(chatId, text);
    
    // 6. 检查对话结束条件
    if (response.contains("再见")) {
        completed = "completed";
    }

    // 7. 文字转语音
    byte[] audioResponse = speechService.textToSpeech(response);

    // 8. 返回音频响应
    return ResponseEntity.ok()
            .header("Content-Type", "audio/wav")
            .header("X-Chat-Status", completed)
            .body(audioResponse);
}
```

## 4. API接口文档

### 4.1 阿里云DashScope语音识别API

**官方文档**: https://help.aliyun.com/zh/dashscope/developer-reference/speech-recognition-api

**API端点**:
```
POST https://dashscope.aliyuncs.com/api/v1/services/audio/asr
```

**请求参数**:
```json
{
  "model": "paraformer-realtime-v2",
  "input": {
    "format": "wav",
    "sample_rate": 16000,
    "audio": "base64_encoded_audio_data"
  },
  "parameters": {
    "language_hints": ["zh", "en"]
  }
}
```

**响应格式**:
```json
{
  "output": {
    "sentences": [
      {
        "text": "识别的文本内容",
        "begin_time": 0,
        "end_time": 2000
      }
    ]
  },
  "usage": {
    "duration": 2000
  }
}
```

### 4.2 项目内部API接口

#### 4.2.1 语音面试接口

**接口地址**: `POST /interview/face2faceChat`

**请求参数**:
- `chatId` (String, 必需): 会话ID
- `userName` (String, 可选): 用户名
- `audio` (MultipartFile, 可选): 音频文件

**请求示例**:
```bash
curl -X POST "http://localhost:8088/interview/face2faceChat" \
     -F "chatId=interview-001" \
     -F "userName=张三" \
     -F "audio=@recording.wav"
```

**响应**:
- Content-Type: `audio/wav`
- Header: `X-Chat-Status: [active|completed]`
- Body: 音频数据流

#### 4.2.2 音频流处理接口

**接口地址**: `GET /api/stream-audio`

**功能**: 实时音频流处理
**响应类型**: `application/octet-stream`

### 4.3 第三方依赖API

#### 4.3.1 阿里云DashScope

**官网**: https://dashscope.aliyun.com/
**文档**: https://help.aliyun.com/zh/dashscope/
**定价**: https://help.aliyun.com/zh/dashscope/product-overview/billing-overview

**支持的模型**:
- `paraformer-realtime-v2`: 实时语音识别
- `paraformer-v2`: 离线语音识别
- `whisper-1`: OpenAI Whisper模型

#### 4.3.2 FFmpeg

**官网**: https://ffmpeg.org/
**下载**: https://ffmpeg.org/download.html
**文档**: https://ffmpeg.org/documentation.html

**Windows安装**:
```bash
# 下载预编译版本
https://www.gyan.dev/ffmpeg/builds/

# 或使用包管理器
choco install ffmpeg
```

**Linux安装**:
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install ffmpeg

# CentOS/RHEL
sudo yum install ffmpeg
```

## 5. 配置说明

### 5.1 应用配置文件

**文件位置**: `consumer/src/main/resources/application.properties`

```properties
# 阿里云DashScope配置
spring.ai.dashscope.api-key=${Ali_API_KEY}
spring.ai.dash-scope.audio.api-key=${Ali_API_KEY}
spring.ai.dash-scope.audio.options.model=sambert-zhixiang-v1

# 服务端口
server.port=8088

# 文件上传限制
spring.servlet.multipart.max-file-size=50MB
spring.servlet.multipart.max-request-size=50MB

# Vosk模型路径（备用）
vosk.model.path=classpath:models/vosk-model-small-cn-0.22

# 认证密钥
auth.token.secret=ai-secret
```

### 5.2 环境变量配置

**必需环境变量**:
```bash
# 阿里云API密钥
export Ali_API_KEY="your_dashscope_api_key"

# FFmpeg路径（Windows）
export FFMPEG_PATH="C:\\software\\ffmpeg-7.1-full_build\\bin\\ffmpeg.exe"
```

### 5.3 Maven依赖配置

```xml
<dependencies>
    <!-- 阿里云DashScope SDK -->
    <dependency>
        <groupId>com.alibaba</groupId>
        <artifactId>dashscope-sdk-java</artifactId>
        <version>2.12.0</version>
    </dependency>

    <!-- Spring Boot Web -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-web</artifactId>
    </dependency>

    <!-- 音频处理 -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-webflux</artifactId>
    </dependency>
</dependencies>
```

## 6. 部署指南

### 6.1 系统要求

**硬件要求**:
- CPU: 2核心以上
- 内存: 4GB以上
- 存储: 10GB可用空间
- 网络: 稳定的互联网连接

**软件要求**:
- Java 17+
- FFmpeg 4.0+
- Maven 3.6+

### 6.2 部署步骤

#### 6.2.1 环境准备

1. **安装Java**:
```bash
# 检查Java版本
java -version

# 如果未安装，下载安装JDK 17+
# https://adoptium.net/
```

2. **安装FFmpeg**:
```bash
# Windows (使用Chocolatey)
choco install ffmpeg

# Linux (Ubuntu)
sudo apt install ffmpeg

# 验证安装
ffmpeg -version
```

3. **获取阿里云API密钥**:
   - 访问 https://dashscope.aliyun.com/
   - 注册账号并创建API密钥
   - 记录API Key用于配置

#### 6.2.2 项目构建

```bash
# 克隆项目
git clone <project-repository>
cd AI-Interview

# 设置环境变量
export Ali_API_KEY="your_api_key_here"

# 构建项目
mvn clean package -DskipTests

# 启动后端服务
java -jar consumer/target/consumer-0.0.1-SNAPSHOT.jar

# 启动前端服务（另一个终端）
java -jar frontend/target/admin-0.0.1-SNAPSHOT.jar
```

#### 6.2.3 Docker部署（可选）

```dockerfile
# Dockerfile
FROM openjdk:17-jdk-slim

# 安装FFmpeg
RUN apt-get update && apt-get install -y ffmpeg

# 复制应用
COPY consumer/target/consumer-0.0.1-SNAPSHOT.jar app.jar

# 设置环境变量
ENV Ali_API_KEY=${Ali_API_KEY}

# 暴露端口
EXPOSE 8088

# 启动应用
ENTRYPOINT ["java", "-jar", "/app.jar"]
```

```bash
# 构建镜像
docker build -t ai-interview .

# 运行容器
docker run -d -p 8088:8088 \
  -e Ali_API_KEY="your_api_key" \
  ai-interview
```

### 6.3 验证部署

1. **检查服务状态**:
```bash
# 检查后端服务
curl http://localhost:8088/actuator/health

# 检查前端服务
curl http://localhost:8080
```

2. **测试语音识别**:
```bash
# 使用测试音频文件
curl -X POST "http://localhost:8088/interview/face2faceChat" \
     -F "chatId=test-001" \
     -F "audio=@test-audio.wav" \
     --output response.wav
```

## 7. 最佳实践

### 7.1 性能优化

#### 7.1.1 音频预处理优化

```java
// 音频质量检查
public boolean isAudioQualityGood(File audioFile) {
    try {
        AudioInputStream audioStream = AudioSystem.getAudioInputStream(audioFile);
        AudioFormat format = audioStream.getFormat();

        // 检查采样率
        if (format.getSampleRate() < 8000) {
            return false;
        }

        // 检查文件大小
        if (audioFile.length() < 1024) { // 小于1KB
            return false;
        }

        return true;
    } catch (Exception e) {
        return false;
    }
}
```

#### 7.1.2 缓存策略

```java
@Service
public class CachedSpeechService {
    private final Cache<String, String> transcriptionCache =
        Caffeine.newBuilder()
            .maximumSize(1000)
            .expireAfterWrite(1, TimeUnit.HOURS)
            .build();

    public String transcribeWithCache(File audioFile) {
        String audioHash = calculateFileHash(audioFile);
        return transcriptionCache.get(audioHash,
            key -> speechToTextService.transcribeAudio(audioFile));
    }
}
```

### 7.2 错误处理

#### 7.2.1 重试机制

```java
@Retryable(value = {Exception.class}, maxAttempts = 3, backoff = @Backoff(delay = 1000))
public String transcribeWithRetry(File audioFile) throws IOException {
    try {
        return speechToTextService.transcribeAudio(audioFile);
    } catch (Exception e) {
        log.warn("语音识别失败，准备重试: {}", e.getMessage());
        throw e;
    }
}

@Recover
public String recover(Exception ex, File audioFile) {
    log.error("语音识别最终失败，使用备用方案: {}", ex.getMessage());
    // 使用Vosk本地模型作为备用
    return voskService.transcribe(audioFile);
}
```

#### 7.2.2 降级策略

```java
@Component
public class SpeechRecognitionFallback {

    public String transcribeWithFallback(File audioFile) {
        try {
            // 主要识别服务
            return dashScopeService.transcribe(audioFile);
        } catch (Exception e) {
            log.warn("主识别服务失败，切换到备用服务");
            try {
                // 备用识别服务
                return voskService.transcribe(audioFile);
            } catch (Exception fallbackException) {
                log.error("所有识别服务都失败");
                return "识别失败，请重试";
            }
        }
    }
}
```

### 7.3 监控和日志

#### 7.3.1 性能监控

```java
@Component
public class SpeechRecognitionMetrics {
    private final MeterRegistry meterRegistry;
    private final Timer recognitionTimer;
    private final Counter successCounter;
    private final Counter failureCounter;

    public SpeechRecognitionMetrics(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        this.recognitionTimer = Timer.builder("speech.recognition.duration")
            .description("语音识别耗时")
            .register(meterRegistry);
        this.successCounter = Counter.builder("speech.recognition.success")
            .description("识别成功次数")
            .register(meterRegistry);
        this.failureCounter = Counter.builder("speech.recognition.failure")
            .description("识别失败次数")
            .register(meterRegistry);
    }

    public String transcribeWithMetrics(File audioFile) {
        return recognitionTimer.recordCallable(() -> {
            try {
                String result = speechToTextService.transcribeAudio(audioFile);
                successCounter.increment();
                return result;
            } catch (Exception e) {
                failureCounter.increment();
                throw e;
            }
        });
    }
}
```

#### 7.3.2 日志配置

```yaml
# logback-spring.xml
logging:
  level:
    com.guangge.Interview.audio: DEBUG
    com.alibaba.dashscope: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/speech-recognition.log
    max-size: 100MB
    max-history: 30
```

### 7.4 安全考虑

#### 7.4.1 API密钥管理

```java
@Configuration
public class SecurityConfig {

    @Value("${spring.ai.dashscope.api-key}")
    private String apiKey;

    @PostConstruct
    public void validateApiKey() {
        if (StringUtils.isEmpty(apiKey) || "your_api_key".equals(apiKey)) {
            throw new IllegalStateException("请配置有效的阿里云API密钥");
        }
    }
}
```

#### 7.4.2 文件安全

```java
public class AudioFileValidator {

    private static final Set<String> ALLOWED_EXTENSIONS =
        Set.of("wav", "mp3", "opus", "m4a");
    private static final long MAX_FILE_SIZE = 50 * 1024 * 1024; // 50MB

    public boolean isValidAudioFile(MultipartFile file) {
        // 检查文件扩展名
        String filename = file.getOriginalFilename();
        if (filename == null) return false;

        String extension = filename.substring(filename.lastIndexOf(".") + 1).toLowerCase();
        if (!ALLOWED_EXTENSIONS.contains(extension)) {
            return false;
        }

        // 检查文件大小
        if (file.getSize() > MAX_FILE_SIZE) {
            return false;
        }

        // 检查MIME类型
        String contentType = file.getContentType();
        return contentType != null && contentType.startsWith("audio/");
    }
}
```

## 8. 故障排除

### 8.1 常见问题

#### 8.1.1 FFmpeg相关问题

**问题**: `FFmpeg 转换失败`
**解决方案**:
```bash
# 检查FFmpeg安装
ffmpeg -version

# 检查路径配置
which ffmpeg  # Linux/Mac
where ffmpeg  # Windows

# 手动指定路径
export FFMPEG_PATH="/usr/local/bin/ffmpeg"
```

#### 8.1.2 API调用问题

**问题**: `API密钥无效`
**解决方案**:
1. 检查API密钥是否正确
2. 确认账户余额充足
3. 验证API权限设置

**问题**: `识别准确率低`
**解决方案**:
1. 确保音频质量（16kHz, 单声道）
2. 减少背景噪音
3. 调整VAD参数
4. 使用更高质量的录音设备

### 8.2 调试技巧

#### 8.2.1 音频文件分析

```java
public void analyzeAudioFile(File audioFile) {
    try {
        AudioInputStream audioStream = AudioSystem.getAudioInputStream(audioFile);
        AudioFormat format = audioStream.getFormat();

        log.info("音频格式分析:");
        log.info("采样率: {} Hz", format.getSampleRate());
        log.info("声道数: {}", format.getChannels());
        log.info("位深度: {} bits", format.getSampleSizeInBits());
        log.info("编码: {}", format.getEncoding());
        log.info("文件大小: {} bytes", audioFile.length());

    } catch (Exception e) {
        log.error("音频文件分析失败: {}", e.getMessage());
    }
}
```

#### 8.2.2 网络连接测试

```java
public boolean testDashScopeConnection() {
    try {
        // 测试网络连接
        URL url = new URL("https://dashscope.aliyuncs.com");
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("GET");
        connection.setConnectTimeout(5000);
        connection.setReadTimeout(5000);

        int responseCode = connection.getResponseCode();
        return responseCode == 200;
    } catch (Exception e) {
        log.error("DashScope连接测试失败: {}", e.getMessage());
        return false;
    }
}
```

## 9. 扩展功能

### 9.1 多语言支持

```java
public class MultiLanguageSpeechService {

    public String transcribeWithLanguageDetection(File audioFile) {
        // 自动语言检测
        String detectedLanguage = detectLanguage(audioFile);

        RecognitionParam param = RecognitionParam.builder()
                .apiKey(apiKey)
                .model("paraformer-realtime-v2")
                .format("wav")
                .sampleRate(16000)
                .parameter("language_hints", new String[]{detectedLanguage})
                .build();

        return recognizer.call(param, audioFile);
    }
}
```

### 9.2 实时流式识别

```java
@Component
public class StreamingSpeechRecognition {

    public Flux<String> transcribeStream(Flux<byte[]> audioStream) {
        return audioStream
            .buffer(Duration.ofSeconds(1))
            .flatMap(this::processAudioChunk)
            .filter(text -> !text.isEmpty());
    }

    private Mono<String> processAudioChunk(List<byte[]> audioChunks) {
        return Mono.fromCallable(() -> {
            // 合并音频块
            byte[] combinedAudio = combineAudioChunks(audioChunks);

            // 创建临时文件
            File tempFile = createTempAudioFile(combinedAudio);

            try {
                return speechToTextService.transcribeAudio(tempFile);
            } finally {
                tempFile.delete();
            }
        }).subscribeOn(Schedulers.boundedElastic());
    }
}
```

---

## 📞 技术支持

如有技术问题，请联系：
- **项目文档**: 参考项目README.md
- **API文档**: https://help.aliyun.com/zh/dashscope/
- **社区支持**: 提交Issue到项目仓库

---

**文档版本**: v1.0
**最后更新**: 2025-01-01
**维护团队**: AI面试系统开发团队
