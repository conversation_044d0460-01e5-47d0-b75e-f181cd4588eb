# ASR 问题修复进度报告

## 执行时间
**开始时间**: 2024年12月19日
**完成时间**: 2024年12月19日
**执行状态**: ✅ 已完成

## 任务清单执行情况

### 1. 讯飞ASR增量重复问题优化 ✅ 已完成

**问题描述**: 讯飞ASR在返回增量结果时，出现如 "好好那好那宽好那宽和好那宽和高好那宽和高分好那宽和高分别好那宽和高分别对好" 这样的严重重复。

**解决方案**:
- ✅ 实现了新的 `removeXunfeiIncrementalDuplicates` 函数，专门处理讯飞ASR特有的前缀式增量重复模式
- ✅ 保留了原有的 `removeGenericIncrementalDuplicates` 函数作为通用处理算法
- ✅ 修改了 `removeIncrementalDuplicates` 函数，优先使用讯飞特有算法，回退到通用算法

**技术实现**:
- 从输入字符串的末尾开始，尝试不同长度的后缀作为"核心真实文本"
- 检查前缀部分是否可以由后缀的前缀按顺序拼接而成
- 选择能够成功匹配且最长的后缀作为清理后的结果

### 2. 阿里ASR task_id不匹配问题修复 ✅ 已完成

**问题描述**: 阿里ASR连接成功后，后续消息中的 task_id 与初始 StartTranscription 消息的 task_id 不一致，导致 "Gateway:MESSAGE_INVALID:The task id 'xxx' of this message doesn't match last message 'yyy'!" 错误。

**解决方案**:
- ✅ 在 `callAlibabaASR` 函数中，生成一次 `taskId` 供整个会话使用
- ✅ 确保 `StartTranscription`、`StopTranscription` 消息都使用相同的 `task_id`
- ✅ 添加了服务端返回的 `task_id` 验证，确保一致性

**技术实现**:
- 在WebSocket连接建立时生成一次UUID作为taskId
- 所有后续消息复用这个taskId
- 添加task_id不匹配的错误处理

### 3. 流式WebM数据PCM转换失败修复 ✅ 已完成

**问题描述**: 对于后续的WebM音频块（非第一个块），使用 ffmpeg 转换为PCM时常失败，报 "EBML header parsing failed" 和 "Invalid data found when processing input" 错误。

**解决方案**:
- ✅ 实现了基于会话的持续 `ffmpeg` 转换流
- ✅ 为每个用户的音频流会话维护一个长期运行的 `ffmpeg` 进程
- ✅ 后续WebM数据块直接写入此 `ffmpeg` 进程的标准输入流
- ✅ 从 `ffmpeg` 进程的标准输出流读取转换后的PCM数据块

**技术实现**:
- 添加了 `FfmpegProcessInfo` 接口和 `ffmpegInstances` Map
- 实现了 `getOrCreateFfmpegInstance` 函数管理FFmpeg实例
- 实现了 `handleIncomingWebmChunk` 函数处理WebM音频块
- 添加了会话结束时的FFmpeg实例清理逻辑

### 4. 语音缓冲和停顿检测时机问题优化 ✅ 已完成

**问题描述**: 语音片段的缓冲、处理以及停顿检测的时机不够精确。当前处理音频的条件是 buffer.chunks.length >= 3 || buffer.totalSize >= 50000。

**解决方案**:
- ✅ 实现了基于超时的停顿检测逻辑
- ✅ 添加了 `PcmAudioBuffer` 接口用于PCM数据缓冲
- ✅ 实现了 `onPcmChunkReceived` 函数处理PCM数据块
- ✅ 实现了 `processAndSendAudioForASR` 函数处理音频段
- ✅ 添加了全局停顿检测间隔检查器

**技术实现**:
- 当接收到新的PCM音频块时，记录当前时间
- 设置周期性检查器，定期遍历所有活动的音频缓冲区
- 如果缓冲区超过停顿超时时间且有数据，触发ASR处理
- 添加了缓冲区过大时的强制发送机制

## 代码修改总结

### 新增接口和类型
- `FfmpegProcessInfo`: FFmpeg流式处理接口
- `PcmAudioBuffer`: PCM音频缓冲区接口

### 新增全局变量
- `pcmAudioBuffers`: PCM数据缓冲区Map
- `ffmpegInstances`: FFmpeg实例管理Map

### 新增函数
- `removeXunfeiIncrementalDuplicates`: 讯飞特有去重算法
- `removeGenericIncrementalDuplicates`: 通用去重算法
- `getOrCreateFfmpegInstance`: FFmpeg实例管理
- `handleIncomingWebmChunk`: WebM音频块处理
- `onPcmChunkReceived`: PCM数据块处理
- `processAndSendAudioForASR`: 音频段ASR处理
- `handleAudioStreamEnd`: 音频流结束处理
- `cleanupSessionFfmpeg`: FFmpeg实例清理
- `cleanupSessionAudioBuffer`: 音频缓冲区清理

### 修改的函数
- `removeIncrementalDuplicates`: 重构为调用新的去重算法
- `processAndSendAudioForASR`: 修改为调用新的`transcribePCMAudio`函数
- WebSocket消息处理逻辑: 使用新的流式处理方式
- WebSocket关闭清理逻辑: 添加新缓冲区和实例的清理

### 关键Bug修复
- **FFmpeg冲突问题**: 修复了新旧FFmpeg处理系统的冲突，避免"write EOF"错误
- **函数调用错误**: 修复了`transcribeWithAlibabaASR`函数名错误，改为正确的`callAlibabaASR`

## 项目运行状态

✅ **后端服务**: 成功启动在 http://localhost:3000
✅ **前端服务**: 成功启动在 http://localhost:5173
✅ **编译状态**: 无错误，所有修改已成功应用
✅ **FFmpeg冲突问题**: 已修复，避免了重复的FFmpeg进程冲突
✅ **API连接**: 前后端通信正常，/api/positions等API正常工作

## 预期效果

1. **讯飞ASR**: 增量重复问题得到显著改善，能够正确提取核心文本
2. **阿里ASR**: task_id不匹配错误得到解决，连接稳定性提升
3. **音频转换**: 流式WebM到PCM转换成功率大幅提升，支持连续音频流
4. **停顿检测**: 基于时间的停顿检测更加精确，减少误触发

## 下一步建议

1. 进行实际音频测试，验证各项修复的效果
2. 监控系统日志，观察ASR服务的稳定性
3. 根据实际使用情况调整停顿检测超时时间
4. 考虑添加更多的错误处理和恢复机制
