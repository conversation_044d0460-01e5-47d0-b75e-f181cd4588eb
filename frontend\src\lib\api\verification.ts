// API 基础URL
const API_BASE_URL = process.env.NODE_ENV === 'production'
  ? 'https://mianshijun.xyz/api'
  : '/api';

// 自定义错误类
export class VerificationError extends Error {
  code?: string;
  status?: number;
  details?: any;

  constructor(message: string, code?: string, status?: number, details?: any) {
    super(message);
    this.name = 'VerificationError';
    this.code = code;
    this.status = status;
    this.details = details;
  }
}

export interface SendVerificationCodeRequest {
  identifier: string; // 邮箱或手机号
  type: 'EMAIL' | 'SMS';
  purpose?: 'LOGIN';
}

export interface SendVerificationCodeResponse {
  success: boolean;
  message: string;
  data?: {
    expiresIn: number;
    canResendAfter: number;
  };
  error?: {
    code: string;
    details?: any;
  };
}

export interface LoginWithCodeRequest {
  identifier: string; // 邮箱或手机号
  code: string;
  type: 'EMAIL' | 'SMS';
  inviteCode?: string; // 邀请码（可选）
}

export interface LoginWithCodeResponse {
  success: boolean;
  message: string;
  data?: {
    token: string;
    user: {
      id: string;
      email: string;
      name: string;
      phoneNumber?: string;
      balance?: {
        amount: number;
      };
    };
    expiresIn: number;
  };
  error?: {
    code: string;
  };
}

// 发送验证码
export async function sendVerificationCode(
  identifier: string,
  type: 'EMAIL' | 'SMS'
): Promise<SendVerificationCodeResponse> {
  try {
    const response = await fetch(`${API_BASE_URL}/auth/send-verification-code`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        identifier,
        type,
        purpose: 'LOGIN'
      }),
    });

    const data = await response.json();

    if (!response.ok) {
      // 根据错误代码提供更友好的错误信息
      let errorMessage = data.message || '发送验证码失败';

      if (response.status === 429) {
        errorMessage = data.message || '请求过于频繁，请稍后重试';
      } else if (response.status === 400) {
        if (data.error?.code === 'INVALID_EMAIL') {
          errorMessage = '请输入有效的邮箱地址';
        } else if (data.error?.code === 'INVALID_PHONE') {
          errorMessage = '请输入有效的手机号';
        }
      } else if (response.status >= 500) {
        errorMessage = '服务器暂时不可用，请稍后重试';
      }

      throw new VerificationError(
        errorMessage,
        data.error?.code,
        response.status,
        data.error?.details
      );
    }

    return data;
  } catch (error: any) {
    console.error('Send verification code error:', error);

    if (error instanceof VerificationError) {
      throw error;
    }

    // 网络错误或其他未知错误
    if (error.name === 'TypeError' && error.message.includes('fetch')) {
      throw new VerificationError('网络连接失败，请检查网络后重试', 'NETWORK_ERROR');
    }

    throw new VerificationError(error.message || '发送验证码失败，请稍后重试', 'UNKNOWN_ERROR');
  }
}

// 验证码登录
export async function loginWithCode(
  identifier: string,
  code: string,
  type: 'EMAIL' | 'SMS',
  inviteCode?: string
): Promise<LoginWithCodeResponse> {
  try {
    const response = await fetch(`${API_BASE_URL}/auth/login-with-code`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        identifier,
        code,
        type,
        ...(inviteCode && { inviteCode })
      }),
    });

    const data = await response.json();

    if (!response.ok) {
      // 根据错误代码提供更友好的错误信息
      let errorMessage = data.message || '登录失败';

      if (response.status === 400) {
        if (data.error?.code === 'INVALID_CODE') {
          errorMessage = '验证码错误，请重新输入';
        } else if (data.error?.code === 'ACCOUNT_LOCKED') {
          errorMessage = '账户已被锁定，请稍后重试';
        } else if (data.error?.code === 'VALIDATION_ERROR') {
          errorMessage = '输入格式不正确，请检查后重试';
        }
      } else if (response.status === 404) {
        errorMessage = '用户不存在，请先注册';
      } else if (response.status >= 500) {
        errorMessage = '服务器暂时不可用，请稍后重试';
      }

      throw new VerificationError(
        errorMessage,
        data.error?.code,
        response.status,
        data.error?.details
      );
    }

    return data;
  } catch (error: any) {
    console.error('Login with code error:', error);

    if (error instanceof VerificationError) {
      throw error;
    }

    // 网络错误或其他未知错误
    if (error.name === 'TypeError' && error.message.includes('fetch')) {
      throw new VerificationError('网络连接失败，请检查网络后重试', 'NETWORK_ERROR');
    }

    throw new VerificationError(error.message || '登录失败，请稍后重试', 'UNKNOWN_ERROR');
  }
}

// 验证邮箱格式
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// 验证手机号格式
export function isValidPhone(phone: string): boolean {
  const phoneRegex = /^1[3-9]\d{9}$/;
  return phoneRegex.test(phone);
}

// 验证验证码格式
export function isValidCode(code: string): boolean {
  const codeRegex = /^\d{6}$/;
  return codeRegex.test(code);
}
