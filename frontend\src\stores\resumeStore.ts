import { create } from 'zustand';
import { persist, PersistOptions } from 'zustand/middleware';
import { Resume, resumeService } from '../lib/api/apiService';

// 专家建议的状态模型：明确区分持久化和临时状态
interface ResumeDisplayState {
  // 代表已保存在服务器上的简历
  persisted: Resume | null;
  // 代表用户在当前会话中选择的新文件
  temporary: {
    file: File;
    fileName: string;
    status: 'selected' | 'uploading' | 'error';
    error?: string;
    uploadTime: Date;
  } | null;
}

interface ResumeState {
  // 新的统一状态模型
  displayState: ResumeDisplayState;
  // 用户所有的简历列表（从后端获取）
  resumes: Resume[];
  // 是否正在加载简历数据
  isLoading: boolean;
  // 是否正在删除简历
  isDeleting: boolean;
  // 加载错误信息
  error: string | null;

  // 兼容性：保留旧的uploadedResume接口
  uploadedResume: {
    fileName: string;
    uploadTime: Date;
  } | null;

  // 新的状态管理方法
  setTemporaryFile: (file: File, status?: 'selected' | 'uploading' | 'error', error?: string) => void;
  clearTemporaryFile: () => void;
  updateTemporaryStatus: (status: 'selected' | 'uploading' | 'error', error?: string) => void;

  // 兼容性方法：保留旧接口
  setUploadedResume: (fileName: string) => void;
  clearUploadedResume: () => void;

  // 从后端加载用户的简历
  loadResumes: () => Promise<Resume[]>;
  // 设置简历列表
  setResumes: (resumes: Resume[]) => void;
  // 添加新简历到列表
  addResume: (resume: Resume) => void;
  // 从前端状态中移除简历（仅前端操作）
  removeResume: (id: string) => void;
  // 完整删除简历（调用API并更新前端状态）
  deleteResume: (id: string) => Promise<void>;

  // 便利方法：获取当前应该显示的简历
  getCurrentDisplayResume: () => { fileName: string; source: 'temporary' | 'persisted' } | null;
  hasAnyResume: () => boolean;
}

// 定义持久化类型
type ResumePersist = (
  config: (
    set: (state: Partial<ResumeState>) => void,
    get: () => ResumeState,
    api: any
  ) => ResumeState,
  options: PersistOptions<ResumeState, Pick<ResumeState, 'resumes' | 'uploadedResume' | 'displayState'>>
) => any;

const useResumeStore = create<ResumeState>()(
  (persist as ResumePersist)(
    (set, get) => ({
      // 初始状态
      displayState: {
        persisted: null,
        temporary: null,
      },
      uploadedResume: null, // 兼容性
      resumes: [],
      isLoading: false,
      isDeleting: false,
      error: null,

      // 新的状态管理方法
      setTemporaryFile: (file: File, status: 'selected' | 'uploading' | 'error' = 'selected', error?: string) => {
        const { displayState } = get();
        set({
          displayState: {
            ...displayState,
            temporary: {
              file,
              fileName: file.name,
              status,
              error,
              uploadTime: new Date(),
            }
          },
          // 同时更新兼容性字段
          uploadedResume: {
            fileName: file.name,
            uploadTime: new Date(),
          }
        });
      },

      clearTemporaryFile: () => {
        const { displayState } = get();
        set({
          displayState: {
            ...displayState,
            temporary: null,
          },
          // 同时清除兼容性字段
          uploadedResume: null,
        });
      },

      updateTemporaryStatus: (status: 'selected' | 'uploading' | 'error', error?: string) => {
        const { displayState } = get();
        if (displayState.temporary) {
          set({
            displayState: {
              ...displayState,
              temporary: {
                ...displayState.temporary,
                status,
                error,
              }
            }
          });
        }
      },

      // 兼容性方法：保留旧接口
      setUploadedResume: (fileName: string) =>
        set({
          uploadedResume: {
            fileName,
            uploadTime: new Date()
          }
        }),

      // 清除当前上传的简历
      clearUploadedResume: () => {
        const { displayState } = get();
        set({
          uploadedResume: null,
          displayState: {
            ...displayState,
            temporary: null,
          }
        });
      },
      
      // 从后端加载用户的所有简历
      loadResumes: async () => {
        set({ isLoading: true, error: null });
        try {
          console.log('正在从后端加载简历数据...');
          const resumes = await resumeService.getResumes();
          console.log('成功加载简历数据:', resumes);

          // 更新持久化状态
          const { displayState } = get();
          const latestResume = resumes && resumes.length > 0 ? resumes[0] : null;

          set({
            resumes,
            isLoading: false,
            displayState: {
              ...displayState,
              persisted: latestResume,
            }
          });
          return resumes;
        } catch (error: any) {
          console.error('加载简历失败:', error);
          set({
            error: error.message || '加载简历失败',
            isLoading: false
          });
          throw error;
        }
      },
      
      // 设置简历列表
      setResumes: (resumes: Resume[]) => {
        const { displayState } = get();
        const latestResume = resumes && resumes.length > 0 ? resumes[0] : null;
        set({
          resumes,
          displayState: {
            ...displayState,
            persisted: latestResume,
          }
        });
      },

      // 添加新简历到列表
      addResume: (resume: Resume) => {
        const { resumes, displayState } = get();
        const newResumes = [resume, ...resumes]; // 新简历放在最前面
        set({
          resumes: newResumes,
          displayState: {
            ...displayState,
            persisted: resume, // 新添加的简历成为最新的持久化简历
          }
        });
      },
      
      // 从前端状态中移除简历（仅前端操作）
      removeResume: (id: string) => {
        const { resumes } = get();
        set({ resumes: resumes.filter(resume => resume.id !== id) });
      },
      
      // 完整删除简历（调用API并更新前端状态）
      deleteResume: async (id: string) => {
        set({ isDeleting: true, error: null });
        try {
          console.log('正在删除简历:', id);
          // 调用后端API删除简历
          await resumeService.deleteResume(id);
          console.log('简历删除成功');

          // 从前端状态中移除简历
          const { resumes, displayState } = get();
          const updatedResumes = resumes.filter(resume => resume.id !== id);
          const newLatestResume = updatedResumes.length > 0 ? updatedResumes[0] : null;

          set({
            resumes: updatedResumes,
            isDeleting: false,
            displayState: {
              ...displayState,
              persisted: newLatestResume,
            }
          });
        } catch (error: any) {
          console.error('删除简历失败:', error);
          set({
            error: error.message || '删除简历失败',
            isDeleting: false
          });
          throw error;
        }
      },

      // 便利方法：获取当前应该显示的简历
      getCurrentDisplayResume: () => {
        const { displayState } = get();

        // 优先显示临时文件
        if (displayState.temporary) {
          return {
            fileName: displayState.temporary.fileName,
            source: 'temporary' as const,
          };
        }

        // 其次显示持久化简历
        if (displayState.persisted) {
          return {
            fileName: displayState.persisted.fileName,
            source: 'persisted' as const,
          };
        }

        return null;
      },

      // 便利方法：检查是否有任何简历
      hasAnyResume: () => {
        const { displayState } = get();
        return !!(displayState.temporary || displayState.persisted);
      }
    }),
    {
      name: 'resume-storage', // 本地存储的键名
      partialize: (state: ResumeState) => ({
        resumes: state.resumes,
        uploadedResume: state.uploadedResume,
        displayState: {
          persisted: state.displayState.persisted,
          temporary: null, // 临时文件不持久化
        }
      }), // 只持久化这些字段
    }
  )
);

export default useResumeStore;
