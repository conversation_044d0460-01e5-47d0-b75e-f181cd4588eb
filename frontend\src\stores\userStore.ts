import { create } from 'zustand';
import { persist, PersistOptions } from 'zustand/middleware';

// 用户信息接口
export interface UserInfo {
  id: string;
  email: string;
  name?: string;
  phoneNumber?: string;
  createdAt: string;
}

// 主题类型
export type ThemeMode = 'light' | 'dark' | 'system';

// 用户状态接口
interface UserState {
  // 用户信息
  userInfo: UserInfo | null;
  isLoadingUserInfo: boolean;
  userInfoError: string | null;

  // 主题设置
  themeMode: ThemeMode;

  // 操作方法
  setUserInfo: (userInfo: UserInfo | null) => void;
  setLoadingUserInfo: (loading: boolean) => void;
  setUserInfoError: (error: string | null) => void;
  setThemeMode: (mode: ThemeMode) => void;
  clearUserData: () => void;
}

// 持久化配置
type UserPersist = (
  config: (
    set: (state: Partial<UserState>) => void,
    get: () => UserState,
    api: any
  ) => UserState,
  options: PersistOptions<UserState, Pick<UserState, 'themeMode'>>
) => any;

// 创建用户状态存储
const useUserStore = create<UserState>()(
  (persist as UserPersist)(
    (set, get) => ({
      // 初始状态
      userInfo: null,
      isLoadingUserInfo: false,
      userInfoError: null,
      themeMode: 'system',

      // 设置用户信息
      setUserInfo: (userInfo: UserInfo | null) => {
        set({ 
          userInfo,
          userInfoError: null // 清除错误状态
        });
      },

      // 设置加载状态
      setLoadingUserInfo: (loading: boolean) => {
        set({ isLoadingUserInfo: loading });
      },

      // 设置错误状态
      setUserInfoError: (error: string | null) => {
        set({ userInfoError: error });
      },

      // 设置主题模式
      setThemeMode: (mode: ThemeMode) => {
        set({ themeMode: mode });

        // 应用主题到DOM
        const root = document.documentElement;

        if (mode === 'dark') {
          root.classList.add('dark');
        } else if (mode === 'light') {
          root.classList.remove('dark');
        } else {
          // system mode - 检测系统偏好
          const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
          if (prefersDark) {
            root.classList.add('dark');
          } else {
            root.classList.remove('dark');
          }
        }
      },

      // 清除用户数据
      clearUserData: () => {
        set({
          userInfo: null,
          isLoadingUserInfo: false,
          userInfoError: null,
        });
      },
    }),
    {
      name: 'user-storage', // 本地存储的键名
      partialize: (state: UserState) => ({ 
        themeMode: state.themeMode 
      }), // 只持久化主题设置
    }
  )
);

// 初始化主题
const initializeTheme = () => {
  const { themeMode, setThemeMode } = useUserStore.getState();
  setThemeMode(themeMode); // 触发主题应用逻辑
};

// 监听系统主题变化
const setupSystemThemeListener = () => {
  const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
  
  const handleChange = () => {
    const { themeMode, setThemeMode } = useUserStore.getState();
    if (themeMode === 'system') {
      setThemeMode('system'); // 重新应用系统主题
    }
  };

  mediaQuery.addEventListener('change', handleChange);
  
  // 返回清理函数
  return () => mediaQuery.removeEventListener('change', handleChange);
};

// 导出初始化函数
export { initializeTheme, setupSystemThemeListener };

export default useUserStore;
