# 面试君设计系统 (MianshiJun Design System)

版本: 1.2 (MVP Aligned - React & Vite)

最后更新: 2025年5月10日

## 0. MVP阶段实现说明

本文档定义了“面试君”产品的完整设计系统规范。在MVP（最小可行产品）阶段，我们将遵循以下原则进行组件和样式的实现：

- **核心优先**: 优先实现P0级核心功能页面所需的关键组件及其主要状态。
- **样式对齐**: 颜色、排版、基础间距等核心视觉元素严格按照本设计系统执行。
- **组件简化**: 对于复杂组件（如表单控件、导航），MVP阶段可能只实现其最常用或核心的变体和状态。例如，按钮主要实现中号的Primary和Secondary类型。
- **逐步完善**: 未在MVP阶段完全实现的组件细节和动效，将在后续迭代 (P1, P2) 中逐步补充和优化。
- **Tailwind CSS 作为唯一真实来源**: 所有样式定义最终以 `tailwind.config.js` 的配置和Tailwind CSS类名的应用为准。

## 1. 设计理念与原则

### 1.1. 核心原则

- **用户为中心 (User-Centric)**: 设计决策始终以提升用户在面试场景下的体验、效率和信心为首要目标。
- **一致性 (Consistency)**: 在整个产品中保持视觉风格、交互模式和术语的统一，降低用户学习成本。
- **高效性 (Efficiency)**: 优化信息架构和交互流程，确保用户能够快速获取所需信息并完成操作，尤其在面试的紧张环境中。
- **简洁性 (Simplicity)**: 界面元素和功能设计力求简洁明了，避免不必要的复杂性，减少认知负荷。
- **可访问性 (Accessibility)**: 遵循WCAG 2.1 AA标准，确保产品对残障用户友好。
- **专业性 (Professionalism)**: 设计风格体现产品的专业度和可信赖感，符合AI辅助工具的定位。
- **适应性 (Adaptability)**: 系统需支持响应式设计，在不同设备和屏幕尺寸上均提供良好体验。

### 1.2. 设计语言关键词

现代、扁平、专业、科技感、友好、清晰、高效。

## 2. 视觉识别系统 (Visual Identity)

### 2.1. 品牌标志 (Logo)

- 主标志 (Primary Logo)

  :

  - 组成: “面试君”中文字样 + 抽象化的对话气泡与AI脑回路结合的图形标志。
  - 图形与文字比例: 图形高度与文字“面”字高度一致，图形位于文字左侧，间距为“面”字宽度的1/4。
  - 含义: 体现AI赋能沟通，辅助面试。

- 标志色版 (Color Versions)

  :

  - **全彩版 (Full Color)**: 文字为 `text-brand-blue`，图形为 `text-brand-blue` 和 `text-brand-orange` 的组合。
  - **单色蓝版 (Monochrome Blue)**: 文字和图形均为 `text-brand-blue`。
  - **反白版 (Reversed White)**: 文字和图形均为 `text-white`。
  - **单色黑版 (Monochrome Black)**: 文字和图形均为 `text-gray-800`。

- 最小使用尺寸 (Minimum Size)

  :

  - 数字应用: 标志整体高度不小于 `h-6` (`24px`)。
  - 印刷应用: 标志整体高度不小于 `10mm`。

- 保护区域 (Clear Space)

  :

  - 标志四周必须保留相当于标志高度 `1/3` 的空白区域。

### 2.2. 色彩系统 (Color System)

**核心原则**: 所有颜色定义在 `tailwind.config.js` 的 `theme.extend.colors` 中，并在组件中通过Tailwind类名调用。

#### 2.2.1. 主要色彩 (Primary Colors)

- 品牌主蓝 (brand-blue)

  :

  - 定义: `colors: { 'brand-blue': '#1E40AF' }`
  - Tailwind类: `bg-brand-blue`, `text-brand-blue`, `border-brand-blue`
  - 用途: 品牌标识、主要行动点 (Primary CTAs)、导航高亮、重要信息标题。

- 辅助行动橙 (brand-orange)

  :

  - 定义: `colors: { 'brand-orange': '#F97316' }`
  - Tailwind类: `bg-brand-orange`, `text-brand-orange`, `border-brand-orange`
  - 用途: 次要行动点 (Secondary CTAs)、警告提示、突出显示特定功能或优惠。

#### 2.2.2. 主色调色板 (Primary Palette - Shades & Tints)

- 在 

  ```
  tailwind.config.js
  ```

   中，可以基于品牌主蓝和辅助行动橙生成色阶，或直接使用Tailwind内置的 

  ```
  blue
  ```

   和 

  ```
  orange
  ```

   色阶并指定特定色号用于品牌色。

  - 例如，如果 `brand-blue` 对应 `blue-800`，则其色阶可参考 `blue-900` 至 `blue-50`。

- 品牌蓝系 (Blue Scale)

  :

  - `blue-900` (`#1E3A8A`)
  - `blue-800` (`#1E40AF`) - **`brand-blue` 可指定为此值**
  - `blue-700` (`#1D4ED8`)
  - `blue-600` (`#2563EB`)
  - `blue-500` (`#3B82F6`)
  - `blue-200` (`#BFDBFE`)
  - `blue-100` (`#DBEAFE`)
  - `blue-50` (`#EFF6FF`)

- 辅助橙系 (Orange Scale)

  :

  - `orange-700` (`#C2410C`)
  - `orange-600` (`#EA580C`)
  - `orange-500` (`#F97316`) - **`brand-orange` 可指定为此值**
  - `orange-400` (`#FB923C`)
  - `orange-200` (`#FED7AA`)
  - `orange-100` (`#FFEDD5`)

#### 2.2.3. 中性色彩 (Neutral Colors) - 使用Tailwind内置的 `gray` 色系

- **纯白 (white)**: `text-white`, `bg-white`

- 灰色系 (Gray Scale)

  :

  - `gray-900` (`#111827`)
  - `gray-800` (`#1F2937`)
  - `gray-700` (`#374151`)
  - `gray-600` (`#4B5563`)
  - `gray-500` (`#6B7280`)
  - `gray-400` (`#9CA3AF`)
  - `gray-300` (`#D1D5DB`)
  - `gray-200` (`#E5E7EB`)
  - `gray-100` (`#F3F4F6`)
  - `gray-50` (`#F9FAFB`)

#### 2.2.4. 功能色彩 (Functional Colors) - 使用Tailwind内置色系，并可定义别名

- 成功 (success)

  :

  - 主要: `text-green-600`, `bg-green-500` (基于 `#10B981`)
  - 背景: `bg-green-50` 或 `bg-green-100`
  - 边框: `border-green-500`
  - 文本: `text-green-700` 或 `text-green-800`

- 警告 (warning)

  :

  - 主要: `text-amber-600`, `bg-amber-500` (基于 `#F59E0B`)
  - 背景: `bg-amber-50` 或 `bg-amber-100`
  - 边框: `border-amber-500`
  - 文本: `text-amber-700` 或 `text-amber-800`

- 错误 (error)

  :

  - 主要: `text-red-600`, `bg-red-500` (基于 `#EF4444`)
  - 背景: `bg-red-50` 或 `bg-red-100`
  - 边框: `border-red-500`
  - 文本: `text-red-700` 或 `text-red-800`

- 信息 (info)

  :

  - 主要: `text-blue-600`, `bg-blue-500` (基于 `#3B82F6`)
  - 背景: `bg-blue-50` 或 `bg-blue-100`
  - 边框: `border-blue-500`
  - 文本: `text-blue-700` 或 `text-blue-800`

#### 2.2.5. 色彩使用规范

- **对比度**: 确保文本与背景色的对比度至少符合WCAG AA级别。
- **主色与辅助色比例**: 整体界面以中性色和 `brand-blue` 为主，`brand-orange` 和功能色点缀使用。
- **MVP阶段**: 优先保证核心交互元素的颜色符合规范。

## 3. 排版系统 (Typography)

### 3.1. 字体家族 (Font Family)

- 配置

  : 在 

  ```
  tailwind.config.js
  ```

   中配置 

  ```
  theme.fontFamily
  ```

  :

  JavaScript

  ```
  // tailwind.config.js
  module.exports = {
    theme: {
      extend: {
        fontFamily: {
          sans: ['Noto Sans SC', 'Inter', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Open Sans', 'Helvetica Neue', 'sans-serif', 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'],
          mono: ['SFMono-Regular', 'Menlo', 'Monaco', 'Consolas', 'Liberation Mono', 'Courier New', 'monospace'],
        },
      },
    },
  }
  ```

- **引入**: 确保在项目中通过CSS (`@font-face`) 正确引入 `Noto Sans SC` 和 `Inter` 字体文件。

- **MVP阶段**: 确保 `Inter` 字体正确加载和应用。`Noto Sans SC` 作为中文首选，若有中文字符则必须保证其加载。

### 3.2. 字重 (Font Weights) - 使用Tailwind类名

- `font-light` (300)
- `font-normal` (400) - 正文默认
- `font-medium` (500) - 按钮文本、标签
- `font-semibold` (600) - 卡片标题、小节标题
- `font-bold` (700) - 页面主标题

### 3.3. 字体大小与行高 (Font Sizes & Line Heights) - 使用Tailwind类名

| **用途 (Semantic Use)**  | **Tailwind 类 (Size / Leading)**          | **备注**                        |
| ------------------------ | ----------------------------------------- | ------------------------------- |
| **显示标题 (Display)**   | `text-5xl lg:text-6xl` / `leading-tight`  | 极少数情况                      |
| **页面主标题 (h1)**      | `text-4xl lg:text-5xl` / `leading-normal` | 每个页面的唯一主标题            |
| **章节标题 (h2)**        | `text-3xl lg:text-4xl` / `leading-snug`   | 主要内容区域的标题              |
| **区块标题 (h3)**        | `text-2xl lg:text-3xl` / `leading-normal` | 卡片标题、次级区块标题          |
| **小节标题 (h4)**        | `text-xl lg:text-2xl` / `leading-relaxed` | 更细分内容的标题                |
| **组件标题 (h5)**        | `text-lg lg:text-xl` / `leading-normal`   | 组件内部的标题，如模态框标题    |
| **强调/引导文本 (Lead)** | `text-lg` / `leading-relaxed`             | 用于段落前的引导性文本          |
| **正文 (Body 1 / p)**    | `text-base` / `leading-loose`             | 主要段落文本 (`16px` 基础)      |
| **次要正文 (Body 2)**    | `text-sm` / `leading-relaxed`             | 辅助性描述文本、注释 (`14px`)   |
| **按钮文本 (Button)**    | `text-base` (中号按钮) / `leading-none`   | 行高固定为1以保证垂直居中       |
| **小号按钮/标签**        | `text-sm` / `leading-none`                |                                 |
| **说明/图注 (Caption)**  | `text-xs` / `leading-normal`              | 图片注释、表单辅助提示 (`12px`) |
| **超小文本 (Overline)**  | `text-[10px]` / `leading-normal`          | 用于日期上方的小标题等 (自定义) |

- **MVP阶段**: 重点实现 `h1-h4`, `Body 1`, `Body 2`, `Button`, `Caption` 的排版。

### 3.4. 段落与间距 (Paragraphs & Spacing)

- **段落最大宽度**: `max-w-prose` (Tailwind类)。
- **段间距**: 使用 `mb-4` (1rem) 或 `mb-6` (1.5rem)。
- **列表间距**: 列表项 `li` 之间使用 `mb-2` 或 `mb-1`。

### 3.5. 文本链接 (Text Links)

- 视觉样式

  :

  - 默认: `text-blue-600 hover:text-blue-800 underline`
  - `focus-visible:ring-2 focus-visible:ring-offset-1 focus-visible:ring-blue-500 rounded-sm`

- **下划线**: Tailwind默认处理，可按需调整。

### 3.6. 文本对齐 (Text Alignment)

- `text-left`, `text-center`, `text-right`。

## 4. 布局系统 (Layout System)

### 4.1. 栅格系统 (Grid System)

- **实现**: 使用Tailwind CSS的栅格类 (`grid`, `grid-cols-*`, `gap-*`)。

- 列间距 (Gutter)

  :

  - 手机: `gap-4` (1rem)
  - 平板: `gap-6` (1.5rem)
  - 桌面: `gap-8` (2rem)

- 容器外边距 (Container Padding/Margin)

  :

  - 手机: `p-4`
  - 平板: `p-8`
  - 桌面: `px-8 lg:px-16`，内容区 `max-w-7xl mx-auto`。

### 4.2. 间距系统 (Spacing System) - 使用Tailwind的间距刻度

- **基础单位**: Tailwind的 `1` unit = `0.25rem` (`4px`)。
- **间距等级**: `p-0`, `p-1`, `p-2`, `p-3`, `p-4`, `p-5`, `p-6`, `p-8`, `p-10`, `p-12`, `p-16`, `p-20`, `p-24` 等。
- **MVP阶段**: 常用间距 `p-2, p-4, p-6, p-8` 和对应的 `m-*` 必须统一。

### 4.3. 断点 (Breakpoints) - Tailwind CSS默认断点

- `sm`: `640px`
- `md`: `768px`
- `lg`: `1024px`
- `xl`: `1280px`
- `2xl`: `1536px`

### 4.4. Z-轴层级 (Z-index Scale) - Tailwind `z-*` 类

- `z-10`, `z-20`, `z-30`, `z-40`, `z-50`。

## 5. 图标系统 (Iconography)

### 5.1. 图标库 (Icon Library)

- **首选**: Lucide Icons (https://lucide.dev/) - 以SVG形式直接在React组件中使用或通过 `lucide-react` 库。
- **备选/补充**: Heroicons (https://heroicons.com/) - Tailwind UI常用。

### 5.2. 图标样式 (Icon Style)

- **主要风格**: 线性图标 (`stroke-width="1.5"` 或 `stroke-width="2"`)。

### 5.3. 图标尺寸 (Icon Sizes) - Tailwind `h-* w-*` 类

- `h-3 w-3` (`12px`)
- `h-4 w-4` (`16px`) - 默认
- `h-5 w-5` (`20px`)
- `h-6 w-6` (`24px`)

### 5.4. 图标颜色 (Icon Colors)

- **默认**: `text-current` (继承父元素文本颜色)。
- **功能性图标**: `text-success-600`, `text-warning-600`, `text-error-600`, `text-info-600`。

### 5.5. 使用规范

- **语义化**: 提供 `aria-label` 或通过 `aria-hidden="true"`。
- **点击区域**: 若可点击，确保父元素有足够的 `padding` 以扩大点击区域。

## 6. 组件规范 (Component System)

**MVP阶段实现说明**: 优先实现中号(default)尺寸的Primary和Secondary按钮，以及基础的输入框、标签、辅助文本。卡片和模态框实现基础版本。

### 6.1. 按钮 (Buttons) - 使用Tailwind类组合

#### 6.1.1. 类型 (Types)

- **主按钮 (Primary Button)**: `bg-brand-blue hover:bg-blue-700 text-white font-medium`
- **次要按钮 (Secondary Button)**: `bg-white text-brand-blue border border-brand-blue hover:bg-blue-50`
- **文本按钮 (Text Button)**: `text-brand-blue hover:bg-blue-50`
- **危险按钮 (Destructive Button)**: `bg-error-500 hover:bg-red-700 text-white` (主) / `text-error-600 hover:bg-red-50` (文本)

#### 6.1.2. 尺寸 (Sizes) - 通过 `px-* py-* text-*` 控制

- **中号 (Medium) - 默认**: `py-2 px-4 text-base rounded-md`
- **小号 (Small)**: `py-1 px-3 text-sm rounded-md`
- **大号 (Large)**: `py-3 px-6 text-lg rounded-md`
- **MVP阶段**: 优先实现中号。

#### 6.1.3. 状态 (States)

- **默认**: 如上。
- **悬停 (Hover)**: 已在类型中通过 `hover:` 定义。
- **激活/点击 (Active/Pressed)**: 可通过 `active:bg-blue-900` (主按钮) 等定义。
- **聚焦 (Focus)**: `focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-blue` (或对应行动色)。
- **禁用 (Disabled)**: `disabled:opacity-50 disabled:cursor-not-allowed`。背景和文本颜色需相应调整为灰色系，如 `bg-gray-200 text-gray-400 border-gray-200`。

#### 6.1.4. 带图标按钮 (Button with Icon)

- 图标与文本间距: `space-x-2` (用于Flex布局)。
- 单独图标按钮: `p-2` (调整padding)，提供 `aria-label`。

#### 6.1.5. 圆角 (Border Radius)

- `rounded-md` (`6px`) - 默认
- `rounded` (`4px`)
- `rounded-full`

### 6.2. 表单控件 (Form Controls)

#### 6.2.1. 输入框 (Input Fields)

- 通用样式

  :

  ```
  block w-full rounded-md border-gray-300 shadow-sm focus:border-brand-blue focus:ring-brand-blue sm:text-sm
  ```

  (Tailwind Forms插件基础样式，可定制)

  - `py-2 px-3` (中号)

- 状态

  :

  - **聚焦**: 已包含在上述类中。
  - **禁用**: `disabled:bg-gray-100 disabled:text-gray-400 disabled:border-gray-200`
  - **错误**: `border-error-500 text-error-600 focus:border-error-500 focus:ring-error-500`

- **MVP阶段**: 实现基础文本输入框及其Default, Focus, Disabled, Error状态。

#### 6.2.2. 标签 (Labels)

- `block text-sm font-medium text-gray-700 mb-1`

#### 6.2.3. 辅助文本/提示信息 (Helper Text / Hint Text)

- `mt-1 text-xs text-gray-500` (常规)
- `mt-1 text-xs text-error-600` (错误)

#### 6.2.4. 复选框 (Checkboxes) 与 单选按钮 (Radio Buttons)

- **样式**: `rounded text-brand-blue focus:ring-brand-blue border-gray-300` (Tailwind Forms插件)
- **标签文本**: `ml-2 text-sm text-gray-700`
- **MVP阶段**: 实现基础功能和样式。

#### 6.2.5. 下拉选择框 (Select)

- **样式**: `block w-full rounded-md border-gray-300 py-2 px-3 shadow-sm focus:border-brand-blue focus:outline-none focus:ring-brand-blue sm:text-sm` (Tailwind Forms插件)
- **MVP阶段**: 实现基础功能和样式。

#### 6.2.6. 开关 (Toggle Switch)

- **MVP阶段**: 可暂不实现，或使用简化的复选框代替。若实现，需自定义组件或找第三方React组件。

### 6.3. 卡片 (Cards)

- **通用样式**: `bg-white shadow-md rounded-lg overflow-hidden`

- **内边距**: `p-4` or `p-6`

- 结构

  :

  - **卡片头部 (可选)**: `px-4 py-3 border-b border-gray-200 sm:px-6`
  - **卡片主体**: `p-4 sm:p-6`
  - **卡片脚部 (可选)**: `px-4 py-3 bg-gray-50 sm:px-6`

- **MVP阶段**: 实现基础卡片样式用于内容包裹。

### 6.4. 导航组件 (Navigation) - MVP简化

#### 6.4.1. 顶部导航栏 (Top Navigation / Header)

- **高度**: `h-16` (`64px`)

- **背景色**: `bg-white shadow-sm`

- **布局**: `flex items-center justify-between px-4 sm:px-6 lg:px-8`

- 导航项

  :

  ```
  text-gray-600 hover:text-brand-blue px-3 py-2 rounded-md text-sm font-medium
  ```

  - 激活: `text-brand-blue font-semibold`

- **MVP阶段**: 实现基础Logo、核心导航链接、用户登录/注册按钮。移动端汉堡菜单。

### 6.5. 反馈组件 (Feedback Components) - MVP简化

#### 6.5.1. 提示消息 (Alerts / Banners)

- 样式

  :

  ```
  rounded-md p-4
  ```

  - 成功: `bg-green-50 text-green-700 border-l-4 border-green-500`
  - 错误: `bg-red-50 text-red-700 border-l-4 border-red-500`

- **MVP阶段**: 实现成功和错误两种基本提示。

#### 6.5.2. 轻提示/吐司 (Toasts / Snackbars)

- **MVP阶段**: 可暂不实现，或使用简单的Alert替代。

#### 6.5.3. 模态框 (Modals / Dialogs)

- **遮罩层**: `fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity`

- 对话框容器

  :

  ```
  bg-white rounded-lg shadow-xl transform transition-all sm:my-8 sm:max-w-lg sm:w-full
  ```

  - 头部: `px-4 pt-5 pb-4 sm:p-6 sm:pb-4`
  - 主体: `sm:flex sm:items-start` -> 内容区
  - 脚部: `px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse`

- **MVP阶段**: 实现基础模态框结构，用于简单确认或信息展示。

#### 6.5.4. 加载指示器 (Loading Indicators)

- 旋转器 (Spinner)

  :

  - `animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-brand-blue` (简单实现)

- 骨架屏 (Skeleton Screens)

  :

  - `animate-pulse bg-gray-200 rounded`，配合 `h-* w-*` 定义形状。

- **MVP阶段**: 至少实现一种Spinner。

## 7. 动效系统 (Motion & Animation)

### 7.1. 设计原则

- 有意义、快速响应、平滑自然、一致性。

### 7.2. 过渡时长 (Transition Durations) - Tailwind `duration-*`

- `duration-150` (快速) - 常用
- `duration-300` (中等) - 面板、模态框

### 7.3. 缓动函数 (Easing Functions) - Tailwind `ease-*`

- `ease-in-out` (默认)

### 7.4. 常用动效属性 (Animated Properties)

- `opacity`, `transform` (translate, scale), `background-color`, `color`, `border-color`。
- **MVP阶段**: 重点实现Hover状态的颜色/背景色过渡，以及模态框、下拉菜单的显隐过渡。

## 8. 无障碍设计规范 (Accessibility - A11y)

遵循 WCAG 2.1 AA 级别为最低标准。

- **键盘导航**: 所有交互元素键盘可达，焦点清晰可见 (`focus:ring-2 focus:ring-brand-blue`)。
- **语义化HTML**: React组件中依然要保证输出的HTML是语义化的。
- **ARIA**: 按需使用，如 `aria-live` 报告动态更新，`aria-expanded` 控制折叠。
- **颜色对比度**: 严格检查。
- **表单**: `<label for="...">` 与 `<input id="...">` 关联。错误信息通过 `aria-describedby` 关联。
- **MVP阶段**: 保证核心交互元素的键盘导航和焦点可见性，表单标签关联。

## 9. 设计资源与工具 (Design Resources & Tools)

- **设计软件**: Figma
- **图标**: Lucide Icons
- **前端框架**: React
- **CSS框架**: Tailwind CSS v3.x
- **构建工具**: Vite

## 10. 更新与维护 (Updates & Maintenance)

- **版本号**: 遵循语义化版本控制。
- **更新日志**: 记录变更。
- **评审流程**: 设计与开发团队共同评审。
