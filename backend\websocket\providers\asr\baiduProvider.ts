// 百度ASR提供商
import axios from 'axios';
import { ASRServiceInterface, ASRResult, ASROptions, ASRConfig, ASRProvider, ASRError } from '../../../types/asr.js';

export class BaiduProvider implements ASRServiceInterface {
  private config: ASRConfig;
  private baiduConfig: {
    APP_ID: string;
    CLIENT_ID: string;
    CLIENT_SECRET: string;
  };
  private accessToken: string | null = null;
  private tokenExpiry: number = 0;

  constructor() {
    this.config = {
      provider: ASRProvider.BAIDU,
      priority: 3,
      timeout: 10000,
      retryCount: 2,
      weight: 1.0,
      enabled: true
    };

    this.baiduConfig = {
      APP_ID: process.env.BAIDU_APP_ID || '118223154',
      CLIENT_ID: process.env.BAIDU_CLIENT_ID || 'EoiwilRVLVzBuJddijQ9JTZU',
      CLIENT_SECRET: process.env.BAIDU_CLIENT_SECRET || 'YkRgIT1qDmpNx5qBLfHg63EGmU6kFsSI'
    };
  }

  getName(): string {
    return ASRProvider.BAIDU;
  }

  isAvailable(): boolean {
    return this.config.enabled && 
           !!this.baiduConfig.APP_ID && 
           !!this.baiduConfig.CLIENT_ID && 
           !!this.baiduConfig.CLIENT_SECRET;
  }

  getConfig(): ASRConfig {
    return { ...this.config };
  }

  updateConfig(config: Partial<ASRConfig>): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * 百度语音识别
   */
  async recognize(audioBuffer: Buffer, options?: ASROptions): Promise<ASRResult> {
    const startTime = Date.now();
    
    if (!this.isAvailable()) {
      throw this.createError('Baidu ASR service not available', 'SERVICE_UNAVAILABLE', false);
    }

    console.log(`🎯 Baidu ASR: Starting recognition, audio size: ${audioBuffer.length} bytes`);

    try {
      // 1. 获取access_token
      const accessToken = await this.getBaiduAccessToken();
      if (!accessToken) {
        throw this.createError('Failed to get Baidu access token', 'AUTHENTICATION_FAILED', true);
      }

      // 2. 调用极速版语音识别API
      const result = await this.performRecognition(audioBuffer, accessToken, options);
      const processingTime = Date.now() - startTime;
      
      console.log(`✅ Baidu ASR completed in ${processingTime}ms: "${result.text}"`);
      
      return {
        ...result,
        processingTime,
        timestamp: Date.now()
      };
    } catch (error) {
      console.error('❌ Baidu ASR error:', error);
      if (error instanceof Error && (error as ASRError).provider) {
        throw error;
      }
      throw this.createError(`Baidu ASR error: ${(error as Error).message}`, 'PROCESSING_ERROR', true);
    }
  }

  /**
   * 获取百度Access Token
   */
  private async getBaiduAccessToken(): Promise<string | null> {
    // 检查token是否还有效
    if (this.accessToken && Date.now() < this.tokenExpiry) {
      return this.accessToken;
    }

    try {
      console.log('🔑 Getting Baidu access token...');
      
      const response = await axios.post(
        'https://aip.baidubce.com/oauth/2.0/token',
        null,
        {
          params: {
            grant_type: 'client_credentials',
            client_id: this.baiduConfig.CLIENT_ID,
            client_secret: this.baiduConfig.CLIENT_SECRET
          },
          timeout: 5000
        }
      );

      if (response.data.access_token) {
        this.accessToken = response.data.access_token;
        // Token有效期通常是30天，这里设置为25天后过期
        this.tokenExpiry = Date.now() + (25 * 24 * 60 * 60 * 1000);
        console.log('✅ Baidu access token obtained successfully');
        return this.accessToken;
      } else {
        console.error('❌ Baidu token response missing access_token:', response.data);
        return null;
      }
    } catch (error) {
      console.error('❌ Failed to get Baidu access token:', error);
      return null;
    }
  }

  /**
   * 执行语音识别
   */
  private async performRecognition(audioBuffer: Buffer, accessToken: string, options?: ASROptions): Promise<ASRResult> {
    try {
      const response = await axios.post(
        'https://vop.baidu.com/pro_api',
        {
          format: 'pcm',
          rate: options?.sampleRate || 16000,
          channel: options?.channels || 1,
          cuid: 'r5Oitju6zxx2EHcQoTLDwmEBRRP6TIUh',
          dev_pid: 80001, // 极速版模型
          token: accessToken,
          speech: audioBuffer.toString('base64'),
          len: audioBuffer.length
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          },
          timeout: options?.timeout || this.config.timeout
        }
      );

      console.log('📥 Baidu ASR response:', response.data);

      if (response.data.err_no === 0 && response.data.result && response.data.result.length > 0) {
        const text = response.data.result[0];
        const cleanedText = this.cleanText(text);
        
        return {
          text: cleanedText,
          confidence: this.calculateConfidence(response.data),
          provider: ASRProvider.BAIDU,
          processingTime: 0, // Will be set by caller
          timestamp: Date.now(),
          isPartial: false
        };
      } else {
        const errorMsg = this.getBaiduErrorMessage(response.data.err_no);
        throw this.createError(`Baidu ASR error (${response.data.err_no}): ${errorMsg}`, 'PROCESSING_ERROR', true);
      }
    } catch (error) {
      if (axios.isAxiosError(error)) {
        if (error.code === 'ECONNABORTED') {
          throw this.createError('Baidu ASR request timeout', 'TIMEOUT', true);
        } else if (error.response) {
          throw this.createError(`Baidu ASR HTTP error: ${error.response.status}`, 'NETWORK_ERROR', true);
        } else {
          throw this.createError(`Baidu ASR network error: ${error.message}`, 'NETWORK_ERROR', true);
        }
      }
      throw error;
    }
  }

  /**
   * 计算置信度
   */
  private calculateConfidence(responseData: any): number {
    // 百度API不直接提供置信度，根据错误码和结果质量估算
    if (responseData.err_no === 0 && responseData.result && responseData.result.length > 0) {
      const text = responseData.result[0];
      if (text.length > 10) {
        return 0.9;
      } else if (text.length > 5) {
        return 0.8;
      } else {
        return 0.7;
      }
    }
    return 0.5;
  }

  /**
   * 获取百度错误信息
   */
  private getBaiduErrorMessage(errorCode: number): string {
    const errorMessages: { [key: number]: string } = {
      3300: '输入参数不正确',
      3301: '音频质量过差',
      3302: '鉴权失败',
      3303: '语音服务器后端问题',
      3304: '用户的请求QPS超限',
      3305: '用户的日pv（日请求量）超限',
      3307: '语音过长',
      3308: '音频无效',
      3309: '音频文件过大',
      3310: '音频文件下载失败',
      3311: '音频时长过长',
      3312: '音频格式不支持'
    };
    
    return errorMessages[errorCode] || `未知错误 (${errorCode})`;
  }

  /**
   * 清理文本
   */
  private cleanText(text: string): string {
    if (!text) return '';
    
    return text.trim()
      .replace(/\s+/g, ' ')
      .replace(/[^\u4e00-\u9fa5a-zA-Z0-9，。！？、；：""''（）\s]/g, '');
  }

  /**
   * 创建ASR错误
   */
  private createError(message: string, code: string, retryable: boolean): ASRError {
    const error = new Error(message) as ASRError;
    error.provider = ASRProvider.BAIDU;
    error.code = code;
    error.retryable = retryable;
    return error;
  }

  async destroy(): Promise<void> {
    console.log('🧹 Baidu ASR provider destroyed');
    // 清理token
    this.accessToken = null;
    this.tokenExpiry = 0;
  }
}
