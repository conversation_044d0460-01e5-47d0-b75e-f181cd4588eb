// 功能开关管理系统 - 重构计划的技术基石
// 实现动态功能控制，支持灰度发布和紧急熔断

export interface FeatureFlag {
  key: string;
  enabled: boolean;
  rolloutPercentage: number;
  targetUsers?: string[];
  metadata?: Record<string, any>;
}

export interface FeatureFlagConfig {
  // WebSocket重构相关开关
  'enable-unified-websocket': FeatureFlag;
  'enable-new-chat-service': FeatureFlag;
  'enable-new-media-signaling': FeatureFlag;
  'enable-session-orchestration': FeatureFlag;
  'enable-ai-analysis-relay': FeatureFlag;
  
  // 调试和监控开关
  'enable-websocket-debug-logs': FeatureFlag;
  'enable-connection-monitoring': FeatureFlag;
  'enable-performance-metrics': FeatureFlag;
}

class FeatureFlagManager {
  private static instance: FeatureFlagManager;
  private flags: Partial<FeatureFlagConfig> = {};
  private userId: string | null = null;
  private listeners: Map<string, ((enabled: boolean) => void)[]> = new Map();

  private constructor() {
    this.initializeDefaultFlags();
  }

  public static getInstance(): FeatureFlagManager {
    if (!FeatureFlagManager.instance) {
      FeatureFlagManager.instance = new FeatureFlagManager();
    }
    return FeatureFlagManager.instance;
  }

  /**
   * 初始化默认功能开关配置
   * 所有新功能默认关闭，确保安全的渐进式发布
   */
  private initializeDefaultFlags(): void {
    this.flags = {
      'enable-unified-websocket': {
        key: 'enable-unified-websocket',
        enabled: false, // 默认关闭，等待灰度发布
        rolloutPercentage: 0,
        metadata: {
          description: '启用统一WebSocket管理器',
          owner: 'websocket-team',
          createdAt: new Date().toISOString(),
          lifecycle: 'temporary-release', // 临时发布开关
          expectedRemovalDate: '2025-08-11' // 预期移除日期
        }
      },
      'enable-new-chat-service': {
        key: 'enable-new-chat-service',
        enabled: false,
        rolloutPercentage: 0,
        metadata: {
          description: '启用新的实时聊天微服务',
          owner: 'chat-team',
          createdAt: new Date().toISOString(),
          lifecycle: 'temporary-release'
        }
      },
      'enable-new-media-signaling': {
        key: 'enable-new-media-signaling',
        enabled: false,
        rolloutPercentage: 0,
        metadata: {
          description: '启用新的媒体信令服务',
          owner: 'media-team',
          createdAt: new Date().toISOString(),
          lifecycle: 'temporary-release'
        }
      },
      'enable-session-orchestration': {
        key: 'enable-session-orchestration',
        enabled: false,
        rolloutPercentage: 0,
        metadata: {
          description: '启用会话编排服务',
          owner: 'session-team',
          createdAt: new Date().toISOString(),
          lifecycle: 'temporary-release'
        }
      },
      'enable-ai-analysis-relay': {
        key: 'enable-ai-analysis-relay',
        enabled: false,
        rolloutPercentage: 0,
        metadata: {
          description: '启用AI分析中继服务',
          owner: 'ai-team',
          createdAt: new Date().toISOString(),
          lifecycle: 'temporary-release'
        }
      },
      'enable-websocket-debug-logs': {
        key: 'enable-websocket-debug-logs',
        enabled: true, // 调试期间启用
        rolloutPercentage: 100,
        metadata: {
          description: '启用WebSocket调试日志',
          owner: 'platform-team',
          createdAt: new Date().toISOString(),
          lifecycle: 'long-term-operational' // 长期运维开关
        }
      },
      'enable-connection-monitoring': {
        key: 'enable-connection-monitoring',
        enabled: true,
        rolloutPercentage: 100,
        metadata: {
          description: '启用连接监控',
          owner: 'platform-team',
          createdAt: new Date().toISOString(),
          lifecycle: 'long-term-operational'
        }
      },
      'enable-performance-metrics': {
        key: 'enable-performance-metrics',
        enabled: true,
        rolloutPercentage: 100,
        metadata: {
          description: '启用性能指标收集',
          owner: 'platform-team',
          createdAt: new Date().toISOString(),
          lifecycle: 'long-term-operational'
        }
      }
    };
  }

  /**
   * 设置当前用户ID，用于个性化功能开关
   */
  public setUserId(userId: string): void {
    this.userId = userId;
  }

  /**
   * 检查功能开关是否启用
   * 支持基于用户ID的个性化控制和百分比灰度发布
   */
  public isEnabled(flagKey: keyof FeatureFlagConfig): boolean {
    const flag = this.flags[flagKey];
    if (!flag) {
      console.warn(`🚩 Feature flag '${flagKey}' not found, defaulting to false`);
      return false;
    }

    // 如果功能完全关闭，直接返回false
    if (!flag.enabled) {
      return false;
    }

    // 检查是否在目标用户列表中
    if (flag.targetUsers && this.userId) {
      if (flag.targetUsers.includes(this.userId)) {
        console.log(`🎯 Feature '${flagKey}' enabled for target user: ${this.userId}`);
        return true;
      }
    }

    // 基于百分比的灰度发布
    if (flag.rolloutPercentage > 0 && this.userId) {
      const userHash = this.hashUserId(this.userId);
      const userPercentile = userHash % 100;
      const enabled = userPercentile < flag.rolloutPercentage;
      
      if (enabled) {
        console.log(`🎲 Feature '${flagKey}' enabled for user ${this.userId} (percentile: ${userPercentile}, rollout: ${flag.rolloutPercentage}%)`);
      }
      
      return enabled;
    }

    // 如果没有用户ID，按照全局开关状态
    return flag.enabled && flag.rolloutPercentage === 100;
  }

  /**
   * 动态更新功能开关配置
   * 支持运行时热更新，无需重新部署
   */
  public updateFlag(flagKey: keyof FeatureFlagConfig, updates: Partial<FeatureFlag>): void {
    const currentFlag = this.flags[flagKey];
    if (!currentFlag) {
      console.error(`🚩 Cannot update non-existent flag: ${flagKey}`);
      return;
    }

    const updatedFlag = { ...currentFlag, ...updates };
    this.flags[flagKey] = updatedFlag;

    console.log(`🔄 Feature flag '${flagKey}' updated:`, updates);

    // 通知所有监听器
    this.notifyListeners(flagKey, updatedFlag.enabled);
  }

  /**
   * 紧急熔断：立即关闭指定功能
   * 用于生产环境的紧急故障处理
   */
  public emergencyDisable(flagKey: keyof FeatureFlagConfig, reason: string): void {
    console.error(`🚨 EMERGENCY DISABLE: ${flagKey} - Reason: ${reason}`);
    
    this.updateFlag(flagKey, {
      enabled: false,
      rolloutPercentage: 0,
      metadata: {
        ...this.flags[flagKey]?.metadata,
        emergencyDisabled: true,
        emergencyReason: reason,
        emergencyTimestamp: new Date().toISOString()
      }
    });

    // 发送紧急告警（在实际项目中应该集成告警系统）
    this.sendEmergencyAlert(flagKey, reason);
  }

  /**
   * 监听功能开关状态变化
   */
  public onFlagChange(flagKey: keyof FeatureFlagConfig, callback: (enabled: boolean) => void): () => void {
    if (!this.listeners.has(flagKey)) {
      this.listeners.set(flagKey, []);
    }
    
    this.listeners.get(flagKey)!.push(callback);

    // 返回取消监听的函数
    return () => {
      const callbacks = this.listeners.get(flagKey);
      if (callbacks) {
        const index = callbacks.indexOf(callback);
        if (index > -1) {
          callbacks.splice(index, 1);
        }
      }
    };
  }

  /**
   * 获取所有功能开关的状态概览
   * 用于管理后台和调试
   */
  public getFlagsOverview(): Record<string, { enabled: boolean; rolloutPercentage: number; metadata?: any }> {
    const overview: Record<string, any> = {};
    
    Object.entries(this.flags).forEach(([key, flag]) => {
      if (flag) {
        overview[key] = {
          enabled: flag.enabled,
          rolloutPercentage: flag.rolloutPercentage,
          metadata: flag.metadata
        };
      }
    });

    return overview;
  }

  /**
   * 从远程服务器同步功能开关配置
   * 在实际项目中应该连接到功能开关管理后台
   */
  public async syncFromRemote(): Promise<void> {
    try {
      // TODO: 实现与功能开关后台服务的集成
      // const response = await fetch('/api/feature-flags');
      // const remoteFlags = await response.json();
      // this.flags = { ...this.flags, ...remoteFlags };
      
      console.log('🔄 Feature flags synced from remote (placeholder)');
    } catch (error) {
      console.error('❌ Failed to sync feature flags from remote:', error);
    }
  }

  /**
   * 生成用户ID的哈希值，用于一致性的百分比分配
   */
  private hashUserId(userId: string): number {
    let hash = 0;
    for (let i = 0; i < userId.length; i++) {
      const char = userId.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return Math.abs(hash);
  }

  /**
   * 通知功能开关状态变化的监听器
   */
  private notifyListeners(flagKey: keyof FeatureFlagConfig, enabled: boolean): void {
    const callbacks = this.listeners.get(flagKey);
    if (callbacks) {
      callbacks.forEach(callback => {
        try {
          callback(enabled);
        } catch (error) {
          console.error(`❌ Error in flag change callback for ${flagKey}:`, error);
        }
      });
    }
  }

  /**
   * 发送紧急告警
   * 在实际项目中应该集成到告警系统（如PagerDuty、钉钉等）
   */
  private sendEmergencyAlert(flagKey: string, reason: string): void {
    // TODO: 集成实际的告警系统
    console.error(`🚨 EMERGENCY ALERT: Feature flag ${flagKey} has been emergency disabled. Reason: ${reason}`);
  }
}

// 导出单例实例
export const featureFlagManager = FeatureFlagManager.getInstance();

// 导出便捷的检查函数
export const isFeatureEnabled = (flagKey: keyof FeatureFlagConfig): boolean => {
  return featureFlagManager.isEnabled(flagKey);
};

// 导出紧急熔断函数
export const emergencyDisableFeature = (flagKey: keyof FeatureFlagConfig, reason: string): void => {
  featureFlagManager.emergencyDisable(flagKey, reason);
};
