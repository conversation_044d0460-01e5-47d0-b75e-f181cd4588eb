import React, { useEffect } from 'react';
import { useF<PERSON>, SubmitHandler } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import Input from '../ui/Input'; // 确保路径正确 (Ensure path is correct)
import Button from '../ui/Button'; // 确保路径正确 (Ensure path is correct)
import { X, Trash2 } from 'lucide-react';
import type { Position } from '@new-mianshijun/common';
import { TargetPosition } from '../../lib/api/apiService';

// 更新表单数据的结构和校验规则以匹配附件图片
// Update form data structure and validation rules to match the attachment image
const positionSchema = z.object({
  positionName: z.string().min(1, { message: '岗位名称不能为空' }),
  positionRequirements: z.string().optional(), // 岗位要求，非必填
  companyName: z.string().min(1, { message: '公司名称不能为空' }),
  companyProfile: z.string().optional(), // 公司简介，非必填
});

export type PositionFormData = z.infer<typeof positionSchema>;

interface AddPositionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (data: PositionFormData) => void;
  onDelete?: () => void;
  position?: Position | TargetPosition | null;
  isLoading?: boolean;
}

const AddPositionModal: React.FC<AddPositionModalProps> = ({ isOpen, onClose, onSave, onDelete, position, isLoading = false }) => {
  const {
    register,
    handleSubmit,
    reset,
    setValue,
    formState: { errors, isSubmitting },
  } = useForm<PositionFormData>({
    resolver: zodResolver(positionSchema),
    defaultValues: { // 设置表单字段的默认值为空字符串
      positionName: '',
      positionRequirements: '',
      companyName: '',
      companyProfile: '',
    }
  });
  
  // 当编辑现有岗位时，填充表单数据
  useEffect(() => {
    if (position) {
      // 兼容 Position 和 TargetPosition 类型
      const posName = 'positionName' in position ? position.positionName : position.name;
      setValue('positionName', posName);
      
      // 如果是 TargetPosition 类型，设置额外字段
      if ('positionRequirements' in position && position.positionRequirements) {
        setValue('positionRequirements', position.positionRequirements);
      }
      
      if ('companyName' in position && position.companyName) {
        setValue('companyName', position.companyName);
      }
      
      if ('companyProfile' in position && position.companyProfile) {
        setValue('companyProfile', position.companyProfile);
      }
    }
  }, [position, setValue]);

  const onSubmit: SubmitHandler<PositionFormData> = (data) => {
    onSave(data);
    reset(); // 保存后重置表单
  };

  const handleClose = () => {
    reset(); // 关闭时也重置表单
    onClose();
  };

  if (!isOpen) {
    return null;
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50 transition-opacity duration-300 ease-in-out">
      <div className="bg-white p-6 sm:p-8 rounded-xl shadow-2xl w-full max-w-lg transform transition-all duration-300 ease-in-out scale-100">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl sm:text-2xl font-semibold text-gray-800">
            {position ? '编辑意向岗位' : '添加意向岗位'}
          </h2>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
            aria-label="Close modal"
          >
            <X size={24} />
          </button>
        </div>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4 sm:space-y-5">
          <Input
            id="positionName"
            label="岗位名称 *"
            type="text"
            {...register('positionName')}
            error={errors.positionName?.message}
            className="text-sm sm:text-base" // 调整字体大小
            placeholder="例如：软件工程师"
          />

          {/* 岗位要求 Textarea */}
          {/* Position Requirements Textarea */}
          <div className="space-y-1">
            <label htmlFor="positionRequirements" className="block text-sm font-medium text-gray-700">
              岗位要求
            </label>
            <textarea
              id="positionRequirements"
              {...register('positionRequirements')}
              rows={4} // 增加行数以容纳更多内容
              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-sky-500 focus:border-sky-500 sm:text-sm placeholder-gray-400"
              placeholder="例如：熟悉React、TypeScript，3年以上经验"
            ></textarea>
            {errors.positionRequirements && (
              <p className="text-xs text-red-600">{errors.positionRequirements.message}</p>
            )}
          </div>

          <Input
            id="companyName"
            label="公司名称 *"
            type="text"
            {...register('companyName')}
            error={errors.companyName?.message}
            className="text-sm sm:text-base" // 调整字体大小
            placeholder="例如：某某科技有限公司"
          />

          {/* 公司简介 Textarea */}
          {/* Company Introduction Textarea */}
          <div className="space-y-1">
            <label htmlFor="companyProfile" className="block text-sm font-medium text-gray-700">
              公司简介
            </label>
            <textarea
              id="companyProfile"
              {...register('companyProfile')}
              rows={4} // 增加行数
              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-sky-500 focus:border-sky-500 sm:text-sm placeholder-gray-400"
              placeholder="例如：公司是行业领先的SaaS服务提供商..."
            ></textarea>
            {errors.companyProfile && (
              <p className="text-xs text-red-600">{errors.companyProfile.message}</p>
            )}
          </div>

          <div className="flex flex-col sm:flex-row justify-between space-y-3 sm:space-y-0 sm:space-x-3 pt-4">
            {/* 删除按钮，仅在编辑模式下显示 */}
            <div>
              {position && onDelete && (
                <button
                  type="button"
                  onClick={onDelete}
                  className="w-full sm:w-auto min-w-[100px] px-4 py-2 border border-red-500 rounded-md font-medium flex items-center justify-center text-red-500 hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                >
                  <Trash2 className="w-4 h-4 mr-2 text-red-500" />
                  删除
                </button>
              )}
            </div>
            
            <div className="flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-3 ml-auto">
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                className="w-full sm:w-auto min-w-[100px] px-4 py-2" // 确保按钮有合适的内边距和最小宽度
              >
                取消
              </Button>
              <Button
                type="submit"
                variant="primary"
                isLoading={isSubmitting || isLoading}
                disabled={isSubmitting || isLoading}
                className="w-full sm:w-auto min-w-[100px] px-4 py-2" // 确保按钮有合适的内边距和最小宽度
              >
                {isSubmitting || isLoading ? '保存中...' : '保存'}
              </Button>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddPositionModal;
