# 面试君 (Mia<PERSON><PERSON><PERSON><PERSON>) 设计文档

**版本**: 1.4 (MVP增强版 - React & Vite Aligned, Simplified State Management)

**最后更新**: 2025年5月10日

**负责人**: AI设计开发团队

## 0. 文档修订历史

| **版本** | **日期**          | **修订人**         | **修订说明**                                                 |
| -------- | ----------------- | ------------------ | ------------------------------------------------------------ |
| 1.0      | 2025年5月10日     | AI设计开发团队     | 初始MVP版本创建                                              |
| 1.1      | 2025年5月10日     | AI设计开发团队     | 根据细化要求，增强各模块描述的粒度，确保AI执行无歧义，补充技术实现细节和用户体验考量 |
| 1.2      | 2025年5月10日     | AI设计开发团队     | 技术栈统一：前端调整为Vue.js生态，后端明确为Node.js+Express/Vercel Functions |
| 1.3      | 2025年5月10日     | AI设计开发团队     | 技术栈统一：前端调整为React (Vite)生态，与项目文件夹一致，后端不变。 |
| **1.4**  | **2025年5月10日** | **AI设计开发团队** | **状态管理方案简化：MVP阶段优先推荐React Context API，Zustand作为按需引入的备选。** |

## 1. 产品概览 - 设计理念与技术方案概述

### 1.1. 产品定位与核心价值

面试君 (MianshiJun) 是一款AI实时辅助面试系统，旨在赋能求职者，特别是那些在面试中容易感到紧张、思路卡顿或不善言辞的用户。通过实时语音识别、大模型智能分析和即时反馈，面试君帮助用户更从容、专业地应对面试挑战，展现最佳自我。

**核心价值主张 (Value Proposition)**:

- **提升面试表现**: 通过AI实时生成的专业、结构化的回答建议，优化用户回答的质量、逻辑性和完整性。目标：关键问题采纳AI建议后，用户面试评价平均提升15%。
- **缓解面试焦虑**: 在面试过程中提供即时、可靠的智能支持，如同经验丰富的面试导师在旁，显著增强用户信心，降低紧张感。目标：用户主观紧张度评分（面试后调研）降低25%。
- **复盘与提升**: 面试结束后提供详细的面试记录、AI评估报告（包括优点、待改进点、具体案例分析）和个性化改进建议，助力用户精准复盘，实现面试技能的持续迭代提升。
- **个性化辅导**: 针对不同行业、岗位类型和面试轮次（如HR面、技术面、行为面试）提供定制化的AI辅助策略和知识库支持。

### 1.2. 设计理念

- 用户为中心 (User-Centric)
  - **深度理解用户场景**: 充分考虑用户在真实面试环境下的操作习惯、心理状态和信息获取需求。
  - **最小化干扰**: 辅助信息的呈现方式应简洁、直观、非侵入式，避免在紧张的面试过程中分散用户注意力。
  - **易用性**: 交互流程清晰自然，学习成本低，新用户可快速上手。
- 实时高效 (Real-time & Efficient)
  - **极致低延迟**: 从面试官提问到AI建议显示，端到端延迟控制在3秒以内，确保辅助信息的时效性。
  - **精准识别**: 语音识别准确率在安静环境下达到90%以上，嘈杂环境下（如背景人声）达到80%以上。
  - **快速响应**: AI模型推理速度快，确保建议生成迅速。
- 智能专业 (Intelligent & Professional)
  - **高质量建议**: AI生成的回答建议需具备高度的专业性、针对性和逻辑性，内容来源可靠，符合行业最佳实践。
  - **情境感知**: AI能结合面试上下文（已提问过的问题、岗位要求等）提供更精准的辅助。
  - **可信赖形象**: 产品界面和交互体现专业感，建立用户对AI能力的信任。
- 简洁直观 (Simple & Intuitive)
  - **信息降噪**: 只呈现最核心、最相关的辅助信息，避免信息过载。
  - **视觉引导**: 通过清晰的视觉层级和引导元素，帮助用户快速定位和理解信息。
- 安全私密 (Secure & Private)
  - **数据加密**: 用户面试音视频数据、个人信息等敏感数据在传输和存储过程中全程加密。
  - **权限控制**: 严格控制数据访问权限，用户数据仅用户本人可见或授权访问。
  - **隐私政策透明**: 清晰告知用户数据收集和使用方式，符合GDPR等相关法规。
- 激励成长 (Empowering & Growth-oriented)
  - **赋能而非替代**: 产品定位是辅助用户思考和表达，而非直接替用户回答，鼓励用户主动参与和独立思考。
  - **正向反馈**: 评估报告以积极和建设性的方式提供反馈，激励用户持续改进。

### 1.3. MVP目标 (可量化)

- **核心用户流程闭环**: 100%用户能够顺利完成从注册/登录 -> 选择面试模式 -> 启动面试（屏幕共享与音频授权）-> 接收实时语音转文字 -> 查看AI辅助建议 -> 结束面试 -> 查看基础面试记录与AI建议回顾的完整流程。
- 关键技术模块验证
  - 屏幕共享与音频捕获成功率 > 95%。
  - 实时语音转文字平均准确率（普通话标准发音）> 85%。
  - AI大模型API调用成功率 > 98%，平均响应时长（从发送请求到接收到完整回复）< 2秒。
  - 实时辅助信息（转录文本、AI建议）显示平均延迟 < 3秒（从音频输入到界面显示）。
- 用户接受度与满意度
  - 早期用户（如内测用户50名）中，70%表示产品对其面试表现有帮助。
  - NPS (净推荐值) > 20。
  - 周活跃用户数 (WAU) 达到目标值（例如：100）。
- 系统稳定性与性能
  - 核心功能模块在典型使用场景下，连续使用1小时无崩溃。
  - P95 API响应时间 < 1秒。
  - 服务器平均CPU使用率 < 60%，内存使用率 < 70%。

### 1.4. 技术方案概述 (MVP阶段 - 增强细节)

- 前端 (Client-Side)

  :

  - **核心技术**: HTML5, CSS3 (严格遵循 `design_system.md` 中定义的原子化CSS规范，使用Tailwind CSS v3.x), TypeScript (ES6+)。

  - 框架/库 :

    React v18+

    (采用

    Vite v5.x

     作为构建工具，确保快速的冷启动和热更新)。

    - **组件开发**: 函数组件 (Functional Components) 配合 Hooks (如 `useState`, `useEffect`, `useContext`)。
    - **代码规范**: ESLint (推荐的React规范如 `eslint-config-react-app` 或 `eslint-plugin-react` + Prettier) 强制代码风格统一。

  - 关键API与技术

    - `navigator.mediaDevices.getDisplayMedia({ video: { mediaSource: "screen" }, audio: { echoCancellation: true, noiseSuppression: true, autoGainControl: true } })`: 用于高质量屏幕内容及系统音频捕获。
    - `navigator.mediaDevices.getUserMedia({ audio: { echoCancellation: true, noiseSuppression: true, autoGainControl: true }, video: false })`: 用于高质量麦克风音频捕获。
    - Web Audio API: 用于音频流处理，如音量检测、音频片段截取发送。
    - Web Speech API (`SpeechRecognition`): **仅作为快速原型或网络不稳定时的降级方案**，主要依赖后端ASR。若使用，需明确告知用户其局限性。
    - WebSocket API: 与后端进行低延迟、全双工通信，用于实时传输音频数据、接收转录文本和AI建议。考虑使用如 `react-use-websocket` 或类似库增强连接稳定性与自动重连。

  - 状态管理 (MVP 推荐)

    :

    - **初期与简单全局状态**: 优先使用 React 内置的 **Context API** 结合 `useState` (用于简单状态，如主题切换、简单的用户偏好) 和 `useReducer` (用于较复杂的状态逻辑，如用户认证状态、购物车状态管理)。这有助于减少初期依赖，保持项目简洁。
    - **复杂或高性能需求场景 (按需引入)**: 对于如“实时面试辅助页”中频繁更新、交互复杂的全局状态，或当 Context API 在特定场景下可能引发性能顾虑时（例如大量组件订阅同一Context导致频繁重渲染），可考虑引入轻量级状态管理库 **Zustand**。Zustand 以其简洁的API、易于上手和良好的性能著称，可以与Context API共存，针对性地解决特定模块的状态管理需求。

  - **路由**: **React Router v6+** (配置清晰的路由结构，支持嵌套路由和参数传递)。

  - **UI组件库**: **基于Tailwind CSS自定义核心组件**。严格按照 `design_system.md` 中的组件规范进行实现。可考虑引入如 `Headless UI React` (与Tailwind CSS集成良好) 作为无样式基础组件库，再进行样式定制。图标库使用 `lucide-react`。

  - **国际化 (i18n)**: `i18next` 配合 `react-i18next`，初期支持中文（zh-CN），预留英文（en-US）扩展能力。

- 后端 (Server-Side)

  :

  - **核心技术**: Node.js v18.x (LTS)。

  - 框架 : Express.js v4.x (或直接使用Vercel的请求处理函数构建API)。

    - **API风格**: RESTful API (主要用于用户管理、面试记录等非实时交互) + WebSocket (用于面试中实时交互)。

  - **部署**: Vercel Serverless Functions (用于API接口) 和 Vercel Edge Functions (用于WebSocket服务，利用边缘网络降低延迟)。

  - 实时通信 : WebSocket (使用

    ```
    ws
    ```

    库，并结合Express路由或Vercel Functions进行管理)。

    - 设计心跳机制保持连接活跃。
    - 处理多用户并发连接和消息广播。

  - 身份认证 : JWT (JSON Web Tokens) - 使用

    ```
    jsonwebtoken
    ```

    库生成和验证。

    - Access Token (短效，如15分钟) + Refresh Token (长效，如7天，存储在HttpOnly Cookie中)。
    - 通过中间件机制保护需要认证的路由和WebSocket连接。

- AI集成 (AI Integration)

  :

  - 大语言模型 (LLM)
    - **首选**: DeepSeek API (具体模型根据性价比和效果选择，如DeepSeek-V2)。
    - **备选/实验**: OpenAI API (GPT-4o, GPT-3.5-turbo)。
    - **API调用**: 后端统一封装LLM API调用逻辑，使用 `axios` 或 `node-fetch`。实现请求重试、超时控制、错误处理机制。
    - **Prompt工程**: 设计高效、结构化的Prompt模板，针对不同面试场景和问题类型进行优化，确保AI输出质量。
  - 语音识别 (ASR - Automatic Speech Recognition)
    - **首选云服务**: 阿里云实时语音识别 / 讯飞听见实时语音转写 / 腾讯云实时语音识别。选择标准：高准确率、低延迟、支持流式传输、API稳定、成本可控。
    - **API调用**: 后端接收前端发送的音频流 (如PCM或WAV格式片段)，通过SDK或HTTP API实时调用ASR服务。
    - **流式处理**: 实现音频流的缓冲、分片和有序发送，确保ASR结果的连续性和实时性。

- 数据库 (Database)

  :

  - **类型**: PostgreSQL v15+。
  - **服务**: Neon (Serverless PostgreSQL，与Vercel集成良好，提供自动扩展、分支管理、按需付费)。
  - ORM : Prisma v5.x (提供类型安全的数据库访问、Schema管理、数据库迁移)。
    - 定义清晰的Prisma Schema (`schema.prisma`)，与 `technical_architecture.md` 中的数据库设计保持一致。
  - **数据模型**: 严格按照 `technical_architecture.md` 中定义的表结构和关系。

- 部署与运维 (Deployment & DevOps)

  :

  - **基础设施即代码 (IaC)**: Vercel的配置文件 (`vercel.json`) 管理部署设置。
  - CI/CD : GitHub Actions。
    - Push到特定分支 (如 `main`, `develop`) 自动触发测试、构建和部署到Vercel。
    - Linting和单元测试作为CI流程的一部分。
  - **版本控制**: Git (GitHub)。遵循Gitflow或类似分支策略。
  - 监控与日志
    - **前端**: Vercel Analytics (用户行为分析), Sentry (错误追踪)。
    - **后端**: Vercel Logs (Serverless Function日志), Neon Console (数据库监控), Sentry (错误追踪)。
    - **自定义日志**: 使用 `pino` 或 `winston` 进行结构化日志记录。
  - **环境变量管理**: Vercel提供的环境变量管理功能，区分开发、预览、生产环境。

## 2. 设计系统 - 色彩、排版、组件规范

- **严格遵循**: 本项目所有UI界面和组件的开发，必须严格参照已定义的 `design_system.md` 文件。该文件是视觉和交互的唯一真实来源 (Single Source of Truth)。
- **一致性应用**: 确保 `design_system.md` 中定义的色彩、排版、间距、圆角、图标、组件状态等规范在所有页面和交互元素中得到一致的应用。
- **组件库对接**: 前端开发时，优先**基于Tailwind CSS自定义核心组件**，确保其视觉表现和交互行为与 `design_system.md` 完全匹配。任何自定义组件也必须符合设计系统规范。可按需引入 `Headless UI React` 等无样式组件库作为基础。图标使用 `lucide-react`。
- **响应式设计**: 所有组件和页面布局需根据 `design_system.md` 中定义的断点和响应式原则进行设计和实现。
- **无障碍性 (A11y)**: 严格按照 `design_system.md` 中的无障碍设计规范，确保所有组件和交互对残障用户友好，包括键盘导航、ARIA属性、颜色对比度等。

## 3. 项目架构 - 技术架构与项目结构

- **遵循架构**: 本项目的技术实现，包括前端、后端、数据库和API设计，必须严格遵循 `technical_architecture.md` 文件中定义的架构方案。
- 模块化
  - **前端 (React)**: 按照功能或页面组织组件 (`.tsx` 文件)、自定义Hooks (`useSomething.ts`)、Services、状态管理模块等。例如：`/src/features/interview`, `/src/components/common`, `/src/hooks`, `/src/services/api.ts`。状态管理优先使用Context API (`/src/contexts/AuthContext.tsx`)，按需引入Zustand store (`/src/stores/interviewStore.ts`)。
  - **后端 (Node.js + Express.js/Vercel Functions)**: 按路由或功能模块组织代码文件。例如，`/api/auth.ts`, `/api/interviews.ts`。使用中间件处理通用逻辑。
- **API接口定义**: 所有RESTful API和WebSocket消息格式需与 `technical_architecture.md` 中定义的接口文档保持一致。可使用 `swagger-jsdoc` 和 `swagger-ui-express` (若使用Express) 或其他工具生成和维护API文档。
- **数据流**: 确保前端与后端、后端与AI服务之间的数据交互流程符合 `technical_architecture.md` 中描述的数据流图。
- **安全性**: 严格执行 `technical_architecture.md` 中规划的安全措施，包括身份认证、授权、数据加密、输入验证、攻击防护等。

## 4. 页面详细设计

遵循MVP原则，优先设计和实现核心流程页面。以下页面设计将进一步细化，确保AI执行的准确性。

### 4.1. 核心页面

#### 4.1.1. 首页 / 登录 / 注册 (Landing / Login / Register Page)

- 页面文件名/路由

  - 首页 (未登录): `/` (指向 `LandingPage.tsx` React组件)
  - 登录页: `/login` (指向 `LoginPage.tsx` React组件)
  - 注册页: `/register` (指向 `RegisterPage.tsx` React组件)

- 页面功能与目标

  - 首页 (未登录时)
    - **核心目标**: 清晰、快速地向首次访问用户传递“面试君”的核心价值、解决的关键痛点，并有效引导用户完成注册或登录操作。
    - 功能
      1. **价值主张展示**: 通过引人注目的Hero Section (首屏区域) 展示产品Slogan、核心功能简介和用户收益。
      2. **功能亮点介绍**: 分模块图文并茂地介绍屏幕共享、实时语音识别、AI辅助回答、面试评估等核心功能。
      3. **工作流程演示**: 简明扼要地展示产品如何工作的步骤或短视频。
      4. **用户信任构建**: (MVP后) 可展示用户评价、合作伙伴、媒体报道等。
      5. **行为召唤 (CTA)**: 提供清晰、突出的注册/登录入口按钮。
  - 登录页
    - **核心目标**: 允许已注册用户安全、便捷地登录系统。
    - 功能
      1. 提供邮箱/手机号 + 密码的登录方式。
      2. “忘记密码”功能入口。
      3. (MVP后) 第三方登录选项 (如微信、GitHub)。
      4. 登录错误提示 (如账号或密码错误、账户锁定等)。
  - 注册页
    - **核心目标**: 引导新用户快速、顺利地完成账户创建。
    - 功能
      1. 提供邮箱/手机号 + 密码的注册方式。
      2. 密码强度提示与确认。
      3. 用户协议和隐私政策的勾选确认。
      4. (MVP后) 邀请码输入框。
      5. 注册过程中的输入校验和错误提示。

- 视觉设计风格

  - **整体**: 严格遵循 `design_system.md`。现代、扁平化、专业、科技感，同时兼具亲和力。
  - **色彩**: 以品牌主蓝 (`#1E40AF`) 为主导，辅助行动橙 (`#F97316`) 用于CTA按钮和关键引导元素。中性色用于背景和文本，确保信息层级清晰。
  - **排版**: 采用 `design_system.md` 定义的字体家族、字号、行高和字重，保证中英文阅读的舒适性和美观性。
  - 首页特定
    - **多屏多层响应式布局**: 确保在各种屏幕尺寸下均有良好视觉和交互体验。
    - 动画效果
      - **Hero Section**: 背景可采用微妙的渐变动画或抽象科技粒子动效。标题和CTA按钮可使用平滑的进入动画 (如从下往上渐显)。
      - **滚动交互**: 功能特性区、工作流程区等内容块在滚动进入视口时，可采用视差滚动 (Parallax) 效果或元素渐入 (Fade-in/Slide-in) 动画，增强页面的动态感和吸引力。动画需流畅不卡顿，符合 `design_system.md` 动效规范。
      - **微交互**: 鼠标悬停在功能卡片或CTA按钮上时，应有符合设计系统的视觉反馈 (如轻微放大、阴影变化、颜色变化)。

- 多屏布局结构 (Mobile-First Approach)

  - 导航栏 (通用)
    - **桌面端 (`lg`及以上)**: Logo (左对齐，链接至首页)，主要导航链接 (如“产品特性”、“价格”-MVP可简化，“博客”-MVP后)，用户操作区 (登录/注册按钮 或 用户头像下拉菜单，右对齐)。高度 `64px` 或 `72px`。
    - **移动端/平板端 (`<lg`)**: Logo (左对齐或居中)，右侧为汉堡菜单图标。点击汉堡菜单图标，滑出或下拉显示导航链接和用户操作。高度 `56px` 或 `64px`。
  - 首页 - Hero Section (首屏)
    - **桌面端**: 通常采用左右布局或上下居中大标题布局。左/上侧为大标题、副标题、核心卖点列表和主要CTA按钮 (“立即注册”/“免费试用”)。右/下侧为产品核心功能的可视化展示 (如抽象动效图、简洁的产品界面截图或短视频片段)。
    - **移动端**: 单列堆叠布局。顶部为大标题和副标题，下方为CTA按钮，再下方为简化版的产品视觉元素。
  - 首页 - 功能特性区
    - **桌面端**: 通常采用2-4列网格布局，每列为一个功能卡片 (图标+标题+简短描述)。
    - **移动端**: 单列堆叠布局，每个功能卡片占据整行。
  - 首页 - 如何工作区
    - **桌面端**: 水平步骤条 (3-4步) 或带有编号的图文结合卡片。
    - **移动端**: 垂直步骤条或单列堆叠卡片。
  - **首页 - 再次CTA区**: 在页面中下部或底部再次放置醒目的CTA按钮，引导转化。
  - 登录/注册表单区
    - **桌面端**: 通常页面居中，卡片式布局，最大宽度限制 (如 `400px-480px`)。包含Logo、表单标题、输入框、提交按钮、第三方登录入口 (MVP后)、“忘记密码”/“已有账户登录”等链接。
    - **移动端**: 表单内容垂直铺满或接近铺满屏幕宽度，确保输入框和按钮有足够的操作空间。
  - 页脚 (通用)
    - **桌面端**: 多列布局，包含版权信息、隐私政策、服务条款、联系方式、社交媒体链接 (MVP后)、语言切换 (MVP后)等。
    - **移动端**: 单列堆叠或简化布局。

- 主要组件设计 (参照 

  ```
  design_system.md
  ```

  )

  - **导航栏**: `TopNavigation.tsx` 组件。
  - **按钮**: `Button.tsx` 组件 (Primary, Secondary, Text 类型；Large, Medium, Small 尺寸)。CTA按钮使用Primary, Large。
  - **输入框**: `InputField.tsx` 组件 (Text, Email, Password 类型)。包含Label, Placeholder, Helper Text (用于校验提示)。
  - **功能展示卡片**: `FeatureCard.tsx` 组件 (包含Icon, Title, Description)。
  - **步骤指示器**: `Stepper.tsx` 组件。
  - **页脚**: `Footer.tsx` 组件。

- 交互与动效 (参照 

  ```
  design_system.md
  ```

   动效系统)

  - 首页滚动动画
    - 元素随滚动进入视区时，应用 `FadeInUp` 或 `ScaleIn` 效果。使用自定义 Hook (如 `useIntersectionObserver`) 触发。
    - Hero Section背景可使用缓慢的 `Ken Burns` 效果或粒子动画。
  - **按钮交互**: 严格遵循设计系统定义的 `hover`, `active`, `focus`, `disabled` 状态。
  - 表单交互
    - 输入框聚焦时，边框和标签颜色变化。
    - 实时输入校验，错误/成功状态通过辅助文本和边框颜色反馈。
    - 提交按钮在请求处理中显示加载状态 (Spinner)。
  - **导航交互**: 汉堡菜单打开/关闭有平滑过渡动画 (如滑入/滑出，使用CSS Transitions或`framer-motion`等动画库)。

- 技术实现建议 (React)

  - **动画库**: CSS Transitions/Animations，或 `framer-motion` (用于更复杂的交互动画)。

  - **滚动动画触发**: 自定义Hook `useIntersectionObserver`。

  - 表单处理与校验 :

    ```
    React Hook Form
    ```

    (配合

    ```
    Zod
    ```

    或

    ```
    Yup
    ```

    进行规则定义)。

    - `LoginPage.tsx` 和 `RegisterPage.tsx` 使用 `React Hook Form` 管理表单状态、输入和提交。
    - 定义清晰的校验规则 (如邮箱格式、密码最小长度、密码复杂度等)。

  - API调用 : 使用封装好的

    ```
    axios
    ```

    实例 (配置baseURL, headers, interceptors) 与后端API

    ```
    /api/auth/register
    ```

    ,

    ```
    /api/auth/login
    ```

    通信。

    - 在 `src/services/authService.ts` 中封装注册和登录函数。

  - 状态管理 (Context API / `useReducer`)

    - 创建AuthContext (`/src/contexts/AuthContext.tsx`) 并使用 `useReducer` 管理用户认证状态 (如 `isAuthenticated`, `user`, `token`, `loading`, `error`)。
    - 登录/注册成功后，更新认证状态并持久化token (如 `localStorage` 或安全Cookie)。
    - 根据认证状态，自动重定向用户 (如已登录用户访问登录页则重定向到仪表盘，通过React Router的 `Maps` 组件或 `useNavigate` Hook实现)。

  - **响应式布局**: 使用Tailwind CSS的响应式断点 (`sm:`, `md:`, `lg:`, `xl:`)。

#### 4.1.2. 面试准备页 (Interview Setup Page)

- **页面文件名/路由**: `/interview/setup` (指向 `InterviewSetupPage.tsx` React组件)

- 页面功能与目标

  - **核心目标**: 引导用户在正式开始AI辅助面试前，完成必要的面试参数配置和设备检测与授权，确保面试过程顺利进行。
  - 功能
    1. **面试模式选择 (MVP可选)**: 若支持多种模式 (如“模拟面试-免费体验”、“正式面试-消耗次数”)，允许用户选择。
    2. 面试参数配置
       - **面试岗位/角色输入**: 允许用户输入或选择目标岗位名称 (如“前端开发工程师”、“产品经理”)，用于AI更精准地生成建议。
       - **(MVP后)** 行业选择、公司信息输入、面试轮次选择等。
    3. 设备检测与授权
       - **麦克风权限**: 请求并显示麦克风访问权限状态 (未授权/已授权/已拒绝/检测中)。提供“授权”按钮。
       - **音频输入检测**: 实时显示麦克风音量条/波形，帮助用户确认麦克风工作正常。
       - **屏幕共享权限**: 请求并显示屏幕共享权限状态。提供“选择共享内容”按钮。
       - **(MVP后)** 摄像头权限检测 (若未来支持视频分析)。
    4. **开始面试引导**: 清晰的“开始面试”CTA按钮，在所有必要条件满足前为禁用状态。
    5. **注意事项提示**: 简要提示用户保持环境安静、网络通畅等。

- 视觉设计风格

  - 简洁、聚焦、引导性强，避免不必要的装饰。
  - 采用步骤化或卡片式模块设计，将配置项和检测项清晰分隔。
  - 视觉层级分明，关键操作 (如授权按钮、开始面试按钮) 突出显示。
  - 使用符合 `design_system.md` 的图标和颜色标示状态。

- 多屏布局结构

  - **顶部**: 简洁导航栏 (仅含Logo和“返回”或“退出设置”按钮) 或步骤指示器 (若配置项较多，分步进行)。
  - 主内容区 (单列居中或卡片式布局)
    - 模块一: 面试信息配置 (可选)
      - 表单输入框/下拉选择框，用于设定面试岗位等。
    - 模块二: 设备检测与授权
      - 麦克风检测区
        - 图标 (Lucide: `Mic`) + 状态文字 (“麦克风权限：未授权”)。
        - “授权麦克风”按钮 (Primary Button)。
        - 授权成功后：状态文字变为“麦克风：已连接 - [设备名称]”，显示音量指示条。
        - 若失败/拒绝：状态文字变为“麦克风：授权失败/已拒绝”，提供重试或帮助链接。
      - 屏幕共享检测区
        - 图标 (Lucide: `ScreenShare`) + 状态文字 (“屏幕共享权限：未就绪”)。
        - “选择共享屏幕/窗口”按钮 (Secondary Button)。
        - 选择后：状态文字变为“屏幕共享：已就绪 ([共享窗口标题])”。
        - 若取消/失败：状态文字提示。
    - 模块三: 开始面试
      - 主操作按钮 (Primary Button, Large): “开始AI面试”。按钮下方可有小字提示 (如“点击开始后将消耗1次面试次数” - 若涉及计费)。
  - 辅助信息区 (可选，侧边栏或底部)
    - 简短的提示列表：“请确保网络连接稳定”、“建议佩戴耳机以获得最佳体验”、“面试过程中请保持专注”等。

- 主要组件设计 (参照 

  ```
  design_system.md
  ```

  )

  - **步骤条 (Stepper.tsx)**: 若采用分步配置。
  - **设备状态指示卡片**: `DeviceStatusCard.tsx` (包含Icon, Label, StatusText, ActionButton)。
  - **音量指示器**: `VolumeIndicator.tsx` (基于SVG或Canvas的条形/波形可视化)。
  - **输入框/选择框**: `InputField.tsx`, `SelectField.tsx`。
  - **主操作按钮**: `Button.tsx` (Primary, Large)。
  - **提示消息**: `Alert.tsx` 组件 (用于显示错误或重要提示，如“麦克风被禁用，请在浏览器设置中开启”)。

- 交互与动效

  - 点击“授权”按钮，平滑地弹出浏览器原生权限请求对话框。
  - 设备授权成功/失败后，状态图标和文字即时更新，可伴随轻微的颜色或缩放动画 (使用CSS Transitions 或 `framer-motion`)。
  - 音量指示器根据麦克风输入实时、流畅地波动。
  - “开始AI面试”按钮根据所有前置条件 (如岗位输入完成、麦克风和屏幕共享均已授权/就绪) 动态启用/禁用，禁用时有明确视觉提示 (透明度降低，`cursor:not-allowed`)。
  - 若配置项分步，步骤切换时有平滑的过渡动画 (如卡片横向滑动或淡入淡出)。

- 技术实现建议 (React)

  - 权限请求与状态管理
    - 使用 `navigator.mediaDevices.getUserMedia({ audio: true })` 请求麦克风权限。
    - 使用 `navigator.mediaDevices.getDisplayMedia({ video: true, audio: true })` 请求屏幕共享权限。
    - 通过 `.then()` 和 `.catch()` 处理权限请求结果。
    - 使用 `useState` 管理各项权限的状态 (`'idle'`, `'pending'`, `'granted'`, `'denied'`) 和设备信息。
    - 监听 `MediaStreamTrack` 的 `onended` 事件，以检测用户手动停止屏幕共享的情况。
  - 音频可视化
    - 获取到麦克风的 `MediaStream` 后，创建 `AudioContext`。
    - 创建 `AnalyserNode` 分析音频数据 (`getByteFrequencyData` 或 `getByteTimeDomainData`)。
    - 使用 `requestAnimationFrame` 循环读取分析结果，并更新Canvas或SVG绘制音量条/波形。
  - **面试参数存储**: 用户选择的面试岗位等信息，在点击“开始AI面试”时，连同会话ID等信息传递给后端或下一页面。可暂存于组件的 `useState` 或提升到父组件/Context中（如果多个组件需要共享）。
  - **导航至实时面试页**: 所有条件满足并点击开始后，使用React Router的 `useNavigate` Hook 跳转 (`Maps('/interview/live/' + sessionId)`)，并传递必要参数 (通过state或URL参数)。
  - **错误处理**: 详细处理各种权限请求失败、设备找不到等异常情况，并向用户提供清晰的指引。

#### 4.1.3. 实时面试辅助页 (Live Interview Assistant Page)

- **页面文件名/路由**: `/interview/live/:sessionId` (指向 `LiveInterviewPage.tsx` React组件, `:sessionId` 为面试会话ID)

- 页面功能与目标

  - **核心目标**: 在用户进行真实面试（通过第三方视频会议软件）的同时，提供一个**非侵入式、低延迟、高相关度**的AI辅助界面，帮助用户实时理解面试官问题并获得回答建议。
  - 功能
    1. 面试官问题实时转录
       - 通过屏幕共享捕获面试官的音频（通常来自视频会议软件的系统输出），或直接捕获用户麦克风输入（如果面试官和用户在同一物理空间或用户转述问题）。
       - 将捕获到的音频流实时发送至后端ASR服务进行语音转文字。
       - 最低延迟（目标<1秒）在界面上显示转录后的面试官问题文本。
    2. AI回答建议实时展示
       - 后端在接收到转录文本后，结合面试上下文（岗位信息、历史问答等）调用LLM生成回答建议。
       - AI建议以结构化、易于快速浏览的形式（如核心要点、回答框架、关键词提示）实时推送到前端界面。
       - 建议内容应与当前面试官问题高度相关。
    3. 面试状态与控制
       - 清晰显示当前系统的运行状态 (如“正在聆听”、“AI思考中”、“网络连接断开”等)。
       - 提供“暂停/继续AI辅助”功能 (MVP可选，允许用户临时关闭建议更新)。
       - 提供“结束本次面试辅助”功能，并引导至面试评估页。
       - (MVP后) 用户可对AI建议进行快速反馈 (如“有用”/“无关”)。
    4. **界面最小化与专注**: 整体界面设计需极简，避免分散用户在主面试界面的注意力。考虑浮窗模式或可调整大小的侧边栏模式。

- 视觉设计风格

  - **极简专注**: UI元素精简到极致，避免任何不必要的视觉干扰。信息密度适中。
  - **高可读性**: 采用高对比度的文本颜色 (如深灰/黑色文本在浅色背景上)，字体大小适中 (参考 `design_system.md` 中`body1`或`body2`)，确保在紧张环境下快速阅读。
  - 非侵入式
    - **浮窗模式 (Floating Window - MVP首选)**: 一个可拖动、可调整大小的小窗口，用户可以将其放置在屏幕合适位置，不遮挡视频会议软件的主要区域。窗口默认尺寸应较小。
    - **侧边栏模式 (Sidebar Mode - MVP可选)**: 如果技术上可行且用户接受，可考虑作为浏览器插件或桌面应用的一部分，停靠在屏幕边缘。
  - **信息层级清晰**: 面试官问题和AI建议区域明确区分，最新信息突出显示。

- 多屏布局结构 (主要考虑桌面端小窗口/辅助窗口形态)

  - 窗口标题栏 (若为浮窗模式)
    - 显示产品名称/Logo (极小)。
    - 包含最小化、(可选)置顶、关闭窗口（等同于结束辅助）的控制按钮。
    - 允许拖动窗口。
  - 主内容区域 (垂直布局)
    - 面试官问题显示区 (Transcribed Question Area)
      - 通常位于界面上半部分。
      - 滚动列表形式，最新识别到的问题追加在列表底部，并自动滚动到最新问题。
      - 每条问题可带有时间戳 (可选，用于调试或复盘)。
      - 文本样式：清晰、易读，与AI建议区有视觉区分。
    - AI建议显示区 (AI Suggestion Area)
      - 通常位于界面下半部分，或与问题区并排（若窗口较宽）。
      - 当前问题建议 : 针对最新识别的面试官问题，显示AI生成的回答建议。
        - **核心要点 (Key Points)**: 以列表 (bullet points) 或标签 (tags) 形式展示最重要的2-4个回答切入点或关键词。
        - **回答框架/思路 (Response Framework - 可选展开)**: 提供简要的回答结构或不同角度的思考方向 (如STAR原则提示)。
        - **(MVP后)** 相关知识点链接、追问预测。
      - **历史建议回顾 (可选)**: 允许用户快速回顾前几个问题的AI建议，但需避免界面混乱。
    - 状态指示与控制栏 (Status & Control Bar)
      - 通常位于窗口底部或顶部。
      - **系统状态指示**: 图标+简短文字 (如 Lucide: `Mic` “正在聆听...”, Lucide: `Brain` “AI思考中...”, Lucide: `WifiOff` “网络断开”)。
      - **(可选)** “暂停AI”/“继续AI”切换按钮 (Icon Button, e.g., Lucide: `PauseCircle` / `PlayCircle`)。
      - “结束面试”按钮 (Destructive Button or Icon Button, e.g., Lucide: `LogOut`)。

- 主要组件设计 (参照 

  ```
  design_system.md
  ```

  )

  - **实时文本流组件 (`RealtimeTextStream.tsx`)**: 用于展示面试官问题，支持自动滚动和内容高亮。

  - AI建议卡片/面板 (

    ```
    AISuggestionPanel.tsx
    ```

    )

    - 结构化展示核心要点 (使用 `ul`/`li` 或 `Tag.tsx` 组件)。
    - 可折叠/展开的详细建议区域 (使用 `Collapsible.tsx` 组件，或自定义实现)。
    - 复制按钮 (Lucide: `Copy`)，允许用户快速复制要点文本 (需谨慎设计，避免面试中误操作或过度依赖)。

  - **加载指示器 (`Spinner.tsx`, `Skeleton.tsx`)**: 在AI建议生成过程中显示。

  - **状态图标/文本**: `Icon.tsx` (来自 `lucide-react`) + `Caption` 文本。

  - **控制按钮**: `Button.tsx` (Icon-only, Small size) 或 `TextButton.tsx`。

- 交互与动效

  - 信息更新
    - 新的转录问题和AI建议出现时，应有平滑的加载和进入动画 (如内容从底部或侧边滑入并渐显，使用CSS Transitions或 `framer-motion`，`duration-200` 到 `duration-300`)。
    - 自动滚动到最新信息时，滚动过程应平滑。
  - **AI建议加载**: AI思考中时，建议区域显示 `Spinner.tsx` 或骨架屏 (`Skeleton.tsx`)。建议加载完成后，平滑替换加载指示器。
  - **状态变化反馈**: 系统状态 (聆听、思考、断网) 变化时，对应的图标和文本有即时、清晰的视觉反馈 (如颜色变化、图标动画)。
  - **窗口交互 (若为浮窗)**: 窗口拖动流畅，调整大小时内容自适应。
  - **用户操作反馈**: 点击暂停/继续、结束面试等按钮时，有明确的视觉或状态变化。

- 技术实现建议 (React)

  - WebSocket连接

    - 在 `LiveInterviewPage.tsx` 组件的 `useEffect` 生命周期钩子中，建立与后端WebSocket的连接 (`ws://your-backend-url/ws/interview/:sessionId`)。
    - 使用 `react-use-websocket` Hook 管理WebSocket的生命周期。
    - 监听来自服务器的 `INTERVIEWER_QUESTION` 和 `AI_SUGGESTION` 类型的消息。
    - 接收到消息后，更新组件的本地 `useState` 或Context/Zustand store中的面试数据，触发UI重新渲染。

  - 音频流处理与发送

    - 从 `InterviewSetupPage.tsx` 获取已授权的麦克风 `MediaStream` 和屏幕共享的 `MediaStream`。
    - 使用 Web Audio API (`AudioContext`, `MediaStreamAudioSourceNode`) 处理音频流。
    - **音频分片**: 将音频流分割成小片段。可以使用 `AudioWorklet` 在单独线程处理。
    - **格式转换 (可选)**: 将音频片段转换为后端ASR服务支持的格式。
    - **通过WebSocket发送**: 将处理后的音频片段以 `ArrayBuffer` 或 `Blob` 形式通过WebSocket发送到后端 (自定义消息类型如 `AUDIO_CHUNK`)。

  - UI实时更新

    - 面试官问题列表和AI建议列表使用 `.map()` 渲染。为每个列表项设置唯一的 `key` prop。
    - 确保状态更新逻辑清晰，避免不必要的重复渲染。

  - 浮窗实现 (若采用)

    - 在主页面内模拟浮窗效果。使用一个绝对定位、可拖拽、可调整大小的

      ```
      div
      ```

      元素。

      - 拖拽功能: 使用 `react-draggable` 或自定义 Hook。
      - 调整大小功能: 自定义实现或查找React相关库。

  - 错误处理与重连

    - `react-use-websocket` 通常包含重连逻辑。
    - 在UI上清晰提示网络断开或AI服务异常。

  - 状态管理 (useState / Context API / Zustand - 按需选择)

    - 对于此页面的复杂实时状态 (转录问题列表、AI建议列表、系统运行状态等)，可以考虑：
      - **`useState` / `useReducer`**: 如果状态逻辑主要局限于此页面，且不需要跨页面共享，使用组件本地状态或配合 `useReducer` 可能足够。
      - **Context API + `useReducer`**: 如果部分状态需要被深层子组件消费，可以使用Context。
      - **Zustand (按需引入)**: 如果状态逻辑非常复杂，更新频繁，或者未来需要在应用的其他部分访问这些实时数据，Zustand会是更优的选择，可以提供更好的性能和更简洁的API。MVP阶段可先尝试前两者，遇到瓶颈再考虑Zustand。
    - 存储内容示例: 当前会话ID, 转录问题列表 (`{id, text, timestamp, source}`), AI建议列表 (`{id, questionId, keyPoints[], framework, timestamp}`), 系统状态 (`'listening'`, `'processing_audio'`, `'fetching_ai_suggestion'`, `'error_asr'`, `'error_llm'`, `'disconnected'`)。

#### 4.1.4. 面试评估与反馈页 (Post-Interview Review Page)

- **页面文件名/路由**: `/interview/review/:sessionId` (指向 `InterviewReviewPage.tsx` React组件)

- 页面功能与目标

  - **核心目标**: 为用户提供一次完整面试的回顾、AI生成的初步评估以及可操作的改进建议，帮助用户从面试经验中学习和成长。
  - 功能
    1. **面试概览信息**: 显示本次面试的基本信息 (如面试岗位、日期、时长)。
    2. **完整问答记录**: 按时间顺序清晰展示面试官的每个问题和当时AI提供的核心辅助建议。
    3. **(MVP可选，P1)** 用户实际回答摘要: 如果技术上能在不侵犯隐私且用户明确授权的前提下捕获或由用户手动录入，则展示。
    4. AI综合评估 (MVP阶段简化)
       - **(MVP)** 主要回顾AI建议与问题的匹配度。
       - **(P1)** 针对用户（如果能获取到）回答的流畅度、关键词覆盖、逻辑结构等维度进行初步分析。
       - **(P1)** 总结本次面试中的亮点和可改进点。
    5. 个性化改进建议 (MVP阶段简化)
       - **(MVP)** 基于AI建议，提示用户可以从哪些方面准备类似问题的回答。
       - **(P1)** 针对评估出的薄弱环节，提供具体的学习资源链接或练习方向。
    6. **用户反馈入口 (MVP可选)**: 允许用户对本次AI辅助的整体体验或特定建议的质量进行评分或文字反馈。
    7. **记录导出/分享 (MVP后)**: 允许用户将面试记录和评估报告导出为PDF或文本文件，或通过链接分享。

- 视觉设计风格

  - 清晰、结构化、信息丰富但不过载。
  - 采用类似“报告”或“仪表盘”的专业风格。
  - 数据可视化元素 (如图表、评分条 - P1阶段) 应简洁易懂，符合 `design_system.md`。

- 多屏布局结构

  - 顶部导航/标题区
    - 面包屑导航 (如 “用户中心 > 面试历史 > 本次面试回顾”)。
    - 页面主标题：“面试回顾与评估 - [面试岗位]”。
    - (可选) 操作按钮：返回列表、导出报告。
  - 模块一: 面试概览 (Card组件)
    - 面试岗位、面试日期、总时长、(P1)整体AI评分。
  - 模块二: 问答详情与AI建议回顾 (主要内容区)
    - 时间轴视图/列表视图
      - 每个条目包含：“面试官问题X”、“AI辅助要点/建议”。
      - 点击条目可展开查看更详细的AI建议内容（如果建议较长）。
      - (P1) 若有用户回答摘要，则并列展示。
    - 滚动加载或分页处理长记录。
  - 模块三: AI评估分析 (P1阶段，Card组件或独立区域)
    - **优点分析**: 列点说明。
    - **待改进点**: 列点说明，并可链接到问答详情中的具体问题。
    - **(可选)** 雷达图或柱状图展示各能力维度评分 (如沟通表达、逻辑思维、技术匹配度 - 需LLM支持复杂评估)。
  - 模块四: 个性化改进建议 (P1阶段，Card组件)
    - 针对性建议列表。
  - 模块五: 用户反馈 (MVP可选，简单表单)
    - 评分组件 (如5星)、意见文本框。

- 主要组件设计 (参照 

  ```
  design_system.md
  ```

  )

  - **面包屑导航 (`Breadcrumbs.tsx`)**

  - **卡片 (`Card.tsx`)**: 用于包裹概览、评估、建议等模块。

  - 时间轴 (

    ```
    Timeline.tsx
    ```

    ) 或 可折叠列表 (

    ```
    AccordionList.tsx
    ```

    ) : 展示问答记录。

    - `TimelineItem.tsx` / `AccordionItem.tsx` 包含问题文本和AI建议文本。

  - **评分组件 (`RatingStars.tsx`, `ProgressBar.tsx` - P1)**

  - **按钮 (`Button.tsx`)**: 用于导出、返回等操作。

  - **标签 (`Tag.tsx`)**: 可用于标记问题类型或评估关键词。

  - **(P1) 图表组件**: 使用 `Recharts` 或 `Nivo`。

- 交互与动效

  - 时间轴或列表滚动平滑。长列表应考虑虚拟滚动以优化性能 (如 `react-window` 或 `react-virtualized`)。
  - 折叠面板展开/收起有平滑过渡动画 (使用CSS Transitions 或 `framer-motion`)。
  - (P1) 数据图表加载时可有简单的进入动画。
  - 点击评估中的“待改进点”，可平滑滚动到问答详情中对应的问题位置。

- 技术实现建议 (React)

  - API调用
    - 组件 `useEffect` (带空依赖数组) 时，从后端 `/api/interviews/review/:sessionId` 获取该次面试的完整记录和AI评估数据。
    - 使用 `axios` (或封装的service) 进行数据请求。
    - 显示加载状态 (`Spinner.tsx` 或 `Skeleton.tsx`) 直到数据返回。
  - 数据展示与处理
    - 对从后端获取的面试记录数据进行排序和格式化，以适应时间轴或列表的展示需求。
    - 长文本内容可能需要截断显示，并提供“查看更多”功能。
  - (P1) 文本导出
    - **前端生成PDF**: 使用如 `jsPDF` 和 `html2canvas` 库。
    - **后端生成PDF**: 更推荐。
    - **导出纯文本**: 前端直接将格式化后的文本数据构造成 `.txt` 文件供用户下载。
  - 状态管理 (useState / Context API)
    - 本页面数据主要为一次性加载展示，可优先使用组件本地状态 (`useState`) 管理面试回顾数据、加载状态、错误信息。如果需要在多个子组件间共享，可使用Context API。
  - **错误处理**: 处理API请求失败或数据格式异常的情况，向用户显示友好提示。

### 4.2. 其他页面 (MVP阶段简化或延后，P1/P2阶段完善)

#### 4.2.1. 用户中心 / 仪表盘 (User Dashboard / Profile Page)

- **页面文件名/路由**: `/dashboard` (指向 `DashboardPage.tsx`), `/profile` (指向 `ProfilePage.tsx` - 可合并或作为Dashboard的子页面)

- 页面功能与目标

  - **核心目标**: 为用户提供一个集中的地方管理个人信息、查看面试历史、管理订阅 (若有) 和获取产品更新与帮助。
  - 功能
    1. 用户概览 (Dashboard)
       - 欢迎信息。
       - (可选) 近期面试活动摘要 (如上次面试时间、待复习的面试)。
       - (可选) 快速开始新面试的入口。
       - (可选) 面试次数/订阅状态展示。
    2. 面试历史 (Interview History)
       - 列表形式展示用户所有已完成的面试辅助记录。
       - 每条记录包含关键信息 (如面试岗位、日期、时长、(P1)AI综合评分)。
       - 提供筛选和排序功能 (按日期、按岗位等 - P1)。
       - 点击可跳转到对应的“面试评估与反馈页” (`/interview/review/:sessionId`)。
    3. 个人资料管理 (Profile Management)
       - 查看和修改用户基本信息 (如昵称、头像 - MVP后)。
       - 修改登录邮箱/手机号 (需验证 - P1)。
       - 修改密码。
    4. 账户与订阅管理 (Account & Subscription - P1/P2)
       - 查看当前订阅套餐、有效期、剩余面试次数。
       - 升级/续费套餐入口。
       - 查看账单历史。
       - 管理支付方式。
    5. 设置 (Settings - P1)
       - 通知偏好设置。
       - 语言偏好设置。
       - (可选) AI辅助偏好设置 (如建议详细程度)。
    6. 帮助与支持 (Help & Support - P1)
       - FAQ链接。
       - 联系客服入口。

- 视觉设计风格

  - 整洁、专业、易用、信息组织有序。
  - 采用卡片式布局组织不同模块内容。
  - 符合 `design_system.md` 的整体风格。

- 多屏布局结构

  - 桌面端 : 典型的仪表盘布局。
    - **左侧固定侧边栏导航 (Sidebar Navigation)**: 包含“仪表盘概览”、“面试历史”、“个人资料”、“账户与订阅”、“设置”等链接。当前激活项高亮。 (参考已有的 `Sidebar.tsx`)
    - **右侧主内容区**: 根据侧边栏选择显示对应模块的内容。
  - 移动端
    - 顶部导航栏包含汉堡菜单，用于展开侧边栏导航功能。
    - 内容区单列堆叠显示。

- 主要组件设计 (参照 

  ```
  design_system.md
  ```

  )

  - **侧边栏导航 (`SidebarNavigation.tsx`)**
  - **内容卡片 (`Card.tsx`)**
  - **数据列表/表格 (`DataList.tsx` / `Table.tsx`)**: 用于展示面试历史。包含分页组件 (`Pagination.tsx`)。
  - **表单控件 (`InputField.tsx`, `Button.tsx`, `SelectField.tsx`)**: 用于个人资料编辑、设置等。
  - **用户头像 (`Avatar.tsx`)**
  - **摘要统计组件 (`SummaryStatCard.tsx` - P1)**

- 交互与动效

  - 侧边栏导航项激活和悬停状态清晰。
  - 列表项悬停时可有轻微背景变化。
  - 表单编辑和保存操作有即时反馈。
  - (P1) 数据加载时使用骨架屏或加载指示器。

- 技术实现建议 (React)

  - API调用

    - `/api/user/profile`: 获取用户基本信息。
    - `/api/interviews/history?page=1&limit=10`: 获取用户面试历史列表 (分页)。
    - `/api/user/profile/update` (PUT/PATCH): 更新用户信息。
    - `/api/auth/change-password` (POST): 修改密码。
    - (P1) `/api/subscriptions/current`, `/api/billing/history` 等。

  - 状态管理 (Context API / `useReducer` 或 `useState`)

    - 使用AuthContext中已有的用户资料数据。
    - 面试历史列表、分页信息、加载状态等可使用组件本地的 `useState` 或 `useReducer` 管理。

  - 路由 : 使用嵌套路由 (React Router v6 

    ```
    Outlet
    ```

    ) 实现侧边栏导航对应的内容区切换。

    - 如 `/dashboard/history`, `/dashboard/profile`, `/dashboard/settings`。

  - 数据展示

    - 面试历史列表使用 `.map()` 渲染。日期时间格式化 (使用 `date-fns` 或 `dayjs`)。
    - (P1) 考虑使用 `TanStack Table (React Adapter)` 或类似库实现功能更丰富的表格 (排序、筛选)。

### 4.3. 响应式设计特别强调 (Reiteration on Responsiveness)

- **移动优先 (Mobile-First) 原则**: 在CSS编写和组件设计时，首先考虑移动端的小屏幕布局和交互，然后通过媒体查询或响应式类逐步增强大屏幕的体验。
- **断点一致性**: 严格使用 `design_system.md` 中定义的断点 (`sm`, `md`, `lg`, `xl`, `2xl`)。
- **流式布局与弹性盒子/网格 (Fluid Layouts & Flexbox/Grid)**: 优先使用百分比、`vw`/`vh`、Flexbox和CSS Grid进行布局，避免固定像素宽度导致内容溢出或显示不全。
- 内容可读性与可操作性
  - 确保在小屏幕上文本内容依然清晰可读，不会过小或拥挤。
  - 确保按钮、链接等交互元素的触摸目标区域足够大 (至少 `44px * 44px`，符合Apple和Google的指导规范)。
- 导航适配
  - 顶部导航在小屏幕上转换为汉堡菜单。
  - 侧边栏导航在小屏幕上可默认为折叠状态或通过汉堡菜单触发显示。
- **图像优化**: 使用响应式图片技术 (`<picture>`元素, `srcset`属性) 为不同屏幕尺寸和分辨率提供合适的图像资源。SVG图标因其矢量特性，天然具有响应性。
- **全面测试**: 必须在多种真实设备 (不同品牌和操作系统的手机、平板) 和浏览器缩放、开发者工具模拟器上进行彻底的响应式测试。

### 4.4. 无障碍设计 (Accessibility - A11y) - 实施要点

- 语义化HTML
  - **地标角色 (Landmark Roles)**: 正确使用 `<header>`, `<nav>`, `<main>`, `<aside>`, `<footer>` 划分页面区域。
  - **标题层级 (Heading Levels)**: 严格遵循 `<h1>` 到 `<h6>` 的逻辑层级，`<h1>` 每页唯一。
  - **列表 (Lists)**: 无序列表 `<ul>`，有序列表 `<ol>`，定义列表 `<dl>`。
  - **按钮与链接**: `<button>` 用于执行操作，`<a>` 用于导航。若使用 `div` 等模拟按钮，必须添加 `role="button"` 和键盘事件处理。
- 键盘可访问性
  - **焦点顺序**: 使用 `tabIndex` (通常为 `0` 或 `-1`) 控制非原生可聚焦元素的焦点行为，确保焦点顺序符合逻辑。
  - **自定义组件**: 对于自定义的下拉菜单、滑块、标签页等组件，需自行实现完整的键盘交互 (方向键、`Enter`, `Space`, `Esc`)。
- ARIA属性应用
  - **动态内容**: `aria-live` (设为 `polite` 或 `assertive`) 用于通知屏幕阅读器动态内容的变化 (如错误消息、加载结果)。
  - **状态与属性**: `aria-expanded`, `aria-selected`, `aria-current`, `aria-disabled`, `aria-hidden`, `aria-haspopup` 等，准确描述组件状态。
  - **标签与描述**: `aria-label` (当无可见标签时), `aria-labelledby` (关联可见标签), `aria-describedby` (关联描述性文本)。
- **颜色对比度**: 使用工具 (如WebAIM Contrast Checker, Chrome DevTools) 检查并确保所有文本和重要UI元素满足WCAG AA级对比度要求。
- 表单
  - 所有输入框都有明确的 `<label htmlFor="...">`。
  - 错误提示通过 `aria-describedby` 关联到输入框，并使用 `aria-invalid="true"` 标记错误字段。
- 模态框与弹出层
  - 打开时，焦点移入模态框内。
  - 关闭时，焦点返回到触发元素。
  - 键盘焦点被限制在模态框内 (Focus Trap)。
  - `Esc`键关闭。
- **测试**: 使用屏幕阅读器 (NVDA, VoiceOver, JAWS) 和键盘进行手动测试。利用自动化A11y检查工具 (如Axe DevTools, Lighthouse) 进行初步扫描。

## 5. (MVP后) 进阶功能与未来迭代方向

### 5.1. P1 阶段 (MVP发布后1-3个月)

- **用户反馈系统完善**: 对AI建议的“顶/踩”、星级评分、文字评论功能。
- **AI评估报告增强**: 更细致的维度分析 (如STAR原则应用评估、逻辑性评估、特定岗位技能词覆盖率)。
- **个性化面试题库与练习模式**: 用户可根据目标岗位选择特定题库进行模拟练习，AI扮演面试官。
- **用户回答录入与分析**: 允许用户在面试后手动录入或（在用户授权下）尝试录制自己的回答，AI进行对比分析。
- **基础订阅与支付功能**: 引入付费套餐，提供更多面试次数或高级功能。集成Stripe或微信/支付宝支付。
- **团队协作功能初步探索**: (针对求职辅导机构) 允许多个学生账户关联到一个教师/管理员账户。

### 5.2. P2 阶段 (MVP发布后3-6个月)

- **深度个性化AI模型**: 基于用户历史面试数据和反馈，微调AI模型，提供更具个性化的辅助。
- **多语言支持**: 扩展支持英文等其他主流语言的面试辅助。
- **视频分析与反馈 (需用户明确授权且关注隐私)**: 分析用户面试时的面部表情、眼神交流、肢体语言，提供非语言沟通技巧的反馈。
- **行业/公司专属知识库集成**: 允许用户导入或选择特定行业/公司的面试资料，AI辅助时优先参考。
- **API开放与生态合作**: 考虑向求职平台、在线教育平台开放API，拓展应用场景。

