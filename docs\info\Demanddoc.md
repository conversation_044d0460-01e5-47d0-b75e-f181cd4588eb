---

### **需求文档生成提示语**
```markdown
"根据以下结构化框架生成完整的需求文档，要求：
1. 遵循最小可行产品(MVP)原则，聚焦核心功能
2. 技术方案需兼容零代码平台能力
3. 包含可量化验收标准
4. 输出格式为Markdown
5. 使用中文技术术语

文档框架：
# 项目名称
## 1. 项目概述
## 2. 核心功能
## 3. 系统架构
## 4. 模块详细需求
## 5. 非功能性需求
## 6. 开发计划
## 7. 测试策略
## 8. 部署方案

输入上下文：
<在此插入之前提供的所有系统设计、架构图、功能描述等内容>
@codebase 参考现有设计系统规范
@web 检查行业合规性要求"
```

---

### **完整需求文档示例**
```markdown
# 面试君AI实时辅助面试系统需求文档  
**版本**：1.0 MVP  
**最后更新**：2024年3月  

---

## 1. 项目概述
### 1.1 产品定位
为求职者提供实时AI面试辅助的SaaS工具，通过屏幕共享+语音识别+大模型技术，降低面试焦虑指数30%以上  

### 1.2 MVP目标
- 核心用户流完成度：100%  
- 支付转化率：>5%  
- 端到端延迟：<3秒  

---

## 2. 核心功能
### 2.1 必要功能（P0）
| 功能模块 | 验收标准 | 技术方案 |
|---------|----------|----------|
| 实时语音转写 | 识别准确率>85% | 百度语音识别API+音频流处理 |  
| AI回答建议 | 响应延迟<2秒 | DeepSeek模型+Prompt工程优化 |  
| 面试记录 | 完整对话存档率100% | Firestore实时数据库 |  
| 基础支付 | 微信支付成功率>99% | 平台支付模块+订单校验 |  

### 2.2 优化功能（P1）
- 多面试场景模板
- 邀请奖励系统
- 自适应屏幕布局

---

## 3. 系统架构
### 3.1 技术栈
| 层级 | 技术方案 |  
|------|----------|
| 前端 | Webflow可视化开发 + Tailwind CSS |  
| 后端 | Firebase云函数 + NocoBase零代码平台 |  
| 数据 | Firestore + BigQuery |  

### 3.2 数据流
```mermaid
graph LR
   用户输入 --> 语音识别 --> 大模型处理 --> 输出建议 --> 前端渲染
   支付请求 --> 支付网关 --> 订单更新 --> 次数充值
```

---

## 4. 模块详细需求
### 4.1 面试核心模块
**功能流程**：  
1. 用户启动屏幕共享（WebRTC实现）  
2. 系统捕获音频流（采样率16kHz）  
3. 实时转写为文字（每400ms发送一次）  
4. AI生成建议（Prompt模板见附录）  
5. 前端展示建议（浮动半透明层）  

**异常处理**：  
- 网络中断自动重试3次  
- 敏感词过滤机制  

### 4.2 支付模块
**套餐配置**：  
| 套餐类型 | 价格 | 面试次数 |
| -------- | ---- | -------- |
| 体验版   | 免费 | 1次      |
| 专业版   | ￥99 | 10次     |

**支付流程**：  
```mermaid
sequenceDiagram
   用户->>+前端: 选择套餐
   前端->>+支付网关: 创建订单
   支付网关-->>-前端: 返回支付二维码
   用户->>+微信: 完成支付
   微信-->>-系统: 异步通知
   系统->>+数据库: 更新面试次数
```

---

## 5. 非功能性需求
### 5.1 性能指标
| 指标     | 标准   |
| -------- | ------ |
| 首屏加载 | <1.5s  |
| 语音延迟 | <800ms |
| API响应  | P95<1s |

### 5.2 安全要求
- 语音数据24小时后自动清除  
- GDPR合规的个人信息处理  

### 5.3 可访问性
- WCAG 2.1 AA标准  
- 键盘导航支持率100%  

---

## 6. 开发计划
### 阶段1：核心功能验证（2周）
| 任务         | 交付物             |
| ------------ | ------------------ |
| 语音识别集成 | 可运行的音频流Demo |
| AI回答原型   | 10个测试用例       |

### 阶段2：支付闭环（1周）
- 微信支付沙盒测试
- 订单状态机实现

---

## 7. 测试策略
### 7.1 测试类型
| 类型     | 工具      | 覆盖率     |
| -------- | --------- | ---------- |
| E2E测试  | Cypress   | 主流程100% |
| 压力测试 | Loader.io | 50并发用户 |

### 7.2 验收清单
- [ ] 连续5次面试无数据丢失  
- [ ] 支付失败率<0.5%  

---

## 8. 部署方案
### 8.1 环境配置
| 环境 | 配置           | 用途     |
| ---- | -------------- | -------- |
| 开发 | Firebase免费版 | 功能验证 |
| 生产 | GCP标准型实例  | 正式服务 |

### 8.2 监控体系
- 错误日志：Sentry实时报警  
- 性能监控：Grafana仪表盘  

---

**附录A：AI Prompt模板**  
```json
{
  "role": "资深技术面试官",
  "instruction": "针对候选人的回答：\n1. 提取核心能力关键词\n2. 生成追问问题\n3. 按1-5分初步评分",
  "output_format": {
    "follow_up_question": "string",
    "skill_tags": ["array"],
    "score": "number"
  }
}
```
```

---

### **关键要素说明**
1. **MVP聚焦**：仅保留语音识别、AI建议、支付三个核心模块  
2. **量化指标**：所有需求均附带可测量标准  
3. **零代码适配**：技术栈选择Webflow/Firebase等可视化平台  
4. **风险控制**：明确异常处理和安全要求  
5. **可扩展设计**：模块化架构支持后续迭代  

该文档可直接输入Cursor生成基础代码框架，配合`@codebase`指令持续完善细节。