# QJ-HouseSeek Toast提示系统开发教程

## 目录
1. [Toast系统概述](#toast系统概述)
2. [技术架构设计](#技术架构设计)
3. [环境准备](#环境准备)
4. [核心文件详解](#核心文件详解)
5. [UI样式设计](#ui样式设计)
6. [3秒计时和进度条实现](#3秒计时和进度条实现)
7. [使用方法](#使用方法)
8. [最佳实践](#最佳实践)
9. [常见问题](#常见问题)
10. [完整代码清单](#完整代码清单)

---

## Toast系统概述

### 什么是Toast？
Toast是一种轻量级的消息提示组件，通常出现在页面的右上角，用于向用户显示操作结果、系统通知或重要信息。它会在指定时间后自动消失，不会阻断用户的正常操作流程。

### 为什么需要Toast？
在现代Web应用中，Toast提示有以下优势：
- **非侵入性**：不会打断用户的操作流程
- **用户体验友好**：提供即时反馈，让用户知道操作结果
- **视觉统一**：统一的样式设计保持界面一致性
- **自动消失**：无需用户手动关闭，减少操作负担

### QJ-HouseSeek项目中的Toast特点
- 支持4种类型：成功(success)、错误(error)、信息(info)、警告(warning)
- 3秒自动消失（可自定义）
- 带有进度条显示剩余时间
- 支持手动关闭
- 响应式设计，适配各种屏幕尺寸
- 使用React Context全局管理

---

## 技术架构设计

### 整体架构图
```
App.tsx (ToastProvider包装)
    ├── ToastContext (全局状态管理)
    ├── useToast Hook (业务逻辑)
    ├── ToastContainer (容器组件)
    └── Toast (单个Toast组件)
```

### 核心组件说明
1. **ToastProvider**: 提供全局Toast上下文
2. **useToast Hook**: 管理Toast状态和操作方法
3. **ToastContainer**: Toast容器，负责定位和渲染
4. **Toast**: 单个Toast组件，包含UI和动画
5. **类型定义**: TypeScript类型定义文件

### 技术栈
- **React 18**: 前端框架
- **TypeScript**: 类型安全
- **Tailwind CSS**: 样式框架
- **Lucide React**: 图标库
- **React Portal**: 渲染到body层级

---

## 环境准备

### 依赖安装
确保项目中已安装以下依赖：

```bash
# 核心依赖
npm install react react-dom

# 开发依赖
npm install -D typescript @types/react @types/react-dom

# 样式和图标
npm install tailwindcss lucide-react
```

### Tailwind CSS配置
确保 `tailwind.config.js` 包含以下配置：

```javascript
/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      animation: {
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'slide-up': 'slideUp 0.3s ease-out',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' }
        },
        slideUp: {
          '0%': { 
            transform: 'translateY(10px)', 
            opacity: '0' 
          },
          '100%': { 
            transform: 'translateY(0)', 
            opacity: '1' 
          }
        }
      }
    },
  },
  plugins: [],
}
```

---

## 核心文件详解

### 1. 类型定义文件 (types/toast.ts)

这个文件定义了Toast系统中所有的TypeScript类型，为整个系统提供类型安全保障。

```typescript
// Toast相关类型定义

export type ToastType = 'success' | 'error' | 'info' | 'warning';

export interface Toast {
  id: string;              // 唯一标识符
  type: ToastType;         // Toast类型
  title?: string;          // 可选标题
  message: string;         // 消息内容
  duration?: number;       // 显示时长(毫秒)
  autoClose?: boolean;     // 是否自动关闭
  createdAt: number;       // 创建时间戳
}

export interface ToastOptions {
  type?: ToastType;        // Toast类型
  title?: string;          // 可选标题
  duration?: number;       // 显示时长
  autoClose?: boolean;     // 是否自动关闭
}

export interface UseToastReturn {
  toasts: Toast[];                                           // Toast列表
  showToast: (message: string, options?: ToastOptions) => string;  // 显示Toast
  showSuccess: (message: string, duration?: number) => string;     // 显示成功Toast
  showError: (message: string, duration?: number) => string;       // 显示错误Toast
  showInfo: (message: string, duration?: number) => string;        // 显示信息Toast
  showWarning: (message: string, duration?: number) => string;     // 显示警告Toast
  removeToast: (id: string) => void;                        // 移除Toast
  clearAllToasts: () => void;                               // 清除所有Toast
}
```

**关键点解释**：
- `ToastType`: 限制Toast只能是4种预定义类型
- `Toast`: 完整的Toast数据结构
- `ToastOptions`: 创建Toast时的可选参数
- `UseToastReturn`: Hook返回的方法和数据接口

### 2. Toast Hook (hooks/useToast.ts)

这是Toast系统的核心业务逻辑，管理所有Toast的状态和操作。

```typescript
import { useState, useCallback } from 'react';
import { Toast, ToastOptions, UseToastReturn } from '../types/toast';

// 生成唯一ID
const generateId = (): string => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
};

export const useToast = (): UseToastReturn => {
  const [toasts, setToasts] = useState<Toast[]>([]);

  // 添加Toast
  const showToast = useCallback((message: string, options: ToastOptions = {}): string => {
    const id = generateId();
    const toast: Toast = {
      id,
      type: options.type || 'info',
      title: options.title,
      message,
      duration: options.duration || 3000,
      autoClose: options.autoClose !== false,
      createdAt: Date.now()
    };

    setToasts(prev => [...prev, toast]);

    // 自动移除
    if (toast.autoClose && toast.duration && toast.duration > 0) {
      setTimeout(() => {
        removeToast(id);
      }, toast.duration);
    }

    return id;
  }, []);

  // 移除Toast
  const removeToast = useCallback((id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id));
  }, []);

  // 清除所有Toast
  const clearAllToasts = useCallback(() => {
    setToasts([]);
  }, []);

  // 便捷方法
  const showSuccess = useCallback((message: string, duration = 3000): string => {
    return showToast(message, { type: 'success', duration });
  }, [showToast]);

  const showError = useCallback((message: string, duration = 5000): string => {
    return showToast(message, { type: 'error', duration });
  }, [showToast]);

  const showInfo = useCallback((message: string, duration = 3000): string => {
    return showToast(message, { type: 'info', duration });
  }, [showToast]);

  const showWarning = useCallback((message: string, duration = 4000): string => {
    return showToast(message, { type: 'warning', duration });
  }, [showToast]);

  return {
    toasts,
    showToast,
    showSuccess,
    showError,
    showInfo,
    showWarning,
    removeToast,
    clearAllToasts
  };
};
```

**关键点解释**：
- `generateId()`: 生成唯一ID，结合时间戳和随机数
- `showToast()`: 核心方法，创建并添加Toast到列表
- `removeToast()`: 从列表中移除指定Toast
- `useCallback`: 优化性能，避免不必要的重新渲染
- 便捷方法: 为不同类型提供快捷调用方式
- 默认时长: 错误消息显示5秒，其他3-4秒

---

## UI样式设计

### 设计原则
1. **色彩语义化**: 不同类型使用不同颜色表达含义
2. **轻量简洁**: 不占用过多屏幕空间
3. **视觉层次**: 通过颜色深浅区分重要程度
4. **一致性**: 所有Toast保持统一的视觉风格

### 四种类型的样式配置

```typescript
const getToastConfig = () => {
  switch (toast.type) {
    case 'success':
      return {
        bgColor: 'bg-green-50',      // 浅绿色背景
        borderColor: 'border-green-200',  // 绿色边框
        textColor: 'text-green-800',      // 深绿色文字
        iconColor: 'text-green-500',      // 绿色图标
        icon: CheckCircle                 // 对勾图标
      };
    case 'error':
      return {
        bgColor: 'bg-red-50',        // 浅红色背景
        borderColor: 'border-red-200',    // 红色边框
        textColor: 'text-red-800',        // 深红色文字
        iconColor: 'text-red-500',        // 红色图标
        icon: AlertCircle                 // 警告圆圈图标
      };
    case 'warning':
      return {
        bgColor: 'bg-yellow-50',     // 浅黄色背景
        borderColor: 'border-yellow-200', // 黄色边框
        textColor: 'text-yellow-800',     // 深黄色文字
        iconColor: 'text-yellow-500',     // 黄色图标
        icon: AlertTriangle               // 警告三角图标
      };
    case 'info':
    default:
      return {
        bgColor: 'bg-blue-50',       // 浅蓝色背景
        borderColor: 'border-blue-200',   // 蓝色边框
        textColor: 'text-blue-800',       // 深蓝色文字
        iconColor: 'text-blue-500',       // 蓝色图标
        icon: Info                        // 信息图标
      };
  }
};
```

### 布局结构
```
┌─────────────────────────────────────┐
│ [图标] 标题（可选）              [×] │
│        消息内容                     │
│ ─────────────────────────────────── │  ← 进度条
└─────────────────────────────────────┘
```

---

## 3秒计时和进度条实现

### 计时机制
Toast的自动关闭通过JavaScript的`setTimeout`实现：

```typescript
// 自动关闭逻辑
useEffect(() => {
  if (toast.autoClose && toast.duration && toast.duration > 0) {
    const timer = setTimeout(handleClose, toast.duration);
    return () => clearTimeout(timer);  // 清理定时器
  }
}, [toast.autoClose, toast.duration]);
```

### 进度条实现
进度条通过CSS动画实现，从100%宽度逐渐减少到0%：

```typescript
{/* 进度条组件 */}
{toast.autoClose && toast.duration && toast.duration > 0 && (
  <div className="absolute bottom-0 left-0 right-0 h-1 bg-black bg-opacity-10 rounded-b-lg overflow-hidden">
    <div
      className={`h-full ${config.iconColor.replace('text-', 'bg-')} transition-all ease-linear`}
      style={{
        width: '0%',
        animation: `toast-progress ${toast.duration}ms linear forwards`
      }}
    />
  </div>
)}

{/* CSS动画定义 */}
<style>
  {`
    @keyframes toast-progress {
      from { width: 100%; }
      to { width: 0%; }
    }
  `}
</style>
```

**关键技术点**：
1. **动画时长**: 与Toast显示时长完全同步
2. **颜色匹配**: 进度条颜色与Toast类型颜色一致
3. **线性动画**: 使用`ease-linear`确保匀速进行
4. **响应式**: 进度条宽度自适应Toast宽度

---

## 使用方法

### 基础设置
首先需要在应用根组件中设置ToastProvider：

```typescript
// App.tsx
import { ToastProvider } from './contexts/ToastContext';
import ToastContainer from './components/ui/ToastContainer';

function App() {
  return (
    <ToastProvider>
      {/* 你的应用内容 */}
      <div className="min-h-screen bg-gray-50">
        {/* Toast容器 - 必须放在这里 */}
        <ToastContainer />
        
        {/* 其他组件 */}
        <Header />
        <main>
          {/* 页面内容 */}
        </main>
      </div>
    </ToastProvider>
  );
}
```

### 在组件中使用Toast

```typescript
import React from 'react';
import { useToastContext } from '../contexts/ToastContext';

const MyComponent: React.FC = () => {
  const { showSuccess, showError, showInfo, showWarning } = useToastContext();

  const handleSuccess = () => {
    showSuccess('操作成功！数据已保存');
  };

  const handleError = () => {
    showError('操作失败，请稍后重试', 5000); // 5秒后消失
  };

  const handleInfo = () => {
    showInfo('这是一条信息提示');
  };

  const handleWarning = () => {
    showWarning('请注意：此操作不可撤销');
  };

  return (
    <div>
      <button onClick={handleSuccess}>显示成功消息</button>
      <button onClick={handleError}>显示错误消息</button>
      <button onClick={handleInfo}>显示信息消息</button>
      <button onClick={handleWarning}>显示警告消息</button>
    </div>
  );
};
```

### 高级用法

```typescript
const { showToast, removeToast } = useToastContext();

// 自定义Toast
const customToast = () => {
  const toastId = showToast('自定义消息', {
    type: 'info',
    title: '自定义标题',
    duration: 10000,        // 10秒
    autoClose: false        // 不自动关闭
  });
  
  // 5秒后手动关闭
  setTimeout(() => {
    removeToast(toastId);
  }, 5000);
};
```

---

## 最佳实践

### 1. 消息内容设计
- **简洁明了**: 消息内容应该简短，一般不超过50个字符
- **用户友好**: 使用用户能理解的语言，避免技术术语
- **行动指导**: 错误消息应该告诉用户如何解决问题

```typescript
// ✅ 好的示例
showSuccess('房源发布成功！');
showError('网络连接失败，请检查网络后重试');

// ❌ 不好的示例
showSuccess('Property submission completed successfully with validation passed');
showError('Error 500: Internal server error occurred');
```

### 2. 时长设置原则
- **成功消息**: 3秒（用户需要确认操作成功）
- **信息消息**: 3秒（一般信息）
- **警告消息**: 4秒（需要用户注意）
- **错误消息**: 5秒（用户需要时间阅读和理解）

```typescript
const showSuccess = useCallback((message: string, duration = 3000): string => {
  return showToast(message, { type: 'success', duration });
}, [showToast]);

const showError = useCallback((message: string, duration = 5000): string => {
  return showToast(message, { type: 'error', duration });
}, [showToast]);
```

### 3. 使用场景指南

**成功Toast (Success)**:
- 数据保存成功
- 操作完成确认
- 登录/注册成功

**错误Toast (Error)**:
- 网络请求失败
- 表单验证错误
- 权限不足

**信息Toast (Info)**:
- 系统通知
- 功能说明
- 状态更新

**警告Toast (Warning)**:
- 操作风险提醒
- 数据即将过期
- 重要变更通知

### 4. 性能优化
- 使用`useCallback`包装所有方法，避免不必要的重新渲染
- 限制同时显示的Toast数量（建议最多5个）
- 及时清理定时器，避免内存泄漏

```typescript
// 限制Toast数量
const showToast = useCallback((message: string, options: ToastOptions = {}): string => {
  const id = generateId();

  setToasts(prev => {
    const newToasts = [...prev, toast];
    // 限制最多显示5个Toast
    return newToasts.slice(-5);
  });

  // ... 其他逻辑
}, []);
```

---

## 常见问题

### Q1: Toast不显示怎么办？
**可能原因**:
1. 没有在App组件中添加ToastProvider
2. 没有添加ToastContainer组件
3. CSS样式被覆盖

**解决方案**:
```typescript
// 确保App.tsx中有正确的设置
function App() {
  return (
    <ToastProvider>  {/* 必须包装整个应用 */}
      <div className="min-h-screen bg-gray-50">
        <ToastContainer />  {/* 必须添加容器 */}
        {/* 其他组件 */}
      </div>
    </ToastProvider>
  );
}
```

### Q2: Toast位置不正确？
**原因**: CSS定位问题或z-index层级问题

**解决方案**:
```css
/* 确保ToastContainer有正确的定位 */
.toast-container {
  position: fixed;
  top: 1rem;
  right: 1rem;
  z-index: 9999;  /* 确保在最顶层 */
}
```

### Q3: 进度条颜色不匹配？
**原因**: 颜色替换逻辑错误

**解决方案**:
```typescript
// 确保颜色替换逻辑正确
className={`h-full ${config.iconColor.replace('text-', 'bg-')} transition-all ease-linear`}
```

### Q4: Toast在移动端显示异常？
**解决方案**: 添加响应式样式
```typescript
// 在ToastContainer中添加响应式定位
<div className="fixed top-4 right-4 md:top-4 md:right-4 sm:top-2 sm:right-2 z-[9999] space-y-3">
```

### Q5: 如何调试Toast系统？
```typescript
// 在useToast Hook中添加调试日志
const showToast = useCallback((message: string, options: ToastOptions = {}): string => {
  console.log('Creating toast:', { message, options });
  // ... 其他逻辑
}, []);
```

---

## 完整代码清单

### 1. 类型定义 (src/types/toast.ts)
```typescript
// Toast相关类型定义

export type ToastType = 'success' | 'error' | 'info' | 'warning';

export interface Toast {
  id: string;
  type: ToastType;
  title?: string;
  message: string;
  duration?: number;
  autoClose?: boolean;
  createdAt: number;
}

export interface ToastOptions {
  type?: ToastType;
  title?: string;
  duration?: number;
  autoClose?: boolean;
}

export interface UseToastReturn {
  toasts: Toast[];
  showToast: (message: string, options?: ToastOptions) => string;
  showSuccess: (message: string, duration?: number) => string;
  showError: (message: string, duration?: number) => string;
  showInfo: (message: string, duration?: number) => string;
  showWarning: (message: string, duration?: number) => string;
  removeToast: (id: string) => void;
  clearAllToasts: () => void;
}
```

### 2. Toast Hook (src/hooks/useToast.ts)
```typescript
import { useState, useCallback } from 'react';
import { Toast, ToastOptions, UseToastReturn } from '../types/toast';

// 生成唯一ID
const generateId = (): string => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
};

export const useToast = (): UseToastReturn => {
  const [toasts, setToasts] = useState<Toast[]>([]);

  // 添加Toast
  const showToast = useCallback((message: string, options: ToastOptions = {}): string => {
    const id = generateId();
    const toast: Toast = {
      id,
      type: options.type || 'info',
      title: options.title,
      message,
      duration: options.duration || 3000,
      autoClose: options.autoClose !== false,
      createdAt: Date.now()
    };

    setToasts(prev => [...prev, toast]);

    // 自动移除
    if (toast.autoClose && toast.duration && toast.duration > 0) {
      setTimeout(() => {
        removeToast(id);
      }, toast.duration);
    }

    return id;
  }, []);

  // 移除Toast
  const removeToast = useCallback((id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id));
  }, []);

  // 清除所有Toast
  const clearAllToasts = useCallback(() => {
    setToasts([]);
  }, []);

  // 便捷方法
  const showSuccess = useCallback((message: string, duration = 3000): string => {
    return showToast(message, { type: 'success', duration });
  }, [showToast]);

  const showError = useCallback((message: string, duration = 5000): string => {
    return showToast(message, { type: 'error', duration });
  }, [showToast]);

  const showInfo = useCallback((message: string, duration = 3000): string => {
    return showToast(message, { type: 'info', duration });
  }, [showToast]);

  const showWarning = useCallback((message: string, duration = 4000): string => {
    return showToast(message, { type: 'warning', duration });
  }, [showToast]);

  return {
    toasts,
    showToast,
    showSuccess,
    showError,
    showInfo,
    showWarning,
    removeToast,
    clearAllToasts
  };
};
```

### 3. Toast Context (src/contexts/ToastContext.tsx)
```typescript
import React, { createContext, useContext, ReactNode } from 'react';
import { useToast } from '../hooks/useToast';
import { UseToastReturn } from '../types/toast';

// 创建Toast Context
const ToastContext = createContext<UseToastReturn | undefined>(undefined);

// Toast Provider组件
interface ToastProviderProps {
  children: ReactNode;
}

export const ToastProvider: React.FC<ToastProviderProps> = ({ children }) => {
  const toastMethods = useToast();

  return (
    <ToastContext.Provider value={toastMethods}>
      {children}
    </ToastContext.Provider>
  );
};

// 自定义Hook来使用Toast Context
export const useToastContext = (): UseToastReturn => {
  const context = useContext(ToastContext);
  if (!context) {
    throw new Error('useToastContext must be used within a ToastProvider');
  }
  return context;
};
```

### 4. Toast容器组件 (src/components/ui/ToastContainer.tsx)
```typescript
import React from 'react';
import { createPortal } from 'react-dom';
import Toast from './Toast';
import { useToastContext } from '../../contexts/ToastContext';

const ToastContainer: React.FC = () => {
  const { toasts, removeToast } = useToastContext();

  // 如果没有Toast，不渲染任何内容
  if (toasts.length === 0) {
    return null;
  }

  // 使用Portal渲染到body，确保在最顶层
  return createPortal(
    <div className="fixed top-4 right-4 z-[9999] space-y-3 pointer-events-none">
      {toasts.map((toast) => (
        <div key={toast.id} className="pointer-events-auto">
          <Toast toast={toast} onRemove={removeToast} />
        </div>
      ))}
    </div>,
    document.body
  );
};

export default ToastContainer;
```

### 5. Toast组件 (src/components/ui/Toast.tsx)
```typescript
import React, { useEffect, useState } from 'react';
import { X, CheckCircle, AlertCircle, Info, AlertTriangle } from 'lucide-react';
import { Toast as ToastType } from '../../types/toast';

interface ToastProps {
  toast: ToastType;
  onRemove: (id: string) => void;
}

const Toast: React.FC<ToastProps> = ({ toast, onRemove }) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isLeaving, setIsLeaving] = useState(false);

  useEffect(() => {
    // 进入动画
    const timer = setTimeout(() => setIsVisible(true), 10);
    return () => clearTimeout(timer);
  }, []);

  const handleClose = () => {
    setIsLeaving(true);
    setTimeout(() => onRemove(toast.id), 300);
  };

  // 自动关闭
  useEffect(() => {
    if (toast.autoClose && toast.duration && toast.duration > 0) {
      const timer = setTimeout(handleClose, toast.duration);
      return () => clearTimeout(timer);
    }
  }, [toast.autoClose, toast.duration]);

  // 获取样式配置
  const getToastConfig = () => {
    switch (toast.type) {
      case 'success':
        return {
          bgColor: 'bg-green-50',
          borderColor: 'border-green-200',
          textColor: 'text-green-800',
          iconColor: 'text-green-500',
          icon: CheckCircle
        };
      case 'error':
        return {
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200',
          textColor: 'text-red-800',
          iconColor: 'text-red-500',
          icon: AlertCircle
        };
      case 'warning':
        return {
          bgColor: 'bg-yellow-50',
          borderColor: 'border-yellow-200',
          textColor: 'text-yellow-800',
          iconColor: 'text-yellow-500',
          icon: AlertTriangle
        };
      case 'info':
      default:
        return {
          bgColor: 'bg-blue-50',
          borderColor: 'border-blue-200',
          textColor: 'text-blue-800',
          iconColor: 'text-blue-500',
          icon: Info
        };
    }
  };

  const config = getToastConfig();
  const Icon = config.icon;

  return (
    <div
      className={`
        relative flex items-start gap-3 p-4 rounded-lg border shadow-lg max-w-md w-full
        ${config.bgColor} ${config.borderColor}
        transform transition-all duration-300 ease-in-out
        ${isVisible && !isLeaving ? 'translate-x-0 opacity-100' : 'translate-x-full opacity-0'}
      `}
    >
      {/* 图标 */}
      <div className="flex-shrink-0">
        <Icon size={20} className={config.iconColor} />
      </div>

      {/* 内容 */}
      <div className="flex-1 min-w-0">
        {toast.title && (
          <h4 className={`font-medium ${config.textColor} mb-1`}>
            {toast.title}
          </h4>
        )}
        <p className={`text-sm ${config.textColor} leading-relaxed`}>
          {toast.message}
        </p>
      </div>

      {/* 关闭按钮 */}
      <button
        onClick={handleClose}
        className={`
          flex-shrink-0 p-1 rounded-md transition-colors
          ${config.textColor} hover:bg-black hover:bg-opacity-10
        `}
      >
        <X size={16} />
      </button>

      {/* 进度条（如果有持续时间） */}
      {toast.autoClose && toast.duration && toast.duration > 0 && (
        <div className="absolute bottom-0 left-0 right-0 h-1 bg-black bg-opacity-10 rounded-b-lg overflow-hidden">
          <div
            className={`h-full ${config.iconColor.replace('text-', 'bg-')} transition-all ease-linear`}
            style={{
              width: '0%',
              animation: `toast-progress ${toast.duration}ms linear forwards`
            }}
          />
        </div>
      )}

      <style>
        {`
          @keyframes toast-progress {
            from { width: 100%; }
            to { width: 0%; }
          }
        `}
      </style>
    </div>
  );
};

export default Toast;
```

### 6. 实际使用示例

#### 在登录组件中使用
```typescript
// AuthModal.tsx
import { useToastContext } from '../contexts/ToastContext';

const AuthModal: React.FC<AuthModalProps> = ({ isOpen, onClose }) => {
  const { showSuccess } = useToastContext();

  const handleLogin = async () => {
    try {
      await login(formData.phone, formData.password);
      onClose();
      showSuccess('登录成功！现在可以发布房源了');
    } catch (error) {
      // 错误处理已在AuthContext中完成
    }
  };
};
```

#### 在房源发布组件中使用
```typescript
// PublishPropertySection.tsx
import { useToastContext } from '../contexts/ToastContext';

const PublishPropertySection: React.FC = () => {
  const { showSuccess, showError } = useToastContext();

  const handlePropertySubmit = async (propertyData: any) => {
    try {
      const response = await propertyService.submitProperty(propertyData);
      if (response.success) {
        showSuccess('房源提交成功！我们会在24小时内审核您的房源信息。');
        setIsFormOpen(false);
      } else {
        throw new Error(response.message);
      }
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || error.message || '房源提交失败，请稍后重试';
      showError(errorMessage);
    }
  };
};
```

---

## 总结

### 核心特性回顾
1. **4种类型支持**: success、error、info、warning
2. **自动计时**: 默认3秒，可自定义
3. **进度条显示**: 直观显示剩余时间
4. **响应式设计**: 适配各种屏幕尺寸
5. **全局状态管理**: 使用React Context
6. **TypeScript支持**: 完整的类型定义

### 开发要点
1. **架构清晰**: Hook + Context + Component 分层设计
2. **性能优化**: 使用useCallback避免不必要渲染
3. **用户体验**: 非侵入式设计，自动消失
4. **可维护性**: 代码结构清晰，易于扩展

### 扩展建议
1. **添加音效**: 为不同类型的Toast添加提示音
2. **位置配置**: 支持Toast显示在不同位置
3. **主题定制**: 支持深色模式和自定义主题
4. **动画增强**: 添加更多进入和退出动画效果

### 注意事项
1. 确保ToastProvider包装整个应用
2. ToastContainer必须放在合适的位置
3. 注意z-index层级，确保Toast在最顶层
4. 合理设置Toast显示时长
5. 消息内容要简洁明了，用户友好

通过本教程，您应该能够完全理解和复制QJ-HouseSeek项目的Toast系统。这个系统经过实际项目验证，具有良好的用户体验和开发体验。
