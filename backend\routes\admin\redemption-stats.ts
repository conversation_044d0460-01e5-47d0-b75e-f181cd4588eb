import express, { Request, Response } from 'express';
import jwt from 'jsonwebtoken';
import prisma from '../../lib/prisma';

const router = express.Router();
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

// 扩展Request接口以包含管理员用户信息
interface AdminRequest extends Request {
  adminUser?: {
    userId: string;
  };
}

// JWT 验证中间件 - 管理员权限
const verifyAdminToken = async (req: AdminRequest, res: Response, next: any) => {
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({ 
      success: false,
      message: '未授权访问' 
    });
  }

  const token = authHeader.substring(7);
  try {
    const decoded = jwt.verify(token, JWT_SECRET) as { userId: string };
    
    // 验证用户是否为管理员
    const user = await prisma.user.findUnique({
      where: { id: decoded.userId },
      select: { id: true, role: true }
    });

    if (!user || user.role !== 'ADMIN') {
      return res.status(403).json({ 
        success: false,
        message: '需要管理员权限' 
      });
    }

    req.adminUser = { userId: user.id };
    next();
  } catch (error) {
    return res.status(401).json({ 
      success: false,
      message: '无效的认证令牌' 
    });
  }
};

/**
 * 获取兑换码统计数据
 */
router.get('/', verifyAdminToken, async (req: AdminRequest, res: Response) => {
  try {
    const { days = 30 } = req.query;
    const daysNum = Number(days);
    
    // 计算时间范围
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - daysNum);

    // 1. 基础统计数据
    const [
      totalGenerated,
      totalUsed,
      totalActive,
      totalExpired,
      uniqueUsers,
      recentGenerated
    ] = await Promise.all([
      // 总生成数
      prisma.redemptionCode.count(),
      
      // 总使用数
      prisma.redemptionCode.count({
        where: { isUsed: true }
      }),
      
      // 活跃兑换码数
      prisma.redemptionCode.count({
        where: {
          isUsed: false,
          isActive: true,
          OR: [
            { expiresAt: null },
            { expiresAt: { gt: new Date() } }
          ]
        }
      }),
      
      // 过期兑换码数
      prisma.redemptionCode.count({
        where: {
          expiresAt: { lt: new Date() }
        }
      }),
      
      // 使用兑换码的唯一用户数
      prisma.codeUsage.findMany({
        select: { userId: true },
        distinct: ['userId']
      }).then(users => users.length),
      
      // 最近生成的兑换码数
      prisma.redemptionCode.count({
        where: {
          createdAt: {
            gte: startDate,
            lte: endDate
          }
        }
      })
    ]);

    // 2. 按权益类型统计
    const benefitTypeStats = await prisma.redemptionCode.groupBy({
      by: ['benefitType'],
      _count: {
        id: true
      },
      _sum: {
        benefitValue: true,
        usageCount: true
      }
    });

    // 3. 每日使用趋势（最近30天）
    const dailyUsage = await prisma.codeUsage.findMany({
      where: {
        createdAt: {
          gte: startDate,
          lte: endDate
        }
      },
      select: {
        createdAt: true,
        benefitType: true,
        benefitAmount: true
      }
    });

    // 按日期分组统计
    const dailyStats = dailyUsage.reduce((acc: any, usage) => {
      const date = usage.createdAt.toISOString().split('T')[0];
      if (!acc[date]) {
        acc[date] = {
          date,
          count: 0,
          totalValue: 0,
          byType: {}
        };
      }
      acc[date].count++;
      acc[date].totalValue += usage.benefitAmount;
      
      if (!acc[date].byType[usage.benefitType]) {
        acc[date].byType[usage.benefitType] = 0;
      }
      acc[date].byType[usage.benefitType]++;
      
      return acc;
    }, {});

    // 4. 最近使用记录
    const recentUsage = await prisma.codeUsage.findMany({
      take: 10,
      orderBy: { createdAt: 'desc' },
      include: {
        user: {
          select: {
            email: true,
            name: true
          }
        },
        redemptionCode: {
          select: {
            code: true,
            benefitType: true
          }
        }
      }
    });

    // 5. 使用率计算
    const usageRate = totalGenerated > 0 ? (totalUsed / totalGenerated * 100).toFixed(2) : '0.00';

    return res.json({
      success: true,
      data: {
        overview: {
          totalGenerated,
          totalUsed,
          totalActive,
          totalExpired,
          uniqueUsers,
          recentGenerated,
          usageRate: parseFloat(usageRate)
        },
        benefitTypeStats: benefitTypeStats.map(stat => ({
          type: stat.benefitType,
          count: stat._count.id,
          totalValue: stat._sum.benefitValue || 0,
          totalUsage: stat._sum.usageCount || 0
        })),
        dailyTrend: Object.values(dailyStats).sort((a: any, b: any) => 
          new Date(a.date).getTime() - new Date(b.date).getTime()
        ),
        recentUsage: recentUsage.map(usage => ({
          id: usage.id,
          code: usage.redemptionCode.code,
          benefitType: usage.benefitType,
          benefitAmount: usage.benefitAmount,
          user: {
            email: usage.user.email,
            name: usage.user.name
          },
          usedAt: usage.createdAt
        }))
      }
    });

  } catch (error: any) {
    console.error('获取兑换码统计失败:', error);
    return res.status(500).json({
      success: false,
      message: '获取统计数据失败，请稍后再试'
    });
  }
});

export default router;
