import {
  ASRServiceInterface,
  ASRServiceConfig,
  RecognitionResult,
  ASRError,
  ASRErrorCode
} from '../types/asrTypes';

/**
 * ASR服务基类
 * 提供通用的ASR服务功能和错误处理
 */
export abstract class ASRServiceBase implements ASRServiceInterface {
  protected config: ASRServiceConfig;
  protected isInitialized: boolean = false;
  protected lastRequestTime: number = 0;
  protected requestCount: number = 0;
  protected errorCount: number = 0;

  constructor(config: ASRServiceConfig) {
    this.config = config;
  }

  abstract getName(): string;
  abstract recognize(audioBuffer: Buffer, metadata?: any): Promise<RecognitionResult>;
  protected abstract initializeService(): Promise<void>;
  protected abstract isServiceHealthy(): Promise<boolean>;

  /**
   * 检查服务是否可用
   */
  async isAvailable(): Promise<boolean> {
    if (!this.config.enabled) {
      return false;
    }

    try {
      if (!this.isInitialized) {
        await this.initializeService();
        this.isInitialized = true;
      }

      return await this.isServiceHealthy();
    } catch (error) {
      console.error(`[${this.getName()}] Service availability check failed:`, error);
      return false;
    }
  }

  /**
   * 获取配置
   */
  getConfig(): ASRServiceConfig {
    return { ...this.config };
  }

  /**
   * 更新配置
   */
  updateConfig(config: Partial<ASRServiceConfig>): void {
    this.config = { ...this.config, ...config };
    console.log(`[${this.getName()}] Config updated:`, this.config);
  }

  /**
   * 执行带重试的识别
   */
  protected async executeWithRetry<T>(
    operation: () => Promise<T>,
    context: string = 'operation'
  ): Promise<T> {
    let lastError: Error | null = null;
    
    for (let attempt = 1; attempt <= this.config.retryCount + 1; attempt++) {
      try {
        const startTime = Date.now();
        const result = await Promise.race([
          operation(),
          this.createTimeoutPromise<T>(this.config.timeout)
        ]);
        
        const processingTime = Date.now() - startTime;
        this.updateRequestStats(true, processingTime);
        
        return result;
      } catch (error) {
        lastError = error as Error;
        this.updateRequestStats(false);
        
        console.warn(`[${this.getName()}] ${context} attempt ${attempt} failed:`, error);
        
        if (attempt <= this.config.retryCount) {
          const delay = this.calculateRetryDelay(attempt);
          console.log(`[${this.getName()}] Retrying in ${delay}ms...`);
          await this.sleep(delay);
        }
      }
    }
    
    throw new ASRError(
      `${context} failed after ${this.config.retryCount + 1} attempts: ${lastError?.message}`,
      ASRErrorCode.PROCESSING_ERROR,
      this.getName(),
      false
    );
  }

  /**
   * 创建超时Promise
   */
  private createTimeoutPromise<T>(timeoutMs: number): Promise<T> {
    return new Promise((_, reject) => {
      setTimeout(() => {
        reject(new ASRError(
          `Operation timed out after ${timeoutMs}ms`,
          ASRErrorCode.TIMEOUT,
          this.getName(),
          true
        ));
      }, timeoutMs);
    });
  }

  /**
   * 计算重试延迟（指数退避）
   */
  private calculateRetryDelay(attempt: number): number {
    const baseDelay = 1000; // 1秒
    const maxDelay = 10000; // 10秒
    const delay = Math.min(baseDelay * Math.pow(2, attempt - 1), maxDelay);
    
    // 添加随机抖动
    const jitter = Math.random() * 0.1 * delay;
    return Math.floor(delay + jitter);
  }

  /**
   * 更新请求统计
   */
  private updateRequestStats(success: boolean, processingTime?: number): void {
    this.requestCount++;
    this.lastRequestTime = Date.now();
    
    if (!success) {
      this.errorCount++;
    }
    
    if (processingTime) {
      console.log(`[${this.getName()}] Request completed in ${processingTime}ms`);
    }
  }

  /**
   * 睡眠函数
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 验证音频数据
   */
  protected validateAudioData(audioBuffer: Buffer): void {
    if (!audioBuffer || audioBuffer.length === 0) {
      throw new ASRError(
        'Audio buffer is empty or invalid',
        ASRErrorCode.INVALID_AUDIO,
        this.getName(),
        false
      );
    }

    // 检查音频大小限制（例如：最大10MB）
    const maxSize = 10 * 1024 * 1024;
    if (audioBuffer.length > maxSize) {
      throw new ASRError(
        `Audio buffer too large: ${audioBuffer.length} bytes (max: ${maxSize})`,
        ASRErrorCode.INVALID_AUDIO,
        this.getName(),
        false
      );
    }
  }

  /**
   * 创建基础识别结果
   */
  protected createRecognitionResult(
    text: string,
    confidence: number,
    segmentId: string,
    isPartial: boolean = false,
    processingTime?: number,
    metadata?: any
  ): RecognitionResult {
    return {
      text: text.trim(),
      confidence: Math.max(0, Math.min(1, confidence)),
      timestamp: Date.now(),
      service: this.getName(),
      isPartial,
      segmentId,
      processingTime,
      metadata
    };
  }

  /**
   * 处理服务错误
   */
  protected handleServiceError(error: any, context: string): ASRError {
    if (error instanceof ASRError) {
      return error;
    }

    let errorCode = ASRErrorCode.UNKNOWN_ERROR;
    let retryable = false;

    // 根据错误类型确定错误代码
    if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
      errorCode = ASRErrorCode.NETWORK_ERROR;
      retryable = true;
    } else if (error.code === 'ETIMEDOUT') {
      errorCode = ASRErrorCode.TIMEOUT;
      retryable = true;
    } else if (error.status === 401 || error.status === 403) {
      errorCode = ASRErrorCode.AUTHENTICATION_FAILED;
      retryable = false;
    } else if (error.status === 429) {
      errorCode = ASRErrorCode.QUOTA_EXCEEDED;
      retryable = true;
    } else if (error.status >= 500) {
      errorCode = ASRErrorCode.SERVICE_UNAVAILABLE;
      retryable = true;
    }

    return new ASRError(
      `${context}: ${error.message || 'Unknown error'}`,
      errorCode,
      this.getName(),
      retryable
    );
  }

  /**
   * 获取服务统计信息
   */
  getStats(): {
    requestCount: number;
    errorCount: number;
    errorRate: number;
    lastRequestTime: number;
    isAvailable: boolean;
  } {
    return {
      requestCount: this.requestCount,
      errorCount: this.errorCount,
      errorRate: this.requestCount > 0 ? this.errorCount / this.requestCount : 0,
      lastRequestTime: this.lastRequestTime,
      isAvailable: this.config.enabled && this.isInitialized
    };
  }

  /**
   * 重置统计信息
   */
  resetStats(): void {
    this.requestCount = 0;
    this.errorCount = 0;
    this.lastRequestTime = 0;
    console.log(`[${this.getName()}] Stats reset`);
  }
}

/**
 * 模拟ASR服务（用于测试）
 */
export class MockASRService extends ASRServiceBase {
  private responses: string[] = [
    '这是一个测试识别结果',
    '语音识别正在正常工作',
    '系统运行状态良好',
    '测试音频处理完成'
  ];

  constructor(name: string = 'mock', priority: number = 0) {
    super({
      name,
      priority,
      timeout: 5000,
      retryCount: 2,
      enabled: true,
      weight: 1.0
    });
  }

  getName(): string {
    return this.config.name;
  }

  protected async initializeService(): Promise<void> {
    // 模拟初始化延迟
    await this.sleep(100);
    console.log(`[${this.getName()}] Mock ASR service initialized`);
  }

  protected async isServiceHealthy(): Promise<boolean> {
    return true;
  }

  async recognize(audioBuffer: Buffer, metadata?: any): Promise<RecognitionResult> {
    this.validateAudioData(audioBuffer);

    return this.executeWithRetry(async () => {
      // 模拟处理延迟
      await this.sleep(Math.random() * 1000 + 500);

      // 随机选择响应
      const text = this.responses[Math.floor(Math.random() * this.responses.length)];
      const confidence = 0.8 + Math.random() * 0.2; // 0.8-1.0

      return this.createRecognitionResult(
        text,
        confidence,
        metadata?.segmentId || 'unknown',
        false,
        Date.now() - (metadata?.startTime || Date.now())
      );
    }, 'recognition');
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

/**
 * 真实ASR服务包装器
 * 将现有的ASR函数包装成服务类
 */
export class RealASRService extends ASRServiceBase {
  private asrFunction: (audioBuffer: Buffer) => Promise<string | null>;

  constructor(
    name: string,
    priority: number,
    asrFunction: (audioBuffer: Buffer) => Promise<string | null>,
    config: Partial<ASRServiceConfig> = {}
  ) {
    super({
      name,
      priority,
      timeout: 10000,
      retryCount: 2,
      enabled: true,
      weight: 1.0,
      ...config
    });
    this.asrFunction = asrFunction;
  }

  getName(): string {
    return this.config.name;
  }

  protected async initializeService(): Promise<void> {
    // 真实服务不需要特殊初始化
    console.log(`[${this.getName()}] Real ASR service initialized`);
  }

  protected async isServiceHealthy(): Promise<boolean> {
    // 简单的健康检查 - 可以根据需要扩展
    return true;
  }

  async recognize(audioBuffer: Buffer, metadata?: any): Promise<RecognitionResult> {
    this.validateAudioData(audioBuffer);

    return this.executeWithRetry(async () => {
      const startTime = Date.now();

      // 调用真实的ASR函数
      const text = await this.asrFunction(audioBuffer);

      if (!text || text.trim() === '') {
        throw new Error('ASR service returned empty result');
      }

      // 计算置信度（基于文本长度和服务权重）
      const confidence = Math.min(0.95, 0.7 + (text.length / 100) * 0.2 + (this.config.weight - 1) * 0.1);

      return this.createRecognitionResult(
        text.trim(),
        confidence,
        metadata?.segmentId || 'unknown',
        false,
        Date.now() - startTime
      );
    }, 'recognition');
  }
}
