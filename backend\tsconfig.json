{"compilerOptions": {"target": "es2020", "module": "CommonJS", "moduleResolution": "node", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "skipLibCheck": true, "outDir": "./dist", "resolveJsonModule": true, "baseUrl": ".", "paths": {"@/*": ["*"], "@new-mianshijun/common": ["../packages/common/src"], "@new-mianshijun/common/*": ["../packages/common/src/*"]}}, "ts-node": {"esm": false}, "include": ["**/*.ts"], "exclude": ["node_modules"]}