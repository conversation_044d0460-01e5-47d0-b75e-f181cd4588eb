# 面试君 (MianshiJun) 技术架构设计

**版本**: 1.2 (MVP Aligned - React Frontend)

**最后更新**: 2025年5月10日

## 1. 系统架构概览

面试君旨在成为一个AI实时辅助面试系统。其架构采用现代化的Web应用模型，基于客户端-服务器模式，并充分利用云服务实现高可用性、可扩展性和低延迟的实时交互。系统整体架构分为前端层（用户界面与实时交互处理）、API服务层（业务逻辑与外部服务编排）、以及数据存储层（持久化数据管理）。

### 架构图

代码段

代码段

```
graph TD
    subgraph "用户端 (Client-Side)"
        A[Web浏览器] --> B{前端应用 (React, HTML/CSS/JS)};
        B --> C[屏幕/音频捕获 (WebRTC)];
        B --> D_Client[音频流处理与发送模块];
        B --> E[WebSocket通信模块];
        E --> F[UI实时更新];
    end

    subgraph "服务端 (Server-Side on Vercel)"
        G[API网关 (Vercel Functions)];
        E --> G;
        D_Client --音频流--> G; 
        G --> H{业务逻辑层 (Node.js, Express.js)};
        H --> I[用户认证服务];
        H --> J[面试会话管理];
        H --> K[语音到文本服务接口 (ASR)];
        H --> L[大语言模型 (LLM) 服务接口];
        H --> M[面试评估与反馈 (P1+) ];
    end

    subgraph "数据存储 (Neon PostgreSQL)"
        N[Neon数据库];
        I --> N;
        J --> N;
        M --> N;
        O[面试记录表 (Transcripts, Suggestions)];
        P[用户表 (含余额)];
        Q_Payment[支付订单表 (P0后期/P1)];
        N --> O;
        N --> P;
        N --> Q_Payment;
    end

    subgraph "第三方服务 (Third-Party Services)"
        S[DeepSeek/OpenAI API];
        T[外部语音识别服务 (百度ASR等)];
        L --> S;
        K --> T;
    end

    style B fill:#f9f,stroke:#333,stroke-width:2px
    style H fill:#ccf,stroke:#333,stroke-width:2px
    style N fill:#cfc,stroke:#333,stroke-width:2px
    style S fill:#ff9,stroke:#333,stroke-width:2px
    style T fill:#ff9,stroke:#333,stroke-width:2px
```

**核心流程:**

1. 用户通过Web浏览器访问面试君前端应用 (React)。
2. 用户授权后，前端通过WebRTC捕获屏幕内容（面试官画面）和麦克风音频（面试官语音）。
3. 音频数据被实时处理：前端将捕获的音频流进行分片，并通过WebSocket发送到后端。
4. 后端业务逻辑层接收到音频流，调用外部语音识别服务 (如百度ASR) 将语音转换为文字（面试官提问）。
5. 转换后的文字，结合上下文信息，调用大语言模型服务 (DeepSeek/OpenAI API) 生成回答建议。
6. LLM返回的建议通过WebSocket实时推送回前端。
7. 前端UI实时更新，向用户展示AI辅助信息。
8. 面试结束后，系统可提供基于会话记录的评估和反馈 (P1阶段及以后)。

## 2. 前端技术栈 (客户端层)

### 核心技术

- **HTML5**: 构建页面结构，确保语义化。

- CSS3

  : 负责页面样式与布局。

  - **Tailwind CSS v3.x**: 实用类优先的CSS框架，加速开发并保持一致性。

- **JavaScript (ES6+) / TypeScript**: 实现所有客户端交互逻辑。TypeScript为项目增加类型安全。

- React (v18+)

  : 核心前端框架。

  - **Vite**: 构建工具。

  - **React Router v6+**: 客户端路由。

  - 状态管理 (MVP 推荐)

    :

    - **初期与简单全局状态**: 优先使用 React 内置的 **Context API** 结合 `useState` (用于简单状态) 和 `useReducer` (用于较复杂的状态逻辑，如用户认证状态)。
    - **复杂或高性能需求场景 (按需引入)**: 对于如“实时面试辅助页”中频繁更新、交互复杂的全局状态，或当 Context API 可能引发性能顾虑时，可考虑引入轻量级状态管理库 **Zustand**。

### 关键前端功能模块

#### a. 屏幕与音频捕获模块

- 使用 `navigator.mediaDevices.getDisplayMedia()` 获取屏幕共享权限和视频流。
- 使用 `navigator.mediaDevices.getUserMedia()` 获取麦克风音频流。
- 处理用户授权和错误情况。
- **React实现**: 通常在React组件的 `useEffect` Hook (例如，模拟 `componentDidMount`)或特定用户操作触发的函数中调用这些API。捕获到的 `MediaStream` 对象可以存储在组件的 `state` (使用 `useState`) 中，或通过Context API / Zustand store管理。

JavaScript

```
// 示例逻辑 (在React自定义Hook或组件方法中):
// async function startMediaCapture(onScreenStream, onAudioStream, onError) {
//   try {
//     const screenStream = await navigator.mediaDevices.getDisplayMedia({
//       video: { cursor: "always", displaySurface: "monitor" },
//       audio: { echoCancellation: true, noiseSuppression: true, sampleRate: 44100 }
//     });
//     onScreenStream(screenStream); // 传递屏幕流给React组件或Store

//     let interviewerAudioTrack = screenStream.getAudioTracks()[0];
//     if (interviewerAudioTrack) {
//         const interviewerAudioStream = new MediaStream([interviewerAudioTrack]);
//         onAudioStream(interviewerAudioStream); // 传递面试官音频流
//     } else {
//         // 尝试使用用户麦克风作为备选，或提示检查音频设置
//         const micStream = await navigator.mediaDevices.getUserMedia({
//           audio: { echoCancellation: true, noiseSuppression: true, sampleRate: 44100 },
//           video: false
//         });
//         onAudioStream(micStream); // 明确这是备选方案
//         console.warn("屏幕共享流中未检测到音频轨道，尝试使用麦克风。");
//     }
//   } catch (err) {
//     console.error("媒体捕获错误:", err);
//     onError(err);
//   }
// }
```

#### b. 音频流处理与发送模块 (Client-Side)

- **主要职责**: 从捕获的音频流中提取音频数据，进行分片，并通过WebSocket发送至后端。
- **技术**: Web Audio API (`AudioContext`, `MediaStreamAudioSourceNode`, `ScriptProcessorNode` 或 `AudioWorkletNode`) 进行音频处理和分片。
- 将音频片段 (如PCM裸数据或压缩格式如Opus) 通过WebSocket发送。
- **React实现**: 可以封装成一个自定义Hook (如 `useAudioProcessor.ts`)，在实时面试页面组件中使用。该函数负责启动和停止音频处理，并将处理好的数据块通过WebSocket服务发送。
- **降级方案**: 浏览器内置的 `SpeechRecognition` API (如 `window.SpeechRecognition`) 可以在网络极差或后端ASR服务临时不可用时，作为一种非常有限的、临时的本地识别方案，但MVP主要依赖后端ASR。

JavaScript

```
// 示例逻辑 (在React自定义Hook或组件方法中):
// class AudioStreamProcessor {
//   constructor(mediaStream, webSocketService, sampleRate = 16000) {
//     this.mediaStream = mediaStream;
//     this.webSocketService = webSocketService;
//     this.audioContext = new (window.AudioContext || window.webkitAudioContext)({ sampleRate });
//     this.mediaStreamSource = this.audioContext.createMediaStreamSource(this.mediaStream);
//     this.processor = this.audioContext.createScriptProcessor(4096, 1, 1); // bufferSize, inputChannels, outputChannels

//     this.processor.onaudioprocess = (e) => {
//       const inputData = e.inputBuffer.getChannelData(0);
//       // 可进行PCM转码、压缩等操作
//       // 然后通过WebSocket发送: this.webSocketService.sendAudioChunk(inputData.buffer);
//     };
//   }
//   start() { this.mediaStreamSource.connect(this.processor); this.processor.connect(this.audioContext.destination); }
//   stop() { this.mediaStreamSource.disconnect(); this.processor.disconnect(); this.audioContext.close(); }
// }
```

#### c. WebSocket通信模块

- 建立与后端的WebSocket连接 (例如 `wss://<your-app-domain>.vercel.app/ws/interview/:sessionId`)。
- 发送用户操作、处理后的音频数据块。
- 接收后端推送的ASR转录文本、AI建议、状态更新等。
- 实现重连机制和心跳包。
- **React实现**: 可以封装成一个全局服务或使用类似 `react-use-websocket` 的Hook。在Context API / Zustand store中管理连接状态和接收到的数据。

JavaScript

```
// 示例: WebSocket服务 (概念性, React中会用 react-use-websocket 或自定义封装)
// class InterviewWebSocketService {
//   constructor(sessionId, onMessageCallback) {
//     this.socket = null;
//     this.url = `wss://<your-app-domain>.vercel.app/ws/interview/${sessionId}`;
//     this.onMessageCallback = onMessageCallback;
//     // ...连接、发送、接收、关闭、重连逻辑...
//   }
//   sendAudioChunk(audioBuffer) { /* ... */ }
//   sendTranscriptAck(transcriptId) { /* ... */ }
// }
```

#### d. 实时辅助信息显示模块

- 动态、低延迟地在UI上展示面试官问题摘要和AI生成的回答建议。
- 设计简洁、不干扰用户视线的UI元素 (React组件)。
- **React实现**: 使用React的State (`useState`, `useReducer`) 和 Props 更新UI。列表渲染使用数组的 `.map()` 方法。动画效果可使用CSS Transitions/Animations或 `framer-motion` 等库。

## 3. 后端技术栈 (API服务层 & 业务逻辑层)

### 核心技术

- **Node.js (LTS版本)**: JavaScript运行时环境。
- **Express.js v4.x**: 用于构建Serverless Functions的API路由和请求处理。也可以直接利用Vercel Functions的原生请求处理函数格式。
- **Vercel Serverless Functions**: 部署后端逻辑，自动扩展。
- **TypeScript (推荐)**: 增加代码健壮性。

### 关键后端功能模块

#### a. 用户认证与管理

- JWT (JSON Web Tokens) 进行无状态认证。
- 用户注册、登录、个人信息管理接口。
- 密码使用强哈希算法 (e.g., bcrypt) 存储。

#### b. 面试会话管理

- 创建新的面试会话，生成唯一会话ID。
- 管理会话状态 (例如：`pending`, `active`, `processing_audio`, `generating_suggestion`, `completed`)。
- 记录会话元数据（用户ID、开始/结束时间等）。

#### c. WebSocket服务处理 (Vercel Edge Functions or Serverless Functions with WebSocket support)

- 管理WebSocket连接生命周期。
- 接收客户端发送的音频数据块。
- 将处理后的ASR文本和AI建议广播到对应的客户端。

#### d. 语音到文本服务接口 (ASR)

- 接收来自客户端的音频流数据块。
- 与第三方语音识别服务 (如**百度ASR API (首选)**, Google Speech-to-Text等) 集成。
- 处理音频格式转换 (如果需要)。
- 将识别结果传递给LLM服务接口或直接通过WebSocket推送给客户端（如果LLM在客户端处理部分逻辑，但不推荐）。

#### e. 大语言模型 (LLM) 服务接口

- 接收处理后的面试官提问文本。
- 构建合适的Prompt，包含上下文信息（如面试类型、先前问题、用户简历关键点等）。
- 调用**DeepSeek API (首选)**或OpenAI等大模型的API。
- 处理LLM返回的回答建议，进行必要的格式化或后处理。

#### f. 面试评估与反馈模块 (P1阶段及以后)

- 面试结束后，基于会话记录（问题、AI建议、用户可能的输入）生成总结性评估。
- 存储评估结果，并提供给用户查看。

## 4. 数据存储层 (Neon PostgreSQL)

### 数据库选择

- **Neon**: Serverless PostgreSQL，提供良好的扩展性、自动备份和Vercel集成。

### ERD图 (Entity Relationship Diagram) - MVP Focus

代码段

代码段

```
erDiagram
    USERS ||--o{ INTERVIEW_SESSIONS : "has"
    INTERVIEW_SESSIONS ||--o{ INTERVIEW_TRANSCRIPTS : "contains"
    INTERVIEW_SESSIONS ||--o{ AI_SUGGESTIONS : "generates"
    USERS ||--o{ PAYMENT_ORDERS : "places" -- (P0 后期/P1)

    USERS {
        UUID id PK "用户ID (主键)"
        String email UNIQUE "邮箱"
        String password_hash "密码哈希"
        String name "姓名"
        Integer mock_interview_credits "模拟面试次数 (MVP)"
        Integer formal_interview_credits "正式面试次数 (MVP)"
        Integer mianshijin_balance "面巾余额 (MVP)"
        Timestamp created_at "创建时间"
        Timestamp last_login_at "最后登录时间"
    }

    INTERVIEW_SESSIONS {
        UUID id PK "会话ID (主键)"
        UUID user_id FK "用户ID (外键)"
        String title_job_info "面试岗位/角色信息 (MVP)"
        String status "会话状态 (MVP)"
        Timestamp created_at "创建时间 (MVP)"
        Timestamp started_at "开始时间 (MVP)"
        Timestamp ended_at "结束时间 (MVP)"
        String type "面试类型 (P1+)"
    }

    INTERVIEW_TRANSCRIPTS {
        UUID id PK "记录ID (主键)"
        UUID session_id FK "会话ID (外键) (MVP)"
        String speaker "'interviewer' or 'assistant_context' (MVP)"
        TEXT content "识别的文本内容 (MVP)"
        Timestamp timestamp "文本时间戳 (MVP)"
    }

    AI_SUGGESTIONS {
        UUID id PK "建议ID (主键)"
        UUID session_id FK "会话ID (外键) (MVP)"
        TEXT original_question_summary "原始问题摘要 (MVP)"
        TEXT suggested_response "AI生成的建议回复 (MVP)"
        Timestamp timestamp "生成时间戳 (MVP)"
        UUID transcript_id FK "关联的文本记录ID (P1+)"
        JSON metadata "其他元数据 (P1+)"
    }

    PAYMENT_ORDERS { -- (P0 后期 / P1 阶段)
        UUID id PK "订单ID (主键)"
        UUID user_id FK "用户ID (外键)"
        String package_name "套餐名称"
        DECIMAL amount "订单金额"
        String currency "货币单位"
        String status "订单状态"
        String payment_gateway "支付网关"
        String payment_gateway_transaction_id UNIQUE "网关交易号"
        Timestamp created_at "创建时间"
        Timestamp paid_at "支付时间"
    }

    %% INTERVIEW_EVALUATIONS 表 (P1阶段或以后实现)
    %% INTERVIEW_SESSIONS ||--o{ INTERVIEW_EVALUATIONS : "results_in"
    %% INTERVIEW_EVALUATIONS {
    %%     UUID id PK "评估ID (主键)"
    %%     UUID session_id FK UNIQUE "会话ID (外键, 唯一)"
    %%     INTEGER rating "综合评分 (1-5)"
    %%     TEXT strengths "优点反馈"
    %%     TEXT areas_for_improvement "待改进点"
    %%     TEXT overall_comment "综合评价"
    %%     Timestamp created_at "创建时间"
    %% }
```

### 表结构详细设计 (SQL DDL for Neon PostgreSQL)

#### Users 表 (MVP)

SQL

```
CREATE EXTENSION IF NOT EXISTS "uuid-ossp"; -- For uuid_generate_v4()

CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  email VARCHAR(255) NOT NULL UNIQUE,
  password_hash VARCHAR(255) NOT NULL,
  name VARCHAR(100),
  mock_interview_credits INTEGER DEFAULT 0, -- MVP: 模拟面试次数
  formal_interview_credits INTEGER DEFAULT 0, -- MVP: 正式面试次数
  mianshijin_balance INTEGER DEFAULT 0, -- MVP: 面巾余额
  created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
  last_login_at TIMESTAMPTZ
);
CREATE INDEX idx_users_email ON users(email);
```

#### Interview_Sessions 表 (MVP)

SQL

```
CREATE TABLE interview_sessions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  title_job_info VARCHAR(255), -- MVP: 面试岗位/角色信息
  status VARCHAR(30) NOT NULL DEFAULT 'pending', -- MVP: e.g., pending, active, completed, error
  created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP, -- MVP
  started_at TIMESTAMPTZ, -- MVP
  ended_at TIMESTAMPTZ, -- MVP
  type VARCHAR(50) -- (P1+) e.g., 'technical', 'behavioral', 'hr_round'
);
CREATE INDEX idx_interview_sessions_user_id ON interview_sessions(user_id);
CREATE INDEX idx_interview_sessions_status ON interview_sessions(status);
```

#### Interview_Transcripts 表 (MVP)

SQL

```
CREATE TABLE interview_transcripts (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  session_id UUID NOT NULL REFERENCES interview_sessions(id) ON DELETE CASCADE, -- MVP
  speaker VARCHAR(20) NOT NULL CHECK (speaker IN ('interviewer', 'assistant_context')), -- MVP
  content TEXT NOT NULL, -- MVP
  timestamp TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP -- MVP
);
CREATE INDEX idx_interview_transcripts_session_id ON interview_transcripts(session_id);
```

#### AI_Suggestions 表 (MVP)

SQL

```
CREATE TABLE ai_suggestions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  session_id UUID NOT NULL REFERENCES interview_sessions(id) ON DELETE CASCADE, -- MVP
  original_question_summary TEXT, -- MVP: Summary of the question the suggestion is for
  suggested_response TEXT NOT NULL, -- MVP
  timestamp TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP, -- MVP
  transcript_id UUID REFERENCES interview_transcripts(id) ON DELETE SET NULL, -- (P1+) Link to specific question
  metadata JSONB -- (P1+) Store key points, confidence, etc.
);
CREATE INDEX idx_ai_suggestions_session_id ON ai_suggestions(session_id);
```

#### Payment_Orders 表 (P0 后期 / P1 阶段)

SQL

```
-- (P0 后期 / P1 阶段实现)
CREATE TABLE payment_orders (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  package_name VARCHAR(100) NOT NULL, -- 套餐名称或面巾包名称
  amount DECIMAL(10, 2) NOT NULL, -- 订单金额
  currency VARCHAR(10) NOT NULL DEFAULT 'CNY',
  status VARCHAR(30) NOT NULL DEFAULT 'pending', -- e.g., pending, completed, failed, refunded
  payment_gateway VARCHAR(50), -- e.g., alipay
  payment_gateway_transaction_id VARCHAR(255) UNIQUE, -- 支付网关交易号
  created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
  paid_at TIMESTAMPTZ
);
CREATE INDEX idx_payment_orders_user_id ON payment_orders(user_id);
CREATE INDEX idx_payment_orders_status ON payment_orders(status);
```

#### Interview_Evaluations 表 (P1阶段或以后实现)

SQL

```
-- (P1阶段或以后实现)
-- CREATE TABLE interview_evaluations (
--   id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
--   session_id UUID NOT NULL UNIQUE REFERENCES interview_sessions(id) ON DELETE CASCADE,
--   rating SMALLINT CHECK (rating >= 1 AND rating <= 5),
--   strengths TEXT,
--   areas_for_improvement TEXT,
--   overall_comment TEXT,
--   created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
-- );
```

## 5. API 接口设计

### a. 认证 API (RESTful)

#### 用户注册

- **URL**: `/api/auth/register`
- **方法**: `POST`
- **请求体**: `{ "email": "<EMAIL>", "password": "securePassword", "name": "用户名" }`
- **响应 (成功)**: `201 CREATED` `{ "success": true, "data": { "id": "uuid", "email": "...", "name": "..." }, "token": "jwt-token" }`
- **响应 (失败)**: `400 BAD REQUEST` or `409 CONFLICT`

#### 用户登录

- **URL**: `/api/auth/login`
- **方法**: `POST`
- **请求体**: `{ "email": "<EMAIL>", "password": "securePassword" }`
- **响应 (成功)**: `200 OK` `{ "success": true, "data": { "id": "uuid", ... }, "token": "jwt-token" }`
- **响应 (失败)**: `401 UNAUTHORIZED`

### b. 面试服务 API (RESTful)

#### 创建/开始面试会话

- **URL**: `/api/interviews`
- **方法**: `POST`
- **认证**: 需要JWT
- **请求体**: `{ "title_job_info": "前端开发面试" }` (MVP)
- **响应 (成功)**: `201 CREATED` `{ "success": true, "data": { "id": "session_uuid", "status": "pending", ... } }`

#### 获取面试会话详情 (用于回顾页面)

- **URL**: `/api/interviews/:sessionId/review`
- **方法**: `GET`
- **认证**: 需要JWT
- **响应 (成功)**: `200 OK` `{ "success": true, "data": { interview_session_details, transcripts: [], suggestions: [] } }`

#### 结束面试会话

- **URL**: `/api/interviews/:sessionId/end`
- **方法**: `POST`
- **认证**: 需要JWT
- **响应 (成功)**: `200 OK` `{ "success": true, "data": { "status": "completed", ... } }`

#### 获取用户余额/面试次数 (MVP)

- **URL**: `/api/user/balance`
- **方法**: `GET`
- **认证**: 需要JWT
- **响应 (成功)**: `200 OK` `{ "success": true, "data": { "mock_interview_credits": 2, "formal_interview_credits": 1, "mianshijin_balance": 100 } }`

#### (P0 后期 / P1) 创建支付订单

- **URL**: `/api/payments/create-order`
- **方法**: `POST`
- **认证**: 需要JWT
- **请求体**: `{ "packageName": "基础套餐" }` or `{ "mianshijinPackage": "100面巾" }`
- **响应 (成功)**: `201 CREATED` `{ "success": true, "data": { "orderId": "uuid", "paymentGatewayInfo": { ... } } }`

#### (P0 后期 / P1) 支付回调通知

- **URL**: `/api/payments/alipay-notify` (示例，具体由支付网关定)
- **方法**: `POST` (通常)
- **处理**: 验证通知合法性，更新订单状态和用户余额。

### c. WebSocket 通信设计

- **连接URL**: `wss://<your-app-domain>.vercel.app/ws/interview/:sessionId` (Session ID in path, JWT for auth possibly via query param on initial handshake or within first message)

#### 消息格式 (JSON)

JSON

```
{
  "type": "MESSAGE_TYPE_STRING",
  "payload": {}
}
```

#### 客户端 -> 服务端 消息类型:

- ```
  CLIENT_READY
  ```

  :

  - Payload: `{ "token": "jwt_token_if_not_in_query" }`

- ```
  AUDIO_CHUNK
  ```

  :

  - Payload: `{ "audio_data": "base64_encoded_audio_or_binary", "sequence_id": 123, "is_last_chunk": false }`

- ```
  USER_CONTEXT_UPDATE
  ```

  :

  - Payload: `{ "context_text": "用户提供的上下文", "timestamp": "ISO_string" }`

- ```
  HEARTBEAT
  ```

  :

  - Payload: `{ "timestamp": "ISO_string" }`

#### 服务端 -> 客户端 消息类型:

- ```
  CONNECTION_ACK
  ```

  :

  - Payload: `{ "status": "connected", "sessionId": "uuid" }`

- ```
  INTERVIEWER_TRANSCRIPT_PARTIAL
  ```

  : (可选，用于更实时的反馈)

  - Payload: `{ "text": "部分识别文本", "is_final": false, "transcript_id": "temp_id" }`

- ```
  INTERVIEWER_TRANSCRIPT_FINAL
  ```

  :

  - Payload: `{ "text": "最终识别文本", "is_final": true, "transcript_id": "final_id", "timestamp": "ISO_string" }`

- ```
  AI_SUGGESTION
  ```

  :

  - Payload: `{ "original_question_summary": "问题摘要", "suggested_response": "AI建议", "key_points": ["点1", "点2"], "timestamp": "ISO_string" }`

- ```
  SESSION_STATUS_UPDATE
  ```

  :

  - Payload: `{ "status": "processing_audio | generating_suggestion | interviewer_speaking | idle", "message": "可选的状态消息" }`

- ```
  ERROR_MESSAGE
  ```

  :

  - Payload: `{ "code": "error_code", "message": "错误描述" }`

- ```
  HEARTBEAT_ACK
  ```

  :

  - Payload: `{ "timestamp": "ISO_string" }`

## 6. 第三方服务集成

- 大语言模型 (LLM):
  - **DeepSeek API (首选)** 或 OpenAI API (GPT-3.5/4).
  - 通过HTTPS安全调用其REST API。
  - API密钥管理使用Vercel环境变量。
- 语音识别服务 (ASR):
  - **百度ASR API (首选)**, Google Cloud Speech-to-Text, Azure Speech Service等。
  - 集成方式通常为SDK或REST API，支持流式识别。

## 7. 部署架构

- **前端 (React)**: 部署为静态站点于 Vercel，利用其全球CDN。
- 后端API与WebSocket服务 (Node.js + Express.js):
  - REST APIs: Vercel Serverless Functions。
  - WebSocket: Vercel Edge Functions (更适合低延迟实时通信) 或 Serverless Functions with WebSocket support (需确认Vercel对Express + ws库在Serverless环境的良好支持，或使用原生Vercel WebSocket处理方式)。
- **数据库**: Neon Serverless PostgreSQL，配置连接池以供Serverless函数高效访问。
- CI/CD:
  - 代码托管在GitHub。
  - Vercel与Git仓库集成，实现自动构建和部署 (Push-to-Deploy)。
  - 自动化测试 (单元、集成) 在部署流程中执行。

## 8. 安全措施

- **用户认证**: JWT，HTTPS强制，密码哈希 (bcrypt)。
- **数据传输**: 全程HTTPS/WSS加密。
- API安全:
  - 输入验证和清理 (使用如 `express-validator` 或 `Joi`/`Zod` for Express)。
  - CORS策略配置。
  - 速率限制 (Rate Limiting) 防止滥用 (可使用如 `express-rate-limit`)。
  - API密钥等敏感信息通过Vercel环境变量管理。
- 数据存储:
  - Neon数据库本身提供的安全特性。
  - 数据库访问凭证安全管理。
  - 定期备份 (Neon自动处理)。
- 隐私:
  - 明确告知用户数据如何被使用和存储。
  - 遵守相关数据隐私法规 (如GDPR, PIPL - 如果适用)。
  - 面试音视频数据、转录文本、AI建议等敏感信息，考虑加密存储或定期清理策略。

## 9. 性能与可扩展性

### 性能优化

- 前端 (React):
  - 代码分割 (路由懒加载，如使用 `React.lazy` 和 `Suspense`) 和组件懒加载减少初始加载时间。
  - 静态资源压缩和优化 (Vite内置处理)。
  - CDN分发静态资源 (Vercel自动处理)。
  - 合理使用 `React.memo`, `useMemo`, `useCallback` 缓存，避免不必要的组件重渲染。
  - WebSocket消息 payload 最小化。
- 后端 (Node.js/Express.js on Vercel):
  - Serverless函数冷启动优化。
  - 高效的数据库查询 (使用Prisma的查询优化，确保索引)。
  - 异步操作充分利用Node.js的非阻塞I/O。
- 低延迟:
  - WebSocket优先使用Edge Functions。
  - 选择靠近用户的LLM API节点 (如果可选)。
  - 优化语音识别和LLM调用的响应时间。

### 可扩展性

- **Vercel Serverless/Edge Functions**: 根据请求自动横向扩展。
- **Neon PostgreSQL**: Serverless架构，按需扩展计算和存储资源。
- **第三方服务**: 通常具有高可用性和可扩展性。
- **应用设计**: 无状态API设计，便于扩展。模块化设计，方便未来功能增加或服务替换。

## 10. 监控与日志

- **Vercel Analytics**: 监控前端性能和用户行为。
- **Vercel Logs**: 查看Serverless/Edge函数日志，用于调试和错误追踪。
- **外部日志服务 (可选)**: 集成如Sentry (错误监控), Logtail/Datadog (集中式日志管理)。
- **API调用监控**: 记录对第三方API (LLM, ASR) 的调用次数、延迟、错误率。
- **自定义业务指标**: 监控关键业务流程。

------

我已将 `technical_architecture.md` 文档中的前端技术栈部分从 Vue.js 更新为 React，具体修改包括：

- 文档版本号更新。

- **系统架构概览**中的图表和核心流程描述中的前端应用更新为 React。

- 前端技术栈

  核心技术更新：

  - Vue.js 3 替换为 React (v18+)。
  - Pinia 替换为 Context API / `useReducer` / Zustand (按MVP原则推荐)。
  - Vue Router 替换为 React Router v6+。

- 关键前端功能模块

  下的实现说明和代码注释更新为React风格：

  - `onMounted` 替换为 `useEffect`。
  - Vue Composable 概念替换为 React 自定义Hook。
  - Pinia store 替换为 Context API / Zustand store。
  - `VueUse` 的 `useWebSocket` 替换为 `react-use-websocket` 或类似概念。
  - Vue的响应式系统 (`ref`, `computed`)、列表渲染 (`v-for`) 和动画 (`<Transition>`) 替换为React的对应实现 (`useState`, `.map()`, CSS Transitions/`framer-motion`)。

- **部署架构**中前端描述更新为 React。

- **性能与可扩展性**中前端性能优化点更新为React适用项 (如 `React.memo`, `useMemo`, `useCallback`)。
