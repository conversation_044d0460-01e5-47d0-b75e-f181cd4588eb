// AI性能监控API端点
import { Request, Response } from 'express';
import { logger } from '../utils/logger.js';

// 模拟获取LLM服务实例（在实际应用中，这应该是一个单例服务）
let llmServiceInstance: any = null;

/**
 * 设置LLM服务实例（由MockInterviewService调用）
 */
export function setLLMServiceInstance(instance: any): void {
    llmServiceInstance = instance;
}

/**
 * 获取AI问题生成性能指标
 */
export async function getAIPerformanceMetrics(req: Request, res: Response): Promise<void> {
    try {
        if (!llmServiceInstance) {
            // 如果服务实例不可用，返回默认指标
            const defaultMetrics = {
                averageGenerationTime: 0,
                cacheHitRate: 0,
                totalRequests: 0,
                failureRate: 0,
                cacheSize: 0,
                batchQueueSize: 0,
                status: 'service_unavailable',
                timestamp: Date.now()
            };

            res.json(defaultMetrics);
            return;
        }

        // 获取实际的性能指标
        const metrics = llmServiceInstance.getPerformanceMetrics();

        // 添加额外的元数据
        const enhancedMetrics = {
            ...metrics,
            status: 'active',
            timestamp: Date.now(),
            // 计算一些派生指标
            successRate: 1 - metrics.failureRate,
            cacheEfficiency: metrics.cacheSize > 0 ? metrics.cacheHitRate : 0,
            performanceGrade: calculatePerformanceGrade(metrics)
        };

        logger.info(`📊 AI Performance metrics requested: ${JSON.stringify(enhancedMetrics)}`);
        res.json(enhancedMetrics);

    } catch (error) {
        logger.error('❌ Failed to get AI performance metrics:', error);

        res.status(500).json({
            error: 'Failed to retrieve performance metrics',
            message: error instanceof Error ? error.message : 'Unknown error',
            timestamp: Date.now()
        });
    }
}

/**
 * 计算性能等级
 */
function calculatePerformanceGrade(metrics: any): string {
    let score = 0;

    // 生成时间评分 (40分)
    if (metrics.averageGenerationTime <= 1000) score += 40;
    else if (metrics.averageGenerationTime <= 2000) score += 30;
    else if (metrics.averageGenerationTime <= 3000) score += 20;
    else score += 10;

    // 缓存命中率评分 (30分)
    if (metrics.cacheHitRate >= 0.8) score += 30;
    else if (metrics.cacheHitRate >= 0.6) score += 25;
    else if (metrics.cacheHitRate >= 0.4) score += 20;
    else score += 10;

    // 可靠性评分 (30分)
    if (metrics.failureRate <= 0.02) score += 30;
    else if (metrics.failureRate <= 0.05) score += 25;
    else if (metrics.failureRate <= 0.10) score += 20;
    else score += 10;

    // 根据总分确定等级
    if (score >= 90) return 'A+';
    if (score >= 80) return 'A';
    if (score >= 70) return 'B+';
    if (score >= 60) return 'B';
    if (score >= 50) return 'C+';
    if (score >= 40) return 'C';
    return 'D';
}

/**
 * 重置性能指标（用于测试或维护）
 */
export async function resetAIPerformanceMetrics(req: Request, res: Response): Promise<void> {
    try {
        if (!llmServiceInstance) {
            res.status(503).json({
                error: 'LLM service not available',
                timestamp: Date.now()
            });
            return;
        }

        // 如果LLM服务有重置方法，调用它
        if (typeof llmServiceInstance.resetMetrics === 'function') {
            llmServiceInstance.resetMetrics();
            logger.info('📊 AI Performance metrics reset');

            res.json({
                message: 'Performance metrics reset successfully',
                timestamp: Date.now()
            });
        } else {
            res.status(501).json({
                error: 'Reset functionality not implemented',
                timestamp: Date.now()
            });
        }

    } catch (error) {
        logger.error('❌ Failed to reset AI performance metrics:', error);

        res.status(500).json({
            error: 'Failed to reset performance metrics',
            message: error instanceof Error ? error.message : 'Unknown error',
            timestamp: Date.now()
        });
    }
}

/**
 * 获取AI服务健康状态
 */
export async function getAIServiceHealth(req: Request, res: Response): Promise<void> {
    try {
        const healthStatus = {
            service: 'AI Question Generation',
            status: llmServiceInstance ? 'healthy' : 'unavailable',
            timestamp: Date.now(),
            checks: {
                llmService: llmServiceInstance ? 'available' : 'unavailable',
                cache: llmServiceInstance?.getPerformanceMetrics()?.cacheSize >= 0 ? 'operational' : 'unknown',
                batchProcessor: llmServiceInstance?.getPerformanceMetrics()?.batchQueueSize >= 0 ? 'operational' : 'unknown'
            }
        };

        if (llmServiceInstance) {
            const metrics = llmServiceInstance.getPerformanceMetrics();
            healthStatus.checks = {
                ...healthStatus.checks,
                averageResponseTime: metrics.averageGenerationTime <= 3000 ? 'good' : 'slow',
                errorRate: metrics.failureRate <= 0.1 ? 'acceptable' : 'high',
                cachePerformance: metrics.cacheHitRate >= 0.3 ? 'good' : 'poor'
            };
        }

        const overallHealthy = Object.values(healthStatus.checks).every(
            check => !['unavailable', 'high', 'poor', 'slow'].includes(check)
        );

        res.status(overallHealthy ? 200 : 503).json(healthStatus);

    } catch (error) {
        logger.error('❌ Failed to get AI service health:', error);

        res.status(500).json({
            service: 'AI Question Generation',
            status: 'error',
            error: error instanceof Error ? error.message : 'Unknown error',
            timestamp: Date.now()
        });
    }
}

/**
 * 触发缓存预热
 */
export async function triggerCacheWarmup(req: Request, res: Response): Promise<void> {
    try {
        if (!llmServiceInstance) {
            res.status(503).json({
                error: 'LLM service not available',
                timestamp: Date.now()
            });
            return;
        }

        // 如果LLM服务有预热方法，调用它
        if (typeof llmServiceInstance.pregenerateQuestions === 'function') {
            // 预定义一些常见的面试场景进行预热
            const warmupContexts = [
                {
                    companyName: '科技公司',
                    positionName: '软件工程师',
                    interviewLanguage: 'chinese',
                    answerStyle: 'conversational',
                    previousQuestions: [],
                    previousAnswers: [],
                    currentQuestionIndex: 0,
                    totalQuestions: 5
                },
                {
                    companyName: '互联网公司',
                    positionName: '产品经理',
                    interviewLanguage: 'chinese',
                    answerStyle: 'conversational',
                    previousQuestions: [],
                    previousAnswers: [],
                    currentQuestionIndex: 0,
                    totalQuestions: 5
                }
            ];

            await llmServiceInstance.pregenerateQuestions(warmupContexts, 'high');

            logger.info('🔥 Cache warmup triggered');
            res.json({
                message: 'Cache warmup triggered successfully',
                contextsCount: warmupContexts.length,
                timestamp: Date.now()
            });
        } else {
            res.status(501).json({
                error: 'Cache warmup functionality not implemented',
                timestamp: Date.now()
            });
        }

    } catch (error) {
        logger.error('❌ Failed to trigger cache warmup:', error);

        res.status(500).json({
            error: 'Failed to trigger cache warmup',
            message: error instanceof Error ? error.message : 'Unknown error',
            timestamp: Date.now()
        });
    }
}