好的，我们来完成 **任务 2.2: 麦克风设备授权与检测**。

这是一个纯前端的任务，主要修改 `InterviewSetupPage.tsx` 文件。我们将根据您提供的UI设计图（AI正式面试-2.png）中的右侧“获取电脑音频”部分来实现麦克风的授权和状态显示。

## 任务 2.2: 麦克风设备授权与检测 - 操作清单

项目根目录: local-mianshijun/

前端代码目录: local-mianshijun/frontend/

请在您的 Cursor 编辑器中按照以下步骤操作：

------

### 1. 准备工作 (Preparation)

- **确认组件路径**: 我们会用到 `Button` 组件。根据您之前的项目结构，它应该在 `frontend/src/components/ui/Button.tsx`。
- **引入图标**: 我们会用到 `lucide-react` 中的麦克风图标。

------

### 2. 修改 `InterviewSetupPage.tsx`

这个页面目前直接渲染了 `ResumeUploadPage` 组件。 我们需要修改它，使其包含麦克风授权的逻辑，并且UI风格需要参考您提供的 “AI正式面试-2.png” 图片中的右侧部分。

- **中文**: 打开 `frontend/src/pages/InterviewSetupPage.tsx` 文件，用以下代码**完全替换**其内容。
- **English**: Open the `frontend/src/pages/InterviewSetupPage.tsx` file and **completely replace** its content with the following code.
- **操作 (Action)**:

TypeScript

```
// frontend/src/pages/InterviewSetupPage.tsx
import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import Button from '../components/ui/Button'; // 您的通用按钮组件
import useAuthStore from '../stores/authStore'; // 用于验证用户是否登录
import { Mic, ArrowRight, AlertTriangle, CheckCircle, XCircle } from 'lucide-react'; // 引入图标

// 定义麦克风权限状态类型
type PermissionStatus = 'idle' | 'pending' | 'granted' | 'denied';

const InterviewSetupPage: React.FC = () => {
  const navigate = useNavigate();
  const { isAuthenticated } = useAuthStore();

  // 麦克风权限状态
  const [micPermission, setMicPermission] = useState<PermissionStatus>('idle');
  // 音频流，用于可选的音量指示
  const [audioStream, setAudioStream] = useState<MediaStream | null>(null);
  // (可选MVP) 简单音量指示状态
  const [micWorkingText, setMicWorkingText] = useState<string | null>(null);

  // 检查初始权限状态 (通常浏览器不会持久化权限，但可以尝试查询)
  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/login'); // 如果未登录，跳转到登录页
      return;
    }

    const checkInitialPermission = async () => {
      try {
        const permissionStatus = await navigator.permissions.query({ name: 'microphone' as PermissionName });
        if (permissionStatus.state === 'granted') {
          setMicPermission('granted');
          // 如果已授权，可以尝试直接获取媒体流以显示音量指示
          // await requestMicAndSetupStream(); // 稍后定义这个函数
        } else if (permissionStatus.state === 'denied') {
          setMicPermission('denied');
        } else {
          setMicPermission('idle');
        }
        permissionStatus.onchange = () => {
          setMicPermission(permissionStatus.state as PermissionStatus);
        };
      } catch (error) {
        console.warn('浏览器不支持权限查询 API，或查询失败:', error);
        setMicPermission('idle'); // 默认设为未授权
      }
    };

    checkInitialPermission();

    // 清理函数
    return () => {
      if (audioStream) {
        audioStream.getTracks().forEach(track => track.stop());
      }
    };
  }, [isAuthenticated, navigate, audioStream]);


  // 请求麦克风权限并设置音频流的函数
  const requestMicAndSetupStream = async () => {
    setMicPermission('pending');
    setMicWorkingText(null);
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      setAudioStream(stream);
      setMicPermission('granted');
      setMicWorkingText('麦克风工作中...'); // 简单的文本提示

      // (可选MVP) 实现一个简单的音量检测逻辑
      // 这里可以添加代码通过 Web Audio API 分析 stream 来获取音量
      // 例如，创建一个 AudioContext 和 AnalyserNode

    } catch (err) {
      console.error('麦克风授权失败:', err);
      setMicPermission('denied');
      setMicWorkingText(null);
    }
  };

  const handleRequestMicPermission = () => {
    if (micPermission !== 'granted') {
      requestMicAndSetupStream();
    }
  };

  const handleContinue = () => {
    if (micPermission === 'granted') {
      // 在这里处理进入下一步的逻辑，例如导航到面试页面
      alert('麦克风已授权，准备进入下一步！(功能待开发)');
      // navigate('/interview/live'); // 假设的面试实时页面路由
    } else {
      alert('请先授权麦克风才能继续。');
    }
  };


  const getStatusIndicator = () => {
    switch (micPermission) {
      case 'granted':
        return (
          <div className="flex items-center text-green-600">
            <CheckCircle size={18} className="mr-2" />
            已授权 - {micWorkingText || '准备就绪'}
          </div>
        );
      case 'denied':
        return (
          <div className="flex items-center text-red-600">
            <XCircle size={18} className="mr-2" />
            已拒绝 - 请在浏览器设置中修改
          </div>
        );
      case 'pending':
        return (
          <div className="flex items-center text-yellow-600">
            <AlertTriangle size={18} className="mr-2" />
            等待授权...
          </div>
        );
      case 'idle':
      default:
        return (
          <div className="flex items-center text-gray-500">
            <AlertTriangle size={18} className="mr-2" />
            未授权
          </div>
        );
    }
  };

  return (
    <div className="min-h-screen bg-slate-100 flex flex-col items-center justify-center p-4">
      {/* 主内容卡片区域，参考 AI正式面试-2.png 右侧风格 */}
      <div className="w-full max-w-lg bg-white shadow-xl rounded-xl p-6 sm:p-8">
        <div className="text-center mb-6">
          <h1 className="text-2xl font-semibold text-slate-800">面试设备检测</h1>
          <p className="mt-1 text-sm text-slate-500">请确保您的麦克风工作正常</p>
        </div>

        {/* 麦克风授权与检测模块 */}
        <div className="space-y-4 border border-gray-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Mic className="w-6 h-6 text-sky-500 mr-3" />
              <div>
                <h2 className="text-base font-medium text-slate-700">麦克风设备</h2>
                <p className="text-xs text-slate-500">用于面试过程中的语音交流</p>
              </div>
            </div>
            {micPermission !== 'granted' && micPermission !== 'pending' && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleRequestMicPermission}
                disabled={micPermission === 'pending'}
              >
                {micPermission === 'denied' ? '重新授权' : '授权麦克风'}
              </Button>
            )}
          </div>
          
          {/* 麦克风状态指示 */}
          <div className="pl-9 text-sm">
            {getStatusIndicator()}
          </div>

          {/* (可选MVP) 音量指示条或文本 */}
          {micPermission === 'granted' && micWorkingText && (
            <div className="pl-9 mt-2">
              {/* 这里可以放一个简单的音量条动画，暂时用文本代替 */}
              <div className="h-2 w-full bg-gray-200 rounded">
                <div 
                  className="h-full bg-green-500 rounded animate-pulse" 
                  style={{ width: '70%' }} // 模拟音量
                ></div>
              </div>
            </div>
          )}
        </div>
        
        {micPermission === 'denied' && (
          <div className="mt-3 text-xs text-red-500 bg-red-50 p-2 rounded-md flex items-center">
            <AlertTriangle size={14} className="mr-2 flex-shrink-0" />
            麦克风权限已被拒绝。您需要到浏览器的站点设置中，为此网站手动开启麦克风权限，然后刷新页面。
          </div>
        )}

        {/* "开始面试" 按钮 */}
        <div className="mt-8 text-center">
          <Button
            variant="primary"
            size="lg"
            className="w-full sm:w-auto"
            onClick={handleContinue}
            disabled={micPermission !== 'granted'}
          >
            开始面试
            <ArrowRight className="ml-2 h-5 w-5" />
          </Button>
        </div>
      </div>

      {/* 页脚 */}
      <div className="mt-8 text-center">
        <p className="text-xs text-slate-500">
          &copy; {new Date().getFullYear()} 面试君. AI 助力，面试无忧.
        </p>
      </div>
    </div>
  );
};

export default InterviewSetupPage;
```

- 代码解释与UI调整说明

  :

  - **页面结构**: 整体采用浅灰色背景，内容区为居中白色卡片，与您 `AI正式面试-2.png` 右侧设计风格相似。

  - 麦克风授权模块

    :

    - 使用 `Mic` 图标。
    - 标题为“麦克风设备”，副标题为“用于面试过程中的语音交流”。
    - "授权麦克风"按钮: 当权限为 `idle` 或 `denied` 时显示。点击后调用 `handleRequestMicPermission`。
    - 状态指示: 使用 `getStatusIndicator` 函数根据 `micPermission` 状态 (`idle`, `pending`, `granted`, `denied`) 显示不同的文本和图标 (`AlertTriangle`, `CheckCircle`, `XCircle`)。

  - 状态管理 (`useState`)

    :

    - `micPermission`: 存储麦克风权限状态。
    - `audioStream`: 存储获取到的 `MediaStream` 对象，用于后续可能的音量分析或停止轨道。
    - `micWorkingText`: 一个简单的文本提示，表示麦克风正在工作。

  - 权限请求 (`requestMicAndSetupStream`)

    :

    - 调用 `navigator.mediaDevices.getUserMedia({ audio: true })` 请求权限。
    - 成功后，更新状态为 `granted`，保存 `MediaStream`，并设置 `micWorkingText`。
    - 失败后，更新状态为 `denied`。

  - useEffect

    :

    - 组件加载时，检查是否已登录，未登录则跳转到登录页。
    - （可选）尝试查询初始麦克风权限状态。
    - 组件卸载时，如果 `audioStream` 存在，则停止所有音轨，释放麦克风资源。

  - (可选MVP) 音量指示

    :

    - 在授权成功后，通过 `micWorkingText` 变量显示 "麦克风工作中..."。
    - 添加了一个简单的模拟音量条 (`animate-pulse`)。真实音量条需要使用 Web Audio API 分析 `audioStream`。

  - "开始面试"按钮

    :

    - 只有当 `micPermission` 为 `'granted'` 时才启用。
    - 点击后目前仅显示提示。

  - UI 设计参考

    :

    - “获取电脑音频”部分的 UI 结构和视觉提示（如图标、状态文本颜色）已参考您的图片（AI正式面试-2.png）。
    - 按钮样式和整体卡片布局也尽量贴合图片的专业感。

------

### 3. （确保）添加到应用路由 (Ensure it's Added to Application Routes)

请确保您的 `frontend/src/App.tsx` 中有正确的路由配置指向 `InterviewSetupPage`。从您的文件看，它应该已经存在并且位于 `Layout` 组件内部，并且是受保护的路由，这是正确的。

TypeScript

```
// frontend/src/App.tsx
// ...
<Route path="/" element={<Layout />}>
  {/* ... */}
  <Route path="interview-setup" element={
    <ProtectedRoute>
      <InterviewSetupPage />
    </ProtectedRoute>
  } />
  {/* resume-upload 路由也指向了 InterviewSetupPage，这可能需要您根据实际流程调整 */}
  <Route path="resume-upload" element={
    <ProtectedRoute>
      <InterviewSetupPage />
    </ProtectedRoute>
  } />
  {/* ... */}
</Route>
// ...
```

如果您希望 `/resume-upload` 是一个不同的页面（例如之前的简历上传专用页面），则需要为它创建或恢复相应的组件。目前根据您的 `App.tsx`，它和 `/interview-setup` 指向同一个组件。

------

### 4. 测试页面功能 (Test Page Functionality)

- **中文**:

  1. **启动/重启开发服务器** (前端 `npm run dev`，后端 `vercel dev` 如果需要API交互，但此任务主要是前端)。

  2. **登录**: 首先确保您已登录，因为 `InterviewSetupPage` 是受保护路由。

  3. 访问页面

     :

     - 在浏览器地址栏输入您应用的本地地址加上 `/interview-setup` (例如 `http://localhost:5173/interview-setup`)。

  4. 测试交互

     :

     - **初始状态**: 页面应显示“麦克风设备”模块，状态为“未授权”。“开始面试”按钮应为禁用。

     - 点击“授权麦克风”

       :

       - 浏览器应弹出麦克风权限请求对话框。
       - 状态应变为“等待授权...”。

     - 允许授权

       :

       - 状态应变为“已授权 - 麦克风工作中...”。
       - （可选MVP）应看到模拟音量条或听到音频反馈（如果实现）。
       - “开始面试”按钮应变为启用。

     - 拒绝授权

       :

       - 状态应变为“已拒绝 - 请在浏览器设置中修改”。
       - “授权麦克风”按钮可能变为“重新授权”。
       - “开始面试”按钮应保持禁用。
       - 页面下方应显示红色错误提示。

     - **已授权后点击“开始面试”**: 应弹出提示“麦克风已授权，准备进入下一步！(功能待开发)”。

     - **未授权时点击“开始面试”**: 应弹出提示“请先授权麦克风才能继续。”。

- **English**:

  1. **Start/Restart Development Server** (Frontend `npm run dev`, backend `vercel dev` if API interaction is needed, though this task is mainly frontend).

  2. **Login**: First, ensure you are logged in, as `InterviewSetupPage` is a protected route.

  3. Access the Page

     :

     - Enter your application's local address plus `/interview-setup` in the browser's address bar (e.g., `http://localhost:5173/interview-setup`).

  4. Test Interactions

     :

     - **Initial State**: The page should display the "麦克风设备" (Microphone Device) module, with status "未授权" (Not Authorized). The "开始面试" (Start Interview) button should be disabled.

     - Click "授权麦克风" (Authorize Microphone)

       :

       - The browser should pop up a microphone permission request dialog.
       - The status should change to "等待授权..." (Pending authorization...).

     - Allow Authorization

       :

       - The status should change to "已授权 - 麦克风工作中..." (Granted - Microphone working...).
       - (Optional MVP) You should see the mock volume bar or hear audio feedback (if implemented).
       - The "开始面试" (Start Interview) button should become enabled.

     - Deny Authorization

       :

       - The status should change to "已拒绝 - 请在浏览器设置中修改" (Denied - Please change in browser settings).
       - The "授权麦克风" (Authorize Microphone) button might change to "重新授权" (Re-authorize).
       - The "开始面试" (Start Interview) button should remain disabled.
       - A red error message should appear at the bottom of the section.

     - **Click "开始面试" (Start Interview) after granting permission**: An alert "麦克风已授权，准备进入下一步！(功能待开发)" (Microphone authorized, ready for next step! (Feature in development)) should appear.

     - **Click "开始面试" (Start Interview) without granting permission**: An alert "请先授权麦克风才能继续。" (Please authorize the microphone first to continue.) should appear.

------

### 5. （可选）状态存储 (Optional: State Storage)

- **中文**: 根据任务要求，将麦克风授权状态更新到组件状态 (`useState`) 或 Context / Zustand store。上面的代码已经使用了 `useState` (`micPermission`, `audioStream`, `micWorkingText`)。
- **English**: As per the task requirements, update the microphone authorization status to component state (`useState`) or Context / Zustand store. The code above already uses `useState` (`micPermission`, `audioStream`, `micWorkingText`).

如果这个状态需要在其他页面或组件中使用（例如，在实际面试页面直接使用已授权的 `audioStream`），那么将其提升到 Zustand store 是一个好主意。

**若要使用 Zustand 存储麦克风状态**:

1. **创建/更新 Zustand Store (例如 `deviceStore.ts`)**:

   - 中文：在 `frontend/src/stores/` 目录下创建或修改一个 store 文件。
   - English: Create or modify a store file in `frontend/src/stores/`.

   TypeScript

   ```
   // frontend/src/stores/deviceStore.ts (示例)
   import {create} from 'zustand';
   
   type PermissionStatus = 'idle' | 'pending' | 'granted' | 'denied';
   
   interface DeviceState {
     micPermission: PermissionStatus;
     audioStream: MediaStream | null;
     setMicPermission: (status: PermissionStatus) => void;
     setAudioStream: (stream: MediaStream | null) => void;
     resetDeviceState: () => void;
   }
   
   const useDeviceStore = create<DeviceState>((set) => ({
     micPermission: 'idle',
     audioStream: null,
     setMicPermission: (status) => set({ micPermission: status }),
     setAudioStream: (stream) => set((state) => {
       // 如果之前有流，先停止旧的流
       if (state.audioStream) {
         state.audioStream.getTracks().forEach(track => track.stop());
       }
       return { audioStream: stream };
     }),
     resetDeviceState: () => set({ micPermission: 'idle', audioStream: null }),
   }));
   
   export default useDeviceStore;
   ```

2. **在 `InterviewSetupPage.tsx` 中使用 Store**:

   - 中文：导入并使用 `useDeviceStore` 替代本地的 `useState`。
   - English: Import and use `useDeviceStore` instead of local `useState`.

   TypeScript

   ```
   // frontend/src/pages/InterviewSetupPage.tsx
   // ... (其他导入)
   import useDeviceStore from '../stores/deviceStore'; // 导入Store
   
   // ...
   const InterviewSetupPage: React.FC = () => {
     // ...
     const { 
       micPermission, 
       audioStream, // 直接从 store 读取 audioStream
       setMicPermission, 
       setAudioStream 
     } = useDeviceStore();
     const [micWorkingText, setMicWorkingText] = useState<string | null>(null); // 这个可以保留为本地UI状态
   
     useEffect(() => {
       // ... (isAuthenticated 和 初始权限检查逻辑不变)
       // 清理函数现在可以简化，因为setAudioStream会处理旧流的停止
       return () => {
         // 如果页面卸载时希望清除流（例如，用户导航离开），可以调用
         // setAudioStream(null); // 这会停止当前流
       };
     }, [isAuthenticated, navigate, /* setAudioStream - 如果在effect中使用 */]);
   
     const requestMicAndSetupStream = async () => {
       setMicPermission('pending');
       setMicWorkingText(null);
       try {
         const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
         setAudioStream(stream); // 更新 Store 中的流
         setMicPermission('granted');
         setMicWorkingText('麦克风工作中...');
       } catch (err) {
         console.error('麦克风授权失败:', err);
         setAudioStream(null); // 确保失败时清除流
         setMicPermission('denied');
         setMicWorkingText(null);
       }
     };
   
     // ... (getStatusIndicator 和 handleContinue 等其他逻辑大部分保持不变，只是状态来自store)
     // ...
   };
   ```

   **注意**: 将状态提升到全局 store 会使得 `audioStream` 对象可以在应用的其他部分被访问和控制。当不再需要麦克风时（例如用户离开面试流程），记得调用 `setAudioStream(null)` 来停止并释放麦克风资源。

------

### 6. 提交代码到 GitHub (Commit Code to GitHub)

- **中文**: 完成修改和测试后，将代码提交到 GitHub。

- **English**: After completing modifications and testing, commit your code to GitHub.

- 操作 (Action):

  Bash

  ```
  git add .
  git commit -m "Feat(InterviewSetup): Implement microphone authorization and basic detection (Task 2.2)"
  git push origin main # 或者您的主分支名称
  ```

------

### 7. Vercel 部署后测试 (Test after Vercel Deployment)

- **中文**: 推送到 GitHub 后，Vercel 应该会自动触发新的部署。访问您的 Vercel 部署链接。
- **English**: After pushing to GitHub, Vercel should automatically trigger a new deployment. Visit your Vercel deployment link.
- **中文**: 重复**步骤 4** 中的所有测试步骤，确保在线上环境（需要 HTTPS 才能使用 `getUserMedia`）一切正常。
- **English**: Repeat all testing steps from **Step 4** to ensure everything works correctly in the live environment (HTTPS is required for `getUserMedia`).

------

这个清单应该能帮助您完成任务 2.2。麦克风授权是 Web 应用中一个常见的交互，确保用户体验流畅很重要。如果您在任何步骤遇到问题，请随时告诉我。