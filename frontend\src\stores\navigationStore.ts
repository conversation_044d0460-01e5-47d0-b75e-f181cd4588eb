import { create } from 'zustand';

export type NavigationItem = 'home' | 'simulation' | 'formal' | 'recharge' | 'share';

interface NavigationState {
  activeItem: NavigationItem;
  setActiveItem: (item: NavigationItem) => void;
}

const useNavigationStore = create<NavigationState>((set) => ({
  activeItem: 'home',
  setActiveItem: (item) => set({ activeItem: item }),
}));

export default useNavigationStore;
