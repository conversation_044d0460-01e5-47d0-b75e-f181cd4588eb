import { fetchWithAuth } from './apiService';

// 兑换请求类型
export interface ExchangeRequest {
  type: 'mock' | 'formal';
}

// 兑换响应类型
export interface ExchangeResponse {
  success: boolean;
  message: string;
  data?: {
    exchangeType: 'mock' | 'formal';
    cost: number;
    description: string;
    newBalance: {
      mianshijunBalance: number;
      mockInterviewCredits: number;
      formalInterviewCredits: number;
    };
  };
}

/**
 * 面巾兑换面试次数
 * @param type 兑换类型：'mock' | 'formal'
 * @returns 兑换结果
 */
export const exchangeCredits = async (type: 'mock' | 'formal'): Promise<ExchangeResponse> => {
  try {
    const response = await fetchWithAuth<ExchangeResponse>('/exchange', {
      method: 'POST',
      body: { type },
    });
    return response;
  } catch (error: any) {
    // 重新抛出错误，让调用方处理
    throw new Error(error.message || '兑换失败，请稍后再试');
  }
};
