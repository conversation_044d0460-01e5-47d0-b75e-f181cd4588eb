import { EventEmitter } from 'events';
import {
  ASRState,
  RecognitionSession,
  RecognitionResult,
  VADInfo,
  AudioSegmentInfo,
  StreamingConfig,
  DEFAULT_STREAMING_CONFIG,
  ASRError,
  ASRErrorCode,
  ASREvent,
  StateChangeEvent,
  ResultReadyEvent,
  SessionStatistics,
  ASRServiceInterface
} from '../types/asrTypes';
import { IncrementalResultManager } from './incrementalResultManager';
import { ContextManager } from './contextManager';
import { ASRServiceBase, MockASRService, RealASRService } from './asrServiceBase';
import { TextMerger } from './textMerger';

// 🔥 修复：导入真实的ASR函数
import {
  callIflytekASR,
  callAlibabaASR,
  callBaiduASR
} from '../websocket/interviewWs';

/**
 * 流式识别状态机管理器
 * 管理连续识别过程的状态转换和音频处理
 */
export class StreamingASRManager extends EventEmitter {
  private state: ASRState = ASRState.IDLE;
  private recognitionSessions: Map<string, RecognitionSession> = new Map();
  private config: StreamingConfig;
  private cleanupInterval: NodeJS.Timeout;

  // 核心服务组件
  private asrServices: ASRServiceInterface[] = [];
  private resultManager: IncrementalResultManager;
  private contextManager: ContextManager;
  private textMerger: TextMerger;

  constructor(config: Partial<StreamingConfig> = {}) {
    super();
    this.config = { ...DEFAULT_STREAMING_CONFIG, ...config };

    // 初始化核心组件
    this.resultManager = new IncrementalResultManager();
    this.contextManager = new ContextManager();
    this.textMerger = new TextMerger();

    // 初始化ASR服务（暂时使用Mock服务）
    this.initializeASRServices();

    // 定期清理过期会话
    this.cleanupInterval = setInterval(() => {
      this.cleanupExpiredSessions();
      this.resultManager.cleanup();
      this.contextManager.cleanupExpiredContext();
    }, 60000); // 每分钟清理一次

    console.log('StreamingASRManager initialized with config:', this.config);
  }

  /**
   * 初始化ASR服务
   */
  private initializeASRServices(): void {
    // 🔥 修复：使用真实的ASR服务替代Mock服务
    // 🎯 调试模式：只使用讯飞ASR，注释掉阿里和百度ASR
    this.asrServices = [
      new RealASRService('iflytek', 1, callIflytekASR, {
        timeout: 15000,
        retryCount: 2,
        weight: 1.2
      }),
      // 🚫 调试期间暂时禁用阿里ASR
      // new RealASRService('alibaba', 2, callAlibabaASR, {
      //   timeout: 12000,
      //   retryCount: 2,
      //   weight: 1.1
      // }),
      // 🚫 调试期间暂时禁用百度ASR
      // new RealASRService('baidu', 3, callBaiduASR, {
      //   timeout: 10000,
      //   retryCount: 2,
      //   weight: 1.0
      // })
    ];

    console.log(`✅ Initialized ${this.asrServices.length} real ASR service (iFlyTek only for debugging)`);
  }

  /**
   * 处理智能音频段
   */
  async processAudioSegment(
    sessionId: string,
    audioData: Buffer,
    segmentInfo: AudioSegmentInfo,
    vadInfo?: VADInfo
  ): Promise<RecognitionResult | null> {
    
    const session = this.getOrCreateSession(sessionId);
    session.lastActivityTime = Date.now();
    
    if (segmentInfo) {
      session.currentSegment = segmentInfo;
    }
    
    if (vadInfo) {
      session.vadInfo.push(vadInfo);
      // 保持VAD历史在合理范围内
      if (session.vadInfo.length > 100) {
        session.vadInfo = session.vadInfo.slice(-50);
      }
    }

    console.log(`[${sessionId}] Processing audio segment in state: ${this.state}`, {
      segmentType: segmentInfo?.segmentType,
      duration: segmentInfo?.duration,
      confidence: segmentInfo?.confidence,
      triggerReason: segmentInfo?.triggerReason
    });

    try {
      switch (this.state) {
        case ASRState.IDLE:
          return await this.handleIdleState(session, audioData, segmentInfo, vadInfo);
          
        case ASRState.LISTENING:
          return await this.handleListeningState(session, audioData, segmentInfo, vadInfo);
          
        case ASRState.PROCESSING:
          return await this.handleProcessingState(session, audioData, segmentInfo, vadInfo);
          
        case ASRState.MERGING:
          return await this.handleMergingState(session, audioData, segmentInfo, vadInfo);
          
        case ASRState.OUTPUTTING:
          return await this.handleOutputtingState(session, audioData, segmentInfo, vadInfo);
          
        default:
          console.warn(`Unknown ASR state: ${this.state}`);
          return null;
      }
    } catch (error) {
      console.error(`[${sessionId}] Error processing audio segment:`, error);
      this.emitError(sessionId, new ASRError(
        `Processing error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        ASRErrorCode.PROCESSING_ERROR,
        'StreamingASRManager',
        true
      ));
      return null;
    }
  }

  /**
   * 处理空闲状态
   */
  private async handleIdleState(
    session: RecognitionSession,
    audioData: Buffer,
    segmentInfo?: AudioSegmentInfo,
    vadInfo?: VADInfo
  ): Promise<RecognitionResult | null> {
    
    // 检查是否有语音活动或音频段
    const hasSpeech = vadInfo?.isSpeech || segmentInfo?.segmentType === 'speech';
    const hasValidSegment = segmentInfo && segmentInfo.confidence > 0.3; // 🔥 修复：降低置信度阈值与前端一致

    // 🔥 修复：也处理silence段，因为可能包含低音量语音
    if (hasValidSegment && (hasSpeech || segmentInfo?.segmentType === 'silence')) {
      console.log(`[${session.sessionId}] Speech detected, transitioning to LISTENING`);
      this.transitionTo(ASRState.LISTENING, session.sessionId, 'Speech activity detected');
      
      session.startTime = Date.now();
      session.audioBuffer = [audioData];
      session.partialResults = [];
      session.statistics.totalSegments++;
      
      return null;
    }
    
    return null;
  }

  /**
   * 处理监听状态
   */
  private async handleListeningState(
    session: RecognitionSession,
    audioData: Buffer,
    segmentInfo?: AudioSegmentInfo,
    vadInfo?: VADInfo
  ): Promise<RecognitionResult | null> {
    
    session.audioBuffer.push(audioData);
    
    // 检查是否应该开始识别
    if (this.shouldStartRecognition(session, segmentInfo)) {
      console.log(`[${session.sessionId}] Starting recognition, transitioning to PROCESSING`);
      this.transitionTo(ASRState.PROCESSING, session.sessionId, 'Recognition threshold reached');
      return await this.performRecognition(session);
    }
    
    // 检查是否超时
    if (this.isSessionTimeout(session)) {
      console.log(`[${session.sessionId}] Session timeout, returning to IDLE`);
      this.transitionTo(ASRState.IDLE, session.sessionId, 'Session timeout');
      this.cleanupSession(session);
    }
    
    return null;
  }

  /**
   * 处理处理状态
   */
  private async handleProcessingState(
    session: RecognitionSession,
    audioData: Buffer,
    segmentInfo?: AudioSegmentInfo,
    vadInfo?: VADInfo
  ): Promise<RecognitionResult | null> {
    
    // 继续收集音频数据
    session.audioBuffer.push(audioData);
    
    // 检查是否检测到语音结束
    const isEndOfSpeech = vadInfo?.isEndOfSpeech || 
                         segmentInfo?.triggerReason === 'silence_timeout' ||
                         segmentInfo?.triggerReason === 'speech_segment_complete';
    
    if (isEndOfSpeech || this.detectSpeechEnd(session)) {
      console.log(`[${session.sessionId}] Speech end detected, transitioning to MERGING`);
      this.transitionTo(ASRState.MERGING, session.sessionId, 'Speech end detected');
      return await this.finalizeRecognition(session);
    }
    
    // 检查是否需要进行增量识别
    if (this.shouldPerformIncrementalRecognition(session)) {
      return await this.performIncrementalRecognition(session);
    }
    
    return null;
  }

  /**
   * 处理合并状态
   */
  private async handleMergingState(
    session: RecognitionSession,
    audioData: Buffer,
    segmentInfo?: AudioSegmentInfo,
    vadInfo?: VADInfo
  ): Promise<RecognitionResult | null> {
    
    // 合并所有识别结果
    const mergedResult = await this.mergeAndFinalize(session);
    
    if (mergedResult) {
      console.log(`[${session.sessionId}] Merging complete, transitioning to OUTPUTTING`);
      this.transitionTo(ASRState.OUTPUTTING, session.sessionId, 'Merging completed');
      
      // 发出结果就绪事件
      this.emitResultReady(session.sessionId, mergedResult, false);
      
      return mergedResult;
    }
    
    // 如果合并失败，返回IDLE
    console.warn(`[${session.sessionId}] Merging failed, returning to IDLE`);
    this.transitionTo(ASRState.IDLE, session.sessionId, 'Merging failed');
    this.cleanupSession(session);
    return null;
  }

  /**
   * 处理输出状态
   */
  private async handleOutputtingState(
    session: RecognitionSession,
    audioData: Buffer,
    segmentInfo?: AudioSegmentInfo,
    vadInfo?: VADInfo
  ): Promise<RecognitionResult | null> {
    
    // 输出完成，检查是否有新的语音输入
    const hasSpeech = vadInfo?.isSpeech || segmentInfo?.segmentType === 'speech';
    const hasValidSegment = segmentInfo && segmentInfo.confidence > 0.3; // 🔥 修复：降低置信度阈值
    
    if (hasSpeech && hasValidSegment) {
      console.log(`[${session.sessionId}] New speech detected, transitioning to LISTENING`);
      this.transitionTo(ASRState.LISTENING, session.sessionId, 'New speech detected');
      
      session.startTime = Date.now();
      session.audioBuffer = [audioData];
      session.partialResults = [];
      session.statistics.totalSegments++;
    } else {
      // 返回IDLE状态
      console.log(`[${session.sessionId}] No new speech, returning to IDLE`);
      this.transitionTo(ASRState.IDLE, session.sessionId, 'Output completed');
      this.cleanupSession(session);
    }
    
    return null;
  }

  /**
   * 检查是否应该开始识别
   */
  private shouldStartRecognition(session: RecognitionSession, segmentInfo?: AudioSegmentInfo): boolean {
    const bufferDuration = this.calculateBufferDuration(session.audioBuffer);
    const hasValidSegment = segmentInfo && segmentInfo.confidence > 0.3; // 🔥 修复：降低置信度阈值
    const isLongEnough = bufferDuration >= 1000; // 至少1秒

    return hasValidSegment && isLongEnough;
  }

  /**
   * 检查是否应该进行增量识别
   */
  private shouldPerformIncrementalRecognition(session: RecognitionSession): boolean {
    const bufferDuration = this.calculateBufferDuration(session.audioBuffer);
    const timeSinceLastRecognition = Date.now() - (session.partialResults[session.partialResults.length - 1]?.timestamp || session.startTime);
    
    return bufferDuration >= 2000 && timeSinceLastRecognition >= 1500; // 2秒缓冲且1.5秒间隔
  }

  /**
   * 检测语音结束
   */
  private detectSpeechEnd(session: RecognitionSession): boolean {
    if (session.vadInfo.length < 20) return false;
    
    const recentVAD = session.vadInfo.slice(-20); // 最近20个VAD结果
    const silenceCount = recentVAD.filter(v => !v.isSpeech).length;
    
    return silenceCount >= 15; // 75%为静音认为语音结束
  }

  /**
   * 执行识别
   */
  private async performRecognition(session: RecognitionSession): Promise<RecognitionResult | null> {
    const audioBuffer = Buffer.concat(session.audioBuffer);

    console.log(`[${session.sessionId}] Performing recognition with ${this.asrServices.length} services`);

    // 并行调用多个ASR服务
    const recognitionPromises = this.asrServices.map(async (service) => {
      try {
        if (!(await service.isAvailable())) {
          console.warn(`[${session.sessionId}] Service ${service.getName()} is not available`);
          return null;
        }

        const result = await service.recognize(audioBuffer, {
          segmentId: session.currentSegment?.id || `${session.sessionId}-${Date.now()}`,
          startTime: Date.now()
        });

        console.log(`[${session.sessionId}] ${service.getName()} recognition result:`, {
          text: result.text.substring(0, 50) + '...',
          confidence: result.confidence
        });

        return result;
      } catch (error) {
        console.error(`[${session.sessionId}] ASR service ${service.getName()} failed:`, error);
        return null;
      }
    });

    const results = await Promise.allSettled(recognitionPromises);
    const successfulResults = results
      .filter((result): result is PromiseFulfilledResult<RecognitionResult> =>
        result.status === 'fulfilled' && result.value !== null
      )
      .map(result => result.value);

    if (successfulResults.length > 0) {
      const bestResult = this.selectBestResult(successfulResults);

      // 添加到增量结果管理器
      this.resultManager.addResult(bestResult);
      session.partialResults.push(bestResult);

      // 🔥 修复：发送普通识别结果到前端
      this.emitResultReady(session.sessionId, bestResult, true);

      return bestResult;
    }

    return null;
  }

  /**
   * 执行增量识别
   */
  private async performIncrementalRecognition(session: RecognitionSession): Promise<RecognitionResult | null> {
    // 只使用最近的音频数据进行增量识别
    const recentAudio = session.audioBuffer.slice(-5); // 最近5个音频块
    const audioBuffer = Buffer.concat(recentAudio);

    console.log(`[${session.sessionId}] Performing incremental recognition`);

    // 使用最快的ASR服务进行增量识别
    const fastestService = this.asrServices.find(service => service.getConfig().priority === 1);

    if (!fastestService || !(await fastestService.isAvailable())) {
      console.warn(`[${session.sessionId}] No fast service available for incremental recognition`);
      return null;
    }

    try {
      const result = await fastestService.recognize(audioBuffer, {
        segmentId: session.currentSegment?.id || `${session.sessionId}-incremental-${Date.now()}`,
        startTime: Date.now(),
        isIncremental: true
      });

      result.isPartial = true;

      // 添加到增量结果管理器
      this.resultManager.addResult(result);
      session.partialResults.push(result);

      console.log(`[${session.sessionId}] Incremental recognition result:`, {
        text: result.text.substring(0, 50) + '...',
        confidence: result.confidence
      });

      // 🔥 修复：发送增量识别结果到前端
      this.emitResultReady(session.sessionId, result, true);

      return result;
    } catch (error) {
      console.error(`[${session.sessionId}] Incremental recognition failed:`, error);
      return null;
    }
  }

  /**
   * 最终识别
   */
  private async finalizeRecognition(session: RecognitionSession): Promise<RecognitionResult | null> {
    const audioBuffer = Buffer.concat(session.audioBuffer);

    console.log(`[${session.sessionId}] Finalizing recognition`);

    // 使用所有可用的ASR服务进行最终识别
    const finalPromises = this.asrServices.map(async (service) => {
      try {
        if (!(await service.isAvailable())) {
          return null;
        }

        const result = await service.recognize(audioBuffer, {
          segmentId: session.currentSegment?.id || `${session.sessionId}-final-${Date.now()}`,
          startTime: Date.now(),
          isFinal: true
        });

        result.isPartial = false;
        return result;
      } catch (error) {
        console.error(`[${session.sessionId}] Final ASR service ${service.getName()} failed:`, error);
        return null;
      }
    });

    const results = await Promise.allSettled(finalPromises);
    const successfulResults = results
      .filter((result): result is PromiseFulfilledResult<RecognitionResult> =>
        result.status === 'fulfilled' && result.value !== null
      )
      .map(result => result.value);

    if (successfulResults.length > 0) {
      const bestResult = this.selectBestResult(successfulResults);

      // 添加到增量结果管理器
      this.resultManager.addResult(bestResult);
      session.partialResults.push(bestResult);

      return bestResult;
    }

    return null;
  }

  /**
   * 合并并最终化结果
   */
  private async mergeAndFinalize(session: RecognitionSession): Promise<RecognitionResult | null> {
    console.log(`[${session.sessionId}] Merging and finalizing results using TextMerger`);

    if (session.partialResults.length === 0) {
      console.warn(`[${session.sessionId}] No partial results to merge`);
      return null;
    }

    try {
      // 使用新的智能文本合并器
      const mergedTextResult = await this.textMerger.mergeResults(session.partialResults);

      if (!mergedTextResult || !mergedTextResult.text) {
        console.warn(`[${session.sessionId}] Text merge operation failed`);
        return null;
      }

      // 添加到上下文历史
      this.contextManager.addToContext(session.sessionId, mergedTextResult.text, mergedTextResult.confidence);

      // 创建最终的识别结果
      const finalResult: RecognitionResult = {
        text: mergedTextResult.text,
        confidence: mergedTextResult.confidence,
        timestamp: Date.now(),
        service: 'smart-merged',
        isPartial: false,
        segmentId: session.currentSegment?.id || `${session.sessionId}-smart-merged-${Date.now()}`,
        processingTime: mergedTextResult.processingTime,
        metadata: {
          audioLength: Buffer.concat(session.audioBuffer).length,
          sampleRate: 16000,
          channels: 1,
          mergeMetadata: mergedTextResult.metadata,
          mergeStrategy: mergedTextResult.mergeStrategy
        }
      };

      console.log(`[${session.sessionId}] Smart merged result:`, {
        text: finalResult.text.substring(0, 100) + '...',
        confidence: finalResult.confidence.toFixed(2),
        strategy: mergedTextResult.mergeStrategy,
        segmentCount: mergedTextResult.metadata.totalSegments,
        processingTime: mergedTextResult.processingTime,
        dominantService: mergedTextResult.metadata.dominantService
      });

      return finalResult;
    } catch (error) {
      console.error(`[${session.sessionId}] Error in smart text merging:`, error);

      // 降级到简单合并
      const fallbackText = session.partialResults
        .map(result => result.text)
        .join(' ')
        .trim();

      if (fallbackText) {
        console.log(`[${session.sessionId}] Using fallback simple merge`);

        const fallbackResult: RecognitionResult = {
          text: fallbackText,
          confidence: session.partialResults.reduce((sum, r) => sum + r.confidence, 0) / session.partialResults.length,
          timestamp: Date.now(),
          service: 'fallback-merged',
          isPartial: false,
          segmentId: session.currentSegment?.id || `${session.sessionId}-fallback-${Date.now()}`,
          metadata: {
            audioLength: Buffer.concat(session.audioBuffer).length,
            sampleRate: 16000,
            channels: 1,
            fallbackReason: 'smart-merge-failed'
          }
        };

        this.contextManager.addToContext(session.sessionId, fallbackResult.text, fallbackResult.confidence);
        return fallbackResult;
      }

      return null;
    }
  }

  /**
   * 选择最佳识别结果
   */
  private selectBestResult(results: RecognitionResult[]): RecognitionResult {
    if (results.length === 1) {
      return results[0];
    }

    // 根据置信度和服务优先级选择最佳结果
    return results.reduce((best, current) => {
      const bestScore = this.calculateResultScore(best);
      const currentScore = this.calculateResultScore(current);
      return currentScore > bestScore ? current : best;
    });
  }

  /**
   * 计算结果分数
   */
  private calculateResultScore(result: RecognitionResult): number {
    // 获取服务权重
    const service = this.asrServices.find(s => s.getName() === result.service);
    const serviceWeight = service ? service.getConfig().weight : 1.0;
    const priorityBonus = service ? (4 - service.getConfig().priority) * 0.1 : 0; // 优先级越高分数越高

    // 长度奖励，但有上限
    const lengthBonus = Math.min(result.text.length / 20, 2); // 最多2分

    // 综合分数
    return result.confidence * serviceWeight + priorityBonus + lengthBonus;
  }

  /**
   * 计算缓冲区时长
   */
  private calculateBufferDuration(audioBuffer: Buffer[]): number {
    // 假设16kHz采样率，16位，单声道
    const totalBytes = audioBuffer.reduce((sum, buffer) => sum + buffer.length, 0);
    const totalSamples = totalBytes / 2; // 16位 = 2字节
    return (totalSamples / 16000) * 1000; // 转换为毫秒
  }

  /**
   * 检查会话是否超时
   */
  private isSessionTimeout(session: RecognitionSession): boolean {
    const timeout = this.config.maxSessionDuration;
    return Date.now() - session.lastActivityTime > timeout;
  }

  /**
   * 状态转换
   */
  private transitionTo(newState: ASRState, sessionId: string, reason: string): void {
    const oldState = this.state;
    this.state = newState;
    
    console.log(`[${sessionId}] ASR State transition: ${oldState} -> ${newState} (${reason})`);
    
    // 发出状态变化事件
    this.emitStateChange(sessionId, oldState, newState, reason);
  }

  /**
   * 获取或创建会话
   */
  private getOrCreateSession(sessionId: string): RecognitionSession {
    if (!this.recognitionSessions.has(sessionId)) {
      const session: RecognitionSession = {
        sessionId,
        state: ASRState.IDLE,
        audioBuffer: [],
        startTime: Date.now(),
        lastActivityTime: Date.now(),
        vadInfo: [],
        partialResults: [],
        contextHistory: [],
        statistics: {
          totalSegments: 0,
          totalAudioDuration: 0,
          averageConfidence: 0,
          serviceUsage: {},
          errorCount: 0,
          lastUpdated: Date.now()
        }
      };
      this.recognitionSessions.set(sessionId, session);
      console.log(`[${sessionId}] New recognition session created`);
    }
    return this.recognitionSessions.get(sessionId)!;
  }

  /**
   * 清理会话
   */
  private cleanupSession(session: RecognitionSession): void {
    session.audioBuffer = [];
    session.vadInfo = [];
    session.partialResults = [];
    session.currentSegment = undefined;
  }

  /**
   * 清理过期会话
   */
  private cleanupExpiredSessions(): void {
    const now = Date.now();
    const expiredSessions: string[] = [];
    
    for (const [sessionId, session] of this.recognitionSessions.entries()) {
      if (now - session.lastActivityTime > this.config.maxSessionDuration * 2) {
        expiredSessions.push(sessionId);
      }
    }
    
    expiredSessions.forEach(sessionId => {
      this.recognitionSessions.delete(sessionId);
      console.log(`[${sessionId}] Expired session cleaned up`);
    });
  }

  /**
   * 发出状态变化事件
   */
  private emitStateChange(sessionId: string, fromState: ASRState, toState: ASRState, reason: string): void {
    const event: StateChangeEvent = {
      type: 'state_change',
      sessionId,
      timestamp: Date.now(),
      data: { fromState, toState, reason }
    };
    this.emit('state_change', event);
  }

  /**
   * 发出结果就绪事件
   */
  private emitResultReady(sessionId: string, result: RecognitionResult, isPartial: boolean): void {
    console.log(`🚀 [${sessionId}] Emitting result_ready event:`, {
      text: result.text.substring(0, 50) + '...',
      confidence: result.confidence,
      service: result.service,
      isPartial,
      segmentId: result.segmentId
    });

    const event: ResultReadyEvent = {
      type: 'result_ready',
      sessionId,
      timestamp: Date.now(),
      data: { result, isPartial }
    };

    this.emit('result_ready', event);
    console.log(`✅ [${sessionId}] result_ready event emitted successfully`);
  }

  /**
   * 发出错误事件
   */
  private emitError(sessionId: string, error: ASRError): void {
    const event = {
      type: 'error',
      sessionId,
      timestamp: Date.now(),
      data: { error, context: { state: this.state } }
    };
    this.emit('error', event);
  }

  /**
   * 获取会话状态
   */
  public getSessionState(sessionId: string): ASRState {
    const session = this.recognitionSessions.get(sessionId);
    return session ? this.state : ASRState.IDLE;
  }

  /**
   * 获取会话统计
   */
  public getSessionStatistics(sessionId: string): SessionStatistics | null {
    const session = this.recognitionSessions.get(sessionId);
    return session ? session.statistics : null;
  }

  /**
   * 获取当前状态
   */
  public getCurrentState(): ASRState {
    return this.state;
  }

  /**
   * 更新配置
   */
  public updateConfig(newConfig: Partial<StreamingConfig>): void {
    this.config = { ...this.config, ...newConfig };
    console.log('StreamingASRManager config updated:', this.config);
  }

  /**
   * 销毁管理器
   */
  public destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    this.recognitionSessions.clear();
    this.removeAllListeners();
    console.log('StreamingASRManager destroyed');
  }
}
