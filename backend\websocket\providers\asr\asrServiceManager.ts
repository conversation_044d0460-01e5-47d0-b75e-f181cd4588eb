// ASR服务管理器 - DashScope主导策略
// 🔥 重构：采用阿里云DashScope作为主要识别引擎
import { ASRServiceInterface, ASRResult, ASROptions, ASRProvider, ASRError } from '../../../types/asr.js';
import { DashScopeProvider } from './dashscopeProvider.js';
// 🚫 备用ASR服务 (暂时注释，日后可启用)
// import { AlibabaProvider } from './alibabaProvider.js';
// import { BaiduProvider } from './baiduProvider.js';
import { WhisperProvider } from './whisperProvider.js';
// 移除会话连接管理器依赖，与参考代码保持一致

export class ASRServiceManager {
  private providers: ASRServiceInterface[] = [];
  private isInitialized = false;

  constructor() {
    this.initializeProviders();
  }

  /**
   * 🔥 获取DashScope提供商（与参考代码一致）
   */
  private getDashScopeProvider(): DashScopeProvider | null {
    const dashscopeProvider = this.providers.find(p => p.config.provider === ASRProvider.DASHSCOPE);
    return dashscopeProvider as DashScopeProvider || null;
  }

  /**
   * 🔥 重构：初始化ASR提供商 - DashScope主导
   */
  private initializeProviders(): void {
    try {
      // 🚀 阿里云DashScope - 主要识别引擎 (优先级1)
      if (this.hasDashScopeConfig()) {
        this.providers.push(new DashScopeProvider());
        console.log('✅ DashScope ASR provider initialized (primary engine)');
      } else {
        console.warn('⚠️ DashScope ASR config missing, skipping primary engine');
      }

      // 🔄 OpenAI Whisper - 备用引擎 (优先级2)
      if (this.hasWhisperConfig()) {
        this.providers.push(new WhisperProvider());
        console.log('✅ OpenAI Whisper provider initialized (backup engine)');
      } else {
        console.warn('⚠️ OpenAI Whisper config missing, skipping backup engine');
      }

      // 🚫 其他ASR服务暂时注释 (日后可启用)

      // 阿里云旧版NLS ASR - 备用引擎
      // if (this.hasAlibabaConfig()) {
      //   this.providers.push(new AlibabaProvider());
      //   console.log('✅ Alibaba NLS ASR provider initialized (backup)');
      // }

      // 百度ASR - 备用引擎
      // if (this.hasBaiduConfig()) {
      //   this.providers.push(new BaiduProvider());
      //   console.log('✅ Baidu ASR provider initialized (backup)');
      // }

      this.isInitialized = true;
      console.log(`🎯 ASR Service Manager initialized with ${this.providers.length} providers (DashScope-first strategy)`);
    } catch (error) {
      console.error('❌ Error initializing ASR providers:', error);
      this.isInitialized = false;
    }
  }

  /**
   * 🔥 检查DashScope配置 (主要引擎)
   */
  private hasDashScopeConfig(): boolean {
    return !!process.env.DASHSCOPE_API_KEY;
  }

  /**
   * 检查Whisper配置 (备用引擎)
   */
  private hasWhisperConfig(): boolean {
    return !!process.env.OPENAI_API_KEY;
  }

  // 🚫 备用ASR服务配置检查 (暂时注释，日后可启用)

  /**
   * 检查阿里旧版NLS配置
   */
  // private hasAlibabaConfig(): boolean {
  //   return !!(
  //     process.env.ALIBABA_APPKEY &&
  //     process.env.ALIBABA_ACCESS_KEY_ID &&
  //     process.env.ALIBABA_ACCESS_KEY_SECRET
  //   );
  // }

  /**
   * 检查百度配置
   */
  // private hasBaiduConfig(): boolean {
  //   return !!(
  //     process.env.BAIDU_APP_ID &&
  //     process.env.BAIDU_CLIENT_ID &&
  //     process.env.BAIDU_CLIENT_SECRET
  //   );
  // }

  /**
   * 🔥 重构：DashScope主导的ASR识别策略
   * 优先使用DashScope，失败时使用备用服务
   */
  async recognize(audioBuffer: Buffer, options?: ASROptions): Promise<ASRResult> {
    if (!this.isInitialized || this.providers.length === 0) {
      const error = new Error('No ASR providers available') as ASRError;
      error.provider = ASRProvider.DASHSCOPE; // 默认提供商改为DashScope
      error.code = 'NO_PROVIDERS';
      error.retryable = false;
      throw error;
    }

    console.log(`🚀 Starting DashScope-first ASR recognition with ${this.providers.length} providers`);
    const startTime = Date.now();

    // 过滤可用的提供商
    const availableProviders = this.providers.filter(provider => provider.isAvailable());
    
    if (availableProviders.length === 0) {
      console.error('❌ No available ASR providers found');
      await this.logAllProvidersFailure('All providers unavailable');
      throw this.createComprehensiveError('All ASR providers are currently unavailable');
    }

    // 🔥 DashScope优先策略：先尝试DashScope，失败时使用备用服务
    const dashscopeProvider = availableProviders.find(p => p.getName() === ASRProvider.DASHSCOPE);

    if (dashscopeProvider) {
      try {
        console.log(`🎯 Trying primary engine: DashScope`);
        const result = await dashscopeProvider.recognize(audioBuffer, options);
        const totalTime = Date.now() - startTime;

        console.log(`🎉 DashScope recognition completed successfully in ${totalTime}ms`);
        console.log(`📝 Primary result: "${result.text}"`);

        return {
          ...result,
          processingTime: totalTime,
          provider: ASRProvider.DASHSCOPE
        };
      } catch (error) {
        console.warn(`⚠️ DashScope failed, falling back to backup services:`, error);
      }
    }

    // 如果DashScope失败或不可用，使用备用服务
    const backupProviders = availableProviders.filter(p => p.getName() !== ASRProvider.DASHSCOPE);

    if (backupProviders.length === 0) {
      console.error('❌ No backup ASR providers available');
      await this.logAllProvidersFailure('DashScope failed and no backup providers available');
      throw this.createComprehensiveError('Primary ASR service failed and no backup services available');
    }

    console.log(`🔄 Using ${backupProviders.length} backup providers for recognition`);

    // 并行调用备用服务
    const recognitionPromises = backupProviders.map(async (provider, index) => {
      const providerStartTime = Date.now();
      try {
        console.log(`[Backup ${index + 1}] Starting ${provider.getName()} recognition`);
        const result = await provider.recognize(audioBuffer, options);
        const processingTime = Date.now() - providerStartTime;

        console.log(`✅ [Backup ${index + 1}] ${provider.getName()} completed in ${processingTime}ms: "${result.text.substring(0, 50)}..."`);

        return {
          ...result,
          processingTime,
          provider: provider.getName() as ASRProvider
        };
      } catch (error) {
        const processingTime = Date.now() - providerStartTime;
        console.error(`❌ [Backup ${index + 1}] ${provider.getName()} failed in ${processingTime}ms:`, error);

        const asrError = error as ASRError;
        asrError.provider = provider.getName() as ASRProvider;
        throw asrError;
      }
    });

    try {
      // 使用Promise.any获取最快的成功结果
      const result = await Promise.any(recognitionPromises);
      const totalTime = Date.now() - startTime;

      console.log(`🎉 Backup ASR recognition completed successfully in ${totalTime}ms by ${result.provider}`);
      console.log(`📝 Backup result: "${result.text}"`);

      return result;
    } catch (aggregateError) {
      // 所有提供商都失败了
      const totalTime = Date.now() - startTime;
      console.error(`❌ All ASR providers (including backups) failed after ${totalTime}ms`);

      await this.logAllProvidersFailure('All recognition attempts failed', aggregateError);
      throw this.createComprehensiveError('All ASR services failed to process the audio', aggregateError);
    }
  }

  /**
   * 记录所有提供商失败的详细日志
   */
  private async logAllProvidersFailure(message: string, aggregateError?: any): Promise<void> {
    console.error('🚨 === ASR COMPLETE FAILURE REPORT ===');
    console.error(`📅 Timestamp: ${new Date().toISOString()}`);
    console.error(`📋 Reason: ${message}`);
    console.error(`🔢 Total providers attempted: ${this.providers.length}`);
    
    // 记录每个提供商的状态
    this.providers.forEach((provider, index) => {
      console.error(`[${index + 1}] ${provider.getName()}: ${provider.isAvailable() ? 'Available' : 'Unavailable'}`);
    });

    // 记录具体错误信息
    if (aggregateError && aggregateError.errors) {
      console.error('📝 Individual provider errors:');
      aggregateError.errors.forEach((error: any, index: number) => {
        console.error(`  [${index + 1}] ${error.provider || 'Unknown'}: ${error.message}`);
      });
    }
    
    console.error('🚨 === END FAILURE REPORT ===');
  }

  /**
   * 创建综合错误信息
   */
  private createComprehensiveError(message: string, aggregateError?: any): ASRError {
    const error = new Error(message) as ASRError;
    error.provider = ASRProvider.DASHSCOPE; // 默认改为DashScope
    error.code = 'ALL_PROVIDERS_FAILED';
    error.retryable = true;
    
    if (aggregateError && aggregateError.errors) {
      const errorDetails = aggregateError.errors.map((err: any) => 
        `${err.provider || 'Unknown'}: ${err.message}`
      ).join('; ');
      error.message = `${message}. Details: ${errorDetails}`;
    }
    
    return error;
  }

  /**
   * 获取可用提供商列表
   */
  getAvailableProviders(): ASRServiceInterface[] {
    return this.providers.filter(provider => provider.isAvailable());
  }

  /**
   * 获取所有提供商列表
   */
  getAllProviders(): ASRServiceInterface[] {
    return [...this.providers];
  }

  /**
   * 获取提供商状态报告
   */
  getStatusReport(): { [key: string]: string } {
    const report: { [key: string]: string } = {};
    
    this.providers.forEach(provider => {
      const name = provider.getName().toLowerCase();
      const isAvailable = provider.isAvailable();
      const config = provider.getConfig();
      
      report[name] = isAvailable 
        ? `正常工作 - 优先级${config.priority}，超时${config.timeout}ms`
        : '服务不可用 - 配置缺失或服务异常';
    });
    
    return report;
  }

  /**
   * 销毁所有提供商
   */
  async destroy(): Promise<void> {
    console.log('🧹 Destroying ASR Service Manager');
    
    const destroyPromises = this.providers.map(async (provider) => {
      try {
        await provider.destroy();
        console.log(`✅ ${provider.getName()} provider destroyed`);
      } catch (error) {
        console.error(`❌ Error destroying ${provider.getName()} provider:`, error);
      }
    });
    
    await Promise.allSettled(destroyPromises);
    this.providers = [];
    this.isInitialized = false;
    
    console.log('✅ ASR Service Manager destroyed');
  }
}
