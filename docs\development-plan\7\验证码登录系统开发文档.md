# 面试君验证码登录系统开发文档

**版本**: 1.0  
**创建日期**: 2025-01-09  
**项目**: 面试君 (MianshiJun)  
**开发阶段**: 验证码登录重构

## 目录

1. [项目概述](#1-项目概述)
2. [技术架构设计](#2-技术架构设计)
3. [数据库设计](#3-数据库设计)
4. [API接口设计](#4-api接口设计)
5. [前端UI设计](#5-前端ui设计)
6. [安全机制设计](#6-安全机制设计)
7. [开发任务分解](#7-开发任务分解)
8. [测试方案](#8-测试方案)
9. [部署配置](#9-部署配置)
10. [风险评估与应对](#10-风险评估与应对)

---

## 1. 项目概述

### 1.1 背景与目标

当前面试君项目使用传统的邮箱密码登录方式，存在以下安全风险：
- 密码泄露风险
- 暴力破解攻击
- 用户密码管理负担

**重构目标**：
- 实现邮箱验证码登录（方案一）
- 实现手机短信验证码登录（方案二）
- 提升系统安全性
- 改善用户体验

### 1.2 功能范围

**核心功能**：
- 邮箱验证码发送与验证
- 手机短信验证码发送与验证
- 验证码登录流程
- 安全防护机制

**非功能需求**：
- 验证码5分钟有效期
- 1分钟内限制重复发送
- 3次错误后锁定10分钟
- 支持现有UI设计风格

### 1.3 技术选型

**后端技术栈**：
- Node.js + Express.js
- Prisma ORM
- Nodemailer (邮件服务)
- 阿里云短信服务 (SMS)
- Redis (验证码缓存)
- JWT (身份认证)

**前端技术栈**：
- React 18 + TypeScript
- Tailwind CSS
- Lucide React (图标)
- Zod (表单验证)
- 现有Toast通知系统

---

## 2. 技术架构设计

### 2.1 整体架构图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端 (React)   │    │   后端 (Node.js) │    │   外部服务       │
│                 │    │                 │    │                 │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │ 登录页面     │ │◄──►│ │ 验证码API   │ │◄──►│ │ 邮件服务     │ │
│ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │ Toast通知   │ │    │ │ 认证中间件   │ │    │ │ 阿里云短信   │ │
│ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │ 状态管理     │ │    │ │ 数据库层     │ │    │ │ Redis缓存   │ │
│ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 2.2 核心模块设计

**验证码服务模块** (`backend/services/verificationService.ts`):
- 验证码生成与验证
- 发送频率限制
- 错误次数统计
- 缓存管理

**邮件服务模块** (`backend/services/emailService.ts`):
- SMTP配置管理
- 邮件模板渲染
- 发送状态跟踪

**短信服务模块** (`backend/services/smsService.ts`):
- 阿里云短信API集成
- 短信模板管理
- 发送记录

**认证服务模块** (`backend/services/authService.ts`):
- 验证码登录逻辑
- JWT token生成
- 用户状态管理

### 2.3 数据流设计

```
用户输入邮箱/手机号 → 前端验证 → 调用发送验证码API → 
生成验证码 → 存储到Redis → 发送邮件/短信 → 
用户输入验证码 → 前端验证 → 调用验证API → 
验证码校验 → 生成JWT → 登录成功
```

---

## 3. 数据库设计

### 3.1 现有表结构分析

当前User表结构：
```sql
model User {
  id                String             @id @default(cuid())
  email             String             @unique
  password          String
  name              String?
  phoneNumber       String?            // 已存在手机号字段
  role              UserRole           @default(USER)
  createdAt         DateTime           @default(now())
  updatedAt         DateTime           @updatedAt
  // ... 其他关联字段
}
```

### 3.2 新增表结构

**验证码记录表** (`VerificationCode`):
```sql
model VerificationCode {
  id            String              @id @default(cuid())
  identifier    String              // 邮箱或手机号
  code          String              // 验证码
  type          VerificationType    // EMAIL | SMS
  purpose       VerificationPurpose // LOGIN | REGISTER | RESET_PASSWORD
  expiresAt     DateTime            // 过期时间
  attempts      Int                 @default(0) // 尝试次数
  isUsed        Boolean             @default(false) // 是否已使用
  createdAt     DateTime            @default(now())
  updatedAt     DateTime            @updatedAt
  
  @@index([identifier, type, purpose])
  @@index([expiresAt])
}

enum VerificationType {
  EMAIL
  SMS
}

enum VerificationPurpose {
  LOGIN
  REGISTER
  RESET_PASSWORD
}
```

**发送记录表** (`VerificationLog`):
```sql
model VerificationLog {
  id            String              @id @default(cuid())
  identifier    String              // 邮箱或手机号
  type          VerificationType    // EMAIL | SMS
  purpose       VerificationPurpose // 用途
  status        SendStatus          // 发送状态
  errorMessage  String?             // 错误信息
  ipAddress     String?             // 发送IP
  userAgent     String?             // 用户代理
  createdAt     DateTime            @default(now())
  
  @@index([identifier, createdAt])
  @@index([createdAt])
}

enum SendStatus {
  SUCCESS
  FAILED
  PENDING
}
```

### 3.3 数据库迁移脚本

```sql
-- 创建验证码记录表
CREATE TABLE "VerificationCode" (
    "id" TEXT NOT NULL,
    "identifier" TEXT NOT NULL,
    "code" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "purpose" TEXT NOT NULL,
    "expiresAt" TIMESTAMP(3) NOT NULL,
    "attempts" INTEGER NOT NULL DEFAULT 0,
    "isUsed" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "VerificationCode_pkey" PRIMARY KEY ("id")
);

-- 创建发送记录表
CREATE TABLE "VerificationLog" (
    "id" TEXT NOT NULL,
    "identifier" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "purpose" TEXT NOT NULL,
    "status" TEXT NOT NULL,
    "errorMessage" TEXT,
    "ipAddress" TEXT,
    "userAgent" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "VerificationLog_pkey" PRIMARY KEY ("id")
);

-- 创建索引
CREATE INDEX "VerificationCode_identifier_type_purpose_idx" ON "VerificationCode"("identifier", "type", "purpose");
CREATE INDEX "VerificationCode_expiresAt_idx" ON "VerificationCode"("expiresAt");
CREATE INDEX "VerificationLog_identifier_createdAt_idx" ON "VerificationLog"("identifier", "createdAt");
CREATE INDEX "VerificationLog_createdAt_idx" ON "VerificationLog"("createdAt");
```

---

## 4. API接口设计

### 4.1 发送验证码接口

**接口路径**: `POST /api/auth/send-verification-code`

**请求参数**:
```typescript
interface SendCodeRequest {
  identifier: string;    // 邮箱或手机号
  type: 'EMAIL' | 'SMS'; // 验证码类型
  purpose: 'LOGIN';      // 用途（登录）
}
```

**响应格式**:
```typescript
interface SendCodeResponse {
  success: boolean;
  message: string;
  data?: {
    expiresIn: number;     // 过期时间（秒）
    canResendAfter: number; // 可重新发送时间（秒）
  };
  error?: {
    code: string;
    details?: any;
  };
}
```

**错误码定义**:
- `RATE_LIMITED`: 发送频率限制
- `INVALID_IDENTIFIER`: 无效的邮箱或手机号
- `SERVICE_ERROR`: 服务异常
- `USER_NOT_FOUND`: 用户不存在（登录场景）

### 4.2 验证码登录接口

**接口路径**: `POST /api/auth/login-with-code`

**请求参数**:
```typescript
interface LoginWithCodeRequest {
  identifier: string;    // 邮箱或手机号
  code: string;         // 验证码
  type: 'EMAIL' | 'SMS'; // 验证码类型
}
```

**响应格式**:
```typescript
interface LoginWithCodeResponse {
  success: boolean;
  message: string;
  data?: {
    token: string;        // JWT token
    user: {
      id: string;
      email: string;
      name?: string;
      phoneNumber?: string;
    };
    expiresIn: number;    // token过期时间
  };
  error?: {
    code: string;
    details?: any;
  };
}
```

**错误码定义**:
- `INVALID_CODE`: 验证码错误
- `CODE_EXPIRED`: 验证码已过期
- `CODE_USED`: 验证码已使用
- `TOO_MANY_ATTEMPTS`: 尝试次数过多
- `USER_NOT_FOUND`: 用户不存在

### 4.3 验证码状态查询接口

**接口路径**: `GET /api/auth/verification-status`

**请求参数**:
```typescript
interface StatusRequest {
  identifier: string;
  type: 'EMAIL' | 'SMS';
}
```

**响应格式**:
```typescript
interface StatusResponse {
  success: boolean;
  data: {
    canSend: boolean;      // 是否可以发送
    remainingTime: number; // 剩余等待时间（秒）
    attempts: number;      // 已尝试次数
    maxAttempts: number;   // 最大尝试次数
    isLocked: boolean;     // 是否被锁定
    lockExpires?: number;  // 锁定过期时间
  };
}
```

---

## 5. 前端UI设计

### 5.1 设计原则

严格遵循项目现有的UI设计风格：
- 使用Tailwind CSS样式系统
- 遵循`design_system.md`规范
- 使用Lucide React图标库
- 集成现有Toast通知系统
- 保持与`StandaloneLoginPage.tsx`一致的视觉风格

### 5.2 组件结构设计

**验证码输入组件** (`VerificationCodeInput.tsx`):
```typescript
interface VerificationCodeInputProps {
  length?: number;        // 验证码长度，默认6位
  value: string;
  onChange: (value: string) => void;
  disabled?: boolean;
  error?: string;
  autoFocus?: boolean;
}
```

**发送验证码按钮组件** (`SendCodeButton.tsx`):
```typescript
interface SendCodeButtonProps {
  identifier: string;     // 邮箱或手机号
  type: 'EMAIL' | 'SMS';
  disabled?: boolean;
  onSendSuccess?: () => void;
  onSendError?: (error: string) => void;
}
```

### 5.3 页面布局设计

基于现有`StandaloneLoginPage.tsx`的设计，添加验证码登录选项：

```typescript
// 登录方式选择
const loginMethods = [
  { key: 'email-code', label: '邮箱验证码', icon: Mail },
  { key: 'sms-code', label: '短信验证码', icon: Phone },
  { key: 'password', label: '密码登录', icon: Lock }
];
```

**UI状态管理**:
```typescript
interface LoginState {
  activeMethod: 'email-code' | 'sms-code' | 'password';
  identifier: string;      // 邮箱或手机号
  code: string;           // 验证码
  password: string;       // 密码（兼容模式）
  isLoading: boolean;
  canResend: boolean;
  countdown: number;      // 重发倒计时
  error: string | null;
  success: string | null;
}
```

### 5.4 Toast通知集成

使用现有的Toast系统显示各种状态：

```typescript
import { useToastContext } from '../contexts/ToastContext';

const { showSuccess, showError, showInfo, showWarning } = useToastContext();

// 验证码发送成功
showSuccess('验证码已发送，请查收');

// 验证码发送失败
showError('验证码发送失败，请稍后重试');

// 频率限制提醒
showWarning('发送过于频繁，请稍后再试');

// 登录成功
showSuccess('登录成功！正在跳转...');
```

### 5.5 表单验证规则

使用Zod进行表单验证：

```typescript
// 邮箱验证码登录验证
export const emailCodeLoginSchema = z.object({
  email: z
    .string()
    .min(1, { message: '邮箱是必填项' })
    .email({ message: '请输入有效的邮箱地址' }),
  code: z
    .string()
    .min(6, { message: '请输入6位验证码' })
    .max(6, { message: '验证码为6位数字' })
    .regex(/^\d{6}$/, { message: '验证码只能包含数字' })
});

// 手机验证码登录验证
export const smsCodeLoginSchema = z.object({
  phoneNumber: z
    .string()
    .min(1, { message: '手机号是必填项' })
    .regex(/^1[3-9]\d{9}$/, { message: '请输入有效的手机号' }),
  code: z
    .string()
    .min(6, { message: '请输入6位验证码' })
    .max(6, { message: '验证码为6位数字' })
    .regex(/^\d{6}$/, { message: '验证码只能包含数字' })
});
```

---

## 6. 安全机制设计

### 6.1 验证码安全

**生成规则**:
- 6位纯数字验证码
- 使用加密安全的随机数生成器
- 避免连续数字和重复数字

**存储安全**:
- 验证码加密存储到Redis
- 设置5分钟TTL自动过期
- 使用后立即删除

**验证安全**:
- 最多允许3次验证尝试
- 失败3次后锁定10分钟
- 验证成功后立即失效

### 6.2 发送频率限制

**时间限制**:
- 同一标识符1分钟内只能发送1次
- 同一IP地址1分钟内最多发送5次
- 每日发送上限：同一标识符20次

**实现方式**:
```typescript
// Redis键名设计
const RATE_LIMIT_KEYS = {
  IDENTIFIER: (identifier: string) => `rate_limit:identifier:${identifier}`,
  IP: (ip: string) => `rate_limit:ip:${ip}`,
  DAILY: (identifier: string) => `rate_limit:daily:${identifier}`
};
```

### 6.3 防刷机制

**IP限制**:
- 单IP每小时最多发送50次验证码
- 异常IP自动加入黑名单

**设备指纹**:
- 记录User-Agent和其他浏览器特征
- 检测异常设备行为

**图形验证码**:
- 发送失败3次后要求图形验证码
- 防止自动化攻击

### 6.4 数据安全

**敏感信息保护**:
- 验证码不记录到日志
- 手机号和邮箱脱敏显示
- API响应不包含敏感信息

**传输安全**:
- 强制HTTPS传输
- 请求签名验证
- 防重放攻击

---

## 7. 开发任务分解

### 7.1 后端开发任务

**阶段一：基础架构搭建（2-3天）**
- [ ] 数据库表结构设计和迁移
- [ ] Redis连接配置
- [ ] 基础服务类架构搭建
- [ ] 环境变量配置

**阶段二：邮箱验证码功能（3-4天）**
- [ ] 邮件服务配置（Nodemailer + SMTP）
- [ ] 验证码生成和存储逻辑
- [ ] 发送验证码API实现
- [ ] 验证码登录API实现
- [ ] 频率限制和安全机制

**阶段三：短信验证码功能（2-3天）**
- [ ] 阿里云短信服务集成
- [ ] 短信模板配置
- [ ] 短信发送逻辑实现
- [ ] 手机号验证和格式化

**阶段四：安全增强（2天）**
- [ ] IP限制和黑名单机制
- [ ] 异常检测和报警
- [ ] 日志记录和监控
- [ ] 错误处理优化

### 7.2 前端开发任务

**阶段一：UI组件开发（2-3天）**
- [ ] 验证码输入组件
- [ ] 发送验证码按钮组件
- [ ] 登录方式切换组件
- [ ] 表单验证规则

**阶段二：页面集成（2天）**
- [ ] 修改StandaloneLoginPage组件
- [ ] 添加验证码登录选项
- [ ] 状态管理和流程控制
- [ ] Toast通知集成

**阶段三：API集成（1-2天）**
- [ ] 验证码发送API调用
- [ ] 验证码登录API调用
- [ ] 错误处理和用户反馈
- [ ] 加载状态管理

**阶段四：测试和优化（1天）**
- [ ] 组件单元测试
- [ ] 集成测试
- [ ] 用户体验优化
- [ ] 响应式适配

### 7.3 配置和部署任务

**环境配置（1天）**
- [ ] 邮件服务配置
- [ ] 阿里云短信服务配置
- [ ] Redis服务配置
- [ ] 环境变量管理

**部署和监控（1天）**
- [ ] 生产环境部署
- [ ] 监控和日志配置
- [ ] 性能测试
- [ ] 安全测试

---

## 8. 测试方案

### 8.1 单元测试

**后端测试**:
- 验证码生成和验证逻辑
- 邮件和短信发送服务
- 频率限制机制
- 安全防护功能

**前端测试**:
- 验证码输入组件
- 表单验证逻辑
- API调用和错误处理
- 用户交互流程

### 8.2 集成测试

**API测试**:
- 验证码发送流程
- 登录认证流程
- 错误场景处理
- 并发请求测试

**端到端测试**:
- 完整登录流程
- 多设备兼容性
- 网络异常处理
- 用户体验测试

### 8.3 安全测试

**渗透测试**:
- 验证码暴力破解
- 频率限制绕过
- SQL注入和XSS
- CSRF攻击防护

**压力测试**:
- 高并发验证码发送
- 大量用户同时登录
- 系统资源消耗
- 性能瓶颈分析

---

## 9. 部署配置

### 9.1 环境变量配置

```bash
# 邮件服务配置
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
SMTP_FROM=面试君 <<EMAIL>>

# 阿里云短信配置
ALIBABA_ACCESS_KEY_ID=your-access-key-id
ALIBABA_ACCESS_KEY_SECRET=your-access-key-secret
ALIBABA_SMS_SIGN_NAME=面试君
ALIBABA_SMS_TEMPLATE_CODE=SMS_123456789

# Redis配置
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=your-redis-password

# 安全配置
JWT_SECRET=your-jwt-secret-key
RATE_LIMIT_WINDOW=60000
RATE_LIMIT_MAX_REQUESTS=5
```

### 9.2 服务依赖

**必需服务**:
- Redis服务器（验证码缓存）
- SMTP邮件服务（Gmail/QQ邮箱/阿里云）
- 阿里云短信服务

**可选服务**:
- 监控服务（如Sentry）
- 日志服务（如ELK Stack）
- 缓存服务（如Memcached）

---

## 10. 风险评估与应对

### 10.1 技术风险

**风险**: 邮件服务不稳定
**影响**: 验证码发送失败
**应对**: 配置多个SMTP服务商，实现故障转移

**风险**: 短信服务费用超支
**影响**: 成本控制失效
**应对**: 设置日发送量上限，实现费用预警

**风险**: Redis服务异常
**影响**: 验证码功能不可用
**应对**: 配置Redis集群，实现高可用

### 10.2 安全风险

**风险**: 验证码被恶意获取
**影响**: 账户安全威胁
**应对**: 加强传输加密，限制验证码有效期

**风险**: 大量恶意请求
**影响**: 服务资源耗尽
**应对**: 实现多层防护，IP黑名单机制

### 10.3 业务风险

**风险**: 用户接受度低
**影响**: 用户流失
**应对**: 保留密码登录选项，逐步引导迁移

**风险**: 验证码到达率低
**影响**: 用户无法登录
**应对**: 提供多种验证方式，优化发送成功率

---

## 总结

本文档详细规划了面试君验证码登录系统的开发方案，涵盖了技术架构、数据库设计、API接口、前端UI、安全机制等各个方面。通过分阶段的开发计划和完善的测试方案，确保系统的安全性、稳定性和用户体验。

**预计开发周期**: 2-3周  
**核心开发人员**: 1-2人  
**主要技术难点**: 安全防护机制、多服务集成、用户体验优化

项目实施过程中需要密切关注安全性和用户体验，确保新系统能够平稳替代现有的密码登录方式。

---

## 附录：相关文档

本开发文档包含以下配套文档，请按需查阅：

### 📋 核心文档
- **[验证码登录系统开发文档.md](./验证码登录系统开发文档.md)** - 主要开发文档（当前文档）
- **[实现细节补充文档.md](./实现细节补充文档.md)** - 后端核心服务实现代码示例
- **[前端组件实现示例.md](./前端组件实现示例.md)** - 前端UI组件完整实现代码

### 🚀 部署运维
- **[环境配置与部署指南.md](./环境配置与部署指南.md)** - 完整的环境配置和部署流程
- **[测试方案与质量保证.md](./测试方案与质量保证.md)** - 全面的测试策略和质量保证方案

### 📚 参考资料
- **[Toast开发教程.md](../Others/Toast开发教程.md)** - 项目Toast通知系统使用指南
- **[design_system.md](../../design/design_system.md)** - 项目UI设计系统规范
- **[technical_architecture.md](../../design/technical_architecture.md)** - 项目技术架构文档

### 🔧 开发工具
建议使用以下工具提升开发效率：
- **Postman/Insomnia**: API接口测试
- **Redis Desktop Manager**: Redis数据查看
- **Prisma Studio**: 数据库可视化管理
- **VS Code Extensions**:
  - Prisma
  - Tailwind CSS IntelliSense
  - ES7+ React/Redux/React-Native snippets

### 📞 技术支持
如在开发过程中遇到问题，请参考：
1. 项目现有代码实现模式
2. 相关技术官方文档
3. 团队内部技术分享

---

**开发愉快！** 🎉
