import type { VercelRequest, VercelResponse } from '@vercel/node';
import * as bcrypt from 'bcryptjs';
import * as dotenv from 'dotenv';
import prisma from '../lib/prisma'; // 使用共享的Prisma实例
import { referralService } from '../services/referralService';

// 加载环境变量
dotenv.config();

export default async function handler(
  request: VercelRequest,
  response: VercelResponse,
) {
  if (request.method !== 'POST') {
    return response.status(405).json({ message: 'Only POST requests allowed' });
  }

  try {
    const { email, password, name, inviteCode } = request.body;

    // 1. 基本校验 (Basic Validation)
    if (!email || !password) {
      return response.status(400).json({ message: 'Email and password are required' });
    }
    // 你可以在这里添加更复杂的校验，比如邮箱格式、密码长度等
    // You can add more complex validation here, e.g., email format, password length

    // 2. 检查邮箱是否已存在 (Check if email already exists)
    const existingUser = await prisma.user.findUnique({
      where: { email },
    });

    if (existingUser) {
      return response.status(409).json({ message: 'Email already in use' }); // 409 Conflict
    }

    // 3. 验证邀请码（如果提供）
    let isValidInvite = false;
    if (inviteCode && inviteCode.trim() !== '') {
      isValidInvite = await referralService.validateReferralCode(inviteCode.trim());
      if (!isValidInvite) {
        return response.status(400).json({ message: '邀请码无效或已过期' });
      }
    }

    // 4. 哈希密码 (Hash the password)
    const hashedPassword = await bcrypt.hash(password, 10); // 10 is the salt rounds

    // 5. 创建新用户并初始化余额 (Create new user and initialize balance)
    const newUser = await prisma.user.create({
      data: {
        email,
        password: hashedPassword,
        name: name || null, // 如果提供了名字就用，否则为null (Use name if provided, else null)
        balance: { // 同时创建关联的 UserBalance 记录 (Concurrently create the related UserBalance record)
          create: {
            // 在这里设置初始的面试次数或面巾余额，例如：
            // Set initial interview credits or Mianshijin balance here, e.g.:
            mockInterviewCredits: 2, // 赠送2次模拟面试 (Gift 2 mock interview credits)
            formalInterviewCredits: 0, // 正式面试初始为0次 (Formal interview credits start at 0)
            mianshijunBalance: 100  // 赠送100面巾 (Gift 100 Mianshijun)
          },
        },
      },
      include: { // 同时返回创建的balance信息 (Also return created balance info)
        balance: true,
      }
    });

    // 6. 如果有有效邀请码，创建邀请关系
    if (isValidInvite && inviteCode) {
      try {
        await referralService.createReferralRelation(inviteCode.trim(), newUser.id);
        console.log(`✅ 邀请关系创建成功: 新用户 ${newUser.email} 通过邀请码 ${inviteCode} 注册`);
      } catch (error) {
        console.error('创建邀请关系失败:', error);
        // 不影响注册流程，只记录错误
      }
    }

    // 为了安全，不要在响应中返回密码哈希
    // For security, do not return password hash in the response
    const { password: _, ...userWithoutPassword } = newUser;

    return response.status(201).json({
      success: true,
      message: 'User registered successfully',
      data: userWithoutPassword,
    });

  } catch (error) {
    console.error('Registration error:', error);
    // 检查是否是 Prisma 相关的已知错误 (Check if it's a known Prisma error)
    if (error instanceof Error) {
         return response.status(500).json({ message: 'Internal server error', error: error.message });
    }
    return response.status(500).json({ message: 'Internal server error' });
  } finally {
    // 注释掉 $disconnect，让连接池管理连接
    // await prisma.$disconnect(); // 关闭 Prisma Client 连接 (Close Prisma Client connection)
  }
}