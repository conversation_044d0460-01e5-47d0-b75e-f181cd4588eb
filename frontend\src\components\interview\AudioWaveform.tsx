import React, { useEffect, useRef, useState } from 'react';
import useInterviewStore from '../../stores/interviewStore';

interface AudioWaveformProps {
  isListening: boolean;
  className?: string;
}

const AudioWaveform: React.FC<AudioWaveformProps> = ({ isListening, className }) => {
  const { config } = useInterviewStore();
  const waveformRef = useRef<HTMLDivElement>(null);
  const audioContextRef = useRef<AudioContext | null>(null);
  const analyserRef = useRef<AnalyserNode | null>(null);
  const sourceNodeRef = useRef<MediaStreamAudioSourceNode | null>(null);
  const animationFrameRef = useRef<number | null>(null);
  const [waveHeights, setWaveHeights] = useState<number[]>(Array(10).fill(5));
  
  // 根据音量更新波形高度
  const updateWaveformBasedOnVolume = (volume: number) => {
    // 音量阈值，低于此值视为无声音
    const threshold = 0.01;
    
    if (volume > threshold) {
      // 有声音 - 显示动态波形
      // 基础波形模式 - 保持波形的基本形状
      const basePattern = [
        0.3, 0.5, 0.7, 0.9, 1.0, 0.9, 0.7, 0.5, 0.3, 0.2
      ];
      
      // 根据音量调整波形高度
      const newHeights = basePattern.map(baseHeight => {
        // 将音量值放大，使波形更明显
        const scaledVolume = Math.min(1, volume * 15);
        // 添加一些随机变化，使波形看起来更自然
        const randomFactor = 0.8 + Math.random() * 0.4;
        // 计算最终高度（5-30px）
        return Math.max(5, Math.min(30, Math.floor(baseHeight * scaledVolume * randomFactor * 30)));
      });
      
      setWaveHeights(newHeights);
    } else {
      // 无声音 - 显示静态波形
      setWaveHeights(Array(10).fill(5));
    }
  };
  
  // 初始化音频处理
  const initAudioProcessor = () => {
    try {
      // 检查是否有共享流可用
      if (!config.sharedStream || config.screenShareStatus !== 'sharing') {
        console.warn('无可用的共享音频流');
        setWaveHeights(Array(10).fill(5));
        return;
      }
      
      // 检查是否有音频轨道
      if (config.sharedStream.getAudioTracks().length === 0) {
        console.warn('共享流中没有音频轨道');
        setWaveHeights(Array(10).fill(5));
        return;
      }
      
      // 创建音频上下文
      audioContextRef.current = new (window.AudioContext || (window as any).webkitAudioContext)();
      
      // 创建媒体流源
      sourceNodeRef.current = audioContextRef.current.createMediaStreamSource(config.sharedStream);
      
      // 创建分析器
      analyserRef.current = audioContextRef.current.createAnalyser();
      analyserRef.current.fftSize = 256;
      const bufferLength = analyserRef.current.frequencyBinCount;
      const dataArray = new Uint8Array(bufferLength);
      
      // 连接节点
      sourceNodeRef.current.connect(analyserRef.current);
      
      // 开始分析音频
      const analyzeAudio = () => {
        if (!isListening || !analyserRef.current) return;
        
        // 获取频域数据
        analyserRef.current.getByteFrequencyData(dataArray);
        
        // 计算平均音量
        let sum = 0;
        for (let i = 0; i < bufferLength; i++) {
          sum += dataArray[i];
        }
        const average = sum / bufferLength / 255;
        
        // 更新波形显示
        updateWaveformBasedOnVolume(average);
        
        // 继续下一帧分析
        animationFrameRef.current = requestAnimationFrame(analyzeAudio);
      };
      
      analyzeAudio();
      console.log('已成功连接到共享的系统音频');
    } catch (error) {
      console.error('初始化音频处理器失败:', error);
      // 如果处理失败，使用静态波形
      setWaveHeights(Array(10).fill(5));
    }
  };
  
  // 清理音频处理
  const cleanupAudioProcessor = () => {
    try {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
        animationFrameRef.current = null;
      }

      if (sourceNodeRef.current) {
        sourceNodeRef.current.disconnect();
        sourceNodeRef.current = null;
      }

      if (audioContextRef.current && audioContextRef.current.state !== 'closed') {
        audioContextRef.current.close().catch(err => {
          console.warn('关闭音频上下文失败:', err);
        });
        audioContextRef.current = null;
      }

      analyserRef.current = null;
      console.log('AudioWaveform: 音频处理器已清理');
    } catch (error) {
      console.warn('AudioWaveform: 清理音频处理器时出错:', error);
    }
  };

  useEffect(() => {
    if (isListening) {
      // 仅当屏幕共享状态为共享中时初始化音频处理器
      if (config.screenShareStatus === 'sharing' && config.sharedStream) {
        initAudioProcessor();
      } else {
        console.warn('屏幕共享未启动或无共享流可用');
        setWaveHeights(Array(10).fill(5));
      }
    } else {
      cleanupAudioProcessor();
      // 重置波形高度
      setWaveHeights(Array(10).fill(5));
    }
    
    // 清理函数
    return () => {
      cleanupAudioProcessor();
    };
  }, [isListening, config.screenShareStatus, config.sharedStream]);

  return (
    <div className={`bg-gray-100 rounded-lg overflow-hidden ${className || ''}`}>
      <div 
        ref={waveformRef} 
        className={`h-[40px] flex items-center ${!isListening ? 'opacity-50' : ''}`}
      >
        {isListening ? (
          <div className="w-full flex justify-around items-center px-2">
            {waveHeights.map((height, i) => (
              <div 
                key={i}
                className="bg-blue-500 w-1.5 rounded-full"
                style={{
                  height: `${height}px`,
                  transition: 'height 0.2s ease-in-out'
                }}
              ></div>
            ))}
          </div>
        ) : (
          <div className="w-full h-[1px] border-t border-dashed border-gray-300"></div>
        )}
      </div>
    </div>
  );
};

export default AudioWaveform;
