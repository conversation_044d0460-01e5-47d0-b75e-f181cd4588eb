import prisma from '../lib/prisma';

// 奖励配置
const DEFAULT_REFERRAL_REWARD_AMOUNT = parseInt(process.env.REFERRAL_REWARD_AMOUNT || '100');

// 奖励结果接口
export interface RewardResult {
  success: boolean;
  rewardProcessed: boolean;
  rewardAmount?: number;
  message?: string;
}

// 奖励记录数据接口
export interface RewardRecordData {
  inviterId: string;
  inviteeId: string;
  relationId: string;
  rewardAmount: number;
  rewardType: string;
  orderId?: string;
  codeUsageId?: string;
}

/**
 * 奖励服务类
 */
export class RewardService {

  /**
   * 处理邀请奖励 - 订单支付触发
   */
  async processReferralRewardForOrder(orderId: string): Promise<RewardResult> {
    try {
      return await prisma.$transaction(async (tx) => {
        // 获取订单信息
        const order = await tx.order.findUnique({
          where: { id: orderId },
          include: { user: true }
        });

        if (!order) {
          return { success: false, rewardProcessed: false, message: '订单不存在' };
        }

        // 检查是否已处理过此订单的奖励
        const existingReward = await tx.referralReward.findFirst({
          where: { orderId }
        });

        if (existingReward) {
          return { 
            success: true, 
            rewardProcessed: false, 
            message: '此订单已处理过奖励' 
          };
        }

        // 查找邀请关系
        const referralRelation = await tx.referralRelation.findUnique({
          where: { inviteeId: order.userId },
          include: { inviter: true, invitee: true }
        });

        if (!referralRelation) {
          return { 
            success: true, 
            rewardProcessed: false, 
            message: '用户无邀请关系' 
          };
        }

        // 检查是否已发放过奖励
        if (referralRelation.rewardGranted) {
          return { 
            success: true, 
            rewardProcessed: false, 
            message: '邀请奖励已发放过' 
          };
        }

        // 发放奖励
        const rewardAmount = DEFAULT_REFERRAL_REWARD_AMOUNT;
        await this.grantReferralReward(
          referralRelation.inviterId,
          referralRelation.inviteeId,
          rewardAmount,
          {
            relationId: referralRelation.id,
            orderId,
            rewardType: 'REFERRAL_BONUS'
          },
          tx
        );

        console.log(`✅ 订单邀请奖励发放成功: ${rewardAmount}面巾值 -> ${referralRelation.inviter.email}`);

        return {
          success: true,
          rewardProcessed: true,
          rewardAmount,
          message: '邀请奖励发放成功'
        };
      });

    } catch (error) {
      console.error('处理订单邀请奖励失败:', error);
      return {
        success: false,
        rewardProcessed: false,
        message: '奖励处理失败'
      };
    }
  }

  /**
   * 处理邀请奖励 - 兑换码使用触发
   */
  async processReferralRewardForCodeUsage(codeUsageId: string): Promise<RewardResult> {
    try {
      return await prisma.$transaction(async (tx) => {
        // 获取兑换码使用记录
        const codeUsage = await tx.codeUsage.findUnique({
          where: { id: codeUsageId },
          include: { 
            user: true,
            code: true
          }
        });

        if (!codeUsage) {
          return { success: false, rewardProcessed: false, message: '兑换记录不存在' };
        }

        // 检查是否已处理过此兑换的奖励
        const existingReward = await tx.referralReward.findFirst({
          where: { codeUsageId }
        });

        if (existingReward) {
          return { 
            success: true, 
            rewardProcessed: false, 
            message: '此兑换已处理过奖励' 
          };
        }

        // 查找邀请关系
        const referralRelation = await tx.referralRelation.findUnique({
          where: { inviteeId: codeUsage.userId },
          include: { inviter: true, invitee: true }
        });

        if (!referralRelation) {
          return { 
            success: true, 
            rewardProcessed: false, 
            message: '用户无邀请关系' 
          };
        }

        // 检查是否已发放过奖励
        if (referralRelation.rewardGranted) {
          return { 
            success: true, 
            rewardProcessed: false, 
            message: '邀请奖励已发放过' 
          };
        }

        // 检查兑换的权益类型是否符合奖励条件
        const validBenefitTypes = ['POINTS', 'MOCK_INTERVIEW', 'FORMAL_INTERVIEW'];
        if (!validBenefitTypes.includes(codeUsage.benefitType)) {
          return { 
            success: true, 
            rewardProcessed: false, 
            message: '兑换类型不符合奖励条件' 
          };
        }

        // 发放奖励
        const rewardAmount = DEFAULT_REFERRAL_REWARD_AMOUNT;
        await this.grantReferralReward(
          referralRelation.inviterId,
          referralRelation.inviteeId,
          rewardAmount,
          {
            relationId: referralRelation.id,
            codeUsageId,
            rewardType: 'REFERRAL_BONUS'
          },
          tx
        );

        console.log(`✅ 兑换码邀请奖励发放成功: ${rewardAmount}面巾值 -> ${referralRelation.inviter.email}`);

        return {
          success: true,
          rewardProcessed: true,
          rewardAmount,
          message: '邀请奖励发放成功'
        };
      });

    } catch (error) {
      console.error('处理兑换码邀请奖励失败:', error);
      return {
        success: false,
        rewardProcessed: false,
        message: '奖励处理失败'
      };
    }
  }

  /**
   * 发放邀请奖励（内部方法）
   */
  private async grantReferralReward(
    inviterId: string, 
    inviteeId: string, 
    amount: number,
    options: {
      relationId: string;
      orderId?: string;
      codeUsageId?: string;
      rewardType?: string;
    },
    tx?: any
  ): Promise<void> {
    const prismaClient = tx || prisma;

    try {
      // 创建奖励记录
      await prismaClient.referralReward.create({
        data: {
          inviterId,
          inviteeId,
          relationId: options.relationId,
          rewardAmount: amount,
          rewardType: options.rewardType || 'REFERRAL_BONUS',
          orderId: options.orderId,
          codeUsageId: options.codeUsageId,
          status: 'COMPLETED',
          processedAt: new Date()
        }
      });

      // 更新用户余额
      await this.updateUserBalance(inviterId, amount, prismaClient);

      // 更新邀请关系状态
      await prismaClient.referralRelation.update({
        where: { id: options.relationId },
        data: {
          firstPaymentAt: new Date(),
          rewardGranted: true,
          rewardGrantedAt: new Date()
        }
      });

      // 创建使用记录
      await prismaClient.usageRecord.create({
        data: {
          userId: inviterId,
          type: 'CREDIT_RECHARGE',
          amount: amount,
          reason: `邀请奖励：邀请用户完成首次充值获得${amount}面巾值`
        }
      });

    } catch (error) {
      console.error('发放邀请奖励失败:', error);
      throw error;
    }
  }

  /**
   * 检查是否已发放奖励
   */
  async hasRewardBeenGranted(inviterId: string, inviteeId: string): Promise<boolean> {
    try {
      const relation = await prisma.referralRelation.findFirst({
        where: {
          inviterId,
          inviteeId,
          rewardGranted: true
        }
      });

      return !!relation;

    } catch (error) {
      console.error('检查奖励状态失败:', error);
      return false;
    }
  }

  /**
   * 更新用户余额
   */
  private async updateUserBalance(userId: string, amount: number, prismaClient: any = prisma): Promise<void> {
    try {
      // 确保用户有余额记录
      await prismaClient.userBalance.upsert({
        where: { userId },
        update: {
          mianshijunBalance: {
            increment: amount
          }
        },
        create: {
          userId,
          mockInterviewCredits: 0,
          formalInterviewCredits: 0,
          mianshijunBalance: amount
        }
      });

    } catch (error) {
      console.error('更新用户余额失败:', error);
      throw error;
    }
  }

  /**
   * 创建奖励记录
   */
  async createRewardRecord(data: RewardRecordData): Promise<void> {
    try {
      await prisma.referralReward.create({
        data: {
          ...data,
          status: 'PENDING',
          createdAt: new Date()
        }
      });

    } catch (error) {
      console.error('创建奖励记录失败:', error);
      throw error;
    }
  }
}

// 导出单例实例
export const rewardService = new RewardService();
