import { Router } from 'express';
import { getInterviewReviewData, getInterviewRecords } from './review';

const router = Router();

// 获取面试记录列表
router.get('/', getInterviewRecords);

// 获取面试回顾数据
router.get('/:sessionId/review', getInterviewReviewData);

// 如果需要，可以在此添加其他面试相关的路由，例如:
// router.post('/', createInterviewSessionHandler);
// router.post('/:sessionId/end', endInterviewSessionHandler);

export default router;
