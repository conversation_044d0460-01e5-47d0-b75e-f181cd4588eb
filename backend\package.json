{"name": "backend", "version": "1.0.0", "main": "index.js", "type": "module", "scripts": {"test": "node test-register.js", "dev": "npx tsx watch server.ts", "build": "npx tsx build server.ts", "start": "node dist/server.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@alicloud/dysmsapi20170525": "^4.1.2", "@alicloud/openapi-client": "^0.4.15", "@alicloud/pop-core": "^1.8.0", "@prisma/client": "^6.7.0", "@types/ioredis": "^4.28.10", "@types/multer": "^2.0.0", "@types/nodemailer": "^6.4.17", "@types/uuid": "^10.0.0", "@types/winston": "^2.4.4", "alibabacloud-nls": "^1.0.4", "axios": "^1.9.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "crypto-js": "^4.2.0", "dotenv": "^16.5.0", "express": "^5.1.0", "form-data": "^4.0.2", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "multer": "^2.0.1", "node-fetch": "^3.3.2", "nodemailer": "^7.0.5", "openai": "^4.103.0", "uuid": "^11.1.0", "winston": "^3.17.0", "ws": "^8.18.2"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.18", "@types/express": "^5.0.1", "@types/jsonwebtoken": "^9.0.9", "@types/ws": "^8.18.1", "prisma": "^6.7.0", "ts-node": "^10.9.2", "tsx": "^4.19.4"}}