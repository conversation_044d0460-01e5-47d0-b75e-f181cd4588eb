import { z } from 'zod';

// 注册表单验证模式 (Registration form validation schema)
export const registerSchema = z.object({
  email: z
    .string()
    .min(1, { message: '邮箱是必填项' })
    .email({ message: '请输入有效的邮箱地址' }),
  password: z
    .string()
    .min(8, { message: '密码至少需要8个字符' })
    .regex(/[A-Z]/, { message: '密码需要至少一个大写字母' })
    .regex(/[a-z]/, { message: '密码需要至少一个小写字母' })
    .regex(/[0-9]/, { message: '密码需要至少一个数字' }),
  confirmPassword: z.string().min(1, { message: '请确认您的密码' }),
  name: z.string().optional(),
}).refine(data => data.password === data.confirmPassword, {
  message: '密码和确认密码不匹配',
  path: ['confirmPassword'],
});

// 提取类型 (Extract types)
export type RegisterFormValues = z.infer<typeof registerSchema>;

// 登录表单验证模式 (Login form validation schema)
export const loginSchema = z.object({
  email: z
    .string()
    .min(1, { message: '邮箱是必填项' })
    .email({ message: '请输入有效的邮箱地址' }),
  password: z
    .string()
    .min(1, { message: '密码是必填项' }),
});

// 提取类型 (Extract types)
export type LoginFormValues = z.infer<typeof loginSchema>; 