#!/usr/bin/env ts-node

/**
 * 阿里云短信服务测试脚本
 * 用于测试短信发送功能是否正常工作
 */

import { SmsService } from '../services/smsService';
import dotenv from 'dotenv';

// 加载环境变量
dotenv.config();

async function testSmsService() {
  console.log('🚀 开始测试阿里云短信服务...\n');

  // 检查环境变量配置
  console.log('📋 检查环境变量配置:');
  console.log(`ACCESS_KEY_ID: ${process.env.ALIBABA_ACCESS_KEY_ID ? '已配置' : '未配置'}`);
  console.log(`ACCESS_KEY_SECRET: ${process.env.ALIBABA_ACCESS_KEY_SECRET ? '已配置' : '未配置'}`);
  console.log(`SMS_SIGN_NAME: ${process.env.ALIBABA_SMS_SIGN_NAME || '未配置'}`);
  console.log(`SMS_TEMPLATE_CODE: ${process.env.ALIBABA_SMS_TEMPLATE_CODE || '未配置'}\n`);

  if (!process.env.ALIBABA_ACCESS_KEY_ID || !process.env.ALIBABA_ACCESS_KEY_SECRET) {
    console.error('❌ 阿里云访问密钥未配置，请检查环境变量');
    return;
  }

  if (!process.env.ALIBABA_SMS_TEMPLATE_CODE) {
    console.error('❌ 短信模板代码未配置，请检查环境变量');
    return;
  }

  try {
    // 创建短信服务实例
    const smsService = new SmsService();
    
    // 测试连接
    console.log('🔗 测试服务连接...');
    const connectionTest = await smsService.testConnection();
    console.log(`连接测试: ${connectionTest ? '✅ 成功' : '❌ 失败'}\n`);

    // 测试手机号验证
    console.log('📱 测试手机号验证功能:');
    const testPhones = [
      '13800138000', // 有效
      '12345678901', // 无效
      '1380013800',  // 无效
      '15912345678'  // 有效
    ];

    testPhones.forEach(phone => {
      const isValid = SmsService.isValidPhoneNumber(phone);
      console.log(`${phone}: ${isValid ? '✅ 有效' : '❌ 无效'}`);
    });

    console.log('\n⚠️  实际短信发送测试需要有效的手机号码');
    console.log('⚠️  请确保已在阿里云控制台完成签名和模板的申请和审核');
    console.log('⚠️  如需测试实际发送，请修改此脚本添加真实手机号码');

    // 如果需要测试实际发送，取消注释以下代码并提供真实手机号
    /*
    const testPhoneNumber = '13800138000'; // 替换为真实手机号
    const testCode = '123456';
    
    console.log(`\n📤 发送测试短信到 ${testPhoneNumber}...`);
    await smsService.sendVerificationCode(testPhoneNumber, testCode);
    console.log('✅ 短信发送成功！');
    */

  } catch (error: any) {
    console.error('❌ 测试失败:', error.message);
    console.error('详细错误:', error);
  }
}

// 运行测试
testSmsService().then(() => {
  console.log('\n🎉 测试完成');
  process.exit(0);
}).catch((error) => {
  console.error('💥 测试过程中发生错误:', error);
  process.exit(1);
});
