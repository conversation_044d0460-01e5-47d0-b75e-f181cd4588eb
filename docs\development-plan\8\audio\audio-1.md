### **需求最终复述（包含长语音拼接细节）**

我们的核心目标是为“面试辅助”场景开发一个高可用的实时语音转写功能。该功能需要精确捕获面试官通过第三方会议软件发出的声音，将其转化为文字，并按照特定的逻辑切分、拼接后，提供给大语言模型（LLM）进行后续处理。

#### **1. 音频捕获**

- **音频源**: 必须且只能通过操作系统的 **屏幕共享功能** 捕获 **系统音频 (System Audio)**。
- **目的**: 确保音频源为面试官的声音，完全隔离本地麦克风输入，从而排除面试者本人的声音和环境噪声的干扰。
- **用户体验**: 在启动识别前，应用必须清晰地引导用户开启屏幕共享，并选择“共享系统音频”或“共享标签页音频”的选项。

#### **2. 会话生命周期管理**

- **启动**: 当用户在应用内点击“开始面试”或类似按钮时，语音识别会话正式启动，并开始监听音频流。
- **持续运行**: 识别会话在整个面试过程中保持持续活跃状态。
- **关闭**: 只有当用户明确点击“结束面试”或关闭相关页面时，识别会话才终止。

#### **3. 语音分段与发送逻辑**

这是需求的核心，包含三种主要场景：

- **场景A：标准对话（短句）**

  - **定义**: 任何长度小于60秒的语音片段。
  - **触发条件**: 当系统检测到面试官的语音流中出现连续 **3秒钟** 的静音时。
  - **执行动作**: 将静音前累计识别的所有内容，作为一个完整的句子（或段落），立即发送给LLM进行处理。发送后，内部临时结果清空，准备接收下一句话。

- **场景B：长语音（超过60秒且中途无停顿）**

  - **定义**: 面试官连续说话，时长超过 **60秒**，且中途没有任何超过3秒的停顿。

  - **触发条件**: 语音流计时器达到60秒。

  - 执行动作

    :

    1. 系统在内部进行一次“软切分”，将这前60秒的音频流已经产生的 **临时识别结果** 固化下来，但 **不发送** 给LLM。
    2. 系统继续处理第61秒及之后的音频流，并将新识别的临时结果，在逻辑上拼接于之前固化的60秒结果之后。
    3. 这个过程对用户是透明的，识别持续进行。

- **场景C：长语音结束（最终拼接与发送）**

  - **定义**: 一段超过60秒的长语音，最终以一次停顿结束。

  - **举例**: 面试官连续讲了70秒，然后停顿了3秒以上。

  - **触发条件**: 在经历了一次或多次60秒的“软切分”后，系统最终检测到了3秒钟的静音。

  - 执行动作

    :

    1. 系统将最后一段（本例中为第61-70秒）的识别结果固化。
    2. 将此前所有“软切分”固化的结果（第1-60秒的结果）与最后这段结果 **拼接** 成一个完整的、连续的文本段落。
    3. 将这个 **最终拼接好的长文本** 作为一个整体，发送给LLM进行处理。
    4. 发送完成后，清空所有临时和固化的结果，准备接收下一句话。

#### **4. 核心要求总结**

总结起来，系统需要一个状态管理器，能够：

- **持续监听** 来自系统屏幕共享的音频。
- 通过 **3秒静音** 判断一句话的自然结束，并触发发送。
- 内置一个 **60秒的计时器** 作为“长语音保护机制”，在无停顿时，于后台默默地、分段地固化识别结果。
- 能够将多段“软切分”的结果和最后一句的识别结果 **智能拼接**，确保在长语音结束后，将完整、连贯的全文发送给LLM。
- 在整个过程中，不向ASR服务发送无效的静音数据包。

