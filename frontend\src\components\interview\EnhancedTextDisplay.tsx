import React, { useState, useEffect, useRef, useCallback } from 'react';

export interface TextUpdate {
  id: string;
  text: string;
  confidence: number;
  timestamp: number;
  isPartial: boolean;
  service?: string;
  processingTime?: number;
}

export interface EnhancedTextDisplayProps {
  className?: string;
  placeholder?: string;
  showConfidence?: boolean;
  showMetadata?: boolean;
  animationSpeed?: number;
  maxDisplayLength?: number;
  onTextUpdate?: (text: string) => void;
}

/**
 * 增强的文本显示组件
 * 支持平滑的文字显示动画和实时更新
 */
export const EnhancedTextDisplay: React.FC<EnhancedTextDisplayProps> = ({
  className = '',
  placeholder = '等待语音输入...',
  showConfidence = true,
  showMetadata = false,
  animationSpeed = 50,
  maxDisplayLength = 1000,
  onTextUpdate
}) => {
  const [displayText, setDisplayText] = useState('');
  const [isAnimating, setIsAnimating] = useState(false);
  const [currentUpdate, setCurrentUpdate] = useState<TextUpdate | null>(null);
  const [updateHistory, setUpdateHistory] = useState<TextUpdate[]>([]);
  const [currentPartialText, setCurrentPartialText] = useState(''); // 🔥 新增：当前部分结果
  const [finalizedText, setFinalizedText] = useState(''); // 🔥 新增：已确定的文本

  const animationRef = useRef<number>();
  const timeoutRef = useRef<NodeJS.Timeout>();
  const containerRef = useRef<HTMLDivElement>(null);

  /**
   * 添加新的文本更新
   */
  const addTextUpdate = useCallback((update: TextUpdate) => {
    console.log('📝 Adding text update:', {
      id: update.id,
      text: update.text.substring(0, 50) + '...',
      confidence: update.confidence,
      isPartial: update.isPartial
    });

    setCurrentUpdate(update);

    if (update.isPartial) {
      // 🔥 部分结果：更新当前行，不添加到历史记录
      console.log('🔄 Updating partial result on same line');
      setCurrentPartialText(update.text);

      // 显示：已确定文本 + 当前部分结果
      const newDisplayText = finalizedText + (finalizedText ? ' ' : '') + update.text;
      animateTextChange(displayText, newDisplayText);
    } else {
      // 🔥 最终结果：确定文本，添加到历史记录，开始新行
      console.log('✅ Finalizing result and starting new line');

      const newFinalizedText = finalizedText + (finalizedText ? ' ' : '') + update.text;
      setFinalizedText(newFinalizedText);
      setCurrentPartialText(''); // 清空部分结果

      // 添加到历史记录
      setUpdateHistory(prev => {
        const newHistory = [...prev, update];
        return newHistory.slice(-20);
      });

      // 显示最终确定的文本
      animateTextChange(displayText, newFinalizedText);
    }
  }, [displayText, finalizedText]);

  /**
   * 执行文本变化动画
   */
  const animateTextChange = useCallback((fromText: string, toText: string) => {
    if (isAnimating) {
      // 如果正在动画中，取消当前动画
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    }

    setIsAnimating(true);

    // 找到公共前缀
    let commonPrefixLength = 0;
    const minLength = Math.min(fromText.length, toText.length);
    
    for (let i = 0; i < minLength; i++) {
      if (fromText[i] === toText[i]) {
        commonPrefixLength++;
      } else {
        break;
      }
    }

    const commonPrefix = toText.substring(0, commonPrefixLength);
    const textToAdd = toText.substring(commonPrefixLength);
    const textToRemove = fromText.substring(commonPrefixLength);

    // 执行打字机效果
    performTypewriterAnimation(commonPrefix, textToRemove, textToAdd);
  }, [isAnimating, animationSpeed]);

  /**
   * 执行打字机动画
   */
  const performTypewriterAnimation = useCallback((
    prefix: string,
    toRemove: string,
    toAdd: string
  ) => {
    let currentStep = 0;
    const totalRemoveSteps = toRemove.length;
    const totalAddSteps = toAdd.length;
    const totalSteps = totalRemoveSteps + totalAddSteps;

    const animate = () => {
      if (currentStep < totalRemoveSteps) {
        // 删除阶段
        const removeCount = currentStep + 1;
        const currentText = prefix + toRemove.substring(0, toRemove.length - removeCount);
        setDisplayText(currentText);
      } else {
        // 添加阶段
        const addCount = currentStep - totalRemoveSteps + 1;
        const currentText = prefix + toAdd.substring(0, addCount);
        setDisplayText(currentText);
      }

      currentStep++;

      if (currentStep <= totalSteps) {
        timeoutRef.current = setTimeout(animate, animationSpeed);
      } else {
        setIsAnimating(false);
        // 通知父组件文本更新完成
        if (onTextUpdate) {
          onTextUpdate(prefix + toAdd);
        }
        // 自动滚动到底部
        scrollToBottom();
      }
    };

    animate();
  }, [animationSpeed, onTextUpdate]);

  /**
   * 滚动到底部
   */
  const scrollToBottom = useCallback(() => {
    if (containerRef.current) {
      containerRef.current.scrollTop = containerRef.current.scrollHeight;
    }
  }, []);

  /**
   * 清空显示文本
   */
  const clearText = useCallback(() => {
    setDisplayText('');
    setCurrentUpdate(null);
    setUpdateHistory([]);
    setCurrentPartialText(''); // 🔥 清空部分结果
    setFinalizedText(''); // 🔥 清空已确定文本
    setIsAnimating(false);

    if (animationRef.current) {
      cancelAnimationFrame(animationRef.current);
    }
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
  }, []);

  /**
   * 立即设置文本（无动画）
   */
  const setTextImmediately = useCallback((text: string) => {
    if (animationRef.current) {
      cancelAnimationFrame(animationRef.current);
    }
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    
    setDisplayText(text);
    setIsAnimating(false);
    
    if (onTextUpdate) {
      onTextUpdate(text);
    }
  }, [onTextUpdate]);

  /**
   * 获取置信度颜色
   */
  const getConfidenceColor = (confidence: number): string => {
    if (confidence >= 0.8) return 'text-green-600';
    if (confidence >= 0.6) return 'text-yellow-600';
    return 'text-red-600';
  };

  /**
   * 格式化处理时间
   */
  const formatProcessingTime = (time?: number): string => {
    if (!time) return '-';
    return `${time}ms`;
  };

  // 清理资源
  useEffect(() => {
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  // 暴露方法给父组件
  useEffect(() => {
    // 可以通过ref暴露方法，这里简化处理
  }, []);

  return (
    <div className={`enhanced-text-display ${className}`}>
      {/* 主要文本显示区域 */}
      <div 
        ref={containerRef}
        className="relative bg-white rounded-lg border shadow-sm p-4 min-h-[120px] max-h-[300px] overflow-y-auto"
      >
        {displayText ? (
          <div className="space-y-2">
            <div className="text-gray-800 leading-relaxed whitespace-pre-wrap">
              {displayText.length > maxDisplayLength 
                ? '...' + displayText.slice(-maxDisplayLength)
                : displayText
              }
              {isAnimating && (
                <span className="inline-block w-0.5 h-5 bg-blue-500 animate-pulse ml-1"></span>
              )}
            </div>
          </div>
        ) : (
          <div className="text-gray-400 italic text-center py-8">
            {placeholder}
          </div>
        )}
      </div>

      {/* 元数据显示 */}
      {showMetadata && currentUpdate && (
        <div className="mt-2 text-xs text-gray-500 space-y-1">
          <div className="flex justify-between items-center">
            <span>
              服务: <span className="font-medium">{currentUpdate.service || 'unknown'}</span>
            </span>
            {showConfidence && (
              <span>
                置信度: <span className={`font-medium ${getConfidenceColor(currentUpdate.confidence)}`}>
                  {Math.round(currentUpdate.confidence * 100)}%
                </span>
              </span>
            )}
          </div>
          
          <div className="flex justify-between items-center">
            <span>
              类型: <span className="font-medium">
                {currentUpdate.isPartial ? '部分结果' : '最终结果'}
              </span>
            </span>
            <span>
              处理时间: <span className="font-medium">
                {formatProcessingTime(currentUpdate.processingTime)}
              </span>
            </span>
          </div>
          
          <div className="text-right">
            <span>
              更新时间: <span className="font-medium">
                {new Date(currentUpdate.timestamp).toLocaleTimeString()}
              </span>
            </span>
          </div>
        </div>
      )}

      {/* 控制按钮 */}
      <div className="mt-2 flex justify-end gap-2">
        <button
          onClick={clearText}
          className="px-2 py-1 text-xs text-gray-500 hover:text-gray-700 border border-gray-300 rounded hover:bg-gray-50 transition-colors"
          disabled={!displayText}
        >
          清空
        </button>
        
        {updateHistory.length > 0 && (
          <button
            onClick={() => {
              const lastUpdate = updateHistory[updateHistory.length - 1];
              if (lastUpdate) {
                setTextImmediately(lastUpdate.text);
              }
            }}
            className="px-2 py-1 text-xs text-blue-500 hover:text-blue-700 border border-blue-300 rounded hover:bg-blue-50 transition-colors"
          >
            恢复最新
          </button>
        )}
      </div>

      {/* 历史记录（调试用） */}
      {showMetadata && updateHistory.length > 1 && (
        <details className="mt-2">
          <summary className="text-xs text-gray-500 cursor-pointer hover:text-gray-700">
            历史记录 ({updateHistory.length})
          </summary>
          <div className="mt-1 max-h-32 overflow-y-auto text-xs text-gray-600 space-y-1">
            {updateHistory.slice(-5).map((update, index) => (
              <div key={update.id} className="p-1 bg-gray-50 rounded">
                <div className="flex justify-between">
                  <span className="font-medium">#{updateHistory.length - 5 + index + 1}</span>
                  <span className={getConfidenceColor(update.confidence)}>
                    {Math.round(update.confidence * 100)}%
                  </span>
                </div>
                <div className="truncate">
                  {update.text.substring(0, 50)}...
                </div>
              </div>
            ))}
          </div>
        </details>
      )}
    </div>
  );
};

// 导出方法供外部调用
export interface EnhancedTextDisplayRef {
  addTextUpdate: (update: TextUpdate) => void;
  clearText: () => void;
  setTextImmediately: (text: string) => void;
}

export default EnhancedTextDisplay;
