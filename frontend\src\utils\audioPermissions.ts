export interface AudioPermissionResult {
  granted: boolean;
  error?: string;
}

export const checkMicrophonePermission = async (): Promise<AudioPermissionResult> => {
  try {
    // 检查浏览器是否支持 getUserMedia
    if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
      return {
        granted: false,
        error: '您的浏览器不支持麦克风功能，请使用现代浏览器'
      };
    }

    // 请求麦克风权限
    const stream = await navigator.mediaDevices.getUserMedia({ 
      audio: true 
    });
    
    // 如果成功获取到流，立即停止所有轨道
    stream.getTracks().forEach(track => track.stop());
    
    return { granted: true };
  } catch (error) {
    console.error('Microphone permission error:', error);
    
    if (error instanceof Error) {
      if (error.name === 'NotAllowedError') {
        return {
          granted: false,
          error: '麦克风权限被拒绝，请在浏览器设置中允许麦克风访问'
        };
      } else if (error.name === 'NotFoundError') {
        return {
          granted: false,
          error: '未检测到麦克风设备，请检查您的麦克风连接'
        };
      } else if (error.name === 'NotReadableError') {
        return {
          granted: false,
          error: '麦克风设备被其他应用占用，请关闭其他使用麦克风的应用'
        };
      }
    }
    
    return {
      granted: false,
      error: '麦克风权限检测失败，请重试'
    };
  }
};

export const requestScreenShare = async (): Promise<AudioPermissionResult> => {
  try {
    // 检查浏览器是否支持屏幕共享
    if (!navigator.mediaDevices || !navigator.mediaDevices.getDisplayMedia) {
      return {
        granted: false,
        error: '您的浏览器不支持屏幕共享功能'
      };
    }

    // 请求屏幕共享权限（包含音频）
    const stream = await navigator.mediaDevices.getDisplayMedia({
      video: true,
      audio: true
    });
    
    // 如果成功获取到流，立即停止所有轨道
    stream.getTracks().forEach(track => track.stop());
    
    return { granted: true };
  } catch (error) {
    console.error('Screen share permission error:', error);
    
    if (error instanceof Error) {
      if (error.name === 'NotAllowedError') {
        return {
          granted: false,
          error: '屏幕共享权限被拒绝'
        };
      }
    }
    
    return {
      granted: false,
      error: '屏幕共享权限检测失败，请重试'
    };
  }
};
