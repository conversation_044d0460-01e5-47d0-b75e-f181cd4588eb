import { AudioCircularBuffer } from './circularBuffer';
import { VADResult } from '../types/vadTypes';
import CryptoJS from 'crypto-js';

export interface BufferTriggerInfo {
  shouldTrigger: boolean;
  reason: 'speech_detected' | 'buffer_full' | 'silence_timeout' | 'manual' | 'speech_segment_complete';
  confidence: number;
  audioLength: number;
  segmentDuration: number;
}

export interface AudioBufferConfig {
  shortTermCapacity: number;    // 短期缓冲区容量（帧数）
  mediumTermCapacity: number;   // 中期缓冲区容量（帧数）
  longTermCapacity: number;     // 长期缓冲区容量（帧数）
  minTriggerInterval: number;   // 最小触发间隔（ms）
  maxSpeechDuration: number;    // 最大语音段时长（ms）
  maxSilenceDuration: number;   // 最大静音时长（ms）
  sampleRate: number;           // 采样率
  shortTermDurationMs: number;
  midTermDurationMs: number;
  recognitionIntervalMs: number;
  channels: number;
}

export const DEFAULT_BUFFER_CONFIG: AudioBufferConfig = {
  shortTermCapacity: 100,       // 2秒@50fps
  mediumTermCapacity: 500,      // 10秒@50fps
  longTermCapacity: 1500,       // 30秒@50fps
  minTriggerInterval: 1000,     // 1秒
  maxSpeechDuration: 10000,     // 10秒
  maxSilenceDuration: 2000,     // 2秒
  sampleRate: 16000,
  shortTermDurationMs: 2000,
  midTermDurationMs: 10000,
  recognitionIntervalMs: 1000,
  channels: 1
};

/**
 * 智能音频缓冲管理器
 * 实现多级缓冲策略和智能触发机制
 */
export class AudioBufferManager {
  private shortTermBuffer: AudioCircularBuffer;
  private mediumTermBuffer: AudioCircularBuffer;
  private longTermBuffer: AudioCircularBuffer;
  private vadHistory: VADResult[] = [];
  
  private config: AudioBufferConfig;
  private lastTriggerTime: number = 0;
  private speechStartTime: number = 0;
  private silenceStartTime: number = 0;
  private isInSpeech: boolean = false;
  private currentSegmentId: string = '';
  
  private readonly maxVADHistory = 50;
  private lastRecognitionTime: number = 0;
  private sendOffset: number = 0; // 跟踪已发送音频的偏移量
  private sentHashes: Set<string> = new Set(); // 已发送音频的哈希集合
  private isVoiceActive: boolean = false;

  constructor(config: Partial<AudioBufferConfig> = {}) {
    this.config = { ...DEFAULT_BUFFER_CONFIG, ...config };
    
    this.shortTermBuffer = new AudioCircularBuffer(this.config.shortTermCapacity, this.config.sampleRate);
    this.mediumTermBuffer = new AudioCircularBuffer(this.config.mediumTermCapacity, this.config.sampleRate);
    this.longTermBuffer = new AudioCircularBuffer(this.config.longTermCapacity, this.config.sampleRate);
    
    console.log('AudioBufferManager initialized with config:', this.config);
  }

  /**
   * 添加音频数据块并检查触发条件
   */
  addAudioChunk(audioData: Float32Array, vadResult: VADResult): BufferTriggerInfo {
    // 添加到所有缓冲区
    this.shortTermBuffer.push(audioData);
    this.mediumTermBuffer.push(audioData);
    this.longTermBuffer.push(audioData);
    
    // 更新VAD历史
    this.vadHistory.push(vadResult);
    if (this.vadHistory.length > this.maxVADHistory) {
      this.vadHistory.shift();
    }
    
    // 更新语音状态
    this.updateSpeechState(vadResult);
    
    // 检查触发条件
    return this.checkTriggerConditions();
  }

  /**
   * 更新语音状态
   */
  private updateSpeechState(vadResult: VADResult): void {
    const now = Date.now();
    
    if (vadResult.isSpeech && !this.isInSpeech) {
      // 语音开始
      this.isInSpeech = true;
      this.speechStartTime = now;
      this.silenceStartTime = 0;
      this.currentSegmentId = `segment-${now}`;
      console.log(`🎤 Speech segment started: ${this.currentSegmentId}`);
    } else if (!vadResult.isSpeech && this.isInSpeech) {
      // 可能的语音结束
      if (this.silenceStartTime === 0) {
        this.silenceStartTime = now;
      }
    } else if (vadResult.isSpeech && this.isInSpeech) {
      // 语音继续，重置静音计时
      this.silenceStartTime = 0;
    }
  }

  /**
   * 检查触发条件
   */
  private checkTriggerConditions(): BufferTriggerInfo {
    const now = Date.now();
    
    // 检查最小触发间隔
    if (now - this.lastTriggerTime < this.config.minTriggerInterval) {
      return this.createNonTriggerInfo();
    }
    
    // 检查语音段完成
    if (this.isInSpeech && this.speechStartTime > 0) {
      const speechDuration = now - this.speechStartTime;
      
      // 语音段过长，强制触发
      if (speechDuration > this.config.maxSpeechDuration) {
        console.log(`⏰ Speech segment too long (${speechDuration}ms), forcing trigger`);
        return this.createTriggerInfo('buffer_full', 0.8, speechDuration);
      }
      
      // 检测到静音结束
      if (this.silenceStartTime > 0) {
        const silenceDuration = now - this.silenceStartTime;
        if (silenceDuration > this.config.maxSilenceDuration) {
          console.log(`🔇 Silence detected (${silenceDuration}ms), speech segment complete`);
          this.completeSpeechSegment();
          return this.createTriggerInfo('speech_segment_complete', 0.9, speechDuration);
        }
      }
    }
    
    // 检查缓冲区满
    if (this.shortTermBuffer.isFull()) {
      console.log('📦 Short-term buffer full, triggering recognition');
      return this.createTriggerInfo('buffer_full', 0.7, this.getCurrentSegmentDuration());
    }
    
    // 检查语音检测触发
    if (this.shouldTriggerOnSpeechDetection()) {
      console.log('🎯 Speech pattern detected, triggering recognition');
      return this.createTriggerInfo('speech_detected', 0.8, this.getCurrentSegmentDuration());
    }
    
    return this.createNonTriggerInfo();
  }

  /**
   * 检查是否应该基于语音检测触发
   */
  private shouldTriggerOnSpeechDetection(): boolean {
    if (this.vadHistory.length < 10) return false;
    
    const recentVAD = this.vadHistory.slice(-10);
    const speechCount = recentVAD.filter(v => v.isSpeech).length;
    const avgConfidence = recentVAD.reduce((sum, v) => sum + v.confidence, 0) / recentVAD.length;
    
    // 如果最近10个VAD结果中有7个以上是语音，且平均置信度高，则触发
    return speechCount >= 7 && avgConfidence > 0.7 && this.isInSpeech;
  }

  /**
   * 完成语音段
   */
  private completeSpeechSegment(): void {
    this.isInSpeech = false;
    this.speechStartTime = 0;
    this.silenceStartTime = 0;
    console.log(`✅ Speech segment completed: ${this.currentSegmentId}`);
  }

  /**
   * 获取当前语音段时长
   */
  private getCurrentSegmentDuration(): number {
    if (this.speechStartTime === 0) return 0;
    return Date.now() - this.speechStartTime;
  }

  /**
   * 创建触发信息
   */
  private createTriggerInfo(
    reason: BufferTriggerInfo['reason'], 
    confidence: number,
    segmentDuration: number
  ): BufferTriggerInfo {
    this.lastTriggerTime = Date.now();
    const audioData = this.getRecognitionBuffer();
    
    return {
      shouldTrigger: true,
      reason,
      confidence,
      audioLength: audioData ? audioData.length : 0,
      segmentDuration
    };
  }

  /**
   * 创建非触发信息
   */
  private createNonTriggerInfo(): BufferTriggerInfo {
    return {
      shouldTrigger: false,
      reason: 'manual',
      confidence: 0,
      audioLength: 0,
      segmentDuration: 0
    };
  }

  /**
   * 生成音频数据的哈希指纹
   */
  private generateAudioHash(audioData: Float32Array): string {
    // 将Float32Array转换为字符串进行哈希计算
    const dataString = Array.from(audioData.slice(0, Math.min(1000, audioData.length))).join(',');
    return CryptoJS.MD5(dataString).toString();
  }

  /**
   * 获取用于识别的增量音频缓冲区
   */
  getRecognitionBuffer(): Float32Array | null {
    const now = Date.now();
    
    // 检查是否到了发送间隔
    if (now - this.lastRecognitionTime < this.config.recognitionIntervalMs) {
      return null;
    }

    let audioData: Float32Array | null = null;

    if (this.isVoiceActive) {
      // 语音活跃时，获取短期缓冲区的增量数据
      audioData = this.shortTermBuffer.getIncrementalAudio(this.sendOffset);
    } else {
      // 语音结束时，获取中期缓冲区的增量数据
      audioData = this.mediumTermBuffer.getIncrementalAudio(this.sendOffset);
    }

    if (!audioData || audioData.length === 0) {
      return null;
    }

    // 生成音频哈希
    const audioHash = this.generateAudioHash(audioData);
    
    // 检查是否已经发送过相同的音频
    if (this.sentHashes.has(audioHash)) {
      return null; // 跳过重复音频
    }

    // 记录哈希和更新偏移量
    this.sentHashes.add(audioHash);
    this.markAsSent(audioData.length);
    
    this.lastRecognitionTime = now;
    return audioData;
  }

  /**
   * 标记音频数据已发送，更新偏移量
   */
  private markAsSent(audioLength: number): void {
    this.sendOffset += audioLength;
    
    // 清理过期的哈希记录，避免内存泄漏
    if (this.sentHashes.size > 100) {
      const hashArray = Array.from(this.sentHashes);
      const keepHashes = new Set(hashArray.slice(-50)); // 只保留最近50个哈希
      this.sentHashes = keepHashes;
    }
  }

  /**
   * 重置发送状态
   */
  resetSendState(): void {
    this.sendOffset = 0;
    this.sentHashes.clear();
  }

  /**
   * 设置语音活动状态
   */
  setVoiceActive(active: boolean): void {
    if (this.isVoiceActive !== active) {
      this.isVoiceActive = active;
      if (!active) {
        // 语音结束时重置发送状态
        this.resetSendState();
      }
    }
  }

  /**
   * 获取指定时长的上下文音频
   */
  getContextBuffer(durationMs: number = 5000): Float32Array {
    return this.longTermBuffer.getAudioByDuration(durationMs);
  }

  /**
   * 获取VAD历史
   */
  getVADHistory(): VADResult[] {
    return [...this.vadHistory];
  }

  /**
   * 获取最近的VAD结果
   */
  getRecentVAD(count: number = 10): VADResult[] {
    return this.vadHistory.slice(-count);
  }

  /**
   * 获取缓冲区统计信息
   */
  getBufferStats(): {
    shortTerm: any;
    mediumTerm: any;
    longTerm: any;
    speechState: {
      isInSpeech: boolean;
      currentSegmentId: string;
      speechDuration: number;
      silenceDuration: number;
    };
  } {
    const now = Date.now();
    
    return {
      shortTerm: this.shortTermBuffer.getStats(),
      mediumTerm: this.mediumTermBuffer.getStats(),
      longTerm: this.longTermBuffer.getStats(),
      speechState: {
        isInSpeech: this.isInSpeech,
        currentSegmentId: this.currentSegmentId,
        speechDuration: this.speechStartTime > 0 ? now - this.speechStartTime : 0,
        silenceDuration: this.silenceStartTime > 0 ? now - this.silenceStartTime : 0
      }
    };
  }

  /**
   * 重置所有缓冲区和状态
   */
  reset(): void {
    this.shortTermBuffer.clear();
    this.mediumTermBuffer.clear();
    this.longTermBuffer.clear();
    this.vadHistory = [];
    
    this.lastTriggerTime = 0;
    this.speechStartTime = 0;
    this.silenceStartTime = 0;
    this.isInSpeech = false;
    this.currentSegmentId = '';
    
    console.log('AudioBufferManager reset');
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<AudioBufferConfig>): void {
    this.config = { ...this.config, ...newConfig };
    console.log('AudioBufferManager config updated:', this.config);
  }

  /**
   * 获取当前配置
   */
  getConfig(): AudioBufferConfig {
    return { ...this.config };
  }
}
