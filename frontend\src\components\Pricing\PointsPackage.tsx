// 文件路径: frontend/src/components/Pricing/PointsPackage.tsx
// (File Path: frontend/src/components/Pricing/PointsPackage.tsx)
import React from 'react';
import { motion } from 'framer-motion';
import { Sparkles } from 'lucide-react'; // Or another icon like ShoppingCart if preferred

interface PointsPackageProps {
  pkg: { // Changed from 'package' to 'pkg' for consistency
    id: string;
    points: number;
    price: number;
    buyButtonText?: string;
  };
  onPurchase?: (pkg: any) => void;
}

const PointsPackage: React.FC<PointsPackageProps> = ({ pkg, onPurchase }) => {
  const handlePurchaseClick = () => {
    if (onPurchase) {
      // 构造与套餐卡片相同的数据结构
      const packageData = {
        id: pkg.id,
        title: `${pkg.points}面巾`,
        price: pkg.price,
        description: `购买${pkg.points}面巾`,
        buyButtonText: pkg.buyButtonText
      };
      onPurchase(packageData);
    }
  };
  return (
    <motion.div
      whileHover={{ scale: 1.02, y: -3 }}
      className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl border border-gray-200 p-5 flex justify-between items-center backdrop-blur-sm shadow-lg"
    >
      <div className="flex items-baseline">
        <span className="text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-indigo-600">
          {pkg.points}
        </span>
        <span className="text-base text-gray-600 ml-1.5">面巾</span>
      </div>

      <motion.button
        whileHover={{ scale: 1.05, boxShadow: "0px 5px 15px rgba(0, 0, 0, 0.1)"}}
        whileTap={{ scale: 0.95 }}
        onClick={handlePurchaseClick}
        className="bg-gradient-to-r from-gray-800 to-gray-900 text-white px-5 py-2.5 rounded-full flex items-center gap-2 text-sm font-medium hover:shadow-md transition-shadow"
        // className="bg-blue-600 text-white px-5 py-2 rounded-lg flex items-center gap-2 text-sm font-medium hover:bg-blue-700 transition-colors"
      >
        <span>¥{pkg.price} {pkg.buyButtonText || '购买'}</span>
        <Sparkles className="h-4 w-4" />
      </motion.button>
    </motion.div>
  );
};

export default PointsPackage;
