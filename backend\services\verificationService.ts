import crypto from 'crypto';
import { PrismaClient } from '@prisma/client';
import RedisService from './redisService';
import EmailService from './emailService';
import SmsService from './smsService';
// import MonitoringService from './monitoringService';
// import SecurityService from './securityService';

export class VerificationService {
  private redis: RedisService;
  private prisma: PrismaClient;
  private emailService: EmailService;
  private smsService: SmsService;
  // private monitoringService: MonitoringService;
  // private securityService: SecurityService;

  constructor() {
    this.redis = RedisService.getInstance();
    this.prisma = new PrismaClient();
    this.emailService = new EmailService();
    this.smsService = new SmsService();
    // this.monitoringService = new MonitoringService();
    // this.securityService = new SecurityService();
  }

  // 生成6位数字验证码
  generateCode(): string {
    return Math.floor(100000 + Math.random() * 900000).toString();
  }

  // 发送验证码
  async sendVerificationCode(
    identifier: string,
    type: 'EMAIL' | 'SMS',
    purpose: 'LOGIN' = 'LOGIN',
    ipAddress?: string,
    userAgent?: string
  ): Promise<{ success: boolean; message: string; expiresIn?: number }> {

    try {
      // 安全检查：IP频率限制 (暂时注释)
      // if (ipAddress) {
      //   const ipCheck = await this.securityService.checkIpRateLimit(ipAddress);
      //   if (!ipCheck.allowed) {
      //     const resetTime = ipCheck.resetTime ? new Date(ipCheck.resetTime).toLocaleTimeString('zh-CN') : '稍后';
      //     return {
      //       success: false,
      //       message: `请求过于频繁，请在 ${resetTime} 后重试`
      //     };
      //   }
      // }

      // 安全检查：异常行为检测 (暂时注释)
      // if (ipAddress) {
      //   const anomalyCheck = await this.securityService.detectAnomalousActivity(
      //     identifier,
      //     ipAddress,
      //     `SEND_CODE_${type}`
      //   );
      //
      //   if (anomalyCheck.isSuspicious) {
      //     await this.logSendAttempt(identifier, type, purpose, 'BLOCKED', 'Suspicious activity detected', ipAddress, userAgent);
      //     return {
      //       success: false,
      //       message: '检测到异常活动，请稍后重试或联系客服'
      //     };
      //   }
      // }

      // 检查发送频率限制
      const rateLimitResult = await this.checkRateLimit(identifier, type);
      if (!rateLimitResult.canSend) {
        return {
          success: false,
          message: rateLimitResult.message
        };
      }

      // 生成验证码
      const code = this.generateCode();
      const expiresAt = new Date(Date.now() + 5 * 60 * 1000); // 5分钟后过期

      // 存储验证码到数据库
      await this.prisma.verificationCode.create({
        data: {
          identifier,
          code: await this.hashCode(code), // 加密存储
          type,
          purpose,
          expiresAt
        }
      });

      // 存储到Redis缓存
      const cacheKey = `verification:${identifier}:${type}:${purpose}`;
      await this.redis.set(cacheKey, JSON.stringify({
        code: await this.hashCode(code),
        attempts: 0,
        createdAt: Date.now()
      }), 300); // 5分钟过期

      // 设置发送频率限制
      const rateLimitKey = `rate_limit:${identifier}:${type}`;
      await this.redis.set(rateLimitKey, Date.now().toString(), 60); // 1分钟限制

      // 发送验证码
      if (type === 'EMAIL') {
        await this.sendEmailCode(identifier, code);
      } else if (type === 'SMS') {
        await this.sendSmsCode(identifier, code);
      } else {
        throw new Error('不支持的验证码类型');
      }

      // 记录发送日志
      await this.logSendAttempt(identifier, type, purpose, 'SUCCESS', undefined, ipAddress, userAgent);

      return {
        success: true,
        message: '验证码已发送',
        expiresIn: 300
      };

    } catch (error: any) {
      console.error('Send verification code error:', {
        message: error?.message,
        stack: error?.stack,
        name: error?.name,
        code: error?.code
      });

      // 记录监控事件 (暂时注释)
      // await this.monitoringService.recordFailure(type, error.message, identifier);

      try {
        await this.logSendAttempt(identifier, type, purpose, 'FAILED', error.message, ipAddress, userAgent);
      } catch (logError) {
        console.error('Failed to log send attempt:', logError);
      }

      return {
        success: false,
        message: error?.message || '验证码发送失败，请稍后重试'
      };
    }
  }

  // 验证验证码
  async verifyCode(
    identifier: string,
    code: string,
    type: 'EMAIL' | 'SMS',
    purpose: 'LOGIN' = 'LOGIN'
  ): Promise<{ success: boolean; message: string; isLocked?: boolean }> {

    const cacheKey = `verification:${identifier}:${type}:${purpose}`;
    const lockKey = `verification_lock:${identifier}:${type}`;

    try {
      // 检查是否被锁定
      const isLocked = await this.redis.get(lockKey);
      if (isLocked) {
        const lockExpires = parseInt(isLocked);
        const remainingTime = Math.ceil((lockExpires - Date.now()) / 1000);
        return {
          success: false,
          message: `验证失败次数过多，请等待 ${remainingTime} 秒后再试`,
          isLocked: true
        };
      }

      // 获取验证码信息
      const cacheData = await this.redis.get(cacheKey);
      if (!cacheData) {
        return {
          success: false,
          message: '验证码已过期，请重新获取'
        };
      }

      const verificationData = JSON.parse(cacheData);
      const hashedInputCode = await this.hashCode(code);

      // 验证码错误
      if (verificationData.code !== hashedInputCode) {
        verificationData.attempts += 1;

        // 更新尝试次数
        await this.redis.set(cacheKey, JSON.stringify(verificationData), 300);

        // 检查是否需要锁定
        if (verificationData.attempts >= 3) {
          const lockExpires = Date.now() + 10 * 60 * 1000; // 锁定10分钟
          await this.redis.set(lockKey, lockExpires.toString(), 600);
          await this.redis.del(cacheKey); // 删除验证码

          return {
            success: false,
            message: '验证失败次数过多，账户已被锁定10分钟',
            isLocked: true
          };
        }

        return {
          success: false,
          message: `验证码错误，还可尝试 ${3 - verificationData.attempts} 次`
        };
      }

      // 验证成功，清理缓存
      await this.redis.del(cacheKey);
      await this.redis.del(lockKey);

      // 标记验证码为已使用
      await this.prisma.verificationCode.updateMany({
        where: {
          identifier,
          type,
          purpose,
          isUsed: false
        },
        data: {
          isUsed: true
        }
      });

      return {
        success: true,
        message: '验证成功'
      };

    } catch (error: any) {
      console.error('Verify code error:', error);
      return {
        success: false,
        message: '验证失败，请稍后重试'
      };
    }
  }

  // 检查发送频率限制
  private async checkRateLimit(identifier: string, type: string): Promise<{ canSend: boolean; message: string }> {
    const rateLimitKey = `rate_limit:${identifier}:${type}`;
    const lastSent = await this.redis.get(rateLimitKey);

    if (lastSent) {
      const remainingTime = 60 - Math.floor((Date.now() - parseInt(lastSent)) / 1000);
      if (remainingTime > 0) {
        return {
          canSend: false,
          message: `请等待 ${remainingTime} 秒后再试`
        };
      }
    }

    return { canSend: true, message: '' };
  }

  // 加密验证码
  private async hashCode(code: string): Promise<string> {
    return crypto.createHash('sha256').update(code + process.env.JWT_SECRET).digest('hex');
  }

  // 发送邮件验证码
  private async sendEmailCode(email: string, code: string): Promise<void> {
    await this.emailService.sendVerificationCode(email, code);
  }

  // 发送短信验证码
  private async sendSmsCode(phoneNumber: string, code: string): Promise<void> {
    // 验证手机号格式
    if (!SmsService.isValidPhoneNumber(phoneNumber)) {
      throw new Error('手机号格式不正确');
    }

    await this.smsService.sendVerificationCode(phoneNumber, code);
  }

  // 记录发送日志
  private async logSendAttempt(
    identifier: string,
    type: 'EMAIL' | 'SMS',
    purpose: string,
    status: 'SUCCESS' | 'FAILED',
    errorMessage?: string,
    ipAddress?: string,
    userAgent?: string
  ): Promise<void> {
    try {
      await this.prisma.verificationLog.create({
        data: {
          identifier,
          type,
          purpose: purpose as any,
          status,
          errorMessage,
          ipAddress,
          userAgent
        }
      });
    } catch (error) {
      console.error('Failed to log send attempt:', error);
    }
  }

  // sendCode方法 - sendVerificationCode的别名，保持向后兼容
  async sendCode(
    identifier: string,
    type: 'EMAIL' | 'SMS',
    purpose: 'LOGIN' = 'LOGIN',
    ipAddress?: string,
    userAgent?: string
  ): Promise<{ success: boolean; message: string; expiresIn?: number }> {
    return this.sendVerificationCode(identifier, type, purpose, ipAddress, userAgent);
  }
}

export default VerificationService;
