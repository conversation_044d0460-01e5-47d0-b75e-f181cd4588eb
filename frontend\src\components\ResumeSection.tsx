import React, { useEffect, useState } from 'react';
import { FileText, Upload, CheckCircle, X, AlertCircle } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import useResumeStore from '../stores/resumeStore';
import useAuthStore from '../stores/authStore';

// 不再需要 UploadStatus 接口

const ResumeSection: React.FC = () => {
  const navigate = useNavigate();
  const { isAuthenticated } = useAuthStore();
  const { uploadedResume, clearUploadedResume, resumes, loadResumes, deleteResume, getCurrentDisplayResume, hasAnyResume } = useResumeStore();
  const [loadingState, setLoadingState] = useState<'idle' | 'loading' | 'success' | 'error'>('idle');
  const [deletingId, setDeletingId] = useState<string | null>(null);
  
  // 在组件挂载时加载用户简历数据
  useEffect(() => {
    const fetchResumes = async () => {
      // 只有在用户已登录时才加载简历数据
      if (isAuthenticated && loadingState === 'idle') {
        try {
          setLoadingState('loading');
          await loadResumes();
          setLoadingState('success');
        } catch (error) {
          console.error('加载简历失败:', error);
          setLoadingState('error');
        }
      }
    };
    
    fetchResumes();
  }, [isAuthenticated, loadResumes, loadingState]);
  
  // 使用新的状态管理方法
  const currentDisplayResume = getCurrentDisplayResume();
  const hasResume = hasAnyResume();
  const latestResume = resumes && resumes.length > 0 ? resumes[0] : null;

  const handleFileSelect = () => {
    // 跳转到简历上传页面，使用相对路径确保在当前布局内跳转
    // 使用 replace: true 可以确保替换当前页面而不是添加到历史记录中
    navigate('/resume-upload', { replace: true });
  };

  const handleDeleteResume = async (resumeId?: string) => {
    // 如果有简历ID，则从后端删除简历
    if (resumeId) {
      try {
        setDeletingId(resumeId);
        await deleteResume(resumeId);
        console.log('删除简历成功，正在重新加载简历列表...');
        
        // 删除成功后清除本地上传状态
        clearUploadedResume();
        
        // 重新加载简历列表，确保前端显示最新数据
        setLoadingState('loading');
        await loadResumes();
        setLoadingState('success');
        
        setDeletingId(null);
      } catch (error) {
        console.error('删除简历失败:', error);
        setDeletingId(null);
        setLoadingState('error');
      }
    } else {
      // 如果没有ID，只清除本地上传状态
      clearUploadedResume();
    }
  };

  // 移除了 handleFileChange 函数，因为我们现在直接跳转到面试准备页面
  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl p-4 shadow-sm relative overflow-hidden transition-colors">
      <div className="absolute top-0 left-0 w-2 h-full bg-gradient-to-b from-emerald-400 to-teal-500"></div>

      <div className="flex items-start justify-between">
        <div className="flex-1">
          <div className="flex items-center gap-3 mb-2">
            <span className="p-2 bg-emerald-50 dark:bg-emerald-900/30 rounded-xl">
              <FileText className="w-5 h-5 text-emerald-600 dark:text-emerald-400" />
            </span>
            <h3 className="text-lg font-bold text-gray-800 dark:text-white">简历解析</h3>
          </div>

          <div className="grid grid-cols-2 gap-2">
            <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-300">
              <CheckCircle className="w-4 h-4 text-emerald-500 dark:text-emerald-400" />
              <span>AI 分析职业技能</span>
            </div>
            <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-300">
              <CheckCircle className="w-4 h-4 text-emerald-500 dark:text-emerald-400" />
              <span>智能推荐重点</span>
              {/* 使用新的优先级显示逻辑 */}
              {hasResume && currentDisplayResume && (
                <div className="ml-2 flex items-center gap-1 px-2 py-1 bg-emerald-100 dark:bg-emerald-900/30 text-emerald-700 dark:text-emerald-300 text-xs rounded-full">
                  <span>[{currentDisplayResume.fileName}]已上传</span>
                  {currentDisplayResume.source === 'temporary' && (
                    <span className="text-xs text-blue-600 bg-blue-100 px-1 py-0.5 rounded ml-1">
                      新
                    </span>
                  )}
                  <button
                    onClick={() => handleDeleteResume(latestResume?.id)}
                    disabled={deletingId !== null}
                    className={`ml-1 p-0.5 ${deletingId ? 'opacity-50 cursor-not-allowed' : 'hover:bg-emerald-200 dark:hover:bg-emerald-800'} rounded-full transition-colors`}
                    title="删除简历"
                  >
                    {deletingId === latestResume?.id ? (
                      <span className="animate-pulse">•</span>
                    ) : (
                      <X className="w-3 h-3 text-emerald-600 dark:text-emerald-400 hover:text-emerald-800 dark:hover:text-emerald-200" />
                    )}
                  </button>
                </div>
              )}
              
              {/* 显示加载状态或错误信息 */}
              {loadingState === 'loading' && (
                <div className="ml-2 text-xs text-gray-500 dark:text-gray-400">加载中...</div>
              )}

              {loadingState === 'error' && (
                <div className="ml-2 flex items-center gap-1 text-xs text-red-500 dark:text-red-400">
                  <AlertCircle className="w-3 h-3" />
                  <span>加载失败</span>
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="flex flex-col items-end gap-2">
          {/* 移除文件输入框，因为我们现在直接跳转到面试准备页面 */}
          <button
            onClick={handleFileSelect}
            className="px-4 py-2 rounded-lg font-medium flex items-center gap-2 transition-all bg-gray-900 dark:bg-gray-700 hover:bg-gray-800 dark:hover:bg-gray-600 text-white"
          >
            <Upload className="w-4 h-4" />
            上传简历
          </button>
          <span className="text-xs text-gray-500 dark:text-gray-400">
            支持 PDF、Word、TXT 格式（≤10MB）
          </span>
        </div>
      </div>
    </div>
  );
};

export default ResumeSection;