# 验证码登录系统测试方案与质量保证

## 1. 测试策略概述

### 1.1 测试目标
- 确保验证码发送和验证功能正常
- 验证安全防护机制有效性
- 保证用户体验流畅性
- 确认系统性能和稳定性

### 1.2 测试范围
- 单元测试：核心业务逻辑
- 集成测试：API接口和数据流
- 端到端测试：完整用户流程
- 安全测试：防护机制验证
- 性能测试：并发和负载测试

### 1.3 测试环境
- 开发环境：本地开发测试
- 测试环境：模拟生产环境
- 预生产环境：生产前验证
- 生产环境：线上监控测试

## 2. 单元测试

### 2.1 后端单元测试

#### 验证码服务测试 (verificationService.test.ts)

```typescript
import { VerificationService } from '../services/verificationService';
import { PrismaClient } from '@prisma/client';
import Redis from 'ioredis';

describe('VerificationService', () => {
  let service: VerificationService;
  let mockPrisma: jest.Mocked<PrismaClient>;
  let mockRedis: jest.Mocked<Redis>;

  beforeEach(() => {
    mockPrisma = {
      verificationCode: {
        create: jest.fn(),
        updateMany: jest.fn(),
      },
      verificationLog: {
        create: jest.fn(),
      },
    } as any;

    mockRedis = {
      get: jest.fn(),
      setex: jest.fn(),
      del: jest.fn(),
    } as any;

    service = new VerificationService();
    (service as any).prisma = mockPrisma;
    (service as any).redis = mockRedis;
  });

  describe('generateCode', () => {
    it('应该生成6位数字验证码', () => {
      const code = service.generateCode();
      expect(code).toMatch(/^\d{6}$/);
      expect(code.length).toBe(6);
    });

    it('生成的验证码应该在100000-999999范围内', () => {
      const code = parseInt(service.generateCode());
      expect(code).toBeGreaterThanOrEqual(100000);
      expect(code).toBeLessThanOrEqual(999999);
    });
  });

  describe('sendVerificationCode', () => {
    it('应该成功发送邮箱验证码', async () => {
      mockRedis.get.mockResolvedValue(null);
      mockPrisma.verificationCode.create.mockResolvedValue({} as any);
      mockRedis.setex.mockResolvedValue('OK');

      const result = await service.sendVerificationCode('<EMAIL>', 'EMAIL');

      expect(result.success).toBe(true);
      expect(result.message).toBe('验证码已发送');
      expect(mockPrisma.verificationCode.create).toHaveBeenCalled();
    });

    it('应该拒绝频繁发送请求', async () => {
      mockRedis.get.mockResolvedValue(Date.now().toString());

      const result = await service.sendVerificationCode('<EMAIL>', 'EMAIL');

      expect(result.success).toBe(false);
      expect(result.message).toContain('请等待');
    });
  });

  describe('verifyCode', () => {
    it('应该验证正确的验证码', async () => {
      const hashedCode = await (service as any).hashCode('123456');
      mockRedis.get.mockResolvedValue(JSON.stringify({
        code: hashedCode,
        attempts: 0,
        createdAt: Date.now()
      }));

      const result = await service.verifyCode('<EMAIL>', '123456', 'EMAIL');

      expect(result.success).toBe(true);
      expect(result.message).toBe('验证成功');
    });

    it('应该拒绝错误的验证码', async () => {
      const hashedCode = await (service as any).hashCode('123456');
      mockRedis.get.mockResolvedValue(JSON.stringify({
        code: hashedCode,
        attempts: 0,
        createdAt: Date.now()
      }));

      const result = await service.verifyCode('<EMAIL>', '654321', 'EMAIL');

      expect(result.success).toBe(false);
      expect(result.message).toContain('验证码错误');
    });

    it('应该在3次错误后锁定账户', async () => {
      const hashedCode = await (service as any).hashCode('123456');
      mockRedis.get.mockResolvedValue(JSON.stringify({
        code: hashedCode,
        attempts: 2,
        createdAt: Date.now()
      }));

      const result = await service.verifyCode('<EMAIL>', '654321', 'EMAIL');

      expect(result.success).toBe(false);
      expect(result.isLocked).toBe(true);
      expect(result.message).toContain('锁定');
    });
  });
});
```

#### 邮件服务测试 (emailService.test.ts)

```typescript
import { EmailService } from '../services/emailService';
import nodemailer from 'nodemailer';

jest.mock('nodemailer');

describe('EmailService', () => {
  let service: EmailService;
  let mockTransporter: jest.Mocked<any>;

  beforeEach(() => {
    mockTransporter = {
      sendMail: jest.fn(),
    };
    (nodemailer.createTransport as jest.Mock).mockReturnValue(mockTransporter);
    service = new EmailService();
  });

  describe('sendVerificationCode', () => {
    it('应该成功发送验证码邮件', async () => {
      mockTransporter.sendMail.mockResolvedValue({ messageId: 'test-id' });

      await service.sendVerificationCode('<EMAIL>', '123456');

      expect(mockTransporter.sendMail).toHaveBeenCalledWith({
        from: expect.any(String),
        to: '<EMAIL>',
        subject: '【面试君】登录验证码',
        html: expect.stringContaining('123456')
      });
    });

    it('应该处理发送失败的情况', async () => {
      mockTransporter.sendMail.mockRejectedValue(new Error('SMTP Error'));

      await expect(service.sendVerificationCode('<EMAIL>', '123456'))
        .rejects.toThrow('SMTP Error');
    });
  });
});
```

### 2.2 前端单元测试

#### 验证码输入组件测试 (VerificationCodeInput.test.tsx)

```typescript
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import VerificationCodeInput from '../components/ui/VerificationCodeInput';

describe('VerificationCodeInput', () => {
  const mockOnChange = jest.fn();

  beforeEach(() => {
    mockOnChange.mockClear();
  });

  it('应该渲染6个输入框', () => {
    render(<VerificationCodeInput value="" onChange={mockOnChange} />);
    
    const inputs = screen.getAllByRole('textbox');
    expect(inputs).toHaveLength(6);
  });

  it('应该只允许数字输入', async () => {
    const user = userEvent.setup();
    render(<VerificationCodeInput value="" onChange={mockOnChange} />);
    
    const firstInput = screen.getAllByRole('textbox')[0];
    await user.type(firstInput, 'a1b2c3');
    
    expect(mockOnChange).toHaveBeenCalledWith('123');
  });

  it('应该自动跳转到下一个输入框', async () => {
    const user = userEvent.setup();
    render(<VerificationCodeInput value="" onChange={mockOnChange} />);
    
    const inputs = screen.getAllByRole('textbox');
    await user.type(inputs[0], '1');
    
    expect(inputs[1]).toHaveFocus();
  });

  it('应该支持粘贴完整验证码', async () => {
    const user = userEvent.setup();
    render(<VerificationCodeInput value="" onChange={mockOnChange} />);
    
    const firstInput = screen.getAllByRole('textbox')[0];
    await user.click(firstInput);
    
    // 模拟粘贴事件
    fireEvent.paste(firstInput, {
      clipboardData: {
        getData: () => '123456'
      }
    });
    
    expect(mockOnChange).toHaveBeenCalledWith('123456');
  });

  it('应该显示错误状态', () => {
    render(
      <VerificationCodeInput 
        value="123456" 
        onChange={mockOnChange} 
        error="验证码错误" 
      />
    );
    
    expect(screen.getByText('验证码错误')).toBeInTheDocument();
    
    const inputs = screen.getAllByRole('textbox');
    inputs.forEach(input => {
      expect(input).toHaveClass('border-red-500');
    });
  });
});
```

## 3. 集成测试

### 3.1 API集成测试

#### 验证码发送API测试

```typescript
import request from 'supertest';
import app from '../app';
import { PrismaClient } from '@prisma/client';
import Redis from 'ioredis';

describe('POST /api/auth/send-verification-code', () => {
  let prisma: PrismaClient;
  let redis: Redis;

  beforeAll(async () => {
    prisma = new PrismaClient();
    redis = new Redis(process.env.REDIS_URL);
  });

  afterAll(async () => {
    await prisma.$disconnect();
    await redis.disconnect();
  });

  beforeEach(async () => {
    // 清理测试数据
    await redis.flushall();
    await prisma.verificationCode.deleteMany();
    await prisma.verificationLog.deleteMany();
  });

  it('应该成功发送邮箱验证码', async () => {
    // 创建测试用户
    await prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: 'hashedpassword',
        balance: { create: {} }
      }
    });

    const response = await request(app)
      .post('/api/auth/send-verification-code')
      .send({
        identifier: '<EMAIL>',
        type: 'EMAIL',
        purpose: 'LOGIN'
      });

    expect(response.status).toBe(200);
    expect(response.body.success).toBe(true);
    expect(response.body.data.expiresIn).toBe(300);

    // 验证数据库记录
    const codeRecord = await prisma.verificationCode.findFirst({
      where: { identifier: '<EMAIL>' }
    });
    expect(codeRecord).toBeTruthy();
  });

  it('应该拒绝不存在的用户', async () => {
    const response = await request(app)
      .post('/api/auth/send-verification-code')
      .send({
        identifier: '<EMAIL>',
        type: 'EMAIL',
        purpose: 'LOGIN'
      });

    expect(response.status).toBe(404);
    expect(response.body.success).toBe(false);
    expect(response.body.error.code).toBe('USER_NOT_FOUND');
  });

  it('应该限制发送频率', async () => {
    await prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: 'hashedpassword',
        balance: { create: {} }
      }
    });

    // 第一次发送
    await request(app)
      .post('/api/auth/send-verification-code')
      .send({
        identifier: '<EMAIL>',
        type: 'EMAIL',
        purpose: 'LOGIN'
      });

    // 立即再次发送
    const response = await request(app)
      .post('/api/auth/send-verification-code')
      .send({
        identifier: '<EMAIL>',
        type: 'EMAIL',
        purpose: 'LOGIN'
      });

    expect(response.status).toBe(429);
    expect(response.body.success).toBe(false);
    expect(response.body.error.code).toBe('RATE_LIMITED');
  });
});
```

#### 验证码登录API测试

```typescript
describe('POST /api/auth/login-with-code', () => {
  it('应该成功验证码登录', async () => {
    // 创建用户和验证码
    const user = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: 'hashedpassword',
        balance: { create: {} }
      }
    });

    const code = '123456';
    const hashedCode = crypto.createHash('sha256')
      .update(code + process.env.JWT_SECRET)
      .digest('hex');

    await redis.setex(
      'verification:<EMAIL>:EMAIL:LOGIN',
      300,
      JSON.stringify({
        code: hashedCode,
        attempts: 0,
        createdAt: Date.now()
      })
    );

    const response = await request(app)
      .post('/api/auth/login-with-code')
      .send({
        identifier: '<EMAIL>',
        code: '123456',
        type: 'EMAIL'
      });

    expect(response.status).toBe(200);
    expect(response.body.success).toBe(true);
    expect(response.body.data.token).toBeTruthy();
    expect(response.body.data.user.email).toBe('<EMAIL>');
  });

  it('应该拒绝错误的验证码', async () => {
    const user = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: 'hashedpassword',
        balance: { create: {} }
      }
    });

    const hashedCode = crypto.createHash('sha256')
      .update('123456' + process.env.JWT_SECRET)
      .digest('hex');

    await redis.setex(
      'verification:<EMAIL>:EMAIL:LOGIN',
      300,
      JSON.stringify({
        code: hashedCode,
        attempts: 0,
        createdAt: Date.now()
      })
    );

    const response = await request(app)
      .post('/api/auth/login-with-code')
      .send({
        identifier: '<EMAIL>',
        code: '654321',
        type: 'EMAIL'
      });

    expect(response.status).toBe(400);
    expect(response.body.success).toBe(false);
    expect(response.body.error.code).toBe('INVALID_CODE');
  });
});
```

## 4. 端到端测试

### 4.1 Playwright E2E测试

```typescript
import { test, expect } from '@playwright/test';

test.describe('验证码登录流程', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/login');
  });

  test('邮箱验证码登录完整流程', async ({ page }) => {
    // 选择邮箱验证码登录
    await page.click('text=邮箱验证码');

    // 输入邮箱
    await page.fill('input[type="email"]', '<EMAIL>');

    // 点击获取验证码
    await page.click('text=获取验证码');

    // 等待成功提示
    await expect(page.locator('text=验证码已发送')).toBeVisible();

    // 验证按钮变为倒计时状态
    await expect(page.locator('text=秒后重试')).toBeVisible();

    // 输入验证码（模拟从邮件中获取）
    const codeInputs = page.locator('input[type="text"][maxlength="1"]');
    await codeInputs.nth(0).fill('1');
    await codeInputs.nth(1).fill('2');
    await codeInputs.nth(2).fill('3');
    await codeInputs.nth(3).fill('4');
    await codeInputs.nth(4).fill('5');
    await codeInputs.nth(5).fill('6');

    // 点击登录
    await page.click('text=登录');

    // 验证登录成功跳转
    await expect(page).toHaveURL('/dashboard');
    await expect(page.locator('text=登录成功')).toBeVisible();
  });

  test('验证码错误处理', async ({ page }) => {
    await page.click('text=邮箱验证码');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.click('text=获取验证码');

    // 输入错误验证码
    const codeInputs = page.locator('input[type="text"][maxlength="1"]');
    for (let i = 0; i < 6; i++) {
      await codeInputs.nth(i).fill('0');
    }

    await page.click('text=登录');

    // 验证错误提示
    await expect(page.locator('text=验证码错误')).toBeVisible();
  });

  test('发送频率限制', async ({ page }) => {
    await page.click('text=邮箱验证码');
    await page.fill('input[type="email"]', '<EMAIL>');
    
    // 第一次发送
    await page.click('text=获取验证码');
    await expect(page.locator('text=验证码已发送')).toBeVisible();

    // 立即再次点击
    await page.click('text=获取验证码');
    await expect(page.locator('text=发送过于频繁')).toBeVisible();
  });
});
```

## 5. 性能测试

### 5.1 负载测试脚本

```javascript
// k6负载测试脚本
import http from 'k6/http';
import { check, sleep } from 'k6';

export let options = {
  stages: [
    { duration: '2m', target: 100 }, // 2分钟内增加到100用户
    { duration: '5m', target: 100 }, // 保持100用户5分钟
    { duration: '2m', target: 200 }, // 2分钟内增加到200用户
    { duration: '5m', target: 200 }, // 保持200用户5分钟
    { duration: '2m', target: 0 },   // 2分钟内减少到0用户
  ],
};

export default function () {
  // 测试发送验证码API
  let sendResponse = http.post('http://localhost:3000/api/auth/send-verification-code', {
    identifier: `test${__VU}@example.com`,
    type: 'EMAIL',
    purpose: 'LOGIN'
  }, {
    headers: { 'Content-Type': 'application/json' },
  });

  check(sendResponse, {
    '发送验证码状态为200': (r) => r.status === 200,
    '响应时间小于500ms': (r) => r.timings.duration < 500,
  });

  sleep(1);

  // 测试验证码登录API
  let loginResponse = http.post('http://localhost:3000/api/auth/login-with-code', {
    identifier: `test${__VU}@example.com`,
    code: '123456',
    type: 'EMAIL'
  }, {
    headers: { 'Content-Type': 'application/json' },
  });

  check(loginResponse, {
    '登录响应时间小于300ms': (r) => r.timings.duration < 300,
  });

  sleep(1);
}
```

## 6. 安全测试

### 6.1 安全测试用例

```bash
#!/bin/bash
# security-test.sh

echo "🔒 开始安全测试"

# 测试SQL注入
echo "📝 测试SQL注入防护"
curl -X POST http://localhost:3000/api/auth/send-verification-code \
  -H "Content-Type: application/json" \
  -d '{"identifier":"<EMAIL>; DROP TABLE users;--","type":"EMAIL"}'

# 测试XSS攻击
echo "📝 测试XSS防护"
curl -X POST http://localhost:3000/api/auth/send-verification-code \
  -H "Content-Type: application/json" \
  -d '{"identifier":"<script>alert(\"xss\")</script>@example.com","type":"EMAIL"}'

# 测试暴力破解防护
echo "📝 测试暴力破解防护"
for i in {1..10}; do
  curl -X POST http://localhost:3000/api/auth/login-with-code \
    -H "Content-Type: application/json" \
    -d '{"identifier":"<EMAIL>","code":"000000","type":"EMAIL"}'
  sleep 0.1
done

# 测试频率限制
echo "📝 测试频率限制"
for i in {1..10}; do
  curl -X POST http://localhost:3000/api/auth/send-verification-code \
    -H "Content-Type: application/json" \
    -d '{"identifier":"<EMAIL>","type":"EMAIL"}'
  sleep 0.1
done

echo "✅ 安全测试完成"
```

## 7. 测试自动化

### 7.1 GitHub Actions CI/CD

```yaml
# .github/workflows/test.yml
name: 测试验证码登录系统

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest

    services:
      postgres:
        image: postgres:13
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
    - uses: actions/checkout@v3

    - name: 设置Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'

    - name: 安装依赖
      run: npm ci

    - name: 运行数据库迁移
      run: npx prisma migrate deploy
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_db

    - name: 运行单元测试
      run: npm run test:unit
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_db
        REDIS_URL: redis://localhost:6379
        JWT_SECRET: test-secret

    - name: 运行集成测试
      run: npm run test:integration
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_db
        REDIS_URL: redis://localhost:6379
        JWT_SECRET: test-secret

    - name: 运行E2E测试
      run: npm run test:e2e
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_db
        REDIS_URL: redis://localhost:6379

    - name: 生成测试报告
      run: npm run test:coverage

    - name: 上传覆盖率报告
      uses: codecov/codecov-action@v3
```

## 8. 测试数据管理

### 8.1 测试数据工厂

```typescript
// tests/factories/userFactory.ts
import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

export class UserFactory {
  constructor(private prisma: PrismaClient) {}

  async createUser(overrides: Partial<any> = {}) {
    const defaultData = {
      email: `test${Date.now()}@example.com`,
      password: await bcrypt.hash('password123', 10),
      name: 'Test User',
      balance: { create: {} }
    };

    return this.prisma.user.create({
      data: { ...defaultData, ...overrides },
      include: { balance: true }
    });
  }

  async createVerificationCode(userId: string, overrides: Partial<any> = {}) {
    const defaultData = {
      identifier: `test${Date.now()}@example.com`,
      code: await this.hashCode('123456'),
      type: 'EMAIL',
      purpose: 'LOGIN',
      expiresAt: new Date(Date.now() + 5 * 60 * 1000)
    };

    return this.prisma.verificationCode.create({
      data: { ...defaultData, ...overrides }
    });
  }

  private async hashCode(code: string): Promise<string> {
    return crypto.createHash('sha256')
      .update(code + process.env.JWT_SECRET)
      .digest('hex');
  }
}
```

### 8.2 测试清理脚本

```typescript
// tests/setup/cleanup.ts
export async function cleanupTestData() {
  const prisma = new PrismaClient();
  
  await prisma.verificationLog.deleteMany();
  await prisma.verificationCode.deleteMany();
  await prisma.userBalance.deleteMany();
  await prisma.user.deleteMany();
  
  await prisma.$disconnect();
  
  // 清理Redis测试数据
  const redis = new Redis(process.env.REDIS_URL);
  await redis.flushall();
  await redis.disconnect();
}
```

这个测试方案涵盖了验证码登录系统的全面测试策略，从单元测试到端到端测试，从功能测试到安全测试，确保系统的质量和可靠性。
