import React from 'react';
import { Link } from 'react-router-dom';
import useDocumentTitle from '../hooks/useDocumentTitle';

const NotFoundPage: React.FC = () => {
  // 设置页面标题
  useDocumentTitle('页面未找到');

  return (
    <div className="p-4">
      <h1 className="text-2xl font-bold">404 - 页面未找到 (Page Not Found)</h1>
      <Link to="/" className="text-blue-500 hover:underline">返回首页 (Go back to Home)</Link>
    </div>
  );
};

export default NotFoundPage;
