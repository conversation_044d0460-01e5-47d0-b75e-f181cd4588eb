import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import TabNavigation from './TabNavigation';
import FileUpload from './FileUpload';
import TextPaste from './TextPaste';
import ActionButtons from './ActionButtons';
import { resumeService, ResumeCreateInput, fetchWithAuth } from '../../lib/api/apiService';
import { useToast } from '../../hooks/useToast';
import ToastContainer from '../ui/ToastContainer';
import useResumeStore from '../../stores/resumeStore';
import { Resume } from '../../lib/api/apiService';

const ResumeUploadPage = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState<'upload' | 'paste'>('upload');
  const [file, setFile] = useState<File | null>(null);
  const [text, setText] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Toast功能
  const { toasts, removeToast, showSuccess, showError } = useToast();

  // 简历状态管理
  const { setUploadedResume, addResume } = useResumeStore();

  const handleFileChange = (newFile: File | null) => {
    setFile(newFile);
    if (newFile) {
      setActiveTab('upload');
    }
  };

  const handleTextChange = (newText: string) => {
    setText(newText);
    if (newText) {
      setActiveTab('paste');
    }
  };

  const handleContinue = async () => {
    setIsLoading(true);
    setError(null);

    try {
      if (activeTab === 'upload' && file) {
        console.log('处理文件上传:', file);

        // 第一步：先上传文件到服务器
        const formData = new FormData();
        formData.append('file', file);
        formData.append('fileName', file.name);
        formData.append('fileType', file.type);
        formData.append('fileSize', file.size.toString());

        // 调用文件上传API - 使用统一的认证方式
        const uploadResponse = await fetchWithAuth('/upload/resume', {
          method: 'POST',
          body: formData,
          // 注意：不要设置Content-Type，让浏览器自动设置multipart/form-data边界
        });

        const uploadResult = uploadResponse;
        console.log('文件上传成功:', uploadResult);

        // 第二步：创建简历记录，使用服务器返回的文件路径
        const resumeData: ResumeCreateInput = {
          fileName: file.name,
          filePath: uploadResult.filePath, // 使用服务器返回的安全路径
          fileType: uploadResult.detectedType || file.type,
          fileSize: file.size,
          jobTitle: '' // 可选字段
        };

        // 创建简历记录
        const response = await resumeService.createResume(resumeData);
        console.log('简历记录创建成功:', response);

        // 更新简历状态（本地临时状态和全局状态）
        setUploadedResume(file.name);
        
        // 将新上传的简历添加到全局状态中
        addResume(response);

        // 显示成功提示
        showSuccess(`简历 "${file.name}" 上传成功！`);

        // 延迟导航，让用户看到成功提示
        setTimeout(() => {
          navigate('/dashboard');
        }, 1500);
      } else if (activeTab === 'paste' && text) {
        console.log('处理文本输入:', text);

        // 第一步：将文本作为文件上传到服务器
        const textBlob = new Blob([text], { type: 'text/plain' });
        const textFile = new File([textBlob], `resume_text_${Date.now()}.txt`, { type: 'text/plain' });

        const formData = new FormData();
        formData.append('file', textFile);
        formData.append('fileName', textFile.name);
        formData.append('fileType', 'text/plain');
        formData.append('fileSize', text.length.toString());

        // 调用文件上传API
        const uploadResponse = await fetch('/api/upload/resume', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
          },
          body: formData,
        });

        if (!uploadResponse.ok) {
          const errorData = await uploadResponse.json();
          throw new Error(errorData.message || '文本上传失败');
        }

        const uploadResult = await uploadResponse.json();
        console.log('文本文件上传成功:', uploadResult);

        // 第二步：创建简历记录
        const resumeData: ResumeCreateInput = {
          fileName: textFile.name,
          filePath: uploadResult.filePath,
          fileType: 'text/plain',
          fileSize: text.length,
          jobTitle: '' // 可选字段
        };

        // 创建简历记录
        const response = await resumeService.createResume(resumeData);
        console.log('简历文本记录创建成功:', response);

        // 更新简历状态（本地临时状态和全局状态）
        setUploadedResume('简历文本');
        
        // 将新上传的简历添加到全局状态中
        addResume(response);

        // 显示成功提示
        showSuccess('简历文本上传成功！');

        // 延迟导航，让用户看到成功提示
        setTimeout(() => {
          navigate('/dashboard');
        }, 1500);
      }
    } catch (err: any) {
      console.error('简历上传失败:', err);
      setError(err.message || '上传失败，请稍后重试');
    } finally {
      setIsLoading(false);
    }
  };

  const handleReset = () => {
    // 返回上一页或仪表盘
    navigate('/dashboard');
  };

  // 确保 canContinue 变量的类型为 boolean
  const canContinue: boolean = !isLoading && ((activeTab === 'upload' && !!file) || (activeTab === 'paste' && text.trim().length > 0));

  return (
    <>
      {/* Toast容器 */}
      <ToastContainer toasts={toasts} onRemoveToast={removeToast} />

      <div className="container mx-auto px-6 py-6">
        <header className="mb-5">
          <h1 className="text-3xl font-bold text-gray-800">上传简历</h1>
          <p className="text-gray-600 mt-2">
            请上传简历，AI将依此生成更适合您的问题与答案
          </p>
        </header>

        <div className="bg-white rounded-xl shadow-lg p-7 relative overflow-hidden">
          {isLoading && (
            <div className="absolute inset-0 bg-white bg-opacity-80 flex items-center justify-center z-10">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-500"></div>
            </div>
          )}
          {/* 左侧装饰条 */}
          <div className="absolute top-0 left-0 w-2 h-full bg-gradient-to-b from-emerald-400 to-teal-500"></div>

          <TabNavigation activeTab={activeTab} onChange={setActiveTab} />

          <div className="my-5">
            {activeTab === 'upload' ? (
              <FileUpload file={file} onChange={handleFileChange} />
            ) : (
              <TextPaste text={text} onChange={handleTextChange} />
            )}
          </div>

          {error && (
            <div className="mb-4 p-3 bg-red-50 text-red-600 rounded-lg border border-red-200">
              {error}
            </div>
          )}

          <ActionButtons
            onBack={handleReset}
            onContinue={handleContinue}
            canContinue={canContinue}
            isLoading={isLoading}
          />
        </div>
      </div>
    </>
  );
};

export default ResumeUploadPage;
