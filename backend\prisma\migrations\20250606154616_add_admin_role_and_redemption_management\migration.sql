-- CreateEnum
CREATE TYPE "UserRole" AS ENUM ('USER', 'ADMIN');

-- AlterTable
ALTER TABLE "User" ADD COLUMN     "role" "UserRole" NOT NULL DEFAULT 'USER';

-- AlterTable
ALTER TABLE "redemption_codes" ADD COLUMN     "codePrefix" TEXT,
ADD COLUMN     "createdBy" TEXT,
ADD COLUMN     "description" TEXT,
ADD COLUMN     "isActive" BOOLEAN NOT NULL DEFAULT true,
ADD COLUMN     "usageCount" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN     "usageLimit" INTEGER NOT NULL DEFAULT 1;

-- CreateTable
CREATE TABLE "code_usage_records" (
    "id" TEXT NOT NULL,
    "codeId" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "usedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "benefitType" TEXT NOT NULL,
    "benefitAmount" INTEGER NOT NULL,

    CONSTRAINT "code_usage_records_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "code_usage_records_codeId_idx" ON "code_usage_records"("codeId");

-- CreateIndex
CREATE INDEX "code_usage_records_userId_idx" ON "code_usage_records"("userId");

-- CreateIndex
CREATE INDEX "redemption_codes_createdBy_idx" ON "redemption_codes"("createdBy");
