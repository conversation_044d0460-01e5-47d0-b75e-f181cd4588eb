// AI模拟面试服务
import { EventEmitter } from 'events';
import { AuthenticatedWebSocket } from '../../types/websocket.js';
import {
  MockInterviewQuestionMessage,
  MockInterviewAnswerMessage,
  MockInterviewFeedbackMessage,
  MockInterviewSessionMessage
} from '../../types/websocket.js';
import { LLMService } from '../../services/llmService.js';
import { MockInterviewRecordService } from '../../services/mockInterviewRecordService.js';
import { logger } from '../../utils/logger.js';
import prisma from '../../lib/prisma.js';

interface MockInterviewSession {
  sessionId: string;
  userId: string;
  companyName: string;
  positionName: string;
  interviewLanguage: 'chinese' | 'english';
  answerStyle: 'keywords_conversational' | 'conversational';
  questions: MockQuestion[];
  currentQuestionIndex: number;
  startTime: Date;
  status: 'preparing' | 'in_progress' | 'waiting_for_answer' | 'generating_question' | 'completed';
  answers: MockAnswer[];
}

interface MockQuestion {
  id: string;
  text: string;
  type: 'behavioral' | 'technical' | 'situational' | 'company_specific';
  difficulty: 'easy' | 'medium' | 'hard';
  expectedDuration: number;
  context?: string;
  keywords: string[]; // 期望的关键词
}

interface MockAnswer {
  questionId: string;
  answerText: string;
  answerDuration: number;
  confidence: number;
  timestamp: Date;
  feedback?: MockFeedback;
}

interface MockFeedback {
  score: number;
  strengths: string[];
  improvements: string[];
  keywordsCovered: string[];
  missingKeywords: string[];
  overallAssessment: string;
}

export class MockInterviewService extends EventEmitter {
  private sessions: Map<string, MockInterviewSession> = new Map();
  private questionTemplates: MockQuestion[] = [];
  private llmService: LLMService;
  private recordService: MockInterviewRecordService;

  constructor() {
    super();
    this.llmService = new LLMService();
    this.recordService = new MockInterviewRecordService();
    this.initializeQuestionTemplates();
    logger.info('✅ MockInterviewService initialized with LLM integration and record service');
  }

  /**
   * 初始化问题模板库
   */
  private initializeQuestionTemplates(): void {
    this.questionTemplates = [
      // 行为面试问题
      {
        id: 'behavioral_001',
        text: '请介绍一下你自己，包括你的背景、技能和职业目标。',
        type: 'behavioral',
        difficulty: 'easy',
        expectedDuration: 120,
        keywords: ['背景', '技能', '经验', '目标', '优势']
      },
      {
        id: 'behavioral_002', 
        text: '描述一次你在团队中遇到冲突的情况，你是如何解决的？',
        type: 'behavioral',
        difficulty: 'medium',
        expectedDuration: 180,
        keywords: ['团队合作', '冲突解决', '沟通', '领导力', '协调']
      },
      {
        id: 'behavioral_003',
        text: '告诉我一个你在工作中犯错误的例子，你从中学到了什么？',
        type: 'behavioral', 
        difficulty: 'medium',
        expectedDuration: 150,
        keywords: ['错误', '学习', '改进', '反思', '成长']
      },
      // 技术面试问题
      {
        id: 'technical_001',
        text: '请解释一下你最熟悉的编程语言的特点和优势。',
        type: 'technical',
        difficulty: 'easy',
        expectedDuration: 120,
        keywords: ['编程语言', '特点', '优势', '应用场景', '技术栈']
      },
      {
        id: 'technical_002',
        text: '描述一个你参与的最具挑战性的技术项目，你是如何克服技术难题的？',
        type: 'technical',
        difficulty: 'hard',
        expectedDuration: 240,
        keywords: ['技术项目', '挑战', '解决方案', '创新', '技术深度']
      },
      // 情景面试问题
      {
        id: 'situational_001',
        text: '如果你的项目进度落后于计划，你会采取什么措施来确保按时交付？',
        type: 'situational',
        difficulty: 'medium',
        expectedDuration: 150,
        keywords: ['项目管理', '时间管理', '优先级', '资源调配', '风险控制']
      }
    ];
    
    console.log(`📚 Loaded ${this.questionTemplates.length} question templates`);
  }

  /**
   * 开始AI模拟面试会话
   */
  async startMockInterview(
    sessionId: string,
    userId: string,
    ws: AuthenticatedWebSocket,
    config: {
      companyName: string;
      positionName: string;
      interviewLanguage: 'chinese' | 'english';
      answerStyle: 'keywords_conversational' | 'conversational';
    }
  ): Promise<void> {
    console.log(`🎯 Starting mock interview session: ${sessionId}`);

    // 创建会话
    const session: MockInterviewSession = {
      sessionId,
      userId,
      companyName: config.companyName,
      positionName: config.positionName,
      interviewLanguage: config.interviewLanguage,
      answerStyle: config.answerStyle,
      questions: this.selectQuestionsForPosition(config.positionName),
      currentQuestionIndex: 0,
      startTime: new Date(),
      status: 'preparing',
      answers: []
    };

    this.sessions.set(sessionId, session);

    // 🔥 更新数据库会话的岗位信息
    try {
      const titleJobInfo = `${config.positionName} - ${config.companyName}`;
      await this.updateSessionJobInfo(sessionId, titleJobInfo);
      console.log(`📊 Updated session ${sessionId} with job info: ${titleJobInfo}`);
    } catch (error) {
      console.error(`❌ Failed to update session job info for ${sessionId}:`, error);
    }

    // 🔥 创建面试记录
    try {
      const recordId = await this.recordService.createRecord({
        userId,
        sessionId,
        companyName: config.companyName,
        positionName: config.positionName,
        interviewLanguage: config.interviewLanguage,
        answerStyle: config.answerStyle,
        totalQuestions: session.questions.length
      });

      // 将记录ID存储到会话中
      (session as any).recordId = recordId;
      logger.info(`📝 Created interview record ${recordId} for session ${sessionId}`);
    } catch (error) {
      logger.error(`❌ Failed to create interview record for session ${sessionId}:`, error);
    }

    // 发送会话开始消息
    const sessionStartMessage: MockInterviewSessionMessage = {
      type: 'mock_interview_session_start',
      mode: 'mock',
      sessionId,
      sessionInfo: {
        sessionId,
        companyName: config.companyName,
        positionName: config.positionName,
        totalQuestions: session.questions.length,
        currentQuestionIndex: 0,
        estimatedDuration: session.questions.reduce((total, q) => total + q.expectedDuration, 0) / 60
      },
      status: 'preparing',
      timestamp: Date.now()
    };

    ws.send(JSON.stringify(sessionStartMessage));

    // 🔥 优化：立即发送第一个问题，无需等待
    console.log(`🚀 Immediately sending first question for session: ${sessionId}`);
    this.sendNextQuestion(sessionId, ws);
  }

  /**
   * 根据岗位选择合适的问题
   */
  private selectQuestionsForPosition(positionName: string): MockQuestion[] {
    // 简化版本：选择3-5个问题
    const selectedQuestions: MockQuestion[] = [];
    
    // 总是包含自我介绍
    selectedQuestions.push(this.questionTemplates.find(q => q.id === 'behavioral_001')!);
    
    // 根据岗位类型添加技术问题
    if (positionName.toLowerCase().includes('开发') || positionName.toLowerCase().includes('工程师')) {
      selectedQuestions.push(this.questionTemplates.find(q => q.id === 'technical_001')!);
      selectedQuestions.push(this.questionTemplates.find(q => q.id === 'technical_002')!);
    }
    
    // 添加行为和情景问题
    selectedQuestions.push(this.questionTemplates.find(q => q.id === 'behavioral_002')!);
    selectedQuestions.push(this.questionTemplates.find(q => q.id === 'situational_001')!);
    
    return selectedQuestions;
  }

  /**
   * 发送下一个问题（使用LLM动态生成）
   */
  private async sendNextQuestion(sessionId: string, ws: AuthenticatedWebSocket): Promise<void> {
    logger.info(`🔍 sendNextQuestion called for session: ${sessionId}`);

    const session = this.sessions.get(sessionId);
    if (!session) {
      logger.error(`❌ Session not found: ${sessionId}`);
      return;
    }

    logger.info(`🔍 Session found, currentQuestionIndex: ${session.currentQuestionIndex}, totalQuestions: ${session.questions.length}`);

    if (session.currentQuestionIndex >= session.questions.length) {
      // 面试结束
      this.endMockInterview(sessionId, ws);
      return;
    }

    session.status = 'generating_question';

    try {
      // 🔥 使用LLM动态生成问题
      const interviewContext = {
        companyName: session.companyName,
        positionName: session.positionName,
        interviewLanguage: session.interviewLanguage,
        answerStyle: session.answerStyle,
        previousQuestions: session.answers.map(a => {
          const q = session.questions.find(q => q.id === a.questionId);
          return q ? q.text : '';
        }).filter(Boolean),
        previousAnswers: session.answers.map(a => a.answerText),
        currentQuestionIndex: session.currentQuestionIndex,
        totalQuestions: session.questions.length
      };

      const generatedQuestion = await this.llmService.generateInterviewQuestion(interviewContext);

      // 更新会话中的问题信息
      const currentQuestion = session.questions[session.currentQuestionIndex];
      currentQuestion.text = generatedQuestion.questionText;
      currentQuestion.type = generatedQuestion.questionType;
      currentQuestion.difficulty = generatedQuestion.difficulty;
      currentQuestion.expectedDuration = generatedQuestion.expectedDuration;
      currentQuestion.context = generatedQuestion.context;
      currentQuestion.keywords = generatedQuestion.keywords;

      session.status = 'in_progress';

      const questionMessage: MockInterviewQuestionMessage = {
        type: 'mock_interview_question',
        mode: 'mock',
        sessionId,
        questionId: currentQuestion.id,
        questionText: generatedQuestion.questionText,
        questionType: generatedQuestion.questionType,
        difficulty: generatedQuestion.difficulty,
        expectedDuration: generatedQuestion.expectedDuration,
        context: generatedQuestion.context,
        timestamp: Date.now()
      };

      ws.send(JSON.stringify(questionMessage));
      logger.info(`❓ Sent LLM-generated question ${session.currentQuestionIndex + 1}/${session.questions.length} to session ${sessionId}`);

    } catch (error) {
      logger.error(`❌ Failed to generate question for session ${sessionId}:`, error);

      // 降级到模板问题
      const currentQuestion = session.questions[session.currentQuestionIndex];
      session.status = 'in_progress';

      const questionMessage: MockInterviewQuestionMessage = {
        type: 'mock_interview_question',
        mode: 'mock',
        sessionId,
        questionId: currentQuestion.id,
        questionText: currentQuestion.text,
        questionType: currentQuestion.type,
        difficulty: currentQuestion.difficulty,
        expectedDuration: currentQuestion.expectedDuration,
        context: currentQuestion.context,
        timestamp: Date.now()
      };

      ws.send(JSON.stringify(questionMessage));
      logger.info(`❓ Sent fallback template question ${session.currentQuestionIndex + 1}/${session.questions.length} to session ${sessionId}`);
    }
  }

  /**
   * 处理用户回答
   */
  async handleUserAnswer(sessionId: string, answerMessage: MockInterviewAnswerMessage, ws: AuthenticatedWebSocket): Promise<void> {
    try {
      const session = this.sessions.get(sessionId);
      if (!session) {
        logger.error(`❌ Session not found: ${sessionId}`);
        return;
      }

      logger.info(`💬 Received answer for session ${sessionId}, question ${answerMessage.questionId}`);

      // 保存回答
      const answer: MockAnswer = {
        questionId: answerMessage.questionId,
        answerText: answerMessage.answerText,
        answerDuration: answerMessage.answerDuration,
        confidence: answerMessage.confidence,
        timestamp: new Date()
      };

      session.answers.push(answer);

      // 生成反馈
      logger.info(`🔍 Generating feedback for question ${answerMessage.questionId}...`);
      const feedback = await this.generateFeedback(answerMessage.questionId, answerMessage.answerText, session);
      answer.feedback = feedback;
      logger.info(`✅ Feedback generated successfully for question ${answerMessage.questionId}`);

    // 🔥 保存问题记录到数据库
    try {
      const recordId = (session as any).recordId;
      const question = session.questions.find(q => q.id === answerMessage.questionId);

      if (recordId && question) {
        await this.recordService.addQuestionRecord({
          recordId,
          questionId: answerMessage.questionId,
          questionText: question.text,
          questionType: question.type,
          difficulty: question.difficulty,
          expectedDuration: question.expectedDuration,
          context: question.context,
          keywords: question.keywords,
          answerText: answerMessage.answerText,
          answerDuration: answerMessage.answerDuration,
          score: feedback.score,
          strengths: feedback.strengths,
          improvements: feedback.improvements,
          keywordsCovered: feedback.keywordsCovered,
          missingKeywords: feedback.missingKeywords,
          overallAssessment: feedback.overallAssessment,
          detailedAnalysis: (feedback as any).detailedAnalysis
        });

        logger.info(`📝 Saved question record for ${answerMessage.questionId} in session ${sessionId}`);
      }
    } catch (error) {
      logger.error(`❌ Failed to save question record for session ${sessionId}:`, error);
    }

    // 发送反馈
    const feedbackMessage: MockInterviewFeedbackMessage = {
      type: 'mock_interview_feedback',
      mode: 'mock',
      sessionId,
      questionId: answerMessage.questionId,
      feedback,
      timestamp: Date.now()
    };

    ws.send(JSON.stringify(feedbackMessage));

    // 移动到下一个问题
    session.currentQuestionIndex++;
    session.status = 'generating_question';

    // 等待2秒后发送下一个问题
    setTimeout(() => {
      this.sendNextQuestion(sessionId, ws);
    }, 2000);

    } catch (error) {
      logger.error(`❌ Error handling user answer for session ${sessionId}:`, error);

      // 发送错误消息给前端
      const errorMessage = {
        type: 'error',
        message: 'Failed to process mock interview answer',
        sessionId,
        timestamp: Date.now()
      };

      ws.send(JSON.stringify(errorMessage));
    }
  }

  /**
   * 生成回答反馈（使用LLM智能分析）
   */
  private async generateFeedback(questionId: string, answerText: string, session: MockInterviewSession): Promise<MockFeedback> {
    const question = session.questions.find(q => q.id === questionId);
    if (!question) {
      throw new Error(`Question not found: ${questionId}`);
    }

    try {
      // 🔥 使用LLM进行智能反馈分析
      const interviewContext = {
        companyName: session.companyName,
        positionName: session.positionName,
        interviewLanguage: session.interviewLanguage,
        answerStyle: session.answerStyle,
        previousQuestions: session.answers.map(a => {
          const q = session.questions.find(q => q.id === a.questionId);
          return q ? q.text : '';
        }).filter(Boolean),
        previousAnswers: session.answers.map(a => a.answerText),
        currentQuestionIndex: session.currentQuestionIndex,
        totalQuestions: session.questions.length
      };

      const llmFeedback = await this.llmService.analyzeAnswer(
        question.text,
        answerText,
        interviewContext,
        question.keywords
      );

      logger.info(`📊 Generated LLM feedback for question ${questionId}: score ${llmFeedback.score}/100`);

      return {
        score: llmFeedback.score,
        strengths: llmFeedback.strengths,
        improvements: llmFeedback.improvements,
        keywordsCovered: llmFeedback.keywordsCovered,
        missingKeywords: llmFeedback.missingKeywords,
        overallAssessment: llmFeedback.overallAssessment
      };

    } catch (error) {
      logger.error(`❌ Failed to generate LLM feedback for question ${questionId}:`, error);

      // 降级到基础反馈算法
      return this.generateFallbackFeedback(question, answerText);
    }
  }

  /**
   * 生成备用反馈（当LLM不可用时）
   */
  private generateFallbackFeedback(question: MockQuestion, answerText: string): MockFeedback {
    // 分析关键词覆盖
    const answerLower = answerText.toLowerCase();
    const keywordsCovered = question.keywords.filter(keyword =>
      answerLower.includes(keyword.toLowerCase())
    );
    const missingKeywords = question.keywords.filter(keyword =>
      !answerLower.includes(keyword.toLowerCase())
    );

    // 计算基础分数
    const keywordScore = question.keywords.length > 0
      ? (keywordsCovered.length / question.keywords.length) * 60
      : 50;
    const lengthScore = Math.min(answerText.length / 100, 1) * 20;
    const structureScore = answerText.includes('首先') || answerText.includes('其次') || answerText.includes('最后') ? 20 : 10;

    const totalScore = Math.min(keywordScore + lengthScore + structureScore, 100);

    return {
      score: Math.round(totalScore),
      strengths: this.generateStrengths(keywordsCovered, answerText),
      improvements: this.generateImprovements(missingKeywords, answerText),
      keywordsCovered,
      missingKeywords,
      overallAssessment: this.generateOverallAssessment(totalScore, question.type)
    };
  }

  /**
   * 生成优点评价
   */
  private generateStrengths(keywordsCovered: string[], answerText: string): string[] {
    const strengths: string[] = [];
    
    if (keywordsCovered.length > 0) {
      strengths.push(`很好地涵盖了关键要点：${keywordsCovered.join('、')}`);
    }
    
    if (answerText.length > 200) {
      strengths.push('回答内容详细，信息量丰富');
    }
    
    if (answerText.includes('例如') || answerText.includes('比如')) {
      strengths.push('善于使用具体例子来支撑观点');
    }
    
    return strengths.length > 0 ? strengths : ['回答态度积极，表达清晰'];
  }

  /**
   * 生成改进建议
   */
  private generateImprovements(missingKeywords: string[], answerText: string): string[] {
    const improvements: string[] = [];
    
    if (missingKeywords.length > 0) {
      improvements.push(`可以进一步阐述：${missingKeywords.join('、')}`);
    }
    
    if (answerText.length < 100) {
      improvements.push('回答可以更加详细，提供更多具体信息');
    }
    
    if (!answerText.includes('例如') && !answerText.includes('比如')) {
      improvements.push('建议使用具体例子来增强说服力');
    }
    
    return improvements.length > 0 ? improvements : ['整体表现良好，继续保持'];
  }

  /**
   * 生成总体评价
   */
  private generateOverallAssessment(score: number, questionType: string): string {
    const typeMap = {
      'behavioral': '行为面试',
      'technical': '技术面试', 
      'situational': '情景面试',
      'company_specific': '公司相关'
    };
    
    const typeName = typeMap[questionType as keyof typeof typeMap] || '面试';
    
    if (score >= 80) {
      return `在${typeName}问题上表现优秀，回答全面且有深度。`;
    } else if (score >= 60) {
      return `在${typeName}问题上表现良好，有一定改进空间。`;
    } else {
      return `在${typeName}问题上需要加强，建议多练习相关内容。`;
    }
  }

  /**
   * 结束模拟面试
   */
  private async endMockInterview(sessionId: string, ws: AuthenticatedWebSocket): Promise<void> {
    const session = this.sessions.get(sessionId);
    if (!session) return;

    session.status = 'completed';

    // 🔥 完成面试记录
    try {
      const recordId = (session as any).recordId;
      if (recordId) {
        // 生成总体反馈
        const averageScore = session.answers.reduce((sum, answer) => {
          return sum + (answer.feedback?.score || 0);
        }, 0) / Math.max(session.answers.length, 1);

        const overallFeedback = this.generateOverallFeedback(session, averageScore);

        await this.recordService.completeRecord(sessionId, overallFeedback);
        logger.info(`📝 Completed interview record for session ${sessionId} with average score ${averageScore.toFixed(1)}`);
      }
    } catch (error) {
      logger.error(`❌ Failed to complete interview record for session ${sessionId}:`, error);
    }

    const sessionEndMessage: MockInterviewSessionMessage = {
      type: 'mock_interview_session_end',
      mode: 'mock',
      sessionId,
      sessionInfo: {
        sessionId,
        companyName: session.companyName,
        positionName: session.positionName,
        totalQuestions: session.questions.length,
        currentQuestionIndex: session.currentQuestionIndex,
        estimatedDuration: 0
      },
      status: 'completed',
      timestamp: Date.now()
    };

    ws.send(JSON.stringify(sessionEndMessage));
    logger.info(`🏁 Mock interview completed for session: ${sessionId}`);
  }

  /**
   * 生成总体反馈
   */
  private generateOverallFeedback(session: MockInterviewSession, averageScore: number): string {
    const totalQuestions = session.answers.length;
    const language = session.interviewLanguage;

    if (language === 'chinese') {
      if (averageScore >= 85) {
        return `恭喜！您在本次${session.positionName}模拟面试中表现优秀，平均得分${averageScore.toFixed(1)}分。您展现了扎实的专业能力和良好的表达技巧。建议继续保持这种水准，在正式面试中一定会有出色的表现。`;
      } else if (averageScore >= 70) {
        return `您在本次${session.positionName}模拟面试中表现良好，平均得分${averageScore.toFixed(1)}分。整体回答较为完整，但在某些方面还有提升空间。建议针对薄弱环节进行针对性练习。`;
      } else if (averageScore >= 60) {
        return `您在本次${session.positionName}模拟面试中表现中等，平均得分${averageScore.toFixed(1)}分。建议多关注问题的核心要点，提供更多具体例子来支撑您的回答。`;
      } else {
        return `本次${session.positionName}模拟面试还有较大提升空间，平均得分${averageScore.toFixed(1)}分。建议多练习相关问题，加强对岗位要求的理解，并提高回答的结构性和完整性。`;
      }
    } else {
      if (averageScore >= 85) {
        return `Congratulations! You performed excellently in this ${session.positionName} mock interview with an average score of ${averageScore.toFixed(1)}. You demonstrated solid professional skills and good communication abilities. Keep up this level of performance for your actual interviews.`;
      } else if (averageScore >= 70) {
        return `You performed well in this ${session.positionName} mock interview with an average score of ${averageScore.toFixed(1)}. Your answers were generally complete, but there's room for improvement in some areas. Consider targeted practice for weaker areas.`;
      } else if (averageScore >= 60) {
        return `Your performance in this ${session.positionName} mock interview was average with a score of ${averageScore.toFixed(1)}. Focus more on the core points of questions and provide more specific examples to support your answers.`;
      } else {
        return `There's significant room for improvement in this ${session.positionName} mock interview with a score of ${averageScore.toFixed(1)}. Practice more relevant questions, strengthen your understanding of job requirements, and improve the structure and completeness of your answers.`;
      }
    }
  }

  /**
   * 获取会话信息
   */
  getSession(sessionId: string): MockInterviewSession | undefined {
    return this.sessions.get(sessionId);
  }

  /**
   * 更新数据库会话的岗位信息
   */
  private async updateSessionJobInfo(sessionId: string, titleJobInfo: string): Promise<void> {
    try {
      await prisma.interviewSession.update({
        where: { id: sessionId },
        data: { titleJobInfo }
      });
      console.log(`📊 Updated session ${sessionId} titleJobInfo to: ${titleJobInfo}`);
    } catch (error) {
      console.error(`❌ Failed to update titleJobInfo for session ${sessionId}:`, error);
      throw error;
    }
  }

  /**
   * 清理会话
   */
  cleanupSession(sessionId: string): void {
    this.sessions.delete(sessionId);
    console.log(`🧹 Cleaned up mock interview session: ${sessionId}`);
  }
}
