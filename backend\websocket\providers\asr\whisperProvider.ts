// OpenAI Whisper ASR提供商
import OpenAI from 'openai';
import { ASRServiceInterface, ASRResult, ASROptions, ASRConfig, ASRProvider, ASRError } from '../../../types/asr.js';

export class WhisperProvider implements ASRServiceInterface {
  private config: ASRConfig;
  private openai: OpenAI | null = null;

  constructor() {
    this.config = {
      provider: ASRProvider.WHISPER,
      priority: 4,
      timeout: 20000,
      retryCount: 2,
      weight: 0.8,
      enabled: true
    };

    this.initializeOpenAI();
  }

  getName(): string {
    return ASRProvider.WHISPER;
  }

  isAvailable(): boolean {
    return this.config.enabled && !!this.openai && !!process.env.OPENAI_API_KEY;
  }

  getConfig(): ASRConfig {
    return { ...this.config };
  }

  updateConfig(config: Partial<ASRConfig>): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * 初始化OpenAI客户端
   */
  private initializeOpenAI(): void {
    try {
      const apiKey = process.env.OPENAI_API_KEY;
      if (apiKey) {
        this.openai = new OpenAI({ apiKey });
        console.log('✅ OpenAI Whisper client initialized');
      } else {
        console.warn('⚠️ OpenAI API key not found, Whisper provider disabled');
        this.config.enabled = false;
      }
    } catch (error) {
      console.error('❌ Failed to initialize OpenAI client:', error);
      this.config.enabled = false;
    }
  }

  /**
   * OpenAI Whisper语音识别
   */
  async recognize(audioBuffer: Buffer, options?: ASROptions): Promise<ASRResult> {
    const startTime = Date.now();
    
    if (!this.isAvailable() || !this.openai) {
      throw this.createError('OpenAI Whisper service not available', 'SERVICE_UNAVAILABLE', false);
    }

    console.log(`🎯 OpenAI Whisper: Starting recognition, audio size: ${audioBuffer.length} bytes`);

    try {
      // 🔧 修复：在Node.js中使用Blob而不是File
      console.log('🔄 Converting audio buffer to Blob for Whisper API...');

      // 将PCM转换为WAV格式（Whisper需要有效的音频格式）
      const wavBuffer = this.convertPCMToWAV(audioBuffer, options?.sampleRate || 16000);
      console.log(`🎵 Converted PCM (${audioBuffer.length} bytes) to WAV (${wavBuffer.length} bytes)`);

      // 创建Blob对象用于Whisper API
      const audioBlob = new Blob([wavBuffer], { type: "audio/wav" });

      console.log('📤 Calling OpenAI Whisper API...');

      // 调用Whisper API
      const transcription = await this.openai.audio.transcriptions.create({
        file: audioBlob as any, // TypeScript类型兼容性
        model: "whisper-1",
        language: options?.language || "zh",
        response_format: "text",
        temperature: 0.2 // 降低随机性，提高一致性
      });

      const processingTime = Date.now() - startTime;
      
      if (typeof transcription === 'string' && transcription.trim() !== '') {
        const cleanedText = this.cleanText(transcription.trim());
        
        console.log(`✅ OpenAI Whisper completed in ${processingTime}ms: "${cleanedText}"`);
        
        return {
          text: cleanedText,
          confidence: this.calculateConfidence(cleanedText),
          provider: ASRProvider.WHISPER,
          processingTime,
          timestamp: Date.now(),
          isPartial: false,
          language: options?.language || "zh"
        };
      } else {
        throw this.createError('OpenAI Whisper returned empty result', 'PROCESSING_ERROR', true);
      }
    } catch (error) {
      const processingTime = Date.now() - startTime;
      console.error(`❌ OpenAI Whisper error after ${processingTime}ms:`, error);
      
      if (error instanceof Error && (error as ASRError).provider) {
        throw error;
      }
      
      // 处理OpenAI特定错误
      if (this.isOpenAIError(error)) {
        throw this.handleOpenAIError(error);
      }
      
      throw this.createError(`OpenAI Whisper error: ${(error as Error).message}`, 'PROCESSING_ERROR', true);
    }
  }

  /**
   * 检查是否是OpenAI错误
   */
  private isOpenAIError(error: any): boolean {
    return error && (
      error.status || 
      error.code || 
      (error.error && error.error.type)
    );
  }

  /**
   * 处理OpenAI特定错误
   */
  private handleOpenAIError(error: any): ASRError {
    let message = 'OpenAI Whisper API error';
    let code = 'PROCESSING_ERROR';
    let retryable = true;

    if (error.status) {
      switch (error.status) {
        case 400:
          message = 'Invalid audio format or request';
          code = 'INVALID_AUDIO';
          retryable = false;
          break;
        case 401:
          message = 'Invalid API key';
          code = 'AUTHENTICATION_FAILED';
          retryable = false;
          break;
        case 429:
          message = 'Rate limit exceeded';
          code = 'QUOTA_EXCEEDED';
          retryable = true;
          break;
        case 500:
        case 502:
        case 503:
          message = 'OpenAI server error';
          code = 'SERVICE_UNAVAILABLE';
          retryable = true;
          break;
        default:
          message = `OpenAI HTTP error: ${error.status}`;
          break;
      }
    } else if (error.code === 'ECONNABORTED') {
      message = 'OpenAI request timeout';
      code = 'TIMEOUT';
      retryable = true;
    } else if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
      message = 'OpenAI network connection failed';
      code = 'NETWORK_ERROR';
      retryable = true;
    }

    return this.createError(message, code, retryable);
  }

  /**
   * 计算置信度
   */
  private calculateConfidence(text: string): number {
    // Whisper不提供置信度，根据文本质量估算
    if (!text || text.trim().length === 0) {
      return 0.0;
    }
    
    // 基于文本长度和内容质量的简单估算
    const length = text.trim().length;
    const hasChineseChars = /[\u4e00-\u9fa5]/.test(text);
    const hasRepeatedChars = /(.)\1{3,}/.test(text);
    const hasValidPunctuation = /[，。！？；：]/.test(text);
    
    let confidence = 0.7; // 基础置信度
    
    if (length > 20) confidence += 0.1;
    if (length > 50) confidence += 0.1;
    if (hasChineseChars) confidence += 0.05;
    if (hasValidPunctuation) confidence += 0.05;
    if (hasRepeatedChars) confidence -= 0.2;
    
    return Math.max(0.1, Math.min(0.95, confidence));
  }

  /**
   * 🔧 将PCM数据转换为WAV格式
   * Whisper需要完整的WAV文件格式，不能是原始PCM
   */
  private convertPCMToWAV(pcmBuffer: Buffer, sampleRate: number = 16000): Buffer {
    const numChannels = 1;  // 单声道
    const bitsPerSample = 16;  // 16位
    const bytesPerSample = bitsPerSample / 8;
    const blockAlign = numChannels * bytesPerSample;
    const byteRate = sampleRate * blockAlign;
    const dataSize = pcmBuffer.length;
    const fileSize = 36 + dataSize;

    // 创建WAV文件头
    const wavBuffer = Buffer.alloc(44 + dataSize);
    let offset = 0;

    // RIFF头
    wavBuffer.write('RIFF', offset); offset += 4;
    wavBuffer.writeUInt32LE(fileSize, offset); offset += 4;
    wavBuffer.write('WAVE', offset); offset += 4;

    // fmt子块
    wavBuffer.write('fmt ', offset); offset += 4;
    wavBuffer.writeUInt32LE(16, offset); offset += 4;  // fmt chunk size
    wavBuffer.writeUInt16LE(1, offset); offset += 2;   // audio format (PCM)
    wavBuffer.writeUInt16LE(numChannels, offset); offset += 2;
    wavBuffer.writeUInt32LE(sampleRate, offset); offset += 4;
    wavBuffer.writeUInt32LE(byteRate, offset); offset += 4;
    wavBuffer.writeUInt16LE(blockAlign, offset); offset += 2;
    wavBuffer.writeUInt16LE(bitsPerSample, offset); offset += 2;

    // data子块
    wavBuffer.write('data', offset); offset += 4;
    wavBuffer.writeUInt32LE(dataSize, offset); offset += 4;

    // 复制PCM数据
    pcmBuffer.copy(wavBuffer, offset);

    return wavBuffer;
  }

  /**
   * 清理文本
   */
  private cleanText(text: string): string {
    if (!text) return '';

    return text.trim()
      .replace(/\s+/g, ' ') // 合并多个空格
      .replace(/[^\u4e00-\u9fa5a-zA-Z0-9，。！？、；：""''（）\s]/g, '') // 移除特殊字符
      .replace(/^[，。！？、；：\s]+/, '') // 移除开头的标点符号
      .replace(/[，。！？、；：\s]+$/, ''); // 移除结尾多余的标点符号
  }

  /**
   * 创建ASR错误
   */
  private createError(message: string, code: string, retryable: boolean): ASRError {
    const error = new Error(message) as ASRError;
    error.provider = ASRProvider.WHISPER;
    error.code = code;
    error.retryable = retryable;
    return error;
  }

  async destroy(): Promise<void> {
    console.log('🧹 OpenAI Whisper provider destroyed');
    this.openai = null;
  }
}
