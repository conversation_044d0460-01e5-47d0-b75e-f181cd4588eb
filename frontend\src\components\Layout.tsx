import React from 'react';
import { Outlet, useLocation } from 'react-router-dom'; // 从 react-router-dom 导入 Outlet (Import Outlet from react-router-dom)
import Sidebar from './Sidebar';
import Header from './Header';
import Footer from './Footer';
import ToastContainer from './ui/ToastContainer';
import { useBalance } from '../hooks/useBalance';

const Layout: React.FC = () => {
  const location = useLocation();
  const greetingMessage = "下午好"; // 你可以根据时间动态修改 (You can change this dynamically based on time)

  // 初始化余额管理
  useBalance();

  // 检查是否为AI面试页面或充值中心页面或设置页面或个人中心页面或订单页面或消耗记录页面或分享有礼页面
  const isAIInterviewPage = location.pathname.startsWith('/ai-interview');
  const isPricingPage = location.pathname === '/pricing';
  const isSettingsPage = location.pathname === '/settings';
  const isProfilePage = location.pathname === '/profile';
  const isOrdersPage = location.pathname === '/orders';
  const isUsageRecordsPage = location.pathname === '/usage-records';
  const isReferralRewardsPage = location.pathname === '/referral-rewards';

  return (
    <>
      {/* Toast容器 */}
      <ToastContainer />

      <div className="flex h-screen overflow-hidden bg-slate-50 dark:bg-gray-900 transition-colors"> {/* Added dark mode background */}
        <Sidebar />
        <div className="flex-1 flex flex-col overflow-hidden">
          {/* 根据页面类型显示不同的Header */}
          {!isAIInterviewPage && !isPricingPage && !isSettingsPage && !isProfilePage && !isOrdersPage && !isUsageRecordsPage && !isReferralRewardsPage && <Header greeting={greetingMessage} />}

          {/* 主内容区域 */}
          <main className={`flex-1 overflow-hidden ${!isAIInterviewPage && !isPricingPage ? 'overflow-y-auto p-6' : isPricingPage ? 'overflow-y-auto' : ''}`}>
            <Outlet /> {/* 页面组件会在这里渲染 (Page components will render here) */}
          </main>

          {!isAIInterviewPage && !isPricingPage && !isSettingsPage && !isOrdersPage && !isUsageRecordsPage && !isReferralRewardsPage && <Footer />}
        </div>
      </div>
    </>
  );
};

export default Layout;