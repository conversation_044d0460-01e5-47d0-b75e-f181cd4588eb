// 重构后的面试WebSocket服务器 - 主入口文件
import { Server } from 'http';
import { WebSocketServerManager } from './handlers/webSocketServer.js';

// 全局WebSocket服务器管理器实例
let webSocketServerManager: WebSocketServerManager | null = null;

/**
 * 设置WebSocket服务器
 * 这是主要的导出函数，保持与原有代码的API兼容性
 */
export function setupWebSocket(httpServer: Server): void {
  console.log('🚀 Initializing WebSocket Server for interview system...');
  
  try {
    // 创建WebSocket服务器管理器
    webSocketServerManager = new WebSocketServerManager();
    
    // 设置HTTP服务器的WebSocket升级处理
    webSocketServerManager.setupWebSocketUpgrade(httpServer);
    
    console.log('✅ WebSocket Server initialized successfully');
    console.log('📡 WebSocket Server ready to handle connections on /api/ws/interview/:sessionId');
    
    // 设置优雅关闭处理
    setupGracefulShutdown();
    
  } catch (error) {
    console.error('❌ Failed to initialize WebSocket Server:', error);
    throw error;
  }
}

/**
 * 设置优雅关闭处理
 */
function setupGracefulShutdown(): void {
  const shutdownHandler = async (signal: string) => {
    console.log(`📡 Received ${signal}, shutting down WebSocket Server gracefully...`);
    
    if (webSocketServerManager) {
      try {
        await webSocketServerManager.shutdown();
        console.log('✅ WebSocket Server shutdown completed');
      } catch (error) {
        console.error('❌ Error during WebSocket Server shutdown:', error);
      }
    }
    
    process.exit(0);
  };

  // 监听进程信号
  process.on('SIGTERM', () => shutdownHandler('SIGTERM'));
  process.on('SIGINT', () => shutdownHandler('SIGINT'));
  
  // 监听未捕获的异常
  process.on('uncaughtException', (error) => {
    console.error('❌ Uncaught Exception in WebSocket Server:', error);
    shutdownHandler('uncaughtException');
  });
  
  process.on('unhandledRejection', (reason, promise) => {
    console.error('❌ Unhandled Rejection in WebSocket Server:', reason);
    shutdownHandler('unhandledRejection');
  });
}

/**
 * 获取WebSocket服务器统计信息
 */
export function getWebSocketStats(): any {
  if (!webSocketServerManager) {
    return {
      status: 'not_initialized',
      activeConnections: 0,
      activeSessions: 0
    };
  }
  
  return {
    status: 'running',
    ...webSocketServerManager.getServerStats()
  };
}

/**
 * 手动关闭WebSocket服务器
 */
export async function shutdownWebSocket(): Promise<void> {
  if (webSocketServerManager) {
    await webSocketServerManager.shutdown();
    webSocketServerManager = null;
  }
}

// 导出兼容性函数（保持与原代码的兼容性）
// 这些函数在原代码中被其他模块导入使用
export async function callIflytekASR(audioBuffer: Buffer): Promise<string | null> {
  console.warn('⚠️ callIflytekASR is deprecated, ASR services are now managed by ASRServiceManager');
  return null;
}

export async function callAlibabaASR(audioBuffer: Buffer): Promise<string | null> {
  console.warn('⚠️ callAlibabaASR is deprecated, ASR services are now managed by ASRServiceManager');
  return null;
}

export async function callBaiduASR(audioBuffer: Buffer): Promise<string | null> {
  console.warn('⚠️ callBaiduASR is deprecated, ASR services are now managed by ASRServiceManager');
  return null;
}

// 默认导出（如果需要）
export default {
  setupWebSocket,
  getWebSocketStats,
  shutdownWebSocket,
  callIflytekASR,
  callAlibabaASR,
  callBaiduASR
};

/*
=== 重构说明 ===

这个文件是重构后的主入口文件，保持了与原有代码的API兼容性。

主要改进：
1. 模块化架构：将原来的2200+行代码拆分为多个专门的模块
2. 事件驱动：使用EventEmitter模式处理音频处理和ASR结果
3. 并行ASR：同时调用多个ASR服务，取最快的结果
4. 错误处理：完善的错误处理和日志记录
5. 资源管理：统一的资源生命周期管理
6. 类型安全：严格的TypeScript类型定义

模块结构：
- handlers/webSocketServer.ts: WebSocket服务器核心
- handlers/sessionManager.ts: 会话管理
- handlers/messageHandler.ts: 消息处理
- handlers/aiSuggestionService.ts: AI建议服务
- providers/asr/: ASR服务提供商
- providers/audio/: 音频处理
- types/: 类型定义

环境变量配置：
🔥 重构后的环境变量配置：
- DASHSCOPE_API_KEY (主要ASR引擎)
- OPENAI_API_KEY (备用ASR引擎)
- DEEPSEEK_API_KEY (LLM服务)
- JWT_SECRET (认证密钥)

🚫 备用ASR服务环境变量 (暂时注释，日后可启用)：
- IFLYTEK_APPID, IFLYTEK_API_KEY, IFLYTEK_API_SECRET
- ALIBABA_APPKEY, ALIBABA_ACCESS_KEY_ID, ALIBABA_ACCESS_KEY_SECRET
- BAIDU_APP_ID, BAIDU_CLIENT_ID, BAIDU_CLIENT_SECRET

所有原有功能都得到保留：
- 多格式音频处理 (WebM, OGG, MP4, PCM)
- 多ASR服务支持 (讯飞, 阿里, 百度, Whisper)
- 智能文本去重和清理
- 3秒停顿检测和语音合并
- AI建议生成 (DeepSeek)
- 数据库存储 (转录记录)
- 会话管理和用户认证

=== 重构完成 ===
*/
