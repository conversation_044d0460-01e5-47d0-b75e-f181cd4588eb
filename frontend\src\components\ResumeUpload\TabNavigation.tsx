import React from 'react';

interface TabNavigationProps {
  activeTab: 'upload' | 'paste';
  onChange: (tab: 'upload' | 'paste') => void;
}

const TabNavigation: React.FC<TabNavigationProps> = ({ activeTab, onChange }) => {
  return (
    <div className="border-b border-gray-200">
      <div className="flex space-x-8">
        <div
          className={`tab-item pb-3 text-lg font-medium cursor-pointer ${
            activeTab === 'upload' 
              ? 'border-b-2 border-emerald-500 text-emerald-600' 
              : 'text-gray-500 hover:text-gray-700'
          }`}
          onClick={() => onChange('upload')}
          role="button"
          tabIndex={0}
        >
          上传简历
        </div>
        <div
          className={`tab-item pb-3 text-lg font-medium cursor-pointer ${
            activeTab === 'paste' 
              ? 'border-b-2 border-emerald-500 text-emerald-600' 
              : 'text-gray-500 hover:text-gray-700'
          }`}
          onClick={() => onChange('paste')}
          role="button"
          tabIndex={0}
        >
          粘贴文本
        </div>
      </div>
    </div>
  );
};

export default TabNavigation;
