import React, { useState } from 'react';
import { Shield, Palette, MessageSquare, User } from 'lucide-react';
import AccountSecurity from '../components/settings/AccountSecurity';
import ThemeSettings from '../components/settings/ThemeSettings';
import Feedback from '../components/settings/Feedback';
import useDocumentTitle from '../hooks/useDocumentTitle';

type SettingsTab = 'security' | 'theme' | 'feedback';

const Settings: React.FC = () => {
  // 设置页面标题
  useDocumentTitle('设置');

  const [activeTab, setActiveTab] = useState<SettingsTab>('security');

  const tabs = [
    {
      id: 'security' as SettingsTab,
      name: '账户安全',
      icon: Shield,
      description: '修改密码和安全设置',
    },
    {
      id: 'theme' as SettingsTab,
      name: '主题设置',
      icon: Palette,
      description: '个性化界面外观',
    },
    {
      id: 'feedback' as SettingsTab,
      name: '意见反馈',
      icon: MessageSquare,
      description: '提交问题和建议',
    },
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case 'security':
        return <AccountSecurity />;
      case 'theme':
        return <ThemeSettings />;
      case 'feedback':
        return <Feedback />;
      default:
        return <AccountSecurity />;
    }
  };

  return (
    <div className="max-w-6xl mx-auto">
      {/* 页面标题 */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">设置</h1>
        <p className="text-gray-600 dark:text-gray-300">管理您的账户设置和偏好</p>
      </div>

      <div className="grid lg:grid-cols-4 gap-6">
        {/* 侧边导航 */}
        <div className="lg:col-span-1">
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-4 transition-colors">
            <nav className="space-y-2">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`w-full flex items-center gap-3 px-4 py-3 rounded-lg text-left transition-all ${
                      activeTab === tab.id
                        ? 'bg-blue-50 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 border-l-4 border-blue-600'
                        : 'text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-white'
                    }`}
                  >
                    <Icon className="w-5 h-5" />
                    <div>
                      <div className="font-medium">{tab.name}</div>
                      <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        {tab.description}
                      </div>
                    </div>
                  </button>
                );
              })}
            </nav>
          </div>
        </div>

        {/* 主内容区域 */}
        <div className="lg:col-span-3">
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg transition-colors">
            {/* 标签页标题 */}
            <div className="border-b border-gray-200 dark:border-gray-600 px-6 py-4">
              <div className="flex items-center gap-3">
                {(() => {
                  const currentTab = tabs.find(tab => tab.id === activeTab);
                  const Icon = currentTab?.icon || User;
                  return (
                    <>
                      <Icon className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                      <div>
                        <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                          {currentTab?.name}
                        </h2>
                        <p className="text-sm text-gray-600 dark:text-gray-300">
                          {currentTab?.description}
                        </p>
                      </div>
                    </>
                  );
                })()}
              </div>
            </div>

            {/* 标签页内容 */}
            <div className="p-6">
              {renderTabContent()}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Settings;
