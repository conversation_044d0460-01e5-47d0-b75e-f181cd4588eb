import fs from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';

// 文件类型的魔术字节签名
const MAGIC_BYTES = {
  pdf: [0x25, 0x50, 0x44, 0x46], // %PDF
  docx: [0x50, 0x4B, 0x03, 0x04], // PK.. (ZIP format)
  doc: [0xD0, 0xCF, 0x11, 0xE0, 0xA1, 0xB1, 0x1A, 0xE1], // Microsoft Office
  txt: null, // 文本文件没有固定的魔术字节
};

// 允许的文件类型和最大大小
const ALLOWED_TYPES = ['pdf', 'docx', 'doc', 'txt'];
const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB

export interface FileValidationResult {
  isValid: boolean;
  error?: string;
  detectedType?: string;
  safeFileName?: string;
}

/**
 * 检查文件的魔术字节以验证真实文件类型
 */
export function validateFileType(filePath: string, expectedType: string): Promise<FileValidationResult> {
  return new Promise((resolve) => {
    // 对于文本文件，跳过魔术字节检查
    if (expectedType === 'txt') {
      resolve({
        isValid: true,
        detectedType: 'txt',
      });
      return;
    }

    const magicBytes = MAGIC_BYTES[expectedType as keyof typeof MAGIC_BYTES];
    if (!magicBytes) {
      resolve({
        isValid: false,
        error: `不支持的文件类型: ${expectedType}`,
      });
      return;
    }

    // 读取文件的前几个字节
    const buffer = Buffer.alloc(magicBytes.length);
    fs.open(filePath, 'r', (err, fd) => {
      if (err) {
        resolve({
          isValid: false,
          error: `无法读取文件: ${err.message}`,
        });
        return;
      }

      fs.read(fd, buffer, 0, magicBytes.length, 0, (readErr, bytesRead) => {
        fs.close(fd, () => {}); // 确保文件描述符被关闭

        if (readErr) {
          resolve({
            isValid: false,
            error: `读取文件失败: ${readErr.message}`,
          });
          return;
        }

        if (bytesRead < magicBytes.length) {
          resolve({
            isValid: false,
            error: '文件太小或已损坏',
          });
          return;
        }

        // 检查魔术字节是否匹配
        const matches = magicBytes.every((byte, index) => buffer[index] === byte);
        
        if (matches) {
          resolve({
            isValid: true,
            detectedType: expectedType,
          });
        } else {
          resolve({
            isValid: false,
            error: `文件类型不匹配，期望 ${expectedType} 但检测到不同的文件签名`,
          });
        }
      });
    });
  });
}

/**
 * 验证文件大小
 */
export function validateFileSize(filePath: string): Promise<FileValidationResult> {
  return new Promise((resolve) => {
    fs.stat(filePath, (err, stats) => {
      if (err) {
        resolve({
          isValid: false,
          error: `无法获取文件信息: ${err.message}`,
        });
        return;
      }

      if (stats.size > MAX_FILE_SIZE) {
        resolve({
          isValid: false,
          error: `文件大小超过限制 (${Math.round(stats.size / 1024 / 1024)}MB > 10MB)`,
        });
        return;
      }

      resolve({
        isValid: true,
      });
    });
  });
}

/**
 * 生成安全的文件名
 */
export function generateSafeFileName(originalFileName: string): string {
  const ext = path.extname(originalFileName).toLowerCase();
  const uuid = uuidv4();
  return `${uuid}${ext}`;
}

/**
 * 从文件扩展名推断文件类型
 */
export function getFileTypeFromExtension(fileName: string): string | null {
  const ext = path.extname(fileName).toLowerCase().slice(1); // 移除点号
  
  const typeMap: { [key: string]: string } = {
    'pdf': 'pdf',
    'docx': 'docx',
    'doc': 'doc',
    'txt': 'txt',
  };

  return typeMap[ext] || null;
}

/**
 * 综合文件验证
 */
export async function validateFile(filePath: string, originalFileName: string): Promise<FileValidationResult> {
  try {
    // 1. 从文件名推断类型
    const fileType = getFileTypeFromExtension(originalFileName);
    if (!fileType) {
      return {
        isValid: false,
        error: '不支持的文件格式，请上传 PDF、Word 或 TXT 文件',
      };
    }

    if (!ALLOWED_TYPES.includes(fileType)) {
      return {
        isValid: false,
        error: `不允许的文件类型: ${fileType}`,
      };
    }

    // 2. 验证文件大小
    const sizeValidation = await validateFileSize(filePath);
    if (!sizeValidation.isValid) {
      return sizeValidation;
    }

    // 3. 验证文件类型（魔术字节）
    const typeValidation = await validateFileType(filePath, fileType);
    if (!typeValidation.isValid) {
      return typeValidation;
    }

    // 4. 生成安全文件名
    const safeFileName = generateSafeFileName(originalFileName);

    return {
      isValid: true,
      detectedType: fileType,
      safeFileName,
    };

  } catch (error) {
    return {
      isValid: false,
      error: `文件验证失败: ${error instanceof Error ? error.message : '未知错误'}`,
    };
  }
}

/**
 * 检查文件是否存在
 */
export function fileExists(filePath: string): Promise<boolean> {
  return new Promise((resolve) => {
    fs.access(filePath, fs.constants.F_OK, (err) => {
      resolve(!err);
    });
  });
}
