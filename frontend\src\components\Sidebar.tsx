import React, { useState, useRef, useEffect } from 'react';
import { Home, UserCheck, Award, CreditCard, Gift, Settings, LogOut, User, ChevronDown, LogIn, ShoppingBag, FileText } from 'lucide-react';
import { useNavigate, useLocation } from 'react-router-dom';
import useAuthStore from '../stores/authStore';
import useNavigationStore from '../stores/navigationStore';
import useUserBalanceStore from '../stores/userBalanceStore';

// 邮箱掌码函数，将邮箱中间部分替换为星号
const maskEmail = (email: string): string => {
  if (!email || !email.includes('@')) return email;

  const [username, domain] = email.split('@');
  if (username.length <= 3) return email; // 如果用户名太短，不做处理

  const maskedUsername = username.substring(0, 3) + '****';
  return `${maskedUsername}@${domain}`;
};

const Sidebar: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { isAuthenticated, user, logout } = useAuthStore();
  const { activeItem, setActiveItem } = useNavigationStore();
  const { balance, isLoading, fetchBalance } = useUserBalanceStore();
  const [showEmailMenu, setShowEmailMenu] = useState(false);
  const emailMenuRef = useRef<HTMLDivElement>(null);

  // 处理点击邮箱显示/隐藏菜单
  const toggleEmailMenu = () => {
    setShowEmailMenu(!showEmailMenu);
  };

  // 处理登录
  const handleLogin = () => {
    setShowEmailMenu(false);
    navigate('/ai-login');
  };

  // 处理点击其他地方关闭菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (emailMenuRef.current && !emailMenuRef.current.contains(event.target as Node)) {
        setShowEmailMenu(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // 根据当前路径更新active状态
  useEffect(() => {
    const path = location.pathname;
    if (path === '/' || path === '/dashboard') {
      setActiveItem('home');
    } else if (path === '/interview/mock/config' || path.startsWith('/interview/mock/')) {
      setActiveItem('simulation');
    } else if (path === '/ai-interview') {
      setActiveItem('formal');
    } else if (path === '/pricing') {
      setActiveItem('recharge');
    } else if (path === '/referral-rewards') {
      setActiveItem('share');
    }
  }, [location.pathname, setActiveItem]);

  // 当用户登录时获取余额数据
  useEffect(() => {
    if (isAuthenticated) {
      fetchBalance();
    }
  }, [isAuthenticated, fetchBalance]);

  const handleNavigation = (item: string, path: string) => {
    navigate(path);
  };

  const menuItems = [
    {
      key: 'home',
      icon: <Home size={20} />,
      text: '主页',
      path: '/',
      active: activeItem === 'home'
    },
    {
      key: 'simulation',
      icon: <UserCheck size={20} />,
      text: 'AI模拟面试',
      path: '/interview/mock/config',
      active: activeItem === 'simulation'
    },
    {
      key: 'formal',
      icon: <Award size={20} />,
      text: 'AI正式面试',
      path: '/ai-interview',
      active: activeItem === 'formal'
    },
    {
      key: 'recharge',
      icon: <CreditCard size={20} />,
      text: '充值中心',
      path: '/pricing',
      active: activeItem === 'recharge'
    },
    {
      key: 'share',
      icon: <Gift size={20} />,
      text: '分享有礼',
      path: '/referral-rewards',
      active: activeItem === 'share'
    },
  ];

  return (
    <div className="w-72 min-h-screen bg-white dark:bg-gray-900 p-8 flex flex-col transition-colors">
      <div className="flex items-center gap-3 mb-12">
        <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-purple-500 to-indigo-500 flex items-center justify-center text-white font-bold text-xl shadow-lg">
          M
        </div>
        <span className="text-xl font-bold text-gray-800 dark:text-white">面试君</span>
      </div>

      <nav className="flex-1">
        <ul className="space-y-2">
          {menuItems.map((item, index) => (
            <li key={index}>
              <button
                onClick={() => handleNavigation(item.key, item.path)}
                className={`w-full flex items-center gap-4 px-4 py-3 rounded-xl transition-all ${
                  item.active
                    ? 'bg-gray-900 dark:bg-gray-700 text-white shadow-lg'
                    : 'text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800'
                }`}
              >
                {item.icon}
                <span className="font-medium">{item.text}</span>
              </button>
            </li>
          ))}
        </ul>
      </nav>

      <div className="bg-gradient-to-br from-sky-50 to-indigo-50 dark:from-gray-800 dark:to-gray-700 rounded-xl p-6">
        <div className="flex items-center justify-between mb-4">
          <span className="font-medium text-gray-800 dark:text-white">面试余额</span>
          <button
            onClick={() => navigate('/settings')}
            className="p-2 bg-white dark:bg-gray-600 rounded-lg hover:shadow-md transition-shadow"
          >
            <Settings className="w-4 h-4 text-gray-600 dark:text-gray-300" />
          </button>
        </div>

        <div className="space-y-1.5">
          <div className="bg-white bg-opacity-60 dark:bg-gray-600 dark:bg-opacity-60 backdrop-blur-sm rounded-lg py-1.5 px-3 flex items-center justify-between">
            <span className="text-sm text-gray-600 dark:text-gray-300">模拟面试</span>
            <span className="font-medium text-gray-800 dark:text-white">
              {isLoading ? '...' : (balance?.mockInterviewCredits ?? 0)} 次
            </span>
          </div>
          <div className="bg-white bg-opacity-60 dark:bg-gray-600 dark:bg-opacity-60 backdrop-blur-sm rounded-lg py-1.5 px-3 flex items-center justify-between">
            <span className="text-sm text-gray-600 dark:text-gray-300">正式面试</span>
            <span className="font-medium text-gray-800 dark:text-white">
              {isLoading ? '...' : (balance?.formalInterviewCredits ?? 0)} 次
            </span>
          </div>
          <div className="bg-white bg-opacity-60 dark:bg-gray-600 dark:bg-opacity-60 backdrop-blur-sm rounded-lg py-1.5 px-3 flex items-center justify-between">
            <span className="text-sm text-gray-600 dark:text-gray-300">面巾余额</span>
            <span className="font-medium text-gray-800 dark:text-white">
              {isLoading ? '...' : (balance?.mianshijunBalance ?? 0)} 点
            </span>
          </div>
        </div>

        <div className="mt-4 pt-4 border-t border-gray-100 dark:border-gray-600 relative" ref={emailMenuRef}>
          <button
            onClick={toggleEmailMenu}
            className="w-full flex items-center justify-center gap-1 text-sm text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 transition-colors py-1 px-2 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700"
          >
            {isAuthenticated
              ? maskEmail(user?.email || '未知邮箱')
              : '未登录'
            }
            <ChevronDown size={14} className={`transition-transform ${showEmailMenu ? 'rotate-180' : ''}`} />
          </button>

          {/* 邮箱下拉菜单 */}
          {showEmailMenu && (
            <div className="absolute bottom-full left-0 mb-1 w-full bg-white dark:bg-gray-800 rounded-lg shadow-xl py-1 z-10 border border-gray-100 dark:border-gray-600">
              {isAuthenticated ? (
                <>
                  <div className="px-4 py-2 border-b border-gray-100 dark:border-gray-600">
                    <p className="text-sm font-medium text-gray-700 dark:text-white truncate">{user?.name || '用户'}</p>
                    <p className="text-xs text-gray-500 dark:text-gray-400 truncate">{user?.email}</p>
                  </div>

                  <button
                    onClick={() => {
                      setShowEmailMenu(false);
                      navigate('/profile');
                    }}
                    className="flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700"
                  >
                    <User size={14} className="mr-2" />
                    个人中心
                  </button>

                  <button
                    onClick={() => {
                      setShowEmailMenu(false);
                      navigate('/orders');
                    }}
                    className="flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700"
                  >
                    <ShoppingBag size={14} className="mr-2" />
                    我的订单
                  </button>

                  <button
                    onClick={() => {
                      setShowEmailMenu(false);
                      navigate('/usage-records');
                    }}
                    className="flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700"
                  >
                    <FileText size={14} className="mr-2" />
                    消耗记录
                  </button>

                  <button
                    onClick={() => {
                      setShowEmailMenu(false);
                      navigate('/settings');
                    }}
                    className="flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700"
                  >
                    <Settings size={14} className="mr-2" />
                    设置
                  </button>

                  <div className="border-t border-gray-100 my-1"></div>

                  <button
                    onClick={() => {
                      setShowEmailMenu(false);
                      logout();
                    }}
                    className="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-gray-100"
                  >
                    <LogOut size={14} className="mr-2" />
                    退出登录
                  </button>
                </>
              ) : (
                <>
                  <div className="px-4 py-2 border-b border-gray-100">
                    <p className="text-sm font-medium text-gray-700">未登录状态</p>
                    <p className="text-xs text-gray-500">请登录以使用完整功能</p>
                  </div>

                  <button
                    onClick={handleLogin}
                    className="flex items-center w-full px-4 py-2 text-sm text-blue-600 hover:bg-gray-100"
                  >
                    <LogIn size={14} className="mr-2" />
                    登录
                  </button>
                </>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Sidebar;