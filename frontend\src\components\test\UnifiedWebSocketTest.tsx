// 基于功能开关的WebSocket测试组件 - 绞杀榕模式演示
import React, { useState, useEffect } from 'react';
import { useWebSocket } from '../../providers/WebSocketProvider';
import { useFeatureFlags, FeatureFlagGate } from '../../providers/FeatureFlagProvider';
import type { ConnectionConfig } from '../../services/webSocketManager';

const UnifiedWebSocketTest: React.FC = () => {
  const {
    connect,
    disconnect,
    sendMessage,
    connectionState,
    isConnected,
    currentSessionId,
    metrics,
    addEventListener
  } = useWebSocket();

  const { isEnabled, updateFlag, emergencyDisable } = useFeatureFlags();

  // 本地状态
  const [lastMessage, setLastMessage] = useState<any>(null);
  const [messageHistory, setMessageHistory] = useState<any[]>([]);
  const [testMessage, setTestMessage] = useState('Hello from new WebSocket architecture!');
  const [error, setError] = useState<string | null>(null);

  // 监听WebSocket消息
  useEffect(() => {
    const unsubscribe = addEventListener('test_message', (data) => {
      setLastMessage(data);
      setMessageHistory(prev => [...prev.slice(-9), data]); // 保持最近10条消息
    });

    return unsubscribe;
  }, [addEventListener]);

  // 处理函数
  const handleConnect = async () => {
    try {
      const sessionId = `test_session_${Date.now()}`;
      const config: ConnectionConfig = {
        sessionId,
        interviewType: 'mock',
        interviewLanguage: 'zh',
        answerStyle: 'detailed',
        selectedPositionId: 'test-position'
      };

      await connect(sessionId, config);
    } catch (error) {
      console.error('Connection failed:', error);
    }
  };

  const handleSendTestMessage = () => {
    sendMessage({
      type: 'test_message',
      data: { content: testMessage },
      timestamp: Date.now()
    });
  };

  const handleToggleUnifiedWebSocket = () => {
    const currentState = isEnabled('enable-unified-websocket');
    updateFlag('enable-unified-websocket', !currentState);
  };

  const handleEmergencyDisable = () => {
    emergencyDisable('enable-unified-websocket', 'User requested emergency disable from test interface');
  };

  return (
    <div className="p-6 bg-white rounded-lg shadow-lg max-w-4xl mx-auto">
      <h2 className="text-2xl font-bold mb-4 text-blue-600">🎯 绞杀榕模式 - WebSocket架构重构演示</h2>

      {/* 功能开关状态 */}
      <div className="mb-6 p-4 rounded-lg bg-blue-50 border border-blue-200">
        <h3 className="font-semibold mb-2 text-blue-800">🚩 功能开关控制中心</h3>
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="font-medium">统一WebSocket: </span>
            <span className={`font-bold ${isEnabled('enable-unified-websocket') ? 'text-green-600' : 'text-red-600'}`}>
              {isEnabled('enable-unified-websocket') ? '✅ 启用' : '❌ 禁用'}
            </span>
          </div>
          <div>
            <span className="font-medium">调试日志: </span>
            <span className={`font-bold ${isEnabled('enable-websocket-debug-logs') ? 'text-green-600' : 'text-gray-600'}`}>
              {isEnabled('enable-websocket-debug-logs') ? '✅ 启用' : '⏸️ 禁用'}
            </span>
          </div>
        </div>
        <div className="mt-3 space-x-2">
          <button
            onClick={handleToggleUnifiedWebSocket}
            className="px-3 py-1 bg-blue-500 text-white text-sm rounded hover:bg-blue-600"
          >
            切换统一WebSocket
          </button>
          <button
            onClick={handleEmergencyDisable}
            className="px-3 py-1 bg-red-500 text-white text-sm rounded hover:bg-red-600"
          >
            🚨 紧急熔断
          </button>
        </div>
      </div>

      {/* 功能开关门控组件演示 */}
      <FeatureFlagGate
        flag="enable-unified-websocket"
        fallback={
          <div className="mb-6 p-4 rounded-lg bg-red-50 border border-red-200">
            <h3 className="font-semibold text-red-800 mb-2">⚠️ 新WebSocket架构已禁用</h3>
            <p className="text-red-600 text-sm">
              统一WebSocket功能已被功能开关禁用。这演示了绞杀榕模式中的紧急回退机制。
              在生产环境中，流量将自动路由回旧系统。
            </p>
          </div>
        }
      >
        {/* 连接状态 */}
        <div className="mb-4 p-3 rounded-lg bg-gray-50">
          <h3 className="font-semibold mb-2">📡 连接状态</h3>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>连接状态: <span className={`font-bold ${isConnected ? 'text-green-600' : 'text-red-600'}`}>
              {connectionState === 'connected' ? '✅ 已连接' :
               connectionState === 'connecting' ? '🔄 连接中' :
               connectionState === 'reconnecting' ? '🔄 重连中' :
               connectionState === 'error' ? '❌ 错误' : '⏸️ 未连接'}
            </span></div>
            <div>会话ID: <span className="font-mono text-xs">{currentSessionId || '无'}</span></div>
          </div>
        </div>
      </FeatureFlagGate>

      <FeatureFlagGate flag="enable-unified-websocket">
        {/* 连接指标 */}
        <div className="mb-4 p-3 rounded-lg bg-green-50">
          <h3 className="font-semibold mb-2 text-green-800">📊 连接指标</h3>
          <div className="grid grid-cols-3 gap-4 text-sm">
            <div>连接次数: <span className="font-bold">{metrics.connectionCount}</span></div>
            <div>发送消息: <span className="font-bold">{metrics.messagesSent}</span></div>
            <div>接收消息: <span className="font-bold">{metrics.messagesReceived}</span></div>
            <div>重连次数: <span className="font-bold">{metrics.reconnectAttempts}</span></div>
            <div>平均延迟: <span className="font-bold">{metrics.averageLatency.toFixed(1)}ms</span></div>
            <div>错误次数: <span className="font-bold text-red-600">{metrics.errorCount}</span></div>
          </div>
        </div>

        {/* 错误信息 */}
        {error && (
          <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
            <h3 className="font-semibold text-red-800 mb-1">❌ 错误信息</h3>
            <p className="text-red-600 text-sm">{error}</p>
          </div>
        )}

        {/* 控制按钮 */}
        <div className="mb-4 space-x-2">
          <button
            onClick={handleConnect}
            disabled={isConnected}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:bg-gray-400"
          >
            🔗 建立连接
          </button>
          <button
            onClick={disconnect}
            disabled={!isConnected}
            className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 disabled:bg-gray-400"
          >
            🔌 断开连接
          </button>
          <button
            onClick={handleSendTestMessage}
            disabled={!isConnected}
            className="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 disabled:bg-gray-400"
          >
            📤 发送测试消息
          </button>
        </div>

        {/* 消息输入 */}
        <div className="mb-4">
          <label className="block text-sm font-medium mb-1">测试消息内容:</label>
          <input
            type="text"
            value={testMessage}
            onChange={(e) => setTestMessage(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="输入要发送的测试消息..."
          />
        </div>
      </FeatureFlagGate>

      {/* 最新消息 */}
      {lastMessage && (
        <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <h3 className="font-semibold text-blue-800 mb-1">📨 最新消息</h3>
          <pre className="text-blue-600 text-sm bg-white p-2 rounded border overflow-x-auto">
            {JSON.stringify(lastMessage, null, 2)}
          </pre>
        </div>
      )}

      {/* 消息历史 */}
      {messageHistory.length > 0 && (
        <div className="mb-4 p-3 bg-gray-50 border border-gray-200 rounded-lg">
          <h3 className="font-semibold text-gray-800 mb-2">📜 消息历史 (最近{messageHistory.length}条)</h3>
          <div className="space-y-2 max-h-40 overflow-y-auto">
            {messageHistory.map((msg, index) => (
              <div key={index} className="text-xs bg-white p-2 rounded border">
                <span className="text-gray-500">{new Date(msg.timestamp || Date.now()).toLocaleTimeString()}</span>
                <span className="ml-2 font-mono">{JSON.stringify(msg)}</span>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 说明 */}
      <div className="mt-6 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
        <h3 className="font-semibold text-yellow-800 mb-2">🎯 绞杀榕模式 - WebSocket架构重构演示</h3>
        <div className="text-yellow-700 text-sm space-y-1">
          <p>🚩 <strong>功能开关控制</strong>：通过功能开关实现新旧系统的动态切换和灰度发布</p>
          <p>📡 <strong>ES模块单例</strong>：基于现代TypeScript的"单例即服务"架构模式</p>
          <p>🔄 <strong>自动重连</strong>：支持心跳检测、指数退避重连和连接状态管理</p>
          <p>📊 <strong>实时监控</strong>：提供详细的连接指标和性能数据</p>
          <p>🚨 <strong>紧急熔断</strong>：支持生产环境的紧急故障处理和即时回退</p>
        </div>
      </div>
    </div>
  );
};

export default UnifiedWebSocketTest;
