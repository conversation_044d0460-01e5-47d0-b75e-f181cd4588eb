# AI模拟面试实时页面逻辑优化需求文档

## 项目背景

### 系统概述
我们开发了一个AI面试辅助系统，名为"面试君"，主要包含两种核心功能：
1. **AI模拟面试**：AI充当面试官角色，向用户提问并模拟真实面试场景
2. **AI正式面试**：AI充当面试助手角色，在用户真实面试过程中提供实时建议

### 技术架构
- **前端**：React + TypeScript + Zustand状态管理 + WebSocket实时通信
- **后端**：Node.js + Express + WebSocket + PostgreSQL + Prisma ORM
- **AI服务**：OpenAI GPT + 多ASR提供商（讯飞、阿里云、百度、OpenAI Whisper）
- **核心功能**：实时语音识别(ASR)、AI对话生成、用户认证、余额管理、扣费系统

### 业务流程
用户通过配置页面选择岗位信息和公司信息后，进入模拟面试实时页面，与AI面试官进行对话式面试。系统支持：
- 实时语音识别用户回答
- AI动态生成面试问题
- 蓝色气泡显示AI面试官问题
- 绿色气泡显示用户回答
- 完整的面试会话记录

## 当前遇到的核心问题

### 1. 角色定位混乱问题
**问题描述**：
- AI模拟面试中，AI应该充当**面试官**角色，负责提问和引导
- AI正式面试中，AI应该充当**助手**角色，提供建议和指导
- 当前代码混用了这两种角色的逻辑，导致字段和功能错位

**具体表现**：
- 代码中存在大量`ai_suggestion`字段，这是为正式面试设计的，在模拟面试中不应该存在
- 前端消息类型定义包含`'ai-suggestion'`类型，模拟面试应该只有`'interviewer'`和`'user-answer'`两种类型
- 后端服务逻辑混用了两种模式的处理方式

### 2. 面试流程逻辑错误
**当前错误流程**：
```
进入页面 → 立即开始ASR监听 → 等待用户说话
```

**正确期望流程**：
```
进入页面 → AI发送自我介绍引导问题（蓝色气泡） → 开始ASR监听 → 
用户回答（绿色气泡） → AI分析回答并生成下一个问题 → 循环
```

**具体问题**：
- 用户进入页面后立即开始录音，但此时AI还没有提问
- 缺少AI主动发起对话的逻辑
- 用户回答完毕后没有自动触发AI生成下一个问题

### 3. 消息类型系统设计缺陷
**问题描述**：
- 前端`MockMessage`接口设计不合理，包含了不适用的字段类型
- WebSocket消息处理逻辑没有根据模式（mock/formal）进行区分
- 消息路由和状态管理混乱

### 4. ASR生命周期管理问题
**问题描述**：
- ASR启动时机不正确，应该在收到AI问题后才启动
- 缺少基于AI问题触发的ASR控制逻辑
- 用户回答完毕后的处理流程不完整

## 详细需求说明

### 核心需求
1. **修复角色定位**：确保AI在模拟面试中严格充当面试官角色
2. **修复流程逻辑**：实现正确的"AI提问 → 用户回答 → AI继续提问"循环
3. **优化消息系统**：为模拟面试设计专门的消息类型和处理逻辑
4. **完善ASR控制**：精确控制ASR的启动和停止时机

### 具体功能要求

#### 1. 首次进入页面逻辑
- 用户进入模拟面试实时页面后，**不立即开始ASR监听**
- AI根据用户在配置页面选择的岗位信息和公司信息，主动发送个性化的自我介绍引导问题
- 问题以蓝色气泡显示，标题为"面试官提问"
- 发送完问题后，**才开始ASR监听**

#### 2. 对话循环逻辑
- 用户通过麦克风回答问题，ASR识别后以绿色气泡显示，标题为"求职者回答"
- 用户回答完毕后（基于ASR静音检测机制），自动发送回答内容给后端
- 后端AI分析用户回答，生成下一个面试问题（不与之前问题重复）
- AI发送新问题（蓝色气泡），然后继续ASR监听用户回答
- 如此循环，直到面试结束

#### 3. 问题生成策略
- **第一个问题**：基于岗位和公司信息的个性化自我介绍引导
- **后续问题**：基于用户回答内容和面试进展，动态生成相关问题
- **去重机制**：确保不重复已经问过的问题
- **问题质量**：问题应该符合真实面试场景，有层次和逻辑性

## 技术约束和要求

### 严格约束
1. **UI设计绝对不能改动**：只能修改后端逻辑和前端业务逻辑，不能改变任何UI元素
2. **不影响正式面试功能**：修改不能影响现有的AI正式面试功能
3. **遇到冲突必须中断咨询**：任何与开发冲突的地方都要先咨询确认
4. **不确定必须确认**：任何不确定的地方都要得到确认再继续

### 技术要求
1. **全局思考，完美解决方案**：不是临时修补，要从根本上解决架构问题
2. **深度重构优于增量修复**：如果性能不佳，宁可重构也要做对
3. **状态隔离**：模拟面试和正式面试的状态要完全隔离
4. **错误处理**：完善的错误处理和边界情况处理

## 期望的解决方案架构

### 1. 消息类型系统重构
为模拟面试和正式面试分别定义独立的消息类型系统：
- **模拟面试**：`InterviewerMessage`（蓝色气泡）+ `UserAnswerMessage`（绿色气泡）
- **正式面试**：保持现有的消息类型系统

### 2. 状态机驱动的流程控制
使用状态机模式管理面试流程：
```
WAITING_FOR_AI_QUESTION → LISTENING_FOR_USER_ANSWER → 
PROCESSING_ANSWER → GENERATING_NEXT_QUESTION → 循环
```

### 3. 角色驱动的服务分离
- `MockInterviewService`：专门处理模拟面试逻辑（AI作为面试官）
- `FormalInterviewService`：处理正式面试逻辑（AI作为助手）
- 共享基础设施：ASR、WebSocket、用户管理等

### 4. 事件驱动的消息流
```
AI问题生成 → 发送给前端 → 触发ASR启动 → 
ASR完成 → 发送用户回答 → 触发AI分析 → 生成下一个问题
```

## 成功标准

1. ✅ 进入模拟面试页面后，AI立即发送个性化自我介绍引导问题（蓝色气泡）
2. ✅ 收到AI问题后自动开始ASR监听
3. ✅ 用户回答完毕后自动生成下一个不重复的问题
4. ✅ 整个流程流畅，无状态混乱
5. ✅ 不影响现有正式面试功能
6. ✅ 所有字段和逻辑都符合"AI是面试官"的角色定位

## 风险评估

### 高风险
- WebSocket消息路由重构可能影响现有正式面试功能
- 状态管理重构可能导致状态不一致

### 中风险
- ASR生命周期管理复杂，可能出现状态泄漏
- AI问题生成的性能和质量需要调优

### 低风险
- UI不变，用户体验影响最小
- 现有数据结构基本不变

## 实施建议

建议采用分阶段实施：
1. **阶段1**：架构重构（消息类型、状态机、服务分离）
2. **阶段2**：流程优化（AI问题发送、ASR控制、自动问题生成）
3. **阶段3**：体验优化（个性化问题、去重机制、错误处理）

每个阶段完成后进行充分测试，确保不影响现有功能。

## 技术实现细节

### 关键文件和组件

#### 后端文件
- `backend/websocket/handlers/mockInterviewService.ts` - 模拟面试核心服务
- `backend/websocket/handlers/messageHandler.ts` - WebSocket消息路由
- `backend/websocket/interviewWs.ts` - WebSocket服务器
- `backend/types/websocket.ts` - WebSocket消息类型定义

#### 前端文件
- `frontend/src/stores/mockInterviewStore.ts` - 模拟面试状态管理
- `frontend/src/hooks/useInterviewSession.ts` - 面试会话钩子
- `frontend/src/pages/MockInterviewSessionPage.tsx` - 模拟面试实时页面
- `frontend/src/types/interview.ts` - 面试相关类型定义

### 当前问题的具体代码位置

#### 1. 消息类型问题
**位置**：`frontend/src/stores/mockInterviewStore.ts:4-12`
```typescript
// 当前错误的类型定义
export interface MockMessage {
  type: 'interviewer' | 'ai-suggestion' | 'user-answer'; // ❌ ai-suggestion不应该存在
}

// 应该修改为
export interface MockMessage {
  type: 'interviewer' | 'user-answer'; // ✅ 只有面试官问题和用户回答
}
```

#### 2. ASR启动时机问题
**位置**：`frontend/src/hooks/useInterviewSession.ts:1750-1756`
```typescript
// 当前错误：连接后立即启动录音
if (recordingIntent && !isRecording) {
  setTimeout(() => {
    forceStartRecording(); // ❌ 不应该立即启动
  }, 1000);
}

// 应该修改为：等待AI问题后再启动
// 在收到 mock_interview_question 消息后才启动录音
```

#### 3. 用户回答处理问题
**位置**：`frontend/src/hooks/useInterviewSession.ts:960-983`
```typescript
// 当前缺失：用户回答完毕后没有发送给后端
// 需要添加发送 mock_interview_answer 消息的逻辑
```

#### 4. AI问题生成时机问题
**位置**：`backend/websocket/handlers/mockInterviewService.ts:200-205`
```typescript
// 当前：延迟1秒发送问题
setTimeout(() => {
  this.sendNextQuestion(sessionId, ws);
}, 1000); // ❌ 不应该延迟

// 应该修改为：立即发送第一个问题
this.sendNextQuestion(sessionId, ws); // ✅ 立即发送
```

### ASR终止机制参考

根据 `docs/development-plan/8/audio/audio-docs.md` 文档，ASR终止机制包括：
1. **静音检测**：检测到3秒以上静音时自动终止
2. **手动终止**：用户点击停止按钮
3. **超时终止**：超过最大录音时长自动终止
4. **会话级连接隔离**：每个ASR会话独立管理

### 岗位和公司信息获取

用户在AI模拟面试配置页面选择的信息包括：
- **岗位信息**：从用户预设的岗位列表中选择
- **公司信息**：从用户预设的公司列表中选择
- **面试语言**：中文/英文
- **回答风格**：简洁/详细

这些信息在用户点击"开始面试"时传递给后端，用于生成个性化的第一个问题。

### 消息流程图

```
用户进入页面
    ↓
后端发送 session_started 消息
    ↓
后端立即发送 mock_interview_question 消息（自我介绍引导）
    ↓
前端收到问题，显示蓝色气泡
    ↓
前端自动启动ASR监听
    ↓
用户说话，ASR实时识别
    ↓
检测到静音，ASR终止
    ↓
前端发送 mock_interview_answer 消息给后端
    ↓
后端AI分析回答，生成下一个问题
    ↓
后端发送新的 mock_interview_question 消息
    ↓
循环：前端显示问题 → 启动ASR → 用户回答 → 发送回答 → AI生成问题
```

### 性能优化建议

1. **WebSocket连接复用**：同一个连接处理不同类型的消息，通过消息类型路由
2. **状态更新批处理**：避免频繁的状态更新导致重渲染
3. **ASR结果缓存**：避免重复处理相同的ASR结果
4. **AI问题预生成**：在用户回答的同时开始准备下一个问题

### 错误处理策略

1. **ASR服务失败**：自动切换到备用ASR提供商
2. **AI问题生成失败**：使用预设的备用问题模板
3. **WebSocket连接断开**：自动重连并恢复会话状态
4. **用户中途退出**：清理ASR资源和WebSocket连接

## 测试验证方案

### 功能测试
1. 进入模拟面试页面，验证AI是否立即发送自我介绍问题
2. 验证收到AI问题后ASR是否自动启动
3. 验证用户回答后是否自动生成下一个问题
4. 验证问题是否不重复且符合面试逻辑

### 兼容性测试
1. 验证正式面试功能是否正常工作
2. 验证两种模式的状态是否完全隔离
3. 验证WebSocket消息路由是否正确

### 性能测试
1. 测试ASR启动和停止的响应时间
2. 测试AI问题生成的速度
3. 测试长时间面试的稳定性

### 边界情况测试
1. 网络断开重连场景
2. ASR服务失败场景
3. AI服务失败场景
4. 用户中途退出场景

## 专家实施指导

### 实施优先级
1. **P0（最高优先级）**：修复流程逻辑，确保AI先提问再启动ASR
2. **P1（高优先级）**：修复消息类型系统，移除不适用的字段
3. **P2（中优先级）**：优化问题生成策略，实现个性化和去重
4. **P3（低优先级）**：性能优化和错误处理完善

### 关键注意事项
1. **绝对不能修改UI**：任何UI相关的文件都不能改动
2. **状态隔离**：确保模拟面试和正式面试的状态完全分离
3. **向后兼容**：确保现有API和数据结构的兼容性
4. **渐进式重构**：分阶段实施，每个阶段都要充分测试

### 验收标准
- [ ] 进入页面后AI立即发送个性化问题
- [ ] 收到问题后自动启动ASR
- [ ] 用户回答后自动生成下一个问题
- [ ] 问题不重复且质量高
- [ ] 不影响正式面试功能
- [ ] 性能表现良好
- [ ] 错误处理完善

这个文档为专家团队提供了完整的背景信息、问题分析、解决方案和实施指导，确保能够准确理解需求并实施正确的解决方案。
