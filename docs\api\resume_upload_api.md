# 简历上传 API 规范 (Resume Upload API Specification)

**版本 (Version):** 1.0.0
**最后更新 (Last Updated):** 2025-05-18

## 1. 概述 (Overview)

本接口用于用户上传简历文件。上传的简历将由后端进行处理和存储，并可能与用户账户或特定的面试准备流程关联。

## 2. 端点信息 (Endpoint Information)

* **端点名称 (Endpoint Name):** 上传简历 (Upload Resume)
* **HTTP 方法 (HTTP Method):** `POST`
* **路径 (Path):** `/api/resumes/upload`

## 3. 认证 (Authentication)

* **是否需要认证 (Authentication Required):** 是 (Yes)
* **认证方式 (Mechanism):** JWT Bearer Token
    * 客户端需要在请求的 `Authorization` 头中提供有效的 JWT。
    * 格式 (Format): `Authorization: Bearer <your_jwt_token>`

## 4. 请求 (Request)

### 4.1. 请求头 (Request Headers)

| Header          | 类型 (Type) | 描述 (Description)                            | 是否必须 (Required) | 示例 (Example)                      |
| :-------------- | :---------- | :-------------------------------------------- | :------------------ | :---------------------------------- |
| `Authorization` | `string`    | 用于用户认证的 JWT Bearer Token。             | 是 (Yes)            | `Bearer eyJhbGciOiJIUzI1NiI...`     |
| `Content-Type`  | `string`    | 必须为 `multipart/form-data` 以支持文件上传。 | 是 (Yes)            | `multipart/form-data; boundary=...` |

### 4.2. 请求体 (Request Body)

请求体必须是 `multipart/form-data` 格式。

| 字段名 (Field Name) | 类型 (Type)                | 描述 (Description)                            | 是否必须 (Required) |
| :------------------ | :------------------------- | :-------------------------------------------- | :------------------ |
| `resumeFile`        | `File`                     | 用户选择的简历文件。                          | 是 (Yes)            |
| `jobTitle`          | `string`                   | 用户输入的“面试岗位/角色”，与此简历相关联。   | 是 (Yes)            |
| `interviewSetupId`  | `string` (UUID/ObjectID等) | (可选) 如果适用，关联到特定面试准备流程的ID。 | 否 (No)             |

**文件约束 (File Constraints):**

* **支持的文件类型 (Supported File Types):** PDF (`.pdf`), Word (`.docx`), 纯文本 (`.txt`)
* **最大文件大小 (Maximum File Size):** 10MB

## 5. 响应 (Response)

所有响应均为 JSON 格式。

### 5.1. 成功响应 (Success Response)

* **状态码 (Status Code):** `201 Created`
    * 描述: 简历文件成功上传并被服务器接受处理。
    * **响应体示例 (Example Body):**
        ```json
        {
          "success": true,
          "message": "简历上传成功并已开始处理。(Resume uploaded successfully and processing has started.)",
          "data": {
            "fileId": "unique_file_id_generated_by_server_e.g._uuid",
            "fileName": "MyResume.pdf",
            "fileType": "application/pdf",
            "fileSize": 204800, // 文件大小，单位：字节 (File size in bytes)
            "jobTitleAssociated": "软件工程师 (Software Engineer)",
            "uploadTimestamp": "2025-05-18T10:30:00Z" // ISO 8601 格式的上传时间戳 (Upload timestamp in ISO 8601 format)
          }
        }
        ```

### 5.2. 错误响应 (Error Responses)

* **状态码 (Status Code):** `400 Bad Request`
    * 描述: 请求格式错误或缺少必要信息。
    * **响应体示例 (Example Body - 未上传文件 / No file uploaded):**
        ```json
        {
          "success": false,
          "error": {
            "code": "NO_FILE_UPLOADED",
            "message": "未提供文件，请选择一个简历文件。(No file provided. Please select a resume file.)"
          }
        }
        ```
    * **响应体示例 (Example Body - 无效文件类型 / Invalid file type):**
        ```json
        {
          "success": false,
          "error": {
            "code": "INVALID_FILE_TYPE",
            "message": "文件类型无效。仅支持 PDF, DOCX, TXT 文件。(Invalid file type. Only PDF, DOCX, and TXT files are supported.)"
          }
        }
        ```
    * **响应体示例 (Example Body - 缺少 `jobTitle` / Missing `jobTitle`):**
        ```json
        {
          "success": false,
          "error": {
            "code": "MISSING_REQUIRED_FIELD",
            "message": "缺少必要的字段: jobTitle。(Missing required field: jobTitle.)"
          }
        }
        ```

* **状态码 (Status Code):** `401 Unauthorized`
    * 描述: 用户未认证或 Token 无效。
    * **响应体示例 (Example Body):**
        ```json
        {
          "success": false,
          "error": {
            "code": "UNAUTHORIZED",
            "message": "认证失败或未提供认证凭据。(Authentication failed or credentials not provided.)"
          }
        }
        ```

* **状态码 (Status Code):** `413 Payload Too Large`
    * 描述: 上传的文件大小超过限制 (10MB)。
    * **响应体示例 (Example Body):**
        ```json
        {
          "success": false,
          "error": {
            "code": "FILE_TOO_LARGE",
            "message": "文件过大，最大允许 10MB。(File is too large. Maximum allowed size is 10MB.)"
          }
        }
        ```

* **状态码 (Status Code):** `500 Internal Server Error`
    * 描述: 服务器内部发生未知错误。
    * **响应体示例 (Example Body):**
        ```json
        {
          "success": false,
          "error": {
            "code": "INTERNAL_SERVER_ERROR",
            "message": "服务器发生内部错误，请稍后再试。(An internal server error occurred. Please try again later.)"
          }
        }
        ```

## 6. 注意事项 (Notes)

* 后端在接收到文件后，除了存储文件本身，还可能进行文本提取、初步分析等操作。这些操作的成功与否不直接体现在此上传接口的即时响应中，但 `fileId` 可以用于后续查询处理状态。
* `jobTitle` 字段是根据您前端 `InterviewSetupPage.tsx` 中收集的“面试岗位/角色”信息而添加的，确保简历上传时能与此信息关联。