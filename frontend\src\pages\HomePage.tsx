import React from 'react';
import ResumeSection from '../components/ResumeSection';
import PositionSection from '../components/PositionSection';
import InterviewCard from '../components/InterviewCard';
import useDocumentTitle from '../hooks/useDocumentTitle';
import type { Position } from '@new-mianshijun/common';

const HomePage: React.FC = () => {
  // 设置页面标题
  useDocumentTitle('首页');

  // 模拟数据
  const positions: Position[] = [
    { id: '1', name: '前端开发工程师' },
    { id: '2', name: '产品经理' },
  ];

  const maxPositions = 10;

  return (
    <div className="space-y-8 p-4">
      <ResumeSection />

      <PositionSection
        positions={positions}
        maxPositions={maxPositions}
      />

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <InterviewCard
          type="simulation"
          title="AI模拟面试"
          subtitle="练习模式"
          description="通过AI模拟真实面试场景，获得针对性的反馈建议，帮助你提升面试技巧和自信心，支持多个专业方向，覆盖各类面试问题。"
        />
        <InterviewCard
          type="formal"
          title="AI正式面试"
          subtitle="专业模式"
          description="更专业的面试模拟体验，深入针对你的专业能力和表现，获得详细的分析报告和改进建议，让你在真实面试中脱颖而出。"
        />
      </div>
    </div>
  );
};

export default HomePage;