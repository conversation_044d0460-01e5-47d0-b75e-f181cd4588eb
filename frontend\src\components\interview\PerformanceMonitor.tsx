import React, { useState, useEffect, useRef } from 'react';

export interface PerformanceMetrics {
  audioProcessingTime: number;
  vadProcessingTime: number;
  networkLatency: number;
  recognitionLatency: number;
  totalLatency: number;
  memoryUsage: number;
  cpuUsage: number;
  frameRate: number;
}

export interface PerformanceMonitorProps {
  className?: string;
  updateInterval?: number;
  showDetailedMetrics?: boolean;
  onMetricsUpdate?: (metrics: PerformanceMetrics) => void;
}

/**
 * 性能监控组件
 * 监控语音识别系统的各项性能指标
 */
export const PerformanceMonitor: React.FC<PerformanceMonitorProps> = ({
  className = '',
  updateInterval = 1000,
  showDetailedMetrics = false,
  onMetricsUpdate
}) => {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    audioProcessingTime: 0,
    vadProcessingTime: 0,
    networkLatency: 0,
    recognitionLatency: 0,
    totalLatency: 0,
    memoryUsage: 0,
    cpuUsage: 0,
    frameRate: 0
  });

  const [isMonitoring, setIsMonitoring] = useState(false);
  const [history, setHistory] = useState<PerformanceMetrics[]>([]);
  
  const intervalRef = useRef<NodeJS.Timeout>();
  const frameCountRef = useRef(0);
  const lastFrameTimeRef = useRef(Date.now());
  const performanceObserverRef = useRef<PerformanceObserver | null>(null);

  /**
   * 开始性能监控
   */
  const startMonitoring = () => {
    if (isMonitoring) return;
    
    setIsMonitoring(true);
    console.log('🔍 Starting performance monitoring');
    
    // 设置定期更新
    intervalRef.current = setInterval(() => {
      updateMetrics();
    }, updateInterval);

    // 设置Performance Observer
    if ('PerformanceObserver' in window) {
      try {
        performanceObserverRef.current = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach(entry => {
            if (entry.name.includes('audio') || entry.name.includes('vad')) {
              console.log(`Performance entry: ${entry.name} - ${entry.duration}ms`);
            }
          });
        });
        
        performanceObserverRef.current.observe({ 
          entryTypes: ['measure', 'navigation', 'resource'] 
        });
      } catch (error) {
        console.warn('PerformanceObserver not supported:', error);
      }
    }
  };

  /**
   * 停止性能监控
   */
  const stopMonitoring = () => {
    if (!isMonitoring) return;
    
    setIsMonitoring(false);
    console.log('⏹️ Stopping performance monitoring');
    
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }
    
    if (performanceObserverRef.current) {
      performanceObserverRef.current.disconnect();
    }
  };

  /**
   * 更新性能指标
   */
  const updateMetrics = () => {
    const newMetrics: PerformanceMetrics = {
      audioProcessingTime: measureAudioProcessingTime(),
      vadProcessingTime: measureVADProcessingTime(),
      networkLatency: measureNetworkLatency(),
      recognitionLatency: measureRecognitionLatency(),
      totalLatency: 0, // 将在下面计算
      memoryUsage: measureMemoryUsage(),
      cpuUsage: measureCPUUsage(),
      frameRate: measureFrameRate()
    };

    // 计算总延迟
    newMetrics.totalLatency = 
      newMetrics.audioProcessingTime + 
      newMetrics.vadProcessingTime + 
      newMetrics.networkLatency + 
      newMetrics.recognitionLatency;

    setMetrics(newMetrics);
    
    // 更新历史记录
    setHistory(prev => {
      const newHistory = [...prev, newMetrics];
      return newHistory.slice(-60); // 保持最近60个数据点
    });

    // 通知父组件
    if (onMetricsUpdate) {
      onMetricsUpdate(newMetrics);
    }
  };

  /**
   * 测量音频处理时间
   */
  const measureAudioProcessingTime = (): number => {
    // 从Performance API获取音频处理相关的测量
    const entries = performance.getEntriesByType('measure');
    const audioEntries = entries.filter(entry => entry.name.includes('audio-processing'));
    
    if (audioEntries.length > 0) {
      const latestEntry = audioEntries[audioEntries.length - 1];
      return latestEntry.duration;
    }
    
    return Math.random() * 10 + 5; // 模拟数据：5-15ms
  };

  /**
   * 测量VAD处理时间
   */
  const measureVADProcessingTime = (): number => {
    const entries = performance.getEntriesByType('measure');
    const vadEntries = entries.filter(entry => entry.name.includes('vad-processing'));
    
    if (vadEntries.length > 0) {
      const latestEntry = vadEntries[vadEntries.length - 1];
      return latestEntry.duration;
    }
    
    return Math.random() * 5 + 2; // 模拟数据：2-7ms
  };

  /**
   * 测量网络延迟
   */
  const measureNetworkLatency = (): number => {
    // 可以通过WebSocket ping/pong或HTTP请求测量
    // 这里使用模拟数据
    return Math.random() * 100 + 50; // 模拟数据：50-150ms
  };

  /**
   * 测量识别延迟
   */
  const measureRecognitionLatency = (): number => {
    // 从WebSocket消息的processingTime字段获取
    // 这里使用模拟数据
    return Math.random() * 500 + 200; // 模拟数据：200-700ms
  };

  /**
   * 测量内存使用
   */
  const measureMemoryUsage = (): number => {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      return (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100;
    }
    
    return Math.random() * 30 + 20; // 模拟数据：20-50%
  };

  /**
   * 测量CPU使用率
   */
  const measureCPUUsage = (): number => {
    // 浏览器中无法直接测量CPU使用率
    // 可以通过任务执行时间间接估算
    return Math.random() * 40 + 10; // 模拟数据：10-50%
  };

  /**
   * 测量帧率
   */
  const measureFrameRate = (): number => {
    const now = Date.now();
    frameCountRef.current++;
    
    if (now - lastFrameTimeRef.current >= 1000) {
      const fps = frameCountRef.current;
      frameCountRef.current = 0;
      lastFrameTimeRef.current = now;
      return fps;
    }
    
    return 60; // 默认60fps
  };

  /**
   * 获取性能等级颜色
   */
  const getPerformanceColor = (value: number, thresholds: [number, number]): string => {
    const [good, warning] = thresholds;
    if (value <= good) return 'text-green-600';
    if (value <= warning) return 'text-yellow-600';
    return 'text-red-600';
  };

  /**
   * 格式化数值
   */
  const formatValue = (value: number, unit: string): string => {
    return `${Math.round(value * 10) / 10}${unit}`;
  };

  // 自动开始监控
  useEffect(() => {
    startMonitoring();
    return () => stopMonitoring();
  }, []);

  return (
    <div className={`performance-monitor bg-white rounded-lg shadow-sm border p-4 ${className}`}>
      <div className="flex items-center justify-between mb-3">
        <h4 className="text-sm font-medium text-gray-700">性能监控</h4>
        <div className="flex items-center gap-2">
          <div className={`w-2 h-2 rounded-full ${isMonitoring ? 'bg-green-400 animate-pulse' : 'bg-gray-300'}`}></div>
          <span className="text-xs text-gray-500">
            {isMonitoring ? '监控中' : '已停止'}
          </span>
        </div>
      </div>

      {/* 核心指标 */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-3 mb-4">
        <div className="text-center">
          <div className="text-xs text-gray-500">总延迟</div>
          <div className={`text-lg font-semibold ${getPerformanceColor(metrics.totalLatency, [300, 800])}`}>
            {formatValue(metrics.totalLatency, 'ms')}
          </div>
        </div>
        
        <div className="text-center">
          <div className="text-xs text-gray-500">网络延迟</div>
          <div className={`text-lg font-semibold ${getPerformanceColor(metrics.networkLatency, [100, 200])}`}>
            {formatValue(metrics.networkLatency, 'ms')}
          </div>
        </div>
        
        <div className="text-center">
          <div className="text-xs text-gray-500">内存使用</div>
          <div className={`text-lg font-semibold ${getPerformanceColor(metrics.memoryUsage, [50, 80])}`}>
            {formatValue(metrics.memoryUsage, '%')}
          </div>
        </div>
        
        <div className="text-center">
          <div className="text-xs text-gray-500">帧率</div>
          <div className={`text-lg font-semibold ${getPerformanceColor(60 - metrics.frameRate, [10, 30])}`}>
            {formatValue(metrics.frameRate, 'fps')}
          </div>
        </div>
      </div>

      {/* 详细指标 */}
      {showDetailedMetrics && (
        <div className="space-y-2">
          <div className="text-xs font-medium text-gray-600 border-t pt-2">详细指标</div>
          
          <div className="grid grid-cols-2 gap-2 text-xs">
            <div className="flex justify-between">
              <span className="text-gray-500">音频处理:</span>
              <span className={getPerformanceColor(metrics.audioProcessingTime, [10, 20])}>
                {formatValue(metrics.audioProcessingTime, 'ms')}
              </span>
            </div>
            
            <div className="flex justify-between">
              <span className="text-gray-500">VAD处理:</span>
              <span className={getPerformanceColor(metrics.vadProcessingTime, [5, 10])}>
                {formatValue(metrics.vadProcessingTime, 'ms')}
              </span>
            </div>
            
            <div className="flex justify-between">
              <span className="text-gray-500">识别延迟:</span>
              <span className={getPerformanceColor(metrics.recognitionLatency, [300, 600])}>
                {formatValue(metrics.recognitionLatency, 'ms')}
              </span>
            </div>
            
            <div className="flex justify-between">
              <span className="text-gray-500">CPU使用:</span>
              <span className={getPerformanceColor(metrics.cpuUsage, [30, 60])}>
                {formatValue(metrics.cpuUsage, '%')}
              </span>
            </div>
          </div>
        </div>
      )}

      {/* 性能趋势图（简化版） */}
      {history.length > 10 && (
        <div className="mt-3 pt-2 border-t">
          <div className="text-xs text-gray-500 mb-1">延迟趋势 (最近60秒)</div>
          <div className="h-8 bg-gray-100 rounded relative overflow-hidden">
            <svg className="w-full h-full">
              <polyline
                fill="none"
                stroke="#3B82F6"
                strokeWidth="1"
                points={history.map((metric, index) => {
                  const x = (index / (history.length - 1)) * 100;
                  const y = 100 - (metric.totalLatency / 1000) * 100; // 假设最大1000ms
                  return `${x},${y}`;
                }).join(' ')}
              />
            </svg>
          </div>
        </div>
      )}

      {/* 控制按钮 */}
      <div className="mt-3 flex justify-end gap-2">
        <button
          onClick={isMonitoring ? stopMonitoring : startMonitoring}
          className={`px-2 py-1 text-xs rounded transition-colors ${
            isMonitoring 
              ? 'text-red-600 border border-red-300 hover:bg-red-50'
              : 'text-green-600 border border-green-300 hover:bg-green-50'
          }`}
        >
          {isMonitoring ? '停止监控' : '开始监控'}
        </button>
        
        <button
          onClick={() => setHistory([])}
          className="px-2 py-1 text-xs text-gray-600 border border-gray-300 rounded hover:bg-gray-50 transition-colors"
        >
          清空历史
        </button>
      </div>
    </div>
  );
};

export default PerformanceMonitor;
