import React from 'react';

interface ActionButtonsProps {
  onBack: () => void;
  onContinue: () => void;
  canContinue: boolean;
  isLoading?: boolean;
}

const ActionButtons: React.FC<ActionButtonsProps> = ({ onBack, onContinue, canContinue, isLoading = false }) => {
  return (
    <div className="flex justify-end space-x-4 mt-6">
      <button
        className="px-6 py-2 rounded-lg bg-gray-100 text-gray-600 hover:bg-gray-200 transition-all"
        onClick={onBack}
        disabled={isLoading}
      >
        返回
      </button>
      <button
        className={`px-6 py-2 rounded-lg font-medium text-white transition-all flex items-center justify-center ${
          canContinue && !isLoading
            ? 'bg-emerald-600 hover:bg-emerald-700' 
            : 'bg-emerald-300 cursor-not-allowed'
        }`}
        onClick={canContinue && !isLoading ? onContinue : undefined}
        disabled={!canContinue || isLoading}
      >
        {isLoading ? (
          <>
            <span className="mr-2 h-4 w-4 rounded-full border-2 border-white border-t-transparent animate-spin"></span>
            处理中...
          </>
        ) : (
          '继续'
        )}
      </button>
    </div>
  );
};

export default ActionButtons;
