好的，我们来完成第三周的任务：实时面试页面 - WebSocket与ASR初步集成。

(Okay, let's complete the tasks for the third week: Real-time Interview Page - Preliminary Integration of WebSocket and ASR.)

项目根目录 (Project Root Directory): local-mianshijun/

前端代码目录 (Frontend Code Directory): local-mianshijun/frontend/

后端代码目录 (Backend Code Directory): local-mianshijun/backend/

请在您的 Cursor 编辑器中按照以下步骤操作。

(Please follow these steps in your Cursor editor.)

------

## 任务 3.1: 实时面试辅助页面骨架与WebSocket连接 (1-2天)

(Task 3.1: Skeleton of Real-time Interview Assistance Page and WebSocket Connection (1-2 days))

核心目标 (Core Goal): 创建实时面试辅助页面，并建立与后端的WebSocket连接。

(Create a real-time interview assistance page and establish a WebSocket connection with the backend.)

### 1. 前端工作 (React) (Frontend Work (React))

**1.1 创建 `LiveInterviewPage.tsx` 页面 (Create `LiveInterviewPage.tsx` Page)**

- 中文：在 `frontend/src/pages/` 目录下创建一个新文件，命名为 `LiveInterviewPage.tsx`。

- English: In the `frontend/src/pages/` directory, create a new file named `LiveInterviewPage.tsx`.

- **操作 (Action)**:

  - 在Cursor的文件浏览器中，右键点击 `frontend/src/pages/` -> 新建文件 (New File) -> 输入 `LiveInterviewPage.tsx`。
  - (In Cursor's file explorer, right-click on `frontend/src/pages/` -> New File -> Type `LiveInterviewPage.tsx`.)
  - 粘贴以下基础代码到 `LiveInterviewPage.tsx`：
  - (Paste the following basic code into `LiveInterviewPage.tsx`:)

  TypeScript

  ```
  // frontend/src/pages/LiveInterviewPage.tsx
  import React, { useState, useEffect, useCallback } from 'react';
  import { useLocation, useNavigate }
  from 'react-router-dom';
  import useWebSocket, { ReadyState } from 'react-use-websocket';
  import useAuthStore from '../stores/authStore';
  import useInterviewStore from '../stores/interviewStore'; // 引入Zustand store
  
  const LiveInterviewPage: React.FC = () => {
    const navigate = useNavigate();
    const location = useLocation();
    const { isAuthenticated } = useAuthStore();
    const { config: interviewConfig, sharedStream } = useInterviewStore(); // 从Zustand获取配置和媒体流
  
    // 从路由状态或URL参数获取面试信息
    // Get interview information from route state or URL parameters
    const { positionId, language, answerStyle } = location.state || {};
  
    const [socketUrl, setSocketUrl] = useState<string | null>(null);
    const [messageHistory, setMessageHistory] = useState<MessageEvent[]>([]);
    const [inputMessage, setInputMessage] = useState<string>('');
  
    // 在组件加载时构建 WebSocket URL
    // Construct WebSocket URL when component mounts
    useEffect(() => {
      if (!isAuthenticated) {
        navigate('/login'); // 如果未登录，跳转到登录页
        return;
      }
      // 确保interviewConfig已加载
      // Ensure interviewConfig is loaded
      if (interviewConfig.selectedPositionId) {
        // 实际应用中 sessionId 可以由后端在创建面试会话时生成并返回
        // In a real application, sessionId can be generated by the backend when creating an interview session
        const sessionId = `session_${interviewConfig.selectedPositionId}_${Date.now()}`;
        const wsScheme = window.location.protocol === 'https:' ? 'wss' : 'ws';
        // 注意: Vercel部署时，Serverless Function的WebSocket路径可能需要特定配置
        // Note: When deploying to Vercel, WebSocket path for Serverless Functions might need specific configuration
        // 对于本地开发，Vite代理通常不处理WebSocket，需要直连后端WebSocket端口或由Vercel Dev处理
        // For local development, Vite proxy usually doesn't handle WebSockets; connect directly to backend WebSocket port or let Vercel Dev handle it.
        // 假设后端WebSocket服务运行在与HTTP API相同的域名和端口上，路径为 /api/ws/interview/:sessionId
        // Assuming backend WebSocket service runs on the same domain and port as HTTP API, path is /api/ws/interview/:sessionId
        const backendHost = process.env.NODE_ENV === 'production' ? window.location.host : 'localhost:3000'; // 本地开发指向后端端口
        setSocketUrl(`<span class="math-inline">\{wsScheme\}\://</span>{backendHost}/api/ws/interview/${sessionId}`);
      } else {
        console.warn("面试配置信息不完整，无法初始化WebSocket。");
        // 可以选择导航回设置页面或显示错误
        // Optionally navigate back to setup page or show an error
        // navigate('/interview-setup');
      }
    }, [isAuthenticated, navigate, interviewConfig.selectedPositionId]);
  
    const { sendMessage, lastMessage, readyState } = useWebSocket(socketUrl, {
      onOpen: () => console.log('WebSocket连接已打开 (WebSocket connection opened)'),
      onClose: () => console.log('WebSocket连接已关闭 (WebSocket connection closed)'),
      shouldReconnect: (closeEvent) => true, // 自动重连 (Attempt to reconnect on close)
    });
  
    useEffect(() => {
      if (lastMessage !== null) {
        setMessageHistory((prev) => prev.concat(lastMessage));
      }
    }, [lastMessage]);
  
    const connectionStatus = {
      [ReadyState.CONNECTING]: '连接中 (Connecting)',
      [ReadyState.OPEN]: '已连接 (Open)',
      [ReadyState.CLOSING]: '关闭中 (Closing)',
      [ReadyState.CLOSED]: '已关闭 (Closed)',
      [ReadyState.UNINSTANTIATED]: '未实例化 (Uninstantiated)',
    }[readyState];
  
    const handleSendMessage = useCallback(() => {
      if (inputMessage.trim() !== '') {
        sendMessage(inputMessage);
        setInputMessage('');
      }
    }, [inputMessage, sendMessage]);
  
    // 模拟从 InterviewSetupPage 跳转过来时携带的信息
    // Simulate information carried over from InterviewSetupPage
    useEffect(() => {
      if (positionId && language && answerStyle) {
        console.log('从面试准备页接收到的信息 (Info from InterviewSetupPage):', { positionId, language, answerStyle });
      }
      console.log('从Zustand获取的面试配置 (Interview config from Zustand):', interviewConfig);
      if (sharedStream) {
        console.log('共享的媒体流 (Shared MediaStream):', sharedStream);
        // 这里可以开始处理sharedStream中的音频轨道
        // You can start processing the audio track from sharedStream here
      }
    }, [positionId, language, answerStyle, interviewConfig, sharedStream]);
  
  
    if (!isAuthenticated) {
      return <p>请先登录 (Please log in first)。</p>;
    }
  
    if (!socketUrl && interviewConfig.selectedPositionId) {
      return <p>正在初始化WebSocket连接配置... (Initializing WebSocket connection config...)</p>
    }
  
    if (!interviewConfig.selectedPositionId && !socketUrl) {
      return (
         <div className="p-4">
             <p className="text-red-500">错误：面试配置信息丢失，无法启动实时面试。</p>
             <button onClick={() => navigate('/ai-interview')} className="mt-4 px-4 py-2 bg-blue-500 text-white rounded">
                 返回面试设置
             </button>
         </div>
      );
    }
  
    return (
      <div className="p-4 md:p-6 lg:p-8 min-h-screen bg-gray-100">
        <div className="max-w-4xl mx-auto bg-white shadow-xl rounded-lg p-6">
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-800 mb-6">
            实时面试辅助 (Live Interview Assistant)
          </h1>
          <div className="mb-4">
            <p>岗位 (Position): <span className="font-semibold">{interviewConfig.selectedPositionId || '未指定 (Not specified)'}</span></p>
            <p>语言 (Language): <span className="font-semibold">{interviewConfig.interviewLanguage === 'chinese' ? '中文' : '英文'}</span></p>
            <p>答案风格 (Answer Style): <span className="font-semibold">{interviewConfig.answerStyle === 'keywords_conversational' ? '关键词+口语化' : '口语化'}</span></p>
          </div>
  
          <div className="mb-4">
            <span>WebSocket连接状态 (Connection Status): </span>
            <span className={`font-semibold ${readyState === ReadyState.OPEN ? 'text-green-500' : 'text-orange-500'}`}>
              {connectionStatus}
            </span>
          </div>
  
          <div className="mb-4">
            <h3 className="text-lg font-semibold mb-2">消息历史 (Message History):</h3>
            <ul className="h-64 overflow-y-auto border border-gray-300 rounded p-2 bg-gray-50">
              {messageHistory.map((message, idx) => (
                <li key={idx} className="mb-1 p-1 border-b">
                  {message ? message.data : null}
                </li>
              ))}
            </ul>
          </div>
  
          <div className="flex gap-2">
            <input
              type="text"
              value={inputMessage}
              onChange={(e) => setInputMessage(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
              className="flex-grow p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="输入消息进行测试 (Type a message for testing)"
            />
            <button
              onClick={handleSendMessage}
              disabled={readyState !== ReadyState.OPEN}
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:bg-gray-300"
            >
              发送 (Send)
            </button>
          </div>
  
          <div className="mt-6">
             <button
                 onClick={() => navigate(-1)} // 返回上一页
                 className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
             >
                 结束面试并返回 (End Interview & Go Back)
             </button>
          </div>
        </div>
      </div>
    );
  };
  
  export default LiveInterviewPage;
  ```

**1.2 从 `InterviewSetupPage.tsx` (或 `InterviewConfigForm.tsx`) 跳转 (Navigate from `InterviewSetupPage.tsx` (or `InterviewConfigForm.tsx`))**

- 中文：修改 `frontend/src/components/interview/InterviewConfigForm.tsx` 中 `handleStartInterview` 函数，使其在所有条件满足后跳转到 `LiveInterviewPage`，并传递必要的面试信息。

- English: Modify the `handleStartInterview` function in `frontend/src/components/interview/InterviewConfigForm.tsx` to navigate to `LiveInterviewPage` after all conditions are met, passing necessary interview information.

- **操作 (Action)**:

  - 打开 `frontend/src/components/interview/InterviewConfigForm.tsx`。
  - (Open `frontend/src/components/interview/InterviewConfigForm.tsx`.)
  - 找到 `handleStartInterview` 函数，修改其跳转逻辑。
  - (Find the `handleStartInterview` function and modify its navigation logic.)

  TypeScript

  ```
  // frontend/src/components/interview/InterviewConfigForm.tsx
  // ... (其他导入和代码保持不变) ...
  import { useNavigate } from 'react-router-dom'; // 确保已导入
  import useInterviewStore from '../../stores/interviewStore'; // 确保已导入
  
  const InterviewConfigForm: React.FC = () => {
    const navigate = useNavigate(); // 获取 navigate 函数
    const { config, setSharedStream, setScreenShareStatus } = useInterviewStore(); // 获取最新的config
    // ... (其他 state 和 useEffect 保持不变) ...
  
    const allPrerequisitesMet =
      isAuthenticated &&
      uploadedResume &&
      config.selectedPositionId &&
      micPermissionResult?.granted &&
      config.screenShareStatus === 'sharing' &&
      config.audioCollection;
  
    const handleStartInterview = async () => {
      // ... (现有的校验逻辑保持不变) ...
       if (!uploadedResume) {
         showError('请先上传简历');
         return;
       }
       if (!config.selectedPositionId) {
         showError('请选择面试岗位');
         return;
       }
       const micOK = await verifyMicrophone();
       if (!micOK) {
         showError('麦克风权限是必需的');
         return;
       }
       if (config.screenShareStatus !== 'sharing' || !config.audioCollection) {
         showError('请先成功共享屏幕和系统音频');
         return;
       }
  
      setIsStartingInterview(true);
      showSuccess('所有准备就绪，即将开始面试...');
      console.log('Starting interview with config:', config);
  
      // 传递面试配置信息到 LiveInterviewPage
      // Pass interview config info to LiveInterviewPage
      navigate('/interview/live', {
        state: {
          positionId: config.selectedPositionId,
          language: config.interviewLanguage,
          answerStyle: config.answerStyle,
          // sharedStream 将从 Zustand store 中获取，不需要通过路由 state 传递
          // sharedStream will be obtained from Zustand store, no need to pass via route state
        }
      });
  
      // 注意：setIsStartingInterview(false) 通常在跳转后不需要，因为组件将卸载
      // Note: setIsStartingInterview(false) is usually not needed after navigation as the component will unmount.
      // 如果有需要在跳转前取消加载状态的逻辑，可以保留
    };
    // ... (组件的其余部分保持不变) ...
    return (
      // ... JSX ...
    );
  };
  export default InterviewConfigForm;
  ```

**1.3 安装 `react-use-websocket` (Install `react-use-websocket`)**

- 中文：这是一个方便处理WebSocket连接的React Hook库。

- English: This is a React Hook library that simplifies WebSocket connection handling.

- 操作 (Action)

  :

  - 打开终端，导航到 `frontend` 目录。
  - (Open terminal, navigate to the `frontend` directory.)
  - 运行 (Run): `npm install react-use-websocket`

**1.4 添加路由 (Add Route)**

- 中文：在 `frontend/src/App.tsx` 中为 `LiveInterviewPage` 添加路由。

- English: Add a route for `LiveInterviewPage` in `frontend/src/App.tsx`.

- **操作 (Action)**:

  - 打开 `frontend/src/App.tsx`。
  - (Open `frontend/src/App.tsx`.)
  - 导入 `LiveInterviewPage`。
  - (Import `LiveInterviewPage`.)
  - 在 `<Routes>` 中添加新的 `<Route>` (通常在 `<Layout />` 内部，并设为受保护路由)。
  - (Add a new `<Route>` within `<Routes>` (usually inside `<Layout />` and as a protected route).)

  TypeScript

  ```
  // frontend/src/App.tsx
  // ... (其他导入)
  import LiveInterviewPage from './pages/LiveInterviewPage'; // 新增导入
  
  // ...
  function App() {
    // ...
    return (
      // ...
      <Route path="/" element={<Layout />}>
        {/* ... (其他路由) ... */}
        <Route path="interview/live" element={ // 可以使用更具体的路径如 /interview/session/:sessionId
          <ProtectedRoute>
            <LiveInterviewPage />
          </ProtectedRoute>
        } />
      </Route>
      // ...
    );
  }
  export default App;
  ```

### 2. 后端工作 (Node.js/Vercel Edge Function) (Backend Work)

**2.1 安装 WebSocket 库 (Install WebSocket library)**

- 中文：我们将使用 `ws` 库来在Node.js中处理WebSocket。

- English: We'll use the `ws` library to handle WebSockets in Node.js.

- 操作 (Action)

  :

  - 打开终端，导航到 `backend` 目录。
  - (Open terminal, navigate to the `backend` directory.)
  - 运行 (Run): `npm install ws`
  - 运行 (Run): `npm install --save-dev @types/ws` (用于TypeScript类型定义)

**2.2 创建 WebSocket 服务接入点 (Create WebSocket Service Endpoint)**

- 中文：在 `backend` 目录下创建一个新的文件用于处理WebSocket连接。Vercel推荐将WebSocket服务部署为Edge Function以获得低延迟。

- English: Create a new file in the `backend` directory to handle WebSocket connections. Vercel recommends deploying WebSocket services as Edge Functions for low latency.

- **操作 (Action)**:

  - 在 `backend` 目录下创建一个名为 `ws` 的文件夹。
  - (In the `backend` directory, create a new folder named `ws`.)
  - 在 `backend/ws/` 文件夹下创建一个名为 `interview.ts` 的文件。
  - (In the `backend/ws/` folder, create a file named `interview.ts`.)
  - 路径：`local-mianshijun/backend/ws/interview.ts`
  - (Path: `local-mianshijun/backend/ws/interview.ts`)
  - 粘贴以下代码 (这是一个非常基础的 Vercel Edge Function WebSocket echo server 示例):
  - (Paste the following code (this is a very basic Vercel Edge Function WebSocket echo server example):)

  TypeScript

  ```
  // backend/ws/interview.ts
  import type { NextApiRequest, NextApiResponse } from 'next'; // Vercel Edge Functions 可以使用 Next.js API 路由的类型
  import { WebSocketServer, WebSocket } from 'ws';
  import { Server } from 'http';
  
  // 全局或模块级变量来存储WebSocket服务器实例和连接
  // Global or module-level variable to store WebSocket server instance and connections
  let wss: WebSocketServer | null = null;
  const clients = new Map<string, WebSocket>(); // sessionId -> WebSocket
  
  // Helper to initialize WebSocket server if not already done
  // (Vercel Edge Functions typically handle HTTP server setup implicitly)
  // This setup is more for conceptual understanding or if combining with an HTTP server.
  // For a pure WebSocket Edge Function, Vercel handles the upgrade request.
  const ensureWebSocketServer = (httpServer?: Server) => {
    if (!wss && httpServer) {
      console.log('Initializing WebSocket Server...');
      wss = new WebSocketServer({ server: httpServer, path: '/api/ws/interview' }); // 路径匹配前端
  
      wss.on('connection', (ws, req) => {
        const url = new URL(req.url || '', `http://${req.headers.host}`);
        const sessionId = url.pathname.split('/').pop() || `unknown_session_${Date.now()}`;
  
        console.log(`WebSocket client connected for session: ${sessionId}`);
        clients.set(sessionId, ws);
  
        ws.on('message', (message) => {
          console.log(`Received message for session ${sessionId}: ${message}`);
          // Echo 功能: 将消息发回给同一个客户端
          // Echo functionality: send message back to the same client
          ws.send(`Echo from server (session ${sessionId}): ${message}`);
        });
  
        ws.on('close', () => {
          console.log(`WebSocket client disconnected for session: ${sessionId}`);
          clients.delete(sessionId);
        });
  
        ws.on('error', (error) => {
          console.error(`WebSocket error for session ${sessionId}:`, error);
          clients.delete(sessionId); // 发生错误时也移除客户端
        });
  
        ws.send(`Welcome! You are connected to session: ${sessionId}`);
      });
  
      wss.on('error', (error) => {
         console.error('WebSocket Server Error:', error);
      });
  
      console.log('WebSocket Server initialized and listening.');
    } else if (!wss && !httpServer) {
      console.warn('Cannot initialize WebSocket server without an HTTP server instance for non-Edge Function setup.');
    }
    return wss;
  };
  ```

```
// Vercel Edge Function Handler for WebSocket
// (This is a simplified representation; Vercel's handling of WebSockets in Edge Functions
// might involve different entry points or abstractions than traditional ws with Express)
// The key is that Vercel Edge Runtime supports the WebSocket API.

// For Vercel Serverless Functions (Node.js runtime, not Edge), a common pattern involves
// upgrading the HTTP request in the main server file (e.g., backend/server.ts).

// **Since you are using Express in `backend/server.ts`, we will integrate WebSocket there.**
// **The `backend/ws/interview.ts` file might not be directly used by Vercel if Express handles the upgrade.**
// **Let's modify `backend/server.ts` instead.**

// Placeholder export to make the file valid
export default function handler(req: NextApiRequest, res: NextApiResponse) {
    res.status(404).json({ message: "This endpoint is for WebSocket connections, not HTTP GET." });
}
 ```
```

**2.3 修改 `backend/server.ts` 以集成 WebSocket (Modify `backend/server.ts` to Integrate WebSocket)**

- 中文：我们将修改现有的 `backend/server.ts` (Express 应用)，使其能够处理 WebSocket 的升级请求。

- English: We will modify the existing `backend/server.ts` (Express app) to handle WebSocket upgrade requests.

- **操作 (Action)**:

  - 打开 `local-mianshijun/backend/server.ts`。
  - (Open `local-mianshijun/backend/server.ts`.)
  - 添加 `ws` 的导入和 `WebSocketServer` 的设置。
  - (Add import for `ws` and setup for `WebSocketServer`.)

  TypeScript

  ```
  // backend/server.ts
  import express from 'express';
  import cors from 'cors';
  import dotenv from 'dotenv';
  import { createServer } from 'http'; // 导入 createServer
  import { WebSocketServer, WebSocket } from 'ws'; // 导入 ws
  import { PrismaClient } from '@prisma/client';
  
  // 导入您的路由处理程序 (Import your route handlers)
  import loginHandler from './auth/login';
  import registerHandler from './auth/register';
  import healthHandler from './health'; // 假设 health.ts 导出一个 Express-compatible handler
  import { targetPositionService } from './lib/api/apiService'; // 调整为您的实际服务
  import { resumeService } from './lib/api/apiService'; // 调整为您的实际服务
  import authMiddleware from './middleware/authMiddleware'; // 导入认证中间件
  import { setupWebSocket } from './websocket/interviewWs'; // 我们将把WebSocket逻辑移到这里
  
  dotenv.config();
  
  const app = express();
  const prisma = new PrismaClient(); // Prisma 客户端实例
  
  app.use(cors());
  app.use(express.json());
  
  // HTTP 路由 (HTTP Routes)
  app.get('/api/health', healthHandler);
  app.post('/api/auth/register', registerHandler);
  app.post('/api/auth/login', loginHandler);
  
  // 应用认证中间件到后续需要保护的路由
  // Apply auth middleware to subsequent protected routes
  // 注意: 认证中间件通常不应用于 WebSocket 升级请求路径本身，
  // WebSocket 的认证通常在连接建立时单独处理。
  // Note: Auth middleware is usually not applied to the WebSocket upgrade path itself.
  // WebSocket authentication is typically handled separately upon connection.
  // app.use('/api', authMiddleware); // 如果您希望所有 /api 都受保护 (If you want all /api protected)
  
  // 意向岗位 API 路由 (Target Position API Routes)
  // (这里应该是您 TargetPosition 的 CRUD 路由，并应用 authMiddleware)
  // app.get('/api/positions', authMiddleware, async (req, res) => { /* ... */ });
  // app.post('/api/positions', authMiddleware, async (req, res) => { /* ... */ });
  
  // 简历 API 路由 (Resume API Routes)
  // (这里应该是您 Resume 的 CRUD 路由，并应用 authMiddleware)
  // app.get('/api/resumes', authMiddleware, async (req, res) => { /* ... */ });
  // app.post('/api/resumes', authMiddleware, async (req, res) => { /* ... */ });
  
  
  // --- 创建 HTTP 服务器并集成 WebSocket 服务器 ---
  // --- Create HTTP server and integrate WebSocket server ---
  const server = createServer(app); // 使用 Express app 创建 HTTP 服务器
  
  // 调用 setupWebSocket 并传入 server 实例
  // Call setupWebSocket and pass the server instance
  setupWebSocket(server);
  
  
  const PORT = process.env.PORT || 3000;
  server.listen(PORT, () => { // 使用 server.listen 而不是 app.listen
    console.log(`✅ Express server with WebSocket support running on http://localhost:${PORT}`);
  });
  
  export default app; // 仍然导出 app 供 Vercel Serverless Functions 使用（如果需要）
                      // Still export app for Vercel Serverless Functions (if needed)
  ```

**2.4 创建 `backend/websocket/interviewWs.ts` (Create `backend/websocket/interviewWs.ts`)**

- 中文：我们将WebSocket的主要逻辑（包括创建 `interview_sessions` 记录）放到这个新文件中。

- English: We will put the main WebSocket logic (including creating `interview_sessions` records) into this new file.

- **操作 (Action)**:

  - 在 `backend` 目录下创建一个新文件夹 `websocket`。
  - (In the `backend` directory, create a new folder `websocket`.)
  - 在 `backend/websocket/` 文件夹下创建 `interviewWs.ts`。
  - (In the `backend/websocket/` folder, create `interviewWs.ts`.)
  - 粘贴以下代码：
  - (Paste the following code:)

  TypeScript

  ```
  // backend/websocket/interviewWs.ts
  import { WebSocketServer, WebSocket } from 'ws';
  import { Server } from 'http';
  import { PrismaClient } from '@prisma/client';
  import url from 'url'; // 导入 'url' 模块
  import jwt from 'jsonwebtoken'; // 用于验证token
  
  const prisma = new PrismaClient();
  const JWT_SECRET = process.env.JWT_SECRET || 'your_default_secret';
  
  interface AuthenticatedWebSocket extends WebSocket {
    userId?: string; // 假设JWT解码后包含userId
    sessionId?: string;
  }
  
  // Map to store clients per session, and sessions per client (WebSocket instance)
  const sessionClients = new Map<string, Set<AuthenticatedWebSocket>>(); // sessionId -> Set<WebSocket>
  const clientSessions = new WeakMap<AuthenticatedWebSocket, string>(); // WebSocket -> sessionId
  
  export function setupWebSocket(httpServer: Server) {
    console.log('Attempting to initialize WebSocket Server...');
    // WebSocket服务器将附加到现有的HTTP服务器上
    // The WebSocket server will attach to the existing HTTP server
    const wss = new WebSocketServer({ server: httpServer }); 
                                    // 路径由HTTP服务器的路由处理，这里不需要指定 path
                                    // The path is handled by HTTP server's routing, no need to specify path here
  
    wss.on('connection', async (ws: AuthenticatedWebSocket, req) => {
      // 从连接URL中解析 sessionId 和 token
      // Parse sessionId and token from connection URL
      const requestUrl = req.url ? new URL(req.url, `http://${req.headers.host}`) : null;
      const pathParts = requestUrl?.pathname.split('/');
      const sessionIdFromPath = pathParts?.pop() || `unknown_session_${Date.now()}`;
      const token = requestUrl?.searchParams.get('token');
  
      console.log(`WebSocket connection attempt for session: ${sessionIdFromPath}, token: ${token ? 'present' : 'missing'}`);
  
      // 1. 验证 Token (Authenticate Token)
      if (!token) {
        console.log('WebSocket connection rejected: No token provided.');
        ws.close(1008, 'Token required'); // 1008: Policy Violation
        return;
      }
  
      let decodedUserId: string | undefined;
      try {
        const decoded = jwt.verify(token, JWT_SECRET) as { userId: string }; // 假设JWT中包含userId
        decodedUserId = decoded.userId;
        ws.userId = decodedUserId; // 将userId附加到ws对象上
        ws.sessionId = sessionIdFromPath; // 将sessionId附加到ws对象上
        console.log(`WebSocket token validated for user ${decodedUserId}, session ${sessionIdFromPath}`);
      } catch (err) {
        console.error('WebSocket connection rejected: Invalid token.', err);
        ws.close(1008, 'Invalid token');
        return;
      }
  
      if (!decodedUserId) {
         console.log('WebSocket connection rejected: User ID not found in token.');
         ws.close(1008, 'User ID missing in token');
         return;
      }
  
      // 2. 创建 interview_sessions 表记录 (Create interview_sessions record)
      try {
        const existingSession = await prisma.interviewSession.findUnique({
          where: { id: sessionIdFromPath },
        });
  
        if (!existingSession) {
          // 假设从前端的路由状态或初始消息获取 title_job_info
          // For now, let's use a placeholder or wait for a message from client
          // const jobInfo = requestUrl?.searchParams.get('jobInfo') || 'Default Job Info';
          // 在实际应用中，岗位信息应该在创建会话时就确定，或者通过第一条消息传递
  
          await prisma.interviewSession.create({
            data: {
              id: sessionIdFromPath,
              userId: decodedUserId, // 确保类型匹配 (Ensure type matches)
              status: 'active', //  初始状态 (Initial status)
              titleJobInfo: "Job Info from Client", // TODO: Get this from client or initial setup
              startedAt: new Date(),
            },
          });
          console.log(`New interview session created in DB: ${sessionIdFromPath}`);
        } else {
          console.log(`Joining existing interview session: ${sessionIdFromPath}`);
        }
      } catch (dbError) {
        console.error(`Database error for session ${sessionIdFromPath}:`, dbError);
        ws.close(1011, 'Server error creating/joining session'); // 1011: Internal Error
        return;
      }
  
      // 管理连接 (Manage connections)
      if (!sessionClients.has(sessionIdFromPath)) {
        sessionClients.set(sessionIdFromPath, new Set());
      }
      sessionClients.get(sessionIdFromPath)!.add(ws);
      clientSessions.set(ws, sessionIdFromPath);
  
      console.log(`WebSocket client (User: ${ws.userId}) connected to session: ${sessionIdFromPath}`);
      ws.send(`Welcome! You are connected to session: ${sessionIdFromPath}. User ID: ${ws.userId}`);
  
      ws.on('message', (message) => {
        const currentSessionId = clientSessions.get(ws);
        if (!currentSessionId) {
          console.error('Received message from a WebSocket without a session mapping.');
          return;
        }
        console.log(`Received message for session ${currentSessionId} from user ${ws.userId}: ${message}`);
        // Echo 功能: 将消息发回给同一个客户端
        // Echo functionality: send message back to the same client
        ws.send(`Echo from server (session ${currentSessionId}): ${message}`);
  
        // TODO: 任务 3.2 - 在这里接收音频数据并处理 (Receive audio data here and process it)
        // TODO: 任务 3.3 - 在这里调用 ASR 和 LLM，然后将结果发送回客户端 (Call ASR and LLM here, then send results back to client)
      });
  
      ws.on('close', async () => {
        const closedSessionId = clientSessions.get(ws);
        if (closedSessionId) {
          console.log(`WebSocket client (User: ${ws.userId}) disconnected from session: ${closedSessionId}`);
          const clientsInSession = sessionClients.get(closedSessionId);
          if (clientsInSession) {
            clientsInSession.delete(ws);
            if (clientsInSession.size === 0) {
              sessionClients.delete(closedSessionId);
              console.log(`Session ${closedSessionId} is now empty.`);
              // 更新数据库中的会话状态为 completed
              // Update session status in DB to completed
              try {
                await prisma.interviewSession.update({
                  where: { id: closedSessionId },
                  data: { status: 'completed', endedAt: new Date() },
                });
                console.log(`Session ${closedSessionId} marked as completed in DB.`);
              } catch (dbError) {
                console.error(`Error updating session ${closedSessionId} in DB:`, dbError);
              }
            }
          }
          clientSessions.delete(ws);
        }
      });
  
      ws.on('error', (error) => {
        const errorSessionId = clientSessions.get(ws);
        console.error(`WebSocket error for session ${errorSessionId} (User: ${ws.userId}):`, error);
        if (errorSessionId) {
          const clientsInSession = sessionClients.get(errorSessionId);
          if (clientsInSession) {
            clientsInSession.delete(ws);
          }
          clientSessions.delete(ws);
        }
      });
    });
  
    wss.on('error', (error) => {
      console.error('WebSocket Server Global Error:', error);
    });
  
    console.log('WebSocket Server initialized and listening on the provided HTTP server.');
  }
  ```

  - 重要

    :

    - 我们将 WebSocket 服务器附加到由 Express 创建的 HTTP 服务器上。
    - **认证**: 在 `connection` 事件中，我们从连接 URL 的查询参数 (`?token=...`) 中获取 JWT。您需要在前端发起 WebSocket 连接时附带此 token。
    - **创建 `interview_sessions` 记录**: 当新的 WebSocket 连接建立并且 token 验证通过后，我们为该 `sessionId` 创建或查找一条记录。
    - (We attach the WebSocket server to the HTTP server created by Express.)
    - (**Authentication**: In the `connection` event, we get the JWT from the query parameter (`?token=...`) of the connection URL. You need to include this token when initiating the WebSocket connection from the frontend.)
    - (**Create `interview_sessions` record**: When a new WebSocket connection is established and the token is validated, we create or find a record for that `sessionId`.)

**2.5 修改前端 WebSocket 连接 URL 以包含 Token (Modify Frontend WebSocket Connection URL to Include Token)**

- 中文：在 `frontend/src/pages/LiveInterviewPage.tsx` 中，修改 `useEffect` 来动态构建 `socketUrl`，并在查询参数中包含 JWT。

- English: In `frontend/src/pages/LiveInterviewPage.tsx`, modify the `useEffect` to dynamically build `socketUrl` and include JWT in query parameters.

- **操作 (Action)**:

  TypeScript

  ```
  // frontend/src/pages/LiveInterviewPage.tsx
  // ... (其他导入)
  import useAuthStore from '../stores/authStore'; // 确保导入
  
  // ...
  const LiveInterviewPage: React.FC = () => {
    const { token: authToken, isAuthenticated } = useAuthStore(); // 获取 token
    // ... (其他 state 和 useEffect)
  
    useEffect(() => {
      if (!isAuthenticated) {
        navigate('/login');
        return;
      }
      if (interviewConfig.selectedPositionId && authToken) { // 确保 authToken 存在
        const sessionId = `session_${interviewConfig.selectedPositionId}_${Date.now()}`;
        const wsScheme = window.location.protocol === 'https:' ? 'wss' : 'ws';
        const backendHost = process.env.NODE_ENV === 'production' ? window.location.host : 'localhost:3000';
  
        // 将 token 添加到 WebSocket URL 的查询参数中
        // Add token to WebSocket URL query parameters
        setSocketUrl(`<span class="math-inline">\{wsScheme\}\://</span>{backendHost}/?token=<span class="math-inline">\{encodeURIComponent\(authToken\)\}&sessionId\=</span>{sessionId}`);
        // 注意: Express+ws 通常不直接从路径中取sessionId，我们将sessionId也作为查询参数传递，或让后端从JWT中提取信息来关联
        // 我们修改后端ws逻辑，让它从路径中取sessionId
        // Let's modify backend ws logic to take sessionId from path after /api/ws/interview/
        // The path used by `new WebSocketServer({ server: httpServer })` should not contain the dynamic part.
        // The server will listen on all paths, and we parse `req.url` in `connection` handler.
        // For `backend/server.ts` with Express, the `server.on('upgrade', ...)` will handle paths like `/api/ws/interview/:sessionId`.
        // So, the frontend URL should be:
        // setSocketUrl(`<span class="math-inline">\{wsScheme\}\://</span>{backendHost}/api/ws/interview/<span class="math-inline">\{sessionId\}?token\=</span>{encodeURIComponent(authToken)}`);
      } else {
        if (!authToken) console.warn("Auth token is missing, cannot initialize WebSocket.");
        if (!interviewConfig.selectedPositionId) console.warn("面试配置信息不完整，无法初始化WebSocket。");
      }
    }, [isAuthenticated, navigate, interviewConfig.selectedPositionId, authToken]); // 添加 authToken 到依赖数组
  
    // ... (其余代码)
  };
  ```

  **重要调整**：WebSocket 服务器 (`ws`) 通常直接附加到 HTTP 服务器上，不通过 Express 的路由系统。因此，连接路径通常是根路径或一个特定于WebSocket的简单路径。我们将让 `backend/server.ts` 在升级请求时处理。

**2.6 在 `backend/server.ts` 中处理 WebSocket 升级请求 (Handle WebSocket Upgrade Request in `backend/server.ts`)**

- 中文：我们需要让 Express 服务器知道如何将特定的 HTTP 升级请求转交给 WebSocket 服务器。

- English: We need to let the Express server know how to hand off specific HTTP upgrade requests to the WebSocket server.

- 操作 (Action): 修改 backend/server.ts 中的 server.listen 部分，并在其上方添加 server.on('upgrade', ...) 逻辑。

  setupWebSocket 函数将负责创建和管理 WSS 实例。

  TypeScript

  ```
  // backend/server.ts
  // ... (imports and app setup) ...
  
  const server = createServer(app);
  
  // 设置WebSocket (Setup WebSocket)
  // The setupWebSocket function will now internally create the WebSocketServer
  // and attach it to this 'server' instance.
  // It will also handle the '/api/ws/interview/:sessionId' path logic internally.
  setupWebSocket(server); // 传递HTTP server实例
  
  const PORT = process.env.PORT || 3000;
  server.listen(PORT, () => {
    console.log(`✅ Express server with WebSocket support running on http://localhost:${PORT}`);
    console.log(`WebSocket connections will be handled via HTTP server upgrade.`);
  });
  
  // ... (export default app)
  ```

````
确保 `backend/websocket/interviewWs.ts` 中的 `setupWebSocket` 函数能够处理好路径和参数。
(Ensure the `setupWebSocket` function in `backend/websocket/interviewWs.ts` correctly handles paths and parameters.)

修改 `backend/websocket/interviewWs.ts` 的 `setupWebSocket` 部分，使其能解析路径中的 `sessionId`。

```typescript
// backend/websocket/interviewWs.ts
// ... (imports and PrismaClient) ...

export function setupWebSocket(httpServer: Server) {
  console.log('Attempting to initialize WebSocket Server for path /api/ws/interview/:sessionId ...');
  const wss = new WebSocketServer({ noServer: true }); // We'll handle upgrade manually

  httpServer.on('upgrade', (request, socket, head) => {
    const pathname = request.url ? url.parse(request.url).pathname : undefined;
    const requestUrl = request.url ? new URL(request.url, `http://${request.headers.host}`) : null;
    const token = requestUrl?.searchParams.get('token');

    // 正则匹配路径 /api/ws/interview/:sessionId
    // Regex to match path /api/ws/interview/:sessionId
    const interviewPathRegex = /^\/api\/ws\/interview\/([a-zA-Z0-9_-]+)$/;
    const match = pathname ? interviewPathRegex.exec(pathname) : null;

    if (match && token) { // 检查路径匹配和token存在
      const sessionIdFromPath = match[1];
      console.log(`Upgrade request for path: ${pathname}, sessionId: ${sessionIdFromPath}`);
      
      wss.handleUpgrade(request, socket, head, (wsAuthenticated) => {
        const ws = wsAuthenticated as AuthenticatedWebSocket; // 类型断言
        // 在这里，连接已经建立，可以触发 'connection' 事件
        // Here, connection is established, can trigger 'connection' event
        // 将 sessionId 和 token 传递给 connection 处理逻辑
        // Pass sessionId and token to connection handling logic
        // (ws as any)._req = { url: `/?token=<span class="math-inline">\{token\}&sessionId\=</span>{sessionIdFromPath}`, headers: request.headers }; // 模拟req对象给connection事件用
        // 上面这行模拟req可能不是最佳实践，最好直接在connection回调中处理认证和会话
        
        // 将认证和会话创建逻辑移到这里或 'connection' 事件中
        // Move authentication and session creation logic here or into 'connection' event
        
        wss.emit('connection', ws, request, sessionIdFromPath, token); // 传递 sessionId 和 token
      });
    } else {
      console.log(`Upgrade request for path ${pathname} rejected (path or token mismatch).`);
      socket.destroy();
    }
  });

  // wss.on('connection', async (ws: AuthenticatedWebSocket, req) => { ... })
  // 修改 connection 事件处理器以接收 sessionId 和 token
  wss.on('connection', async (ws: AuthenticatedWebSocket, req, sessionIdFromPath?: string, token?: string | null) => {
    // 如果 sessionIdFromPath 和 token 不是直接传来，需要从 req.url 解析
    // (这部分逻辑已在 http.on('upgrade') 中处理，并作为参数传来)
    if (!sessionIdFromPath || !token) {
        // 这种情况理论上不应该发生，因为我们在 upgrade 时做了检查
        console.error('Session ID or Token missing in WebSocket connection handler.');
        ws.close(1008, 'Internal server error: Session ID or Token missing.');
        return;
    }

    console.log(`WebSocket connection attempt for session: ${sessionIdFromPath}, token: ${token ? 'present' : 'missing'}`);

    // 1. 验证 Token (Authenticate Token)
    let decodedUserId: string | undefined;
    try {
      const decoded = jwt.verify(token, JWT_SECRET) as { userId: string };
      decodedUserId = decoded.userId;
      ws.userId = decodedUserId;
      ws.sessionId = sessionIdFromPath;
      console.log(`WebSocket token validated for user ${decodedUserId}, session ${sessionIdFromPath}`);
    } catch (err) {
      // ... (原有的 token 验证失败逻辑)
      console.error('WebSocket connection rejected: Invalid token.', err);
      ws.close(1008, 'Invalid token');
      return;
    }
    // ... (后续的数据库操作和事件监听器与之前类似)
    // ... (subsequent database operations and event listeners are similar to before)
       // 2. 创建 interview_sessions 表记录 (Create interview_sessions record)
     try {
       const existingSession = await prisma.interviewSession.findUnique({
         where: { id: sessionIdFromPath },
       });

       if (!existingSession) {
         await prisma.interviewSession.create({
           data: {
             id: sessionIdFromPath,
             userId: decodedUserId, 
             status: 'active', 
             titleJobInfo: "Job Info from Client", // TODO
             startedAt: new Date(),
           },
         });
         console.log(`New interview session created in DB: ${sessionIdFromPath}`);
       } else {
         console.log(`Joining existing interview session: ${sessionIdFromPath}`);
       }
     } catch (dbError) {
       console.error(`Database error for session ${sessionIdFromPath}:`, dbError);
       ws.close(1011, 'Server error creating/joining session'); 
       return;
     }
     
     if (!sessionClients.has(sessionIdFromPath)) {
       sessionClients.set(sessionIdFromPath, new Set());
     }
     sessionClients.get(sessionIdFromPath)!.add(ws);
     clientSessions.set(ws, sessionIdFromPath);

     console.log(`WebSocket client (User: ${ws.userId}) connected to session: ${sessionIdFromPath}`);
     ws.send(`Welcome! You are connected to session: ${sessionIdFromPath}. User ID: ${ws.userId}`);

     ws.on('message', (message) => { /* ... */ });
     ws.on('close', async () => { /* ... */ });
     ws.on('error', (error) => { /* ... */ });
  });

  // ... (wss.on('error') 等)
    console.log('WebSocket Server initialized to handle upgrades on /api/ws/interview/:sessionId');
}
```

并且，修改前端 `LiveInterviewPage.tsx` 的 `socketUrl` 设置，以匹配这个路径：
(And, modify the `socketUrl` setting in `frontend/src/pages/LiveInterviewPage.tsx` to match this path:)
```tsx
// frontend/src/pages/LiveInterviewPage.tsx
useEffect(() => {
  // ... (isAuthenticated 和 authToken 检查)
  if (interviewConfig.selectedPositionId && authToken) {
    const sessionId = `session_${interviewConfig.selectedPositionId}_${Date.now()}`;
    const wsScheme = window.location.protocol === 'https:' ? 'wss' : 'ws';
    const backendHost = process.env.NODE_ENV === 'production' ? window.location.host : 'localhost:3000';
    
    // 更新URL以匹配后端的升级路径 (Update URL to match backend upgrade path)
    setSocketUrl(`<span class="math-inline">\{wsScheme\}\://</span>{backendHost}/api/ws/interview/<span class="math-inline">\{sessionId\}?token\=</span>{encodeURIComponent(authToken)}`);
  }
  // ...
}, [/* ... dependencies ... */]);
```
````

### 3. 数据库相关 (Database Related)

- 中文：DDL (数据定义语言) 已在开发计划的第8节提供，并且在任务1.2中创建了 `users` 和 `user_balances` 表。现在需要 `interview_sessions` 表。

- English: DDL (Data Definition Language) was provided in section 8 of the development plan, and `users` and `user_balances` tables were created in task 1.2. Now, the `interview_sessions` table is needed.

- 操作 (Action)

  :

  - 确保 `backend/prisma/schema.prisma` 中已包含 `InterviewSession` 模型定义，并正确关联到 `User`。

  - (Ensure the 

    ```
    InterviewSession
    ```

     model definition is included in 

    ```
    backend/prisma/schema.prisma
    ```

     and correctly related to 

    ```
    User
    ```

    .)

    代码段

    ```
    // backend/prisma/schema.prisma
    model User {
      id               String    @id @default(uuid())
      // ... other fields
      interviewSessions InterviewSession[] // 添加反向关系 (Add back-relation)
    }
    
    model InterviewSession {
      id             String    @id @default(uuid())
      userId         String
      user           User      @relation(fields: [userId], references: [id], onDelete: Cascade)
      titleJobInfo   String?
      status         String    @default("pending") // e.g., pending, active, completed, error
      createdAt      DateTime  @default(now())
      startedAt      DateTime?
      endedAt        DateTime?
    
      transcripts    InterviewTranscript[] // 后续任务添加 (For later tasks)
      aiSuggestions  AISuggestion[]        // 后续任务添加 (For later tasks)
    
      @@map("interview_sessions")
    }
    ```

  - 在 `backend` 目录下运行迁移 (Run migration in the `backend` directory): `npx prisma migrate dev --name add_interview_sessions_table`

  - 重新生成 Prisma Client (Regenerate Prisma Client): `npx prisma generate`

### 4. 测试 (Testing)

- **本地测试 (Local Testing)**:
  1. 启动后端: `cd local-mianshijun/backend && npm run dev`
  2. 启动前端: `cd local-mianshijun/frontend && npm run dev`
  3. 确保已登录。 (Ensure you are logged in.)
  4. 导航到包含 `InterviewConfigForm.tsx` 的页面 (例如 `/ai-interview`)，完成配置并点击“开始面试”。 (Navigate to the page containing `InterviewConfigForm.tsx` (e.g., `/ai-interview`), complete the configuration, and click "Start Interview".)
  5. 检查浏览器开发者工具的网络(Network)标签页，看是否有 WebSocket 连接建立。 (Check the Network tab in browser developer tools to see if a WebSocket connection is established.)
  6. 检查 `LiveInterviewPage.tsx` 是否显示“已连接”状态。 (Check if `LiveInterviewPage.tsx` shows "Connected" status.)
  7. 在输入框发送消息，看是否收到来自服务器的 echo 回显。 (Send a message from the input field and see if you receive an echo from the server.)
  8. 检查后端控制台是否有 WebSocket 连接、消息和数据库操作的日志。 (Check the backend console for logs of WebSocket connections, messages, and database operations.)
  9. 检查 Neon 数据库 `interview_sessions` 表是否创建了新的记录。 (Check if a new record is created in the `interview_sessions` table in your Neon database.)
- **Vercel 部署后测试 (Testing after Vercel Deployment)**:
  - 提交所有更改到 GitHub，等待 Vercel 部署完成。
  - (Commit all changes to GitHub and wait for Vercel deployment to complete.)
  - 在线重复上述测试步骤。确保 WebSocket 连接在线上环境也能正常工作。
  - (Repeat the above test steps online. Ensure WebSocket connection works in the live environment.)

------

## 任务 3.2: 音频流处理与发送 (前端) (1-2天)

(Task 3.2: Audio Stream Processing and Sending (Frontend) (1-2 days))

核心目标 (Core Goal): 从屏幕共享的音频轨道或麦克风获取音频流，进行分片并通过WebSocket发送到后端。

(Capture audio stream from screen-shared audio track or microphone, segment it, and send to backend via WebSocket.)

### 1. 前端工作 (React) (Frontend Work (React))

**1.1 在 `LiveInterviewPage.tsx` 中处理音频流 (Handle Audio Stream in `LiveInterviewPage.tsx`)**

- 中文：我们将使用 `MediaRecorder` API 来录制音频片段。

- English: We will use the `MediaRecorder` API to record audio snippets.

- **操作 (Action)**:

  - 打开 `frontend/src/pages/LiveInterviewPage.tsx`。
  - (Open `frontend/src/pages/LiveInterviewPage.tsx`.)
  - 添加状态和逻辑来处理 `MediaRecorder`。
  - (Add state and logic to handle `MediaRecorder`.)

  TypeScript

  ```
  // frontend/src/pages/LiveInterviewPage.tsx
  // ... (之前的导入)
  import React, { useState, useEffect, useCallback, useRef } from 'react'; // 添加 useRef
  // ...
  
  const LiveInterviewPage: React.FC = () => {
    // ... (已有的 state 和 hooks)
    const mediaRecorderRef = useRef<MediaRecorder | null>(null);
    const audioChunksRef = useRef<Blob[]>([]);
    const { sharedStream, config: interviewConfig } = useInterviewStore(); // 从Zustand获取共享流
  
    // Effect for handling sharedStream and MediaRecorder setup
    useEffect(() => {
      if (sharedStream && readyState === ReadyState.OPEN) {
        console.log('sharedStream is available, attempting to set up MediaRecorder.');
        const audioTracks = sharedStream.getAudioTracks();
        if (audioTracks.length > 0) {
          // 优先使用共享流中的音频轨道 (Prefer audio track from sharedStream)
          // 创建一个新的 MediaStream 只包含音频轨道，以避免录制视频
          // Create a new MediaStream containing only the audio track to avoid recording video
          const audioOnlyStream = new MediaStream(audioTracks);
  
          try {
             // 尝试不同的 MIME 类型 (Try different MIME types)
             const options = { mimeType: 'audio/webm; codecs=opus' };
             if (!MediaRecorder.isTypeSupported(options.mimeType)) {
                 console.warn(`${options.mimeType} is not supported. Trying audio/ogg...`);
                 options.mimeType = 'audio/ogg; codecs=opus';
                 if (!MediaRecorder.isTypeSupported(options.mimeType)) {
                     console.warn(`${options.mimeType} is not supported. Trying audio/mp4...`);
                     options.mimeType = 'audio/mp4'; // Often supported, but might be larger
                      if (!MediaRecorder.isTypeSupported(options.mimeType)) {
                         console.error("No suitable audio/webm, audio/ogg or audio/mp4 MIME type supported for MediaRecorder.");
                         // TODO: 向用户显示错误提示 (Show error to user)
                         return;
                     }
                 }
             }
             console.log(`Using MediaRecorder with MIME type: ${options.mimeType}`);
  
            mediaRecorderRef.current = new MediaRecorder(audioOnlyStream, options);
  
            mediaRecorderRef.current.ondataavailable = (event) => {
              if (event.data.size > 0) {
                audioChunksRef.current.push(event.data);
                // 为了减少延迟，我们可以更频繁地发送小块数据
                // For lower latency, we could send smaller chunks more frequently
                const audioBlob = new Blob(audioChunksRef.current, { type: options.mimeType });
                console.log(`Sending audio chunk, size: ${audioBlob.size} bytes`);
                sendMessage(audioBlob); // 通过 WebSocket 发送 Blob 数据
                audioChunksRef.current = []; // 发送后清空 (Clear after sending)
              }
            };
  
            mediaRecorderRef.current.onstart = () => {
              console.log('MediaRecorder started.');
            };
            mediaRecorderRef.current.onstop = () => {
              console.log('MediaRecorder stopped.');
              // 可以在这里处理停止后的最后数据块 (Can handle final chunk here if needed)
            };
            mediaRecorderRef.current.onerror = (event) => {
              console.error('MediaRecorder error:', event);
            };
  
            // 每秒发送一次数据 (Send data every second)
            mediaRecorderRef.current.start(1000); // timeslice in ms
            console.log('MediaRecorder configured and started.');
  
          } catch (e) {
            console.error('Error setting up MediaRecorder:', e);
            // TODO: 向用户显示错误提示 (Show error to user)
          }
        } else {
          console.warn('共享流中没有可用的音频轨道 (No audio tracks available in sharedStream).');
          // TODO: 可以尝试提示用户检查屏幕共享设置，确保共享了音频
          // TODO: Can prompt user to check screen share settings to ensure audio is shared
        }
      } else {
        if (!sharedStream) console.log('sharedStream is not yet available.');
        if (readyState !== ReadyState.OPEN) console.log(`WebSocket is not open. State: ${connectionStatus}`);
      }
  
      return () => {
        if (mediaRecorderRef.current && mediaRecorderRef.current.state !== 'inactive') {
          mediaRecorderRef.current.stop();
          console.log('MediaRecorder stopped on component unmount or stream change.');
        }
      };
    }, [sharedStream, readyState, sendMessage, connectionStatus]); // 添加依赖
  
    // ... (组件的其余 return 部分)
    return (
      <div className="p-4 md:p-6 lg:p-8 min-h-screen bg-gray-100">
        <div className="max-w-4xl mx-auto bg-white shadow-xl rounded-lg p-6">
          {/* ... (h1, 面试信息, WebSocket状态等保持不变) ... */}
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-800 mb-6">
            实时面试辅助 (Live Interview Assistant)
          </h1>
          {/* ... (显示面试配置信息) ... */}
           <div className="mb-4">
            <p>岗位 (Position): <span className="font-semibold">{interviewConfig.selectedPositionId || '未指定 (Not specified)'}</span></p>
            {/* ... 其他配置信息 ... */}
          </div>
          <div className="mb-4">
            <span>WebSocket连接状态 (Connection Status): </span>
            <span className={`font-semibold ${readyState === ReadyState.OPEN ? 'text-green-500' : 'text-orange-500'}`}>
              {connectionStatus}
            </span>
          </div>
  
          {/* 音频状态指示 */}
          <div className="mb-4">
             <p>音频流状态: {sharedStream && sharedStream.getAudioTracks().length > 0 ? 
                 (mediaRecorderRef.current && mediaRecorderRef.current.state === 'recording' ? 
                     <span className="text-green-500 font-semibold">正在录制并发送音频</span> : 
                     <span className="text-yellow-500 font-semibold">音频轨道可用，等待录制器启动</span>) : 
                 <span className="text-red-500 font-semibold">无可用音频轨道或未共享音频</span>}
             </p>
          </div>
  
          {/* ... (消息历史和发送消息的UI保持不变) ... */}
           <div className="mb-4">
            <h3 className="text-lg font-semibold mb-2">消息历史 (Message History):</h3>
            <ul className="h-64 overflow-y-auto border border-gray-300 rounded p-2 bg-gray-50">
              {messageHistory.map((message, idx) => (
                <li key={idx} className="mb-1 p-1 border-b">
                  {message ? (typeof message.data === 'string' ? message.data : `Received binary data, size: ${(message.data as Blob).size}`) : null}
                </li>
              ))}
            </ul>
          </div>
          {/* ... (结束面试按钮) ... */}
           <div className="mt-6">
             <button
                 onClick={() => navigate(-1)}
                 className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
             >
                 结束面试并返回 (End Interview & Go Back)
             </button>
          </div>
        </div>
      </div>
    );
  };
  export default LiveInterviewPage;
  ```

  - 关键点 (Key Points)

    :

    - 使用 `useRef` 来持有 `MediaRecorder` 实例和 `audioChunks` 数组，这样它们在重渲染之间保持不变且不会触发不必要的重渲染。
    - 从 `useInterviewStore` 获取 `sharedStream`。
    - 在 `useEffect` 中，当 `sharedStream` 可用并且 WebSocket 连接打开时，初始化 `MediaRecorder`。
    - **只录制音频轨道**: `new MediaStream(audioTracks)` 确保只处理音频，即使 `sharedStream` 同时包含视频。
    - **MIME 类型**: 尝试使用 `audio/webm; codecs=opus`，如果不支持则回退。这是一个常见的、压缩效果好的格式。
    - `ondataavailable`: 当 `MediaRecorder` 收集到数据时触发。我们将数据块 (`event.data`) 通过 `sendMessage` 发送到 WebSocket。为了低延迟，我们每次收集到数据就发送，而不是等录制停止。
    - `mediaRecorderRef.current.start(1000)`: `1000` 表示每1000毫秒 (1秒) 触发一次 `ondataavailable` 事件，生成一个音频片段。您可以调整这个值来平衡延迟和发送频率。
    - **清理**: 在 `useEffect` 的返回函数中，如果 `MediaRecorder` 正在录制，则停止它。
    - **UI反馈**: 添加了一个简单的文本来指示音频流的状态。

### 2. 后端工作 (Backend Work)

**2.1 WebSocket服务端接收音频数据 (WebSocket Server Receives Audio Data)**

- 中文：修改 `backend/websocket/interviewWs.ts` 中的 `ws.on('message', ...)` 部分，以识别并初步处理接收到的二进制音频数据。

- English: Modify the `ws.on('message', ...)` part in `backend/websocket/interviewWs.ts` to identify and initially process received binary audio data.

- **操作 (Action)**:

  - 打开 `backend/websocket/interviewWs.ts`。
  - (Open `backend/websocket/interviewWs.ts`.)
  - 修改 `ws.on('message', ...)` 回调。
  - (Modify the `ws.on('message', ...)` callback.)

  TypeScript

  ```
  // backend/websocket/interviewWs.ts
  // ... (其他代码)
  
  ws.on('message', (message) => {
    const currentSessionId = clientSessions.get(ws);
    if (!currentSessionId) {
      console.error('Received message from a WebSocket without a session mapping.');
      return;
    }
  
    if (message instanceof Buffer || message instanceof ArrayBuffer || typeof message === 'string') {
      // 判断消息类型 (Determine message type)
      if (typeof message === 'string') {
        // 处理文本消息 (Handle text messages - e.g., echo test)
        console.log(`Received TEXT message for session ${currentSessionId} from user ${ws.userId}: ${message}`);
        ws.send(`Echo from server (session ${currentSessionId}): ${message}`);
      } else {
        // 处理二进制消息 (音频数据) (Handle binary messages (audio data))
        const dataBuffer = message instanceof Buffer ? message : Buffer.from(message as ArrayBuffer);
        console.log(`Received AUDIO CHUNK for session ${currentSessionId} from user ${ws.userId}, Size: ${dataBuffer.length} bytes`);
  
        // (初步) 简单记录收到的数据类型和大小，用于调试
        // (Initial) Simply log received data type and size for debugging
        // ws.send(`Server received audio chunk of size: ${dataBuffer.length}`); // 可以发回确认，但会增加流量
  
        // TODO: 任务 3.3 - 将 dataBuffer 发送给百度ASR API
        // TODO: Task 3.3 - Send dataBuffer to Baidu ASR API
      }
    } else {
      console.warn(`Received unexpected message type for session ${currentSessionId}:`, typeof message);
    }
  });
  
  // ... (其他代码)
  ```

  - 关键点 (Key Points)

    :

    - 通过 `message instanceof Buffer || message instanceof ArrayBuffer` 来判断是否为二进制数据 (通常是音频数据)。
    - 简单打印日志记录收到的数据大小。
    - 为后续任务 3.3 中集成 ASR 留下了 TODO。

### 3. 测试 (Testing)

- **本地测试 (Local Testing)**:

  1. 启动后端和前端开发服务器。

  2. 登录，并导航到面试设置页面，完成所有设置（包括屏幕共享并**确保共享了音频**）。

  3. 点击“开始面试”进入 `LiveInterviewPage`。

  4. 观察浏览器控制台

     :

     - 应看到 "MediaRecorder configured and started."
     - 每隔约1秒，应看到 "Sending audio chunk, size: ..." 日志。

  5. 观察后端控制台 (运行 `npm run dev` 的终端)

     :

     - 应看到 "Received AUDIO CHUNK for session ..., Size: ..." 日志，这表明后端成功接收到音频数据。

  6. (可选) 如果您在后端 `ws.on('message')` 中添加了发回确认的逻辑，前端的消息历史中应该能看到这些确认。

- **Vercel 部署后测试 (Testing after Vercel Deployment)**:

  - 提交代码，等待部署。
  - 在线重复上述测试。

------

## 任务 3.3: 后端ASR集成与文本返回 (2-3天)

(Task 3.3: Backend ASR Integration and Text Return (2-3 days))

核心目标 (Core Goal): 后端接收音频数据，调用百度ASR API进行语音转文字，并将识别结果通过WebSocket返回给前端。

(Backend receives audio data, calls Baidu ASR API for speech-to-text, and returns the recognition result to the frontend via WebSocket.)

**重要前提**: 您需要拥有一个百度智能云账户，并开通**短语音识别**或**实时语音识别**服务，获取到 API Key 和 Secret Key。

(You need a Baidu AI Cloud account, enable **Short Speech Recognition** or **Real-time Speech Recognition** service, and obtain an API Key and Secret Key.)

### 1. 后端工作 (Node.js/Vercel Function/Edge Function) (Backend Work)

**1.1 安装百度 ASR Node.js SDK (Install Baidu ASR Node.js SDK)**

- 中文：百度智能云通常提供 Node.js SDK 来方便调用其服务。

- English: Baidu AI Cloud usually provides a Node.js SDK for easy service calls.

- 操作 (Action)

  :

  - 查阅百度智能云语音技术文档，找到并安装推荐的 Node.js SDK。通常是 `baidu-aip-sdk`。
  - (Consult Baidu AI Cloud speech technology documentation, find and install the recommended Node.js SDK. It's usually `baidu-aip-sdk`.)
  - 在 `backend` 目录下运行 (Run in the `backend` directory): `npm install baidu-aip-sdk`

**1.2 配置百度 ASR 凭证 (Configure Baidu ASR Credentials)**

- 中文：将您的百度 ASR API Key 和 Secret Key 添加到环境变量。

- English: Add your Baidu ASR API Key and Secret Key to environment variables.

- 操作 (Action)

  :

  - 本地 (`backend/.env`)

    :

    ```
    BAIDU_ASR_APP_ID="你的AppID"
    BAIDU_ASR_API_KEY="你的APIKey"
    BAIDU_ASR_SECRET_KEY="你的SecretKey"
    ```

  - **Vercel 环境变量 (Vercel Environment Variables)**: 在 Vercel 项目设置中添加相同的环境变量。

**1.3 实现 ASR 服务调用逻辑 (Implement ASR Service Call Logic)**

- 中文：在 `backend/websocket/interviewWs.ts` 中，当接收到音频数据块时，调用百度 ASR 服务。

- English: In `backend/websocket/interviewWs.ts`, call Baidu ASR service when audio data chunks are received.

- **操作 (Action)**:

  - 修改 `backend/websocket/interviewWs.ts`。
  - (Modify `backend/websocket/interviewWs.ts`.)

  TypeScript

  ```
  // backend/websocket/interviewWs.ts
  // ... (之前的导入)
  import AipSpeechClient from 'baidu-aip-sdk'.speech; // 导入百度 ASR 客户端
  // import { Buffer } from 'buffer'; // 如果在Edge Function中可能需要显式导入
  
  // ... (prisma, JWT_SECRET, client Maps ...)
  
  // 初始化百度 ASR 客户端 (Initialize Baidu ASR Client)
  const BAIDU_APP_ID = process.env.BAIDU_ASR_APP_ID;
  const BAIDU_API_KEY = process.env.BAIDU_ASR_API_KEY;
  const BAIDU_SECRET_KEY = process.env.BAIDU_ASR_SECRET_KEY;
  
  let asrClient: AipSpeechClient | null = null;
  if (BAIDU_APP_ID && BAIDU_API_KEY && BAIDU_SECRET_KEY) {
    asrClient = new AipSpeechClient(BAIDU_APP_ID, BAIDU_API_KEY, BAIDU_SECRET_KEY);
    console.log('Baidu ASR Client initialized.');
  } else {
    console.error('Baidu ASR credentials not found in environment variables. ASR will not work.');
  }
  
  // 数据库相关：创建 interview_transcripts 表
  // Database related: Create interview_transcripts table
  // (确保您的 Prisma schema 中有 InterviewTranscript 模型，并已迁移)
  // (Ensure you have InterviewTranscript model in your Prisma schema and have migrated)
  /*
  model InterviewTranscript {
    id        String   @id @default(uuid())
    sessionId String
    session   InterviewSession @relation(fields: [sessionId], references: [id])
    speaker   String // 'interviewer' or 'user'
    content   String
    timestamp DateTime @default(now())
    @@map("interview_transcripts")
  }
  */
  
  
  // ... (setupWebSocket 函数)
  export function setupWebSocket(httpServer: Server) {
    // ... (wss = new WebSocketServer(...) ...)
    wss.on('connection', async (ws: AuthenticatedWebSocket, req, sessionIdFromPath?: string, token?: string | null) => {
      // ... (token 验证和会话创建逻辑) ...
  
      ws.on('message', async (message) => { // 将回调设为 async
        const currentSessionId = clientSessions.get(ws);
        if (!currentSessionId || !ws.userId) { /* ... */ return; }
  
        if (message instanceof Buffer || message instanceof ArrayBuffer) {
          const audioBuffer = message instanceof Buffer ? message : Buffer.from(message as ArrayBuffer);
          console.log(`Received AUDIO CHUNK for session ${currentSessionId}, Size: ${audioBuffer.length} bytes`);
  
          if (!asrClient) {
            console.error('ASR client not initialized. Cannot process audio.');
            ws.send(JSON.stringify({ type: 'ASR_ERROR', payload: { message: 'ASR service not available.' }}));
            return;
          }
  
          try {
            // 调用百度短语音识别 (Call Baidu Short Speech Recognition)
            // 文档: https://cloud.baidu.com/doc/SPEECH/s/jk38lx8r3
            // 参数: format (pcm, wav, amr, opus), rate (16000, 8000), dev_pid (语言模型)
            // 我们前端发送的是 webm/opus 或 ogg/opus，百度ASR可能需要转换或直接支持opus
            // 如果是 opus，可以直接发送。如果是 pcm，确保采样率和位数正确。
            // 前端 MediaRecorder 默认可能是 stereo, 百度 ASR 通常需要 mono (channel: 1)
            // **重要: 百度ASR短语音接口通常需要完整的音频文件，而不是流式片段。
            // **对于实时流，应该使用“实时语音识别”服务。这里为了简化MVP，我们假设每个片段是短语音。
            // **This is a simplification. For real-time, Baidu's Real-time ASR API is needed.
  
            // 假设前端发送的是 Opus 编码的 WebM 或 Ogg (MediaRecorder 常用)
            // Let's assume frontend sends Opus encoded WebM or Ogg (common from MediaRecorder)
            // The ASR SDK might expect a specific format (e.g., raw PCM or WAV).
            // For Opus, 'dev_pid' for Mandarin is often 1537. Check Baidu docs.
            // The SDK's `recognize` method expects a Buffer.
  
            // 百度Node SDK的 `recognize` 方法第一个参数是 Buffer, 第二个是 format, 第三个是采样率, 第四个是可选参数对象
            // Baidu Node SDK's `recognize` method: first param is Buffer, second is format, third is sample rate, fourth is options object.
            // 如果前端发送的是 WebM/Opus，你需要确认百度 Node SDK 是否直接支持这种格式，
            // 或者你是否需要在后端将其转换为 PCM 或 WAV。
            // Opus可以直接传递，format设为'opus'，rate设为16000（如果前端是这个采样率）
  
            // 模拟: 假设我们收到了一个opus片段
            const asrFormat = 'opus'; // 假设前端发送的是 opus
            const sampleRate = 16000; // 假设前端采样率
  
            console.log(`Sending to Baidu ASR: format=<span class="math-inline">\{asrFormat\}, rate\=</span>{sampleRate}, buffer length=${audioBuffer.length}`);
            const asrResult = await asrClient.recognize(audioBuffer, asrFormat, sampleRate, {
              dev_pid: 1537, // 普通话 (Mandarin)
              // cuid: ws.userId, // 可选，用于用户相关的识别优化 (Optional, for user-related recognition optimization)
            });
  
            console.log('ASR Result:', asrResult);
  
            if (asrResult && asrResult.err_no === 0 && asrResult.result && asrResult.result.length > 0) {
              const recognizedText = asrResult.result[0];
              console.log(`Recognized Text for session ${currentSessionId}: ${recognizedText}`);
  
              // 将识别结果通过WebSocket发送给前端
              // Send recognition result to frontend via WebSocket
              ws.send(JSON.stringify({ type: 'ASR_RESULT', payload: { text: recognizedText, timestamp: new Date().toISOString() } }));
  
              // 将识别的文本存入 interview_transcripts 表
              // Store recognized text into interview_transcripts table
              await prisma.interviewTranscript.create({
                data: {
                  sessionId: currentSessionId,
                  speaker: 'interviewer', // 假设是面试官的声音 (Assuming it's interviewer's voice)
                  content: recognizedText,
                  // timestamp 会自动生成 (timestamp will be auto-generated)
                },
              });
              console.log(`Transcript saved to DB for session ${currentSessionId}`);
  
            } else if (asrResult && asrResult.err_no !== 0) {
              console.error(`Baidu ASR Error: err_no=<span class="math-inline">\{asrResult\.err\_no\}, err\_msg\=</span>{asrResult.err_msg}`);
              ws.send(JSON.stringify({ type: 'ASR_ERROR', payload: { message: `ASR Error: ${asrResult.err_msg} (Code: ${asrResult.err_no})` }}));
            } else {
              console.warn('ASR did not return a valid result or text was empty.');
              // 可以选择不发送空结果，或者发送一个特定的空结果消息
              // ws.send(JSON.stringify({ type: 'ASR_RESULT', payload: { text: '', timestamp: new Date().toISOString() } }));
            }
          } catch (error) {
            console.error(`Error calling Baidu ASR for session ${currentSessionId}:`, error);
            ws.send(JSON.stringify({ type: 'ASR_ERROR', payload: { message: 'ASR service request failed.' }}));
          }
        } else if (typeof message === 'string') {
          // ... (处理文本消息)
        } else {
          // ... (处理未知消息类型)
        }
      });
      // ... (ws.on('close') 和 ws.on('error'))
    });
    // ... (wss.on('error'))
  }
  ```

  - 关键点 (Key Points)

    :

    - 导入并初始化 `AipSpeechClient`。
    - 在收到音频数据块后，调用 `asrClient.recognize()`。
    - **格式和采样率 (Format and Sample Rate)**: `recognize` 方法需要音频格式 (如 `opus`, `pcm`, `wav`) 和采样率 (如 `16000`)。你需要确保前端 `MediaRecorder` 输出的格式和采样率与这里设置的一致，或者在后端进行转换。Opus 通常是推荐的，因为它压缩率高。
    - **语言模型 (dev_pid)**: `1537` 是普通话的开发平台语音识别模型。如果需要其他语言或场景，查阅百度文档。
    - **错误处理**: 检查 `asrResult.err_no`。`0` 表示成功。
    - **结果发送**: 将识别的文本 (`asrResult.result[0]`) 通过 WebSocket 发送回前端，使用 JSON 格式并定义 `type: 'ASR_RESULT'`。
    - **数据库存储**: 将识别的文本和说话人（这里假定为 'interviewer'）存入 `interview_transcripts` 表。

### 2. 前端工作 (React) (Frontend Work (React))

**2.1 接收并显示 ASR 文本 (Receive and Display ASR Text)**

- 中文：修改 `frontend/src/pages/LiveInterviewPage.tsx` 来处理从 WebSocket 收到的 ASR 结果。

- English: Modify `frontend/src/pages/LiveInterviewPage.tsx` to handle ASR results received from WebSocket.

- **操作 (Action)**:

  - 打开 `frontend/src/pages/LiveInterviewPage.tsx`。
  - (Open `frontend/src/pages/LiveInterviewPage.tsx`.)
  - 修改 `useEffect` 中处理 `lastMessage` 的逻辑，并添加状态来存储转录文本。
  - (Modify the logic in `useEffect` that handles `lastMessage`, and add state to store transcribed text.)

  TypeScript

  ```
  // frontend/src/pages/LiveInterviewPage.tsx
  // ... (其他导入)
  
  interface TranscriptMessage {
    text: string;
    timestamp: string;
    speaker?: 'interviewer' | 'user' | 'system'; // 可以扩展说话人
  }
  
  const LiveInterviewPage: React.FC = () => {
    // ... (已有的 state: socketUrl, messageHistory, inputMessage)
    const [transcripts, setTranscripts] = useState<TranscriptMessage[]>([]); // 新增状态存储转录文本
    const transcriptEndRef = useRef<null | HTMLDivElement>(null); // 用于自动滚动
  
    // ... (useEffect for socketUrl, 其他代码)
  
    useEffect(() => {
      if (lastMessage !== null) {
        setMessageHistory((prev) => prev.concat(lastMessage)); // 保留原始消息历史用于调试
  
        try {
          const data = JSON.parse(lastMessage.data as string);
          if (data.type === 'ASR_RESULT' && data.payload && typeof data.payload.text === 'string') {
            setTranscripts((prevTranscripts) => [
              ...prevTranscripts,
              { text: data.payload.text, timestamp: data.payload.timestamp || new Date().toISOString(), speaker: 'interviewer' }
            ]);
          } else if (data.type === 'ASR_ERROR' && data.payload) {
             console.error('ASR Error from server:', data.payload.message);
             // 可以在UI上显示这个错误 (Can display this error in UI)
             setTranscripts((prevTranscripts) => [
              ...prevTranscripts,
              { text: `ASR Error: ${data.payload.message}`, timestamp: new Date().toISOString(), speaker: 'system' }
            ]);
          }
          // TODO: 任务4.1 - 处理 AI_SUGGESTION 类型的消息
          // TODO: Task 4.1 - Handle AI_SUGGESTION type messages
        } catch (e) {
          // 如果不是JSON或者没有type字段，可能是echo消息或其他
          console.log('Received non-JSON or non-typed message:', lastMessage.data);
        }
      }
    }, [lastMessage]);
  
    // 自动滚动到最新的转录文本 (Auto-scroll to the latest transcript)
    useEffect(() => {
      transcriptEndRef.current?.scrollIntoView({ behavior: "smooth" });
    }, [transcripts]);
  
  
    // ... (connectionStatus, handleSendMessage, 和其他 useEffects)
  
    return (
      <div className="p-4 md:p-6 lg:p-8 min-h-screen bg-gray-100">
        <div className="max-w-4xl mx-auto bg-white shadow-xl rounded-lg p-6">
          {/* ... (标题和面试配置信息) ... */}
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-800 mb-6">
            实时面试辅助
          </h1>
           {/* ... (显示面试配置信息) ... */}
  
          {/* WebSocket 连接状态 */}
          {/* ... */}
  
          {/* 音频状态指示 (保持或改进) */}
          {/* ... */}
  
          {/* 面试官问题显示区域 (修改) */}
          <div className="mb-6">
            <h3 className="text-xl font-semibold mb-3 text-gray-700">面试官问题 (Interviewer's Questions):</h3>
            <div className="h-60 md:h-80 overflow-y-auto border border-gray-300 rounded-lg p-3 bg-gray-50 space-y-2">
              {transcripts.map((item, idx) => (
                <div key={idx} className={`flex ${item.speaker === 'interviewer' ? 'justify-start' : 'justify-end'}`}>
                  <div 
                    className={`max-w-[80%] p-2 rounded-lg text-sm ${
                      item.speaker === 'interviewer' ? 'bg-blue-100 text-blue-800' : 
                      item.speaker === 'system' ? 'bg-red-100 text-red-700' : 
                      'bg-green-100 text-green-800' // 假设用户是绿色 (Assuming user is green)
                    }`}
                  >
                    <p>{item.text}</p>
                    <p className="text-xs text-gray-500 mt-1 text-right">{new Date(item.timestamp).toLocaleTimeString()}</p>
                  </div>
                </div>
              ))}
              <div ref={transcriptEndRef} /> {/* 用于自动滚动的空div (Empty div for auto-scroll) */}
            </div>
          </div>
  
          {/* AI 回答建议区域 (占位符) */}
          <div className="mb-6">
             <h3 className="text-xl font-semibold mb-3 text-gray-700">AI 回答建议 (AI Suggestions):</h3>
             <div className="h-40 border border-dashed border-gray-300 rounded-lg p-3 bg-gray-50 flex items-center justify-center">
                 <p className="text-gray-400">AI建议将显示在此处... (AI suggestions will appear here...)</p>
             </div>
          </div>
  
  
          {/* 测试消息发送UI (可以保留或移除) */}
          {/* ... */}
  
          {/* 结束面试按钮 */}
          {/* ... */}
        </div>
      </div>
    );
  };
  export default LiveInterviewPage;
  ```

  - 关键点 (Key Points)

    :

    - 新增 `transcripts` state (类型为 `TranscriptMessage[]`) 来存储和显示ASR识别的文本。
    - 在处理 `lastMessage` 的 `useEffect` 中，解析收到的JSON。如果 `type` 是 `ASR_RESULT`，则将 `payload.text` 追加到 `transcripts` 数组。
    - **UI更新**: 创建一个新的区域专门显示面试官问题（转录文本）。
    - **自动滚动**: 使用 `useRef` 创建 `transcriptEndRef`，并将其附加到一个空的 `div` 上，放在转录列表的末尾。每当 `transcripts` 更新时，调用 `scrollIntoView()` 来自动滚动。
    - 错误显示: 如果收到 `ASR_ERROR` 类型的消息，也在转录区域显示错误。

### 3. 测试 (Testing)

- **本地测试 (Local Testing)**:

  1. 确保百度 ASR 凭证在 `backend/.env` 中正确配置。

  2. 启动后端和前端开发服务器。

  3. 登录，完成面试设置（共享屏幕并**确保共享了音频**，例如播放一段包含语音的视频）。

  4. 进入实时面试页面。

  5. 对着您共享的屏幕内容（或麦克风，取决于您的音频捕获设置）说话。

  6. 观察前端

     :

     - “面试官问题”区域应该会实时显示您说话内容的转录文本。
     - 文本应该会自动向上滚动。

  7. 观察后端控制台

     :

     - 应看到音频数据块的接收日志。
     - 应看到调用百度 ASR 的日志和 ASR 返回的结果。
     - 应看到转录文本存入数据库的日志。

  8. **检查数据库**: `interview_transcripts` 表中应有新的记录。

- **Vercel 部署后测试 (Testing after Vercel Deployment)**:

  - 确保百度 ASR 凭证已添加到 Vercel 项目的环境变量中。
  - 提交代码，等待部署。
  - 在线重复上述测试。

------

完成以上步骤后，您的应用将具备基本的实时语音转文字功能。下一步将是集成LLM来生成回答建议 (任务4.1)。

(After completing these steps, your application will have basic real-time speech-to-text functionality. The next step will be to integrate LLM to generate response suggestions (Task 4.1).)

如果在任何步骤中遇到问题，请提供详细的错误信息和相关代码片段，我会尽力协助！

(If you encounter any problems during any step, please provide detailed error messages and relevant code snippets, and I will do my best to assist you!)好的，我们来详细规划并执行第三周的任务：“实时面试页面 - WebSocket与ASR初步集成”。

(Okay, let's plan and execute the tasks for the third week: "Real-time Interview Page - Preliminary Integration of WebSocket and ASR.")

项目根目录 (Project Root Directory): local-mianshijun/

前端代码目录 (Frontend Code Directory): local-mianshijun/frontend/

后端代码目录 (Backend Code Directory): local-mianshijun/backend/

我将为您提供非常细致的操作清单，您可以直接在 Cursor 编辑器中操作。

(I will provide you with a very detailed checklist that you can follow directly in your Cursor editor.)

------

## 任务 3.1: 实时面试辅助页面骨架与WebSocket连接 (Task 3.1: Real-time Interview Assistance Page Skeleton & WebSocket Connection)

核心目标 (Core Goal): 创建实时面试辅助页面，并建立与后端的WebSocket连接。

(Create the real-time interview assistance page and establish a WebSocket connection with the backend.)

### 1. 前端工作 (React) (Frontend Work (React))

**1.1. 创建 `LiveInterviewPage.tsx` 页面 (Create `LiveInterviewPage.tsx` Page)**

- **中文描述:** 在 `frontend/src/pages/` 目录下创建一个新文件 `LiveInterviewPage.tsx`，用于实时面试辅助。

- **English Description:** Create a new file `LiveInterviewPage.tsx` in the `frontend/src/pages/` directory for real-time interview assistance.

- 操作 (Action):

  1. 在 Cursor 的文件浏览器中，右键点击 `frontend/src/pages/` 文件夹。 (In Cursor's file explorer, right-click on the `frontend/src/pages/` folder.)

  2. 选择 “新建文件” (New File)。

  3. 输入文件名 `LiveInterviewPage.tsx`。

  4. 粘贴以下代码到 

     ```
     LiveInterviewPage.tsx
     ```

     : (Paste the following code into 

     ```
     LiveInterviewPage.tsx
     ```

     :)

     TypeScript

     ```
     // frontend/src/pages/LiveInterviewPage.tsx
     import React, { useState, useEffect, useCallback } from 'react';
     import { useLocation, useNavigate } from 'react-router-dom';
     import useWebSocket, { ReadyState } from 'react-use-websocket';
     import useAuthStore from '../stores/authStore';
     import useInterviewStore from '../stores/interviewStore';
     
     const LiveInterviewPage: React.FC = () => {
       const navigate = useNavigate();
       const location = useLocation();
       const { isAuthenticated, token: authToken } = useAuthStore();
       const { config: interviewConfig } = useInterviewStore();
     
       const passedState = location.state as {
         positionId?: string;
         language?: 'chinese' | 'english';
         answerStyle?: 'keywords_conversational' | 'conversational';
       } | undefined;
     
       const [socketUrl, setSocketUrl] = useState<string | null>(null);
       const [messageHistory, setMessageHistory] = useState<MessageEvent[]>([]);
       const [inputMessage, setInputMessage] = useState<string>('');
     
       useEffect(() => {
         if (!isAuthenticated) {
           navigate('/login');
           return;
         }
     
         const positionToUse = passedState?.positionId || interviewConfig.selectedPositionId;
     
         if (positionToUse && authToken) {
           const sessionId = `session_${positionToUse}_${Date.now()}`;
           const wsScheme = window.location.protocol === 'https:' ? 'wss' : 'ws';
           const backendHost = process.env.NODE_ENV === 'production' 
                               ? window.location.host 
                               : 'localhost:3000'; // Local dev backend port
     
           // The path should match how the backend WebSocket server is set up.
           // If using Express with 'ws' library and http server upgrade,
           // the path might be just '/' or a specific path handled by `server.on('upgrade')`.
           // For Vercel, if you have a file at `backend/api/ws/interview.ts`, it might be `/api/ws/interview`
           // Let's assume a path that will be handled by the 'upgrade' event on the HTTP server for Express.
           // We'll include sessionId in the path as per your backend setup in interviewWs.ts
           setSocketUrl(`<span class="math-inline">\{wsScheme\}\://</span>{backendHost}/api/ws/interview/<span class="math-inline">\{sessionId\}?token\=</span>{encodeURIComponent(authToken)}`);
           console.log(`WebSocket URL set to: <span class="math-inline">\{wsScheme\}\://</span>{backendHost}/api/ws/interview/${sessionId}?token=...`);
         } else {
           if (!authToken) console.warn("Auth token is missing.");
           if (!positionToUse) console.warn("Position ID is missing.");
           // Optionally navigate back or show error
           // navigate('/ai-interview'); 
         }
       }, [isAuthenticated, navigate, interviewConfig, passedState, authToken]);
     
       const { sendMessage, lastMessage, readyState } = useWebSocket(socketUrl, {
         onOpen: () => console.log('WebSocket连接已打开 (WebSocket connection opened)'),
         onClose: () => console.log('WebSocket连接已关闭 (WebSocket connection closed)'),
         shouldReconnect: (closeEvent) => true,
       });
     
       useEffect(() => {
         if (lastMessage !== null) {
           setMessageHistory((prev) => prev.concat(lastMessage));
         }
       }, [lastMessage]);
     
       const connectionStatus = {
         [ReadyState.CONNECTING]: '连接中 (Connecting)',
         [ReadyState.OPEN]: '已连接 (Open)',
         [ReadyState.CLOSING]: '关闭中 (Closing)',
         [ReadyState.CLOSED]: '已关闭 (Closed)',
         [ReadyState.UNINSTANTIATED]: '未实例化 (Uninstantiated)',
       }[readyState];
     
       const handleSendMessage = useCallback(() => {
         if (inputMessage.trim() !== '') {
           sendMessage(inputMessage);
           setInputMessage('');
         }
       }, [inputMessage, sendMessage]);
     
       useEffect(() => {
         console.log('LiveInterviewPage mounted. Passed state:', passedState);
         console.log('Interview config from Zustand:', interviewConfig);
       }, [passedState, interviewConfig]);
     
       if (!isAuthenticated) {
         return <p className="p-4 text-red-500">请先登录。 (Please log in first.)</p>;
       }
     
       if (!socketUrl && (passedState?.positionId || interviewConfig.selectedPositionId)) {
         return <p className="p-4">正在初始化WebSocket连接配置... (Initializing WebSocket connection config...)</p>;
       }
     
       if (!(passedState?.positionId || interviewConfig.selectedPositionId)) {
         return (
             <div className="p-4">
                 <p className="text-red-500">错误：面试配置信息丢失，无法启动实时面试。 (Error: Interview configuration missing.)</p>
                 <button 
                     onClick={() => navigate('/ai-interview', { replace: true })} 
                     className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
                 >
                     返回面试设置 (Back to Interview Setup)
                 </button>
             </div>
         );
       }
     
       return (
         <div className="p-4 md:p-6 lg:p-8 min-h-screen bg-gray-100">
           <div className="max-w-4xl mx-auto bg-white shadow-xl rounded-lg p-6">
             <h1 className="text-2xl sm:text-3xl font-bold text-gray-800 mb-6">
               实时面试辅助 (Live Interview Assistant)
             </h1>
             <div className="mb-4">
               <p>岗位 (Position): <span className="font-semibold">{passedState?.positionId || interviewConfig.selectedPositionId || '未指定'}</span></p>
               <p>语言 (Language): <span className="font-semibold">{(passedState?.language || interviewConfig.interviewLanguage) === 'chinese' ? '中文' : '英文'}</span></p>
               <p>答案风格 (Answer Style): <span className="font-semibold">{(passedState?.answerStyle || interviewConfig.answerStyle) === 'keywords_conversational' ? '关键词+口语化' : '口语化'}</span></p>
             </div>
     
             <div className="mb-4">
               <span>WebSocket连接状态 (Connection Status): </span>
               <span className={`font-semibold ${readyState === ReadyState.OPEN ? 'text-green-500' : 'text-orange-500'}`}>
                 {connectionStatus}
               </span>
             </div>
     
             <div className="mb-4">
               <h3 className="text-lg font-semibold mb-2">消息历史 (Message History):</h3>
               <ul className="h-64 overflow-y-auto border border-gray-300 rounded p-2 bg-gray-50">
                 {messageHistory.map((message, idx) => (
                   <li key={idx} className="mb-1 p-1 border-b break-all">
                     {message ? message.data : null}
                   </li>
                 ))}
               </ul>
             </div>
     
             <div className="flex gap-2">
               <input
                 type="text"
                 value={inputMessage}
                 onChange={(e) => setInputMessage(e.target.value)}
                 onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                 className="flex-grow p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                 placeholder="输入消息进行测试 (Type a message for testing)"
               />
               <button
                 onClick={handleSendMessage}
                 disabled={readyState !== ReadyState.OPEN}
                 className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:bg-gray-300"
               >
                 发送 (Send)
               </button>
             </div>
     
             <div className="mt-6">
                <button
                    onClick={() => navigate('/ai-interview')} // 返回到配置页面
                    className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
                >
                    结束面试并返回 (End Interview & Go Back)
                </button>
             </div>
           </div>
         </div>
       );
     };
     
     export default LiveInterviewPage;
     ```

**1.2. 从 `InterviewConfigForm.tsx` 跳转并携带信息 (Navigate from `InterviewConfigForm.tsx` with Info)**

- **中文描述:** 修改 `frontend/src/components/interview/InterviewConfigForm.tsx` 中的 `handleStartInterview` 函数，以便在满足所有条件后，使用 React Router 的 `Maps` 函数跳转到 `/interview/live`，并通过 `state` 传递面试岗位、语言和答案风格。

- **English Description:** Modify the `handleStartInterview` function in `frontend/src/components/interview/InterviewConfigForm.tsx` to navigate to `/interview/live` using React Router's `Maps` function when all conditions are met, passing the interview position, language, and answer style via `state`.

- 操作 (Action):

  1. 打开 `frontend/src/components/interview/InterviewConfigForm.tsx`。 (Open `frontend/src/components/interview/InterviewConfigForm.tsx`.)

  2. 确保已从 `react-router-dom` 导入 `useNavigate`，并从 `../stores/interviewStore` 导入 `useInterviewStore`。 (Ensure `useNavigate` is imported from `react-router-dom` and `useInterviewStore` from `../stores/interviewStore`.)

  3. 修改 

     ```
     handleStartInterview
     ```

      函数： (Modify the 

     ```
     handleStartInterview
     ```

      function:)

     TypeScript

     ```
     // frontend/src/components/interview/InterviewConfigForm.tsx
     // ... (其他导入和现有代码)
     import { useNavigate } from 'react-router-dom';
     import useInterviewStore from '../../stores/interviewStore'; // 确保导入
     
     const InterviewConfigForm: React.FC = () => {
       const navigate = useNavigate();
       const { config } = useInterviewStore(); // 获取最新的配置
       // ... (其他 states 和 useEffects)
     
       const allPrerequisitesMet = /* ... (您已有的条件检查) ... */
         isAuthenticated &&
         uploadedResume &&
         config.selectedPositionId &&
         micPermissionResult?.granted &&
         config.screenShareStatus === 'sharing' &&
         config.audioCollection;
     
       const handleStartInterview = async () => {
         // ... (您已有的校验逻辑，如 showError) ...
         if (!allPrerequisitesMet) {
             showError('请完成所有面试准备步骤。');
             // 可以更详细地指出哪个步骤未完成
             if (!uploadedResume) showError('请先上传简历。');
             else if (!config.selectedPositionId) showError('请选择面试岗位。');
             else if (!micPermissionResult?.granted) showError('请先授权麦克风。');
             else if (config.screenShareStatus !== 'sharing' || !config.audioCollection) showError('请先成功共享屏幕和系统音频。');
             return;
         }
     
         setIsStartingInterview(true);
         showSuccess('所有准备就绪，即将开始面试...');
         console.log('Starting interview with config:', config);
     
         // 跳转到实时面试页并携带信息
         // Navigate to live interview page with information
         navigate('/interview/live', {
           state: {
             positionId: config.selectedPositionId,
             language: config.interviewLanguage,
             answerStyle: config.answerStyle,
           }
         });
         // setIsStartingInterview(false); // 跳转后通常不需要，组件会卸载
       };
     
       return ( /* ... JSX ... */ );
     };
     
     export default InterviewConfigForm;
     ```

**1.3. 安装 `react-use-websocket` (Install `react-use-websocket`)**

- **中文描述:** 这是一个简化 WebSocket 操作的 React Hook。
- **English Description:** This is a React Hook to simplify WebSocket operations.
- 操作 (Action):
  1. 打开终端 (Open terminal)。
  2. 导航到 `frontend` 目录: `cd local-mianshijun/frontend`。 (Navigate to `frontend` directory: `cd local-mianshijun/frontend`.)
  3. 运行: `npm install react-use-websocket`。

**1.4. 添加路由到 `App.tsx` (Add Route to `App.tsx`)**

- **中文描述:** 为 `LiveInterviewPage` 添加路由配置。

- **English Description:** Add route configuration for `LiveInterviewPage`.

- 操作 (Action):

  1. 打开 `frontend/src/App.tsx`。

  2. 导入 `LiveInterviewPage`。

  3. 在 

     ```
     <Routes>
     ```

      中添加新的 

     ```
     <Route>
     ```

     ，通常嵌套在主 

     ```
     <Layout />
     ```

      内，并设为受保护路由。 (Import 

     ```
     LiveInterviewPage
     ```

     . Add a new 

     ```
     <Route>
     ```

      within 

     ```
     <Routes>
     ```

     , typically nested inside the main 

     ```
     <Layout />
     ```

      and as a protected route.)

     TypeScript

     ```
     // frontend/src/App.tsx
     // ... (其他导入)
     import LiveInterviewPage from './pages/LiveInterviewPage'; // 新增导入
     
     // ...
     const ProtectedRoute: React.FC<{ children: JSX.Element }> = ({ children }) => { /* ... */ };
     
     function App() {
       // ...
       return (
         // <Router> 包裹在 main.tsx 中 (Router is wrapped in main.tsx)
           <Routes>
             {/* ... (已有路由) ... */}
             <Route path="/" element={<Layout />}> {/* 假设 Layout 是您的主布局 */}
               {/* ... (其他布局内路由) ... */}
               <Route
                 path="interview/live" // 您也可以使用 /interview/session/:sessionId 这样的动态路由
                 element={
                   <ProtectedRoute>
                     <LiveInterviewPage />
                   </ProtectedRoute>
                 }
               />
             </Route>
           </Routes>
         // </Router>
       );
     }
     export default App;
     ```

     参考 `frontend/src/App.tsx`。

------

### 2. 后端工作 (Node.js/Vercel Edge Function) (Backend Work)

**2.1. 安装 WebSocket 库 (Install WebSocket Library)**

- **中文描述:** 为 Node.js 后端安装 `ws` 库。
- **English Description:** Install the `ws` library for the Node.js backend.
- 操作 (Action):
  1. 打开终端 (Open terminal)。
  2. 导航到 `backend` 目录: `cd local-mianshijun/backend`。
  3. 运行: `npm install ws`。
  4. 运行: `npm install --save-dev @types/ws` (用于 TypeScript 类型定义)。

**2.2. 创建 WebSocket 服务接入点及数据库记录逻辑 (Create WebSocket Endpoint & Database Record Logic)**

- **中文描述:** 修改 `backend/server.ts` 以集成 WebSocket，并在连接建立时创建 `interview_sessions` 记录。我们会把 WebSocket 核心逻辑放到一个新文件 `backend/websocket/interviewWs.ts` 中。

- **English Description:** Modify `backend/server.ts` to integrate WebSocket and create an `interview_sessions` record upon connection. We'll put the core WebSocket logic into a new file `backend/websocket/interviewWs.ts`.

- **操作 (Action):**

  1. **创建 `backend/websocket/interviewWs.ts`**:

     - 在 `local-mianshijun/backend/` 目录下创建新文件夹 `websocket`。 (Create a new folder `websocket` in `local-mianshijun/backend/`.)

     - 在 `backend/websocket/` 内创建文件 `interviewWs.ts`。 (Create file `interviewWs.ts` inside `backend/websocket/`.)

     - 粘贴以下代码到 

       ```
       backend/websocket/interviewWs.ts
       ```

       : (Paste the following code into 

       ```
       backend/websocket/interviewWs.ts
       ```

       :)

       TypeScript

       ```
       // backend/websocket/interviewWs.ts
       import { WebSocketServer, WebSocket } from 'ws';
       import { Server } from 'http';
       import { PrismaClient } from '@prisma/client';
       import url from 'url';
       import jwt from 'jsonwebtoken';
       
       const prisma = new PrismaClient();
       const JWT_SECRET = process.env.JWT_SECRET || 'your_default_secret_please_change_in_env';
       
       interface AuthenticatedWebSocket extends WebSocket {
         userId?: string;
         sessionIdFromPath?: string; // sessionId from path
       }
       
       // Store clients per session
       const sessionClients = new Map<string, Set<AuthenticatedWebSocket>>();
       
       export function setupWebSocket(httpServer: Server) {
         console.log('Initializing WebSocket server...');
         const wss = new WebSocketServer({ noServer: true });
       
         httpServer.on('upgrade', async (request, socket, head) => {
           const { pathname, query } = request.url ? url.parse(request.url, true) : { pathname: null, query: {} };
           const token = query.token as string | undefined;
       
           const interviewPathRegex = /^\/api\/ws\/interview\/([a-zA-Z0-9_.-]+)$/;
           const match = pathname ? interviewPathRegex.exec(pathname) : null;
       
           if (!match || !token) {
             console.log('WebSocket upgrade rejected: Path or token mismatch.', { pathname, tokenPresent: !!token });
             socket.destroy();
             return;
           }
       
           const sessionIdFromPath = match[1];
           let decodedUserId: string | undefined;
       
           try {
             const decoded = jwt.verify(token, JWT_SECRET) as { userId: string };
             decodedUserId = decoded.userId;
             if (!decodedUserId) throw new Error('User ID not in token');
           } catch (err) {
             console.error('WebSocket upgrade rejected: Invalid token.', err);
             socket.destroy();
             return;
           }
       
           // Proceed with WebSocket upgrade
           wss.handleUpgrade(request, socket, head, (ws) => {
             const castWs = ws as AuthenticatedWebSocket;
             castWs.userId = decodedUserId;
             castWs.sessionIdFromPath = sessionIdFromPath;
             wss.emit('connection', castWs, request); // Pass original request for context if needed
           });
         });
       
         wss.on('connection', async (ws: AuthenticatedWebSocket, request) => {
           const userId = ws.userId;
           const currentSessionId = ws.sessionIdFromPath;
       
           if (!userId || !currentSessionId) {
             console.error('Connection event: Missing userId or sessionIdFromPath on ws object.');
             ws.close(1008, "Authentication or session error.");
             return;
           }
       
           console.log(`WebSocket client (User: ${userId}) connected for session: ${currentSessionId}`);
       
           try {
             const existingSession = await prisma.interviewSession.findUnique({
               where: { id: currentSessionId },
             });
       
             if (!existingSession) {
               // titleJobInfo could be passed via query params on WebSocket URL or a first message
               const jobInfoFromQuery = request.url ? url.parse(request.url, true).query.jobInfo as string : undefined;
       
               await prisma.interviewSession.create({
                 data: {
                   id: currentSessionId,
                   userId: userId, 
                   status: 'active',
                   titleJobInfo: jobInfoFromQuery || "N/A (Set from client)",
                   startedAt: new Date(),
                 },
               });
               console.log(`New interview session record created in DB: ${currentSessionId}`);
             } else {
               console.log(`Client reconnected to existing interview session: ${currentSessionId}`);
             }
           } catch (dbError) {
             console.error(`Database error for session ${currentSessionId}:`, dbError);
             ws.close(1011, 'Server error during session handling.');
             return;
           }
       
           if (!sessionClients.has(currentSessionId)) {
             sessionClients.set(currentSessionId, new Set());
           }
           sessionClients.get(currentSessionId)!.add(ws);
       
           ws.send(`Welcome! Connected to session: ${currentSessionId}. User: ${userId}`);
       
           ws.on('message', (message) => {
             console.log(`Session ${currentSessionId} User ${userId} received: ${message}`);
             // Echo for testing
             ws.send(`Server Echo (Session ${currentSessionId}): ${message}`);
           });
       
           ws.on('close', async () => {
             console.log(`WebSocket client (User: ${userId}) disconnected from session: ${currentSessionId}`);
             const clientsInSession = sessionClients.get(currentSessionId);
             if (clientsInSession) {
               clientsInSession.delete(ws);
               if (clientsInSession.size === 0) {
                 sessionClients.delete(currentSessionId);
                 console.log(`Session ${currentSessionId} is now empty. Updating DB status.`);
                 try {
                   await prisma.interviewSession.update({
                     where: { id: currentSessionId },
                     data: { status: 'completed', endedAt: new Date() },
                   });
                   console.log(`Session ${currentSessionId} marked as completed in DB.`);
                 } catch (dbError) {
                   console.error(`Error updating session ${currentSessionId} to completed:`, dbError);
                 }
               }
             }
           });
       
           ws.on('error', (error) => {
             console.error(`WebSocket error for session ${currentSessionId} (User: ${userId}):`, error);
             // Cleanup on error as well
             const clientsInSession = sessionClients.get(currentSessionId);
             if (clientsInSession) clientsInSession.delete(ws);
           });
         });
       
         wss.on('error', (error) => {
           console.error('WebSocket Server Global Error:', error);
         });
       
         console.log('WebSocket server is ready to handle upgrade requests on the HTTP server.');
       }
       ```

  2. **修改 `backend/server.ts`**:

     - 导入 `createServer` from `http` 和 `setupWebSocket`。
     - (Import `createServer` from `http` and `setupWebSocket`.)
     - 用 `createServer(app)` 创建 HTTP 服务器实例。
     - (Create an HTTP server instance using `createServer(app)`.)
     - 调用 `setupWebSocket(server)`。
     - (Call `setupWebSocket(server)`.)
     - 使用 `server.listen` 代替 `app.listen`。
     - (Use `server.listen` instead of `app.listen`.) *参考 `backend/server.ts`。*

------

### 3. 数据库相关 (Database Related)

- **中文描述:** 确保 `interview_sessions` 表的 Prisma 模型已定义并迁移。

- **English Description:** Ensure the Prisma model for the `interview_sessions` table is defined and migrated.

- 操作 (Action):

  1. 检查/更新 `backend/prisma/schema.prisma`

     : (Check/Update 

     ```
     backend/prisma/schema.prisma
     ```

     :)

     确保 `User` 模型有到 `InterviewSession` 的反向关系，并且 `InterviewSession` 模型如开发计划中所述。

     (Ensure the 

     ```
     User
     ```

      model has a back-relation to 

     ```
     InterviewSession
     ```

     , and the 

     ```
     InterviewSession
     ```

      model is as described in the development plan.)

     代码段

     ```
     // backend/prisma/schema.prisma
     model User {
       id               String    @id @default(uuid())
       email            String    @unique
       // passwordHash     String // 如果您的 User 模型中有 passwordHash，请保留
       password         String    // 根据您的 login.ts，这里是 password 字段
       name             String?
       createdAt        DateTime  @default(now())
       updatedAt        DateTime  @updatedAt
       balance          UserBalance?
       resumes          Resume[]
       targetPositions  TargetPosition[]
       interviewSessions InterviewSession[] // <--- 确保这行存在 (Ensure this line exists)
     
       @@map("users")
     }
     
     model InterviewSession {
       id             String    @id @default(uuid()) // 会话ID (Session ID)
       userId         String                        // 用户ID (User ID)
       user           User      @relation(fields: [userId], references: [id], onDelete: Cascade)
       titleJobInfo   String?                       // 面试岗位信息 (Interview job info)
       status         String    @default("pending")   // e.g., pending, active, completed, error
       createdAt      DateTime  @default(now())
       startedAt      DateTime?
       endedAt        DateTime?
     
       // 后续任务会用到这些关系 (These relations will be used in later tasks)
       // transcripts    InterviewTranscript[]
       // aiSuggestions  AISuggestion[]
     
       @@map("interview_sessions")
     }
     ```

  2. 运行数据库迁移 (Run Database Migration)

     :

     - 在终端中，导航到 `backend` 目录。 (In terminal, navigate to `backend` directory.)
     - 运行 (Run): `npx prisma migrate dev --name add_interview_sessions`

  3. 重新生成 Prisma Client (Regenerate Prisma Client)

     :

     - 运行 (Run): `npx prisma generate`

------

### 4. 测试 (Testing)

- **本地测试 (Local Testing)**:

  1. 启动后端: `cd local-mianshijun/backend && npm run dev`。

  2. 启动前端: `cd local-mianshijun/frontend && npm run dev`。

  3. 确保已登录。 (Ensure you are logged in.)

  4. 导航到 `InterviewConfigForm.tsx` 所在的页面 (例如 `/ai-interview`)，完成配置，点击“开始面试”。 (Navigate to the page with `InterviewConfigForm.tsx` (e.g., `/ai-interview`), complete configuration, click "Start Interview".)

  5. 检查

     :

     - 浏览器开发者工具的网络(Network)标签页是否有 WebSocket 连接成功 (状态码 101)。 (Check browser developer tools Network tab for successful WebSocket connection (status code 101).)
     - `LiveInterviewPage` 是否显示“已连接”状态。 (Check if `LiveInterviewPage` shows "Connected" status.)
     - 后端控制台是否有 WebSocket 连接和数据库操作日志。 (Check backend console for logs of WebSocket connection and database operations.)
     - Neon 数据库 `interview_sessions` 表是否创建了新记录。 (Check if a new record is created in the `interview_sessions` table in your Neon database.)
     - 在 `LiveInterviewPage` 的输入框发送消息，看是否收到服务器的 echo。 (Send a message from `LiveInterviewPage` input to see server echo.)

- **Vercel 部署后测试 (Testing after Vercel Deployment)**:

  - 提交所有更改到 GitHub。 (Commit all changes to GitHub.)
  - 等待 Vercel 部署完成。 (Wait for Vercel deployment to complete.)
  - 在线重复上述测试步骤。 (Repeat the above test steps online.)

------

## 任务 3.2: 音频流处理与发送 (前端) (Task 3.2: Audio Stream Processing and Sending (Frontend))

核心目标 (Core Goal): 从屏幕共享的音频轨道（面试官声音）或麦克风获取音频流，进行分片并通过WebSocket发送到后端。

(Capture audio stream from screen-shared audio track (interviewer's voice) or microphone, segment it, and send to backend via WebSocket.)

### 1. 前端工作 (React) (Frontend Work (React))

**1.1. 在 `LiveInterviewPage.tsx` 中处理和发送音频流 (Handle and Send Audio Stream in `LiveInterviewPage.tsx`)**

- **中文描述:** 使用 `MediaRecorder` API 录制从 `sharedStream` (来自Zustand `interviewStore`) 中提取的音频轨道，将音频片段 (Blobs) 通过 WebSocket 发送。
- **English Description:** Use the `MediaRecorder` API to record the audio track extracted from `sharedStream` (from Zustand `interviewStore`), and send audio segments (Blobs) via WebSocket.
- 操作 (Action):
  1. 打开 `frontend/src/pages/LiveInterviewPage.tsx`。
  2. 添加 `useRef` 来存储 `MediaRecorder` 实例和音频块。 (Add `useRef` to store `MediaRecorder` instance and audio chunks.)
  3. 在 `useEffect` 中，当 `sharedStream` 可用且 WebSocket 连接打开时，设置并启动 `MediaRecorder`。 (In `useEffect`, when `sharedStream` is available and WebSocket is open, set up and start `MediaRecorder`.)
  4. 在 `ondataavailable` 事件中，将音频数据发送到 WebSocket。 (In the `ondataavailable` event, send audio data to WebSocket.)
  5. 添加必要的清理逻辑。 (Add necessary cleanup logic.) *参考 `frontend/src/pages/LiveInterviewPage.tsx` 中的相关代码块 (Task 3.2)。*

------

### 2. 后端工作 (Backend Work)

**2.1. WebSocket 服务端接收音频数据 (WebSocket Server Receives Audio Data)**

- **中文描述:** 修改 `backend/websocket/interviewWs.ts` 中的 `ws.on('message', ...)` 部分，以识别并初步处理接收到的二进制音频数据。
- **English Description:** Modify the `ws.on('message', ...)` part in `backend/websocket/interviewWs.ts` to identify and initially process received binary audio data.
- 操作 (Action):
  1. 打开 `backend/websocket/interviewWs.ts`。
  2. 修改 `ws.on('message', ...)` 回调以处理 `Buffer` 或 `ArrayBuffer` 类型的消息。 (Modify the `ws.on('message', ...)` callback to handle messages of type `Buffer` or `ArrayBuffer`.)
  3. (初步) 简单记录收到的数据类型和大小，用于调试。 ((Initial) Simply log the received data type and size for debugging.) *参考 `backend/websocket/interviewWs.ts` 中的相关代码块 (Task 3.2)。*

------

### 3. 测试 (Testing)

- 本地测试 (Local Testing)

  :

  1. 启动前后端服务器。 (Start backend and frontend servers.)

  2. 登录，进行面试设置（确保共享音频）。 (Log in, go through interview setup (ensure audio is shared).)

  3. 进入实时面试页面。 (Enter the live interview page.)

  4. 检查

     :

     - 浏览器控制台应显示 `MediaRecorder` 启动和发送音频块的日志。 (Browser console should show logs for `MediaRecorder` starting and sending audio chunks.)
     - 后端控制台应显示接收到音频块及其大小的日志。 (Backend console should show logs for receiving audio chunks and their sizes.)

- Vercel 部署后测试 (Testing after Vercel Deployment)

  :

  - 提交代码并部署。 (Commit code and deploy.)
  - 在线重复测试。 (Repeat tests online.)

------

## 任务 3.3: 后端ASR集成与文本返回 (Task 3.3: Backend ASR Integration and Text Return)

核心目标 (Core Goal): 后端接收音频数据，调用百度ASR API进行语音转文字，并将识别结果通过WebSocket返回给前端。

(Backend receives audio data, calls Baidu ASR API for speech-to-text, and returns the recognition result to the frontend via WebSocket.)

重要前提 (Important Prerequisite): 您需要拥有一个百度智能云账户，并开通短语音识别或实时语音识别服务，获取到 API Key 和 Secret Key。

(You need a Baidu AI Cloud account, enable Short Speech Recognition or Real-time Speech Recognition service, and obtain an API Key and Secret Key.)

### 1. 后端工作 (Node.js/Vercel Function/Edge Function) (Backend Work)

**1.1. 安装百度 ASR Node.js SDK (Install Baidu ASR Node.js SDK)**

- 操作 (Action):
  - 在 `backend` 目录运行 (Run in `backend` directory): `npm install baidu-aip-sdk`

**1.2. 配置百度 ASR 凭证 (Configure Baidu ASR Credentials)**

- 操作 (Action):

  - 将百度 ASR APP_ID, API_KEY, SECRET_KEY 添加到 

    ```
    backend/.env
    ```

     和 Vercel 项目的环境变量中。 (Add Baidu ASR APP_ID, API_KEY, SECRET_KEY to 

    ```
    backend/.env
    ```

     and Vercel project's environment variables.)

    ```
    BAIDU_ASR_APP_ID="你的AppID"
    BAIDU_ASR_API_KEY="你的APIKey"
    BAIDU_ASR_SECRET_KEY="你的SecretKey"
    ```

**1.3. 实现 ASR 服务调用逻辑 (Implement ASR Service Call Logic)**

- 操作 (Action):
  - 修改 `backend/websocket/interviewWs.ts`，在接收到音频数据块时，调用百度 ASR 服务，并将结果发送回客户端。同时，将识别的文本存入 `interview_transcripts` 表。 (Modify `backend/websocket/interviewWs.ts`. When audio chunks are received, call Baidu ASR service and send results back to the client. Also, store recognized text in the `interview_transcripts` table.)
  - *参考 `backend/websocket/interviewWs.ts` 中的相关代码块 (Task 3.3)。*

**1.4. 数据库 `interview_transcripts` 表 (Database `interview_transcripts` Table)**

- 操作 (Action):

  1. 更新 `backend/prisma/schema.prisma`

     : (Update 

     ```
     backend/prisma/schema.prisma
     ```

     :)

     代码段

     ```
     // backend/prisma/schema.prisma
     // ... (InterviewSession 模型) ...
     model InterviewTranscript {
       id        String   @id @default(uuid())
       sessionId String
       session   InterviewSession @relation(fields: [sessionId], references: [id], onDelete: Cascade)
       speaker   String // 'interviewer' or 'user' (or 'system' for errors)
       content   String
       timestamp DateTime @default(now())
     
       @@map("interview_transcripts")
     }
     
     model InterviewSession {
       // ... (其他字段)
       transcripts    InterviewTranscript[] // <--- 添加/确保这行存在 (Add/ensure this line exists)
     }
     ```

  2. 运行数据库迁移 (Run Database Migration)

     :

     - `cd local-mianshijun/backend`
     - `npx prisma migrate dev --name add_interview_transcripts`

  3. 重新生成 Prisma Client (Regenerate Prisma Client)

     :

     - `npx prisma generate`

------

### 2. 前端工作 (React) (Frontend Work (React))

**2.1. 接收并显示 ASR 文本 (Receive and Display ASR Text)**

- 操作 (Action):
  - 修改 `frontend/src/pages/LiveInterviewPage.tsx` 来处理从 WebSocket 收到的 `ASR_RESULT` 和 `ASR_ERROR` 类型的消息，并将转录文本显示在界面特定区域，实现追加显示和自动滚动。 (Modify `frontend/src/pages/LiveInterviewPage.tsx` to handle `ASR_RESULT` and `ASR_ERROR` messages from WebSocket, display transcribed text in a specific UI area, with append display and auto-scroll.)
  - *参考 `frontend/src/pages/LiveInterviewPage.tsx` 中的相关代码块 (Task 3.3)。*

------

### 3. 测试 (Testing)

- 本地与 Vercel 测试 (Local and Vercel Testing)

  :

  1. 确保百度 ASR 凭证正确配置。 (Ensure Baidu ASR credentials are correctly configured.)

  2. 启动服务，登录，完成面试设置并共享音频。 (Start services, log in, complete interview setup and share audio.)

  3. 对着共享音频源说话。 (Speak towards the shared audio source.)

  4. 检查

     :

     - 前端“面试官问题”区域是否实时显示转录文本。 (Check if the "Interviewer's Questions" area on the frontend displays transcribed text in real-time.)
     - 后端控制台是否有 ASR 调用和数据库存储日志。 (Check backend console for ASR call and database storage logs.)
     - 数据库 `interview_transcripts` 表是否有新记录。 (Check if new records exist in the `interview_transcripts` database table.)

------

请您按照这个清单逐步操作。如果在执行过程中遇到任何问题或有疑问，请随时告诉我！

(Please follow this checklist step-by-step. If you encounter any issues or have questions during the process, feel free to let me know!)