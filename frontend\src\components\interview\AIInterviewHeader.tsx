import React from 'react';

export type TabType = 'interview' | 'records';

interface TabItem {
  key: TabType;
  label: string;
}

interface AIInterviewHeaderProps {
  activeTab: TabType;
  onTabChange: (tab: TabType) => void;
  mode?: 'live' | 'mock';
  title?: string;
}

const AIInterviewHeader: React.FC<AIInterviewHeaderProps> = ({
  activeTab,
  onTabChange,
  mode = 'live',
  title
}) => {
  const defaultTitle = mode === 'mock' ? 'AI模拟面试' : 'AI正式面试';
  const displayTitle = title || defaultTitle;

  const tabs: TabItem[] = [
    { key: 'interview', label: displayTitle },
    { key: 'records', label: '面试记录' }
  ];

  return (
    <header className="bg-white border-b border-gray-100 px-8 pt-6">
      <div className="flex space-x-8">
        {tabs.map((tab) => (
          <button
            key={tab.key}
            onClick={() => onTabChange(tab.key)}
            className={`pb-4 px-1 text-sm font-medium transition-colors relative ${
              activeTab === tab.key
                ? 'text-blue-600'
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            {tab.label}
            {activeTab === tab.key && (
              <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-blue-600"></div>
            )}
          </button>
        ))}
      </div>
    </header>
  );
};

export default AIInterviewHeader;
