import useAuthStore from '../../stores/authStore';

// API 基础URL
const API_BASE_URL = process.env.NODE_ENV === 'production'
  ? 'https://mianshijun.xyz/api'
  : '/api';

// 兑换响应接口
interface RedeemResponse {
  success: boolean;
  message: string;
  benefitType: string;
  benefitValue: number;
  newBalance: number;
}

// 兑换错误响应接口
interface RedeemErrorResponse {
  message: string;
}

// 带认证的fetch函数
const fetchWithAuth = async <T>(url: string, options: RequestInit = {}): Promise<T> => {
  const token = useAuthStore.getState().token;
  
  if (!token) {
    throw new Error('用户未登录');
  }

  const response = await fetch(`${API_BASE_URL}${url}`, {
    ...options,
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`,
      ...options.headers,
    },
  });

  if (!response.ok) {
    let errorMessage = '网络错误';
    try {
      const errorData = await response.json();
      errorMessage = errorData.message || errorMessage;
    } catch {
      // 如果无法解析错误响应，使用默认错误消息
    }
    throw new Error(errorMessage);
  }

  return response.json();
};

// 兑换码兑换API函数
export const redeemCodeApi = async (code: string): Promise<RedeemResponse> => {
  try {
    const response = await fetchWithAuth<RedeemResponse>('/redeem', {
      method: 'POST',
      body: JSON.stringify({ code }),
    });
    return response;
  } catch (error: any) {
    // 如果是503错误（数据库连接恢复），提示用户重试
    if (error.message.includes('数据库连接已恢复')) {
      throw new Error('数据库连接已恢复，请重新点击兑换按钮');
    }
    // 重新抛出错误，让调用方处理
    throw new Error(error.message || '兑换失败，请稍后再试');
  }
};
