import React from 'react';
import { useNavigate } from 'react-router-dom';

interface HeaderProps {
  companyName?: string;
  positionName?: string;
  elapsedTime: string;
  onEndInterview: () => void;
}

const Header: React.FC<HeaderProps> = ({
  companyName = '公司名称',
  positionName = '岗位名称',
  elapsedTime,
  onEndInterview
}) => {
  const navigate = useNavigate();

  const handleEndInterview = () => {
    onEndInterview();
    navigate('/dashboard'); // 结束面试后回到主页面（仪表盘）
  };

  return (
    <div className="flex items-center justify-between w-full px-6 py-4 bg-white border-b border-gray-100 shadow-md">
      <div className="text-xl font-bold text-gray-800 bg-gradient-to-r from-gray-800 to-gray-700 bg-clip-text text-transparent">
        {companyName}{positionName && ` - ${positionName}`}
      </div>
      <div className="flex items-center gap-6">
        <div className="flex items-center gap-2 px-3 py-1.5 bg-gray-50 rounded-xl shadow-sm">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="w-4 h-4 text-blue-500">
            <circle cx="12" cy="12" r="10" />
            <polyline points="12 6 12 12 16 14" />
          </svg>
          <span className="text-sm font-medium text-gray-700">面试已进行 {elapsedTime}</span>
        </div>
        <button
          onClick={handleEndInterview}
          className="px-4 py-2 text-sm font-medium text-white bg-gradient-to-br from-violet-500 to-blue-500 rounded-xl hover:shadow-lg shadow-md transition-all duration-200"
        >
          结束面试
        </button>
      </div>
    </div>
  );
};

export default Header;
