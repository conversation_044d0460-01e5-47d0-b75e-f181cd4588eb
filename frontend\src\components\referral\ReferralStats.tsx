import React from 'react';
import { Users, User<PERSON>he<PERSON>, Gift, Clock, RefreshCw } from 'lucide-react';

// 邀请统计接口
interface ReferralStats {
  totalInvited: number;
  successfulReferrals: number;
  totalRewards: number;
  pendingRewards: number;
}

// 组件属性接口
interface ReferralStatsProps {
  stats: ReferralStats | null;
  isLoading?: boolean;
  onRefresh?: () => void;
}

/**
 * 邀请统计展示组件
 */
const ReferralStats: React.FC<ReferralStatsProps> = ({
  stats,
  isLoading = false,
  onRefresh
}) => {

  if (isLoading) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 h-full">
        <div className="animate-pulse h-full">
          <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded mb-4"></div>
          <div className="space-y-4 flex-1">
            {[1, 2, 3, 4].map((i) => (
              <div key={i} className="h-16 bg-gray-200 dark:bg-gray-700 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  const defaultStats: ReferralStats = {
    totalInvited: 0,
    successfulReferrals: 0,
    totalRewards: 0,
    pendingRewards: 0
  };

  const displayStats = stats || defaultStats;

  // 统计卡片数据
  const statCards = [
    {
      title: '总邀请人数',
      value: displayStats.totalInvited,
      icon: Users,
      color: 'blue',
      description: '累计邀请用户数'
    },
    {
      title: '成功充值人数',
      value: displayStats.successfulReferrals,
      icon: UserCheck,
      color: 'green',
      description: '已完成首次充值'
    },
    {
      title: '累计奖励',
      value: displayStats.totalRewards,
      icon: Gift,
      color: 'purple',
      description: '面巾值',
      suffix: '面巾值'
    },
    {
      title: '待发放奖励',
      value: displayStats.pendingRewards,
      icon: Clock,
      color: 'orange',
      description: '面巾值',
      suffix: '面巾值'
    }
  ];

  // 获取颜色类名
  const getColorClasses = (color: string) => {
    const colorMap = {
      blue: {
        bg: 'bg-blue-50 dark:bg-blue-900/20',
        border: 'border-blue-200 dark:border-blue-800',
        icon: 'text-blue-600 dark:text-blue-400',
        text: 'text-blue-700 dark:text-blue-300'
      },
      green: {
        bg: 'bg-green-50 dark:bg-green-900/20',
        border: 'border-green-200 dark:border-green-800',
        icon: 'text-green-600 dark:text-green-400',
        text: 'text-green-700 dark:text-green-300'
      },
      purple: {
        bg: 'bg-purple-50 dark:bg-purple-900/20',
        border: 'border-purple-200 dark:border-purple-800',
        icon: 'text-purple-600 dark:text-purple-400',
        text: 'text-purple-700 dark:text-purple-300'
      },
      orange: {
        bg: 'bg-orange-50 dark:bg-orange-900/20',
        border: 'border-orange-200 dark:border-orange-800',
        icon: 'text-orange-600 dark:text-orange-400',
        text: 'text-orange-700 dark:text-orange-300'
      }
    };
    return colorMap[color as keyof typeof colorMap] || colorMap.blue;
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 h-full flex flex-col">
      {/* 标题和刷新按钮 */}
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
          邀请统计
        </h2>
        {onRefresh && (
          <button
            onClick={onRefresh}
            className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors duration-200"
            title="刷新数据"
          >
            <RefreshCw className="w-4 h-4" />
          </button>
        )}
      </div>

      {/* 统计卡片 */}
      <div className="space-y-4 flex-1">
        {statCards.map((card, index) => {
          const colors = getColorClasses(card.color);
          const IconComponent = card.icon;
          
          return (
            <div
              key={index}
              className={`p-4 ${colors.bg} border ${colors.border} rounded-lg transition-all duration-200 hover:shadow-sm`}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className={`p-2 rounded-lg ${colors.bg}`}>
                    <IconComponent className={`w-5 h-5 ${colors.icon}`} />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      {card.title}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {card.description}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <p className={`text-2xl font-bold ${colors.text}`}>
                    {card.value.toLocaleString()}
                  </p>
                  {card.suffix && (
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {card.suffix}
                    </p>
                  )}
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* 转化率显示 */}
      <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between text-sm">
          <span className="text-gray-600 dark:text-gray-400">转化率</span>
          <span className="font-medium text-gray-900 dark:text-white">
            {displayStats.totalInvited > 0 
              ? `${((displayStats.successfulReferrals / displayStats.totalInvited) * 100).toFixed(1)}%`
              : '0%'
            }
          </span>
        </div>
        
        {/* 转化率进度条 */}
        <div className="mt-2 w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
          <div 
            className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full transition-all duration-500"
            style={{ 
              width: displayStats.totalInvited > 0 
                ? `${(displayStats.successfulReferrals / displayStats.totalInvited) * 100}%`
                : '0%'
            }}
          ></div>
        </div>
      </div>

      {/* 奖励说明 */}
      <div className="mt-6 p-4 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
        <div className="text-center">
          <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            每成功邀请一位用户
          </p>
          <p className="text-lg font-bold text-blue-600 dark:text-blue-400">
            获得 100 面巾值奖励
          </p>
        </div>
      </div>

      {/* 数据更新时间 */}
      <div className="mt-4 text-center">
        <p className="text-xs text-gray-500 dark:text-gray-400">
          数据实时更新 • 最后更新: {new Date().toLocaleTimeString()}
        </p>
      </div>
    </div>
  );
};

export default ReferralStats;
