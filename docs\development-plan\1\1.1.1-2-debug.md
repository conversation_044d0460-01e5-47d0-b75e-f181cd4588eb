接下来，我将为您提供一份详细的操作清单，以便您在 Cursor 中直接操作，逐步解决这些问题。

**重要前提 (Important Prerequisites Reminder):**

- 项目已在本地运行 (Project is running locally):
  - 前端: 在 `MianshiJun_AI_Project/frontend` 目录下运行 `npm run dev`。 (Frontend: In `MianshiJun_AI_Project/frontend` directory, run `npm run dev`.)
  - 后端 (如果需要与API交互): 在 `MianshiJun_AI_Project` 根目录下运行 `vercel dev`。 (Backend (if API interaction is needed): In `MianshiJun_AI_Project` root directory, run `vercel dev`.)
- **Cursor 编辑器已打开对应文件 (Cursor editor has the relevant files open).**
- **Git 已配置，并且了解基本的 `git add .`, `git commit -m "message"`, `git push origin main` 操作。** (Git is configured, and you understand basic `git add .`, `git commit -m "message"`, `git push origin main` operations.)

------

## UI 修改与功能模块添加 - 操作清单

## UI Modifications & Feature Module Addition - Checklist

------

### Part 1: 品牌名称统一 (Unify Brand Name)

目标 (Goal): 将侧边栏的“面试助手”改为“面试君”。

(Change "面试助手" in the sidebar to "面试君".)

1. **打开文件 (Open File):**

   - **操作 (Action):** 在 Cursor 编辑器中，打开 `frontend/src/components/Sidebar.tsx` 文件。 (In Cursor editor, open the `frontend/src/components/Sidebar.tsx` file.)

2. **定位并修改文本 (Locate and Modify Text):**

   - 操作 (Action):

      在 

     ```
     Sidebar.tsx
     ```

      文件中，找到显示“面试助手”文本的地方。根据您上传的 

     ```
     Sidebar.tsx
     ```

      文件，它应该在这里： (In the 

     ```
     Sidebar.tsx
     ```

      file, find where the text "面试助手" is displayed. Based on your uploaded 

     ```
     Sidebar.tsx
     ```

     , it should be here:)

     TypeScript

     ```
     // ...其他代码 (other code)...
     <h1 className="text-2xl font-bold text-sky-500">面试助手</h1>
     // ...其他代码 (other code)...
     ```

   - 将其修改为 (Change it to):

     TypeScript

     ```
     // ...其他代码 (other code)...
     <h1 className="text-2xl font-bold text-sky-500">面试君</h1>
     // ...其他代码 (other code)...
     ```

3. **保存文件 (Save File):**

   - **操作 (Action):** 按 `Ctrl+S` (Windows/Linux) 或 `Cmd+S` (Mac) 保存文件。 (Press `Ctrl+S` (Windows/Linux) or `Cmd+S` (Mac) to save the file.)

4. **查看效果 (Check the Result):**

   - **操作 (Action):** 如果你的前端开发服务器 (`npm run dev`) 正在运行，浏览器中的页面应该会自动刷新。查看侧边栏的品牌名称是否已更新为“面试君”。 (If your frontend development server (`npm run dev`) is running, the page in your browser should refresh automatically. Check if the brand name in the sidebar has updated to "面试君".)

------

### Part 2: 导航链接显示逻辑优化 (Optimize Navigation Link Display Logic)

目标 (Goal): 当用户已登录时，头部导航不应再显示“注册”、“登录”链接，并且“首页”链接也应移除或调整。

(When a user is logged in, the header navigation should no longer display "Register" and "Login" links, and the "Home" link should also be removed or adjusted.)

为了实现这个，我们需要一个判断用户是否登录的状态。目前我们还没有完整的登录逻辑，所以我们先假设一个登录状态，并根据这个状态来调整显示。

(To achieve this, we need a state to determine if the user is logged in. We don't have full login logic yet, so we'll simulate a logged-in state for now and adjust the display accordingly.)

1. **打开文件 (Open File):**

   - **操作 (Action):** 打开 `frontend/src/components/Header.tsx` 文件。 (Open the `frontend/src/components/Header.tsx` file.)

2. **模拟登录状态并修改导航 (Simulate Logged-in State and Modify Navigation):**

   - 操作 (Action):

     1. 在 

        ```
        Header.tsx
        ```

         组件函数的顶部，我们添加一个临时的变量来模拟登录状态。在真实应用中，这个状态会从 Context API 或 Zustand store 获取。 (At the top of the 

        ```
        Header.tsx
        ```

         component function, let's add a temporary variable to simulate the login state. In a real application, this state would come from Context API or a Zustand store.)

        TypeScript

        ```
        import React from 'react';
        import { Link } from 'react-router-dom'; // 确保已导入 (Make sure Link is imported)
        import { UserCircleIcon, BellIcon, Cog6ToothIcon } from '@heroicons/react/24/outline'; // 从你的代码中看，这些已导入 (These are already imported from your code)
        
        interface HeaderProps {
          greeting: string;
        }
        
        const Header: React.FC<HeaderProps> = ({ greeting }) => {
          // 模拟登录状态，之后会用真实的状态管理替换
          // Simulate login state, will be replaced with real state management later
          const isLoggedIn = true; // <--- 添加这一行，true 代表已登录，false 代表未登录 (Add this line, true means logged in, false means not logged in)
        
          return (
            <header className="bg-white shadow-sm p-4 flex justify-between items-center">
              {/* ... 你现有的 greeting 和 search bar 代码 ... */}
              {/* ... Your existing greeting and search bar code ... */}
              <div className="text-gray-700 text-lg">{greeting}, 欢迎来到面试君!</div>
              {/* 搜索框可以暂时保留或注释掉 (Search bar can be kept or commented out for now) */}
              {/* <div className="relative">
                <input
                  type="text"
                  placeholder="搜索问题或功能..."
                  className="pl-10 pr-4 py-2 border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-sky-500 focus:border-transparent"
                />
                <MagnifyingGlassIcon className="h-5 w-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" />
              </div> */}
        
              {/* 导航链接区域修改 (Navigation links area modification) */}
              <nav className="flex items-center gap-x-4 sm:gap-x-6">
                {!isLoggedIn && ( // 如果未登录则显示这些链接 (Show these links if not logged in)
                  <>
                    <Link to="/" className="text-sm font-medium text-gray-600 hover:text-sky-500">
                      首页 (Home)
                    </Link>
                    <Link to="/login" className="text-sm font-medium text-gray-600 hover:text-sky-500">
                      登录 (Login)
                    </Link>
                    <Link to="/register" className="text-sm font-medium text-gray-600 hover:text-sky-500">
                      注册 (Register)
                    </Link>
                  </>
                )}
        
                {/* 右侧图标保持不变 (Right-side icons remain unchanged) */}
                <button
                  type="button"
                  className="-m-2.5 p-2.5 text-gray-400 hover:text-gray-500"
                >
                  <span className="sr-only">View notifications</span>
                  <BellIcon className="h-6 w-6" aria-hidden="true" />
                </button>
                <button
                  type="button"
                  className="-m-2.5 p-2.5 text-gray-400 hover:text-gray-500"
                >
                  <span className="sr-only">View settings</span>
                  <Cog6ToothIcon className="h-6 w-6" aria-hidden="true" />
                </button>
                {isLoggedIn && ( // 如果已登录则显示用户头像 (Show user avatar if logged in)
                  <div className="relative">
                    <button className="flex items-center justify-center h-8 w-8 rounded-full bg-sky-500 text-white text-sm font-semibold focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-sky-500">
                      A {/* 替换为真实用户首字母或头像 (Replace with real user initial or avatar) */}
                    </button>
                    {/* 这里可以添加用户下拉菜单 (User dropdown menu can be added here) */}
                  </div>
                )}
              </nav>
            </header>
          );
        };
        
        export default Header;
        ```

     2. 解释 (Explanation):

        - 我们添加了一个 `isLoggedIn` 变量，并设置为 `true` 来模拟用户已登录。 (We added an `isLoggedIn` variable and set it to `true` to simulate a logged-in user.)
        - 然后我们用 `{!isLoggedIn && (...)}` 包裹了“首页”、“登录”、“注册”链接。这意味着：**仅当 `isLoggedIn` 为 `false` (未登录) 时，这些链接才会显示。** (Then we wrapped the "Home", "Login", "Register" links with `{!isLoggedIn && (...) }`. This means: **these links will only be displayed if `isLoggedIn` is `false` (not logged in).**)
        - 用户头像 (`<UserCircleIcon />` 或你现在的 'A' 按钮) 现在用 `{isLoggedIn && (...)}` 包裹，表示**仅当 `isLoggedIn` 为 `true` (已登录) 时才显示。** (The user avatar (your 'A' button) is now wrapped with `{isLoggedIn && (...) }`, meaning it will **only be displayed if `isLoggedIn` is `true` (logged in).**)

3. **保存文件 (Save File):**

   - **操作 (Action):** 保存 `Header.tsx` 文件。 (Save the `Header.tsx` file.)

4. **查看效果 (Check the Result):**

   - **操作 (Action):** 浏览器中的页面应该会自动刷新。因为我们设置了 `isLoggedIn = true`，所以“首页”、“登录”、“注册”链接现在应该消失了，只剩下右侧的通知、设置图标和用户头像（'A'按钮）。 (The page in your browser should refresh. Since we set `isLoggedIn = true`, the "Home", "Login", "Register" links should now be gone, leaving only the notification, settings icons, and the user avatar ('A' button) on the right.)
   - 你可以尝试将 `isLoggedIn` 改为 `false` 并保存，看看那些链接是否会重新出现，以此来验证逻辑。记得改回 `true`。 (You can try changing `isLoggedIn` to `false` and save to see if the links reappear, to verify the logic. Remember to change it back to `true`.)

------

### Part 3: 缺失核心功能模块 (Add Missing Core Feature Modules)

目标 (Goal): 在首页 (HomePage.tsx) 中添加设计稿中显示的“简历解析”、“意向岗位”、“AI模拟面试”、“AI正式面试”这些模块的占位符或基础结构。

(Add placeholders or basic structure for the "Resume Analysis", "Target Positions", "AI Mock Interview", and "AI Formal Interview" modules on the HomePage.tsx as shown in the design drafts.)

由于这些模块比较复杂，我们先创建它们的组件文件，并在 HomePage.tsx 中引入它们，显示简单的占位文本。

(Since these modules are quite complex, we'll first create their component files and import them into HomePage.tsx, displaying simple placeholder text.)

1. **创建组件文件夹 (Create Components Folder - if specific to HomePage):**

   - 如果这些模块是 `HomePage` 特有的，可以考虑在 `src/pages/` 下创建一个 `HomePageSections` 或类似的文件夹。但为了简单起见，我们暂时都放在 `src/components/` 下，或者你也可以创建一个 `src/features/` 文件夹来存放这些大块的功能。
   - **为了保持和你现有结构一致，我们还是放在 `src/components/` 下吧。** 你已经有 `ResumeSection.tsx` 和 `PositionSection.tsx` 了，很好！我们来创建剩下的。 (To keep consistency with your existing structure, let's put them in `src/components/`. You already have `ResumeSection.tsx` and `PositionSection.tsx`, which is great! Let's create the remaining ones.)

2. **创建/修改 `frontend/src/components/ResumeSection.tsx`:**

   - 操作 (Action):

      你已经有这个文件了。打开它，确保它有一个基本的骨架，或者更新成类似下面的样子，以便我们看到它： (You already have this file. Open it and ensure it has a basic skeleton, or update it to something like the following so we can see it:)

     TypeScript

     ```
     import React from 'react';
     import { DocumentTextIcon, LightBulbIcon, SparklesIcon, ArrowUpTrayIcon } from '@heroicons/react/24/outline'; // 导入你可能需要的图标 (Import icons you might need)
     
     const ResumeSection: React.FC = () => {
       return (
         <div className="bg-white p-6 rounded-lg shadow-lg mb-6"> {/* 卡片样式 (Card styling) */}
           <h2 className="text-xl font-semibold text-gray-800 mb-4">简历解析 (Resume Analysis)</h2>
           <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
             {/* 示例：AI分析职业技能 (Example: AI analyzes professional skills) */}
             <div className="border border-gray-200 p-3 rounded-md">
               <DocumentTextIcon className="h-6 w-6 text-sky-500 mb-2" />
               <h3 className="font-medium text-gray-700">AI分析职业技能</h3>
               <p className="text-sm text-gray-500">快速提炼您的核心竞争力。</p>
             </div>
             {/* 示例：智能推荐重点 (Example: Intelligent recommendation of key points) */}
             <div className="border border-gray-200 p-3 rounded-md">
               <LightBulbIcon className="h-6 w-6 text-sky-500 mb-2" />
               <h3 className="font-medium text-gray-700">智能推荐重点</h3>
               <p className="text-sm text-gray-500">突出您的求职亮点。</p>
             </div>
             {/* 示例：个性化建议 (Example: Personalized suggestions) */}
             <div className="border border-gray-200 p-3 rounded-md">
               <SparklesIcon className="h-6 w-6 text-sky-500 mb-2" />
               <h3 className="font-medium text-gray-700">个性化建议</h3>
               <p className="text-sm text-gray-500">针对性优化您的简历内容。</p>
             </div>
           </div>
           <button className="bg-sky-500 hover:bg-sky-600 text-white font-medium py-2 px-4 rounded-md flex items-center">
             <ArrowUpTrayIcon className="h-5 w-5 mr-2" />
             上传简历 (Upload Resume)
           </button>
           <p className="text-xs text-gray-400 mt-2">支持 .pdf, .doc, .docx 格式，文件大小不超过 5MB</p>
         </div>
       );
     };
     
     export default ResumeSection;
     ```

3. **创建/修改 `frontend/src/components/PositionSection.tsx`:**

   - 操作 (Action):

      你也已经有这个文件了。打开并更新它，或者确保它有类似下面的基础内容： (You also already have this file. Open and update it, or ensure it has basic content like below:)

     TypeScript

     ```
     import React from 'react';
     import { BriefcaseIcon, PlusCircleIcon } from '@heroicons/react/24/outline'; // 导入图标 (Import icons)
     
     const PositionSection: React.FC = () => {
       // 模拟数据 (Mock data)
       const targetPositions = [
         { id: 1, title: '前端开发工程师', company: '科技公司A', status: '简历已投递' },
         { id: 2, title: '产品经理', company: '互联网大厂B', status: '待优化简历' },
       ];
     
       return (
         <div className="bg-white p-6 rounded-lg shadow-lg mb-6">
           <div className="flex justify-between items-center mb-4">
             <h2 className="text-xl font-semibold text-gray-800">意向岗位 (Target Positions)</h2>
             <button className="bg-green-500 hover:bg-green-600 text-white font-medium py-2 px-4 rounded-md flex items-center text-sm">
               <PlusCircleIcon className="h-5 w-5 mr-2" />
               添加岗位 (Add Position)
             </button>
           </div>
           <p className="text-sm text-gray-600 mb-4">
             已添加 <span className="font-bold text-sky-500">{targetPositions.length}</span> 个意向岗位，您还可以添加 <span className="font-bold text-sky-500">{10 - targetPositions.length}</span> 个。
           </p>
           <div className="space-y-3">
             {targetPositions.map(pos => (
               <div key={pos.id} className="border border-gray-200 p-3 rounded-md hover:shadow-md transition-shadow">
                 <div className="flex justify-between items-start">
                   <div>
                     <h3 className="font-medium text-gray-700">{pos.title}</h3>
                     <p className="text-xs text-gray-500">{pos.company}</p>
                   </div>
                   <span className="text-xs bg-sky-100 text-sky-700 px-2 py-0.5 rounded-full">{pos.status}</span>
                 </div>
               </div>
             ))}
             {targetPositions.length === 0 && (
               <p className="text-sm text-gray-500 text-center py-4">暂无意向岗位，快去添加吧！ (No target positions yet, add some!)</p>
             )}
           </div>
         </div>
       );
     };
     
     export default PositionSection;
     ```

4. **创建/修改 `frontend/src/components/InterviewCard.tsx`:**

   - 操作 (Action):

      你也有这个文件。我们用它来做 “AI模拟面试” 和 “AI正式面试” 的卡片。让它更通用一些。 (You also have this file. We'll use it for "AI Mock Interview" and "AI Formal Interview" cards. Let's make it a bit more generic.)

     TypeScript

     ```
     import React from 'react';
     import { ArrowRightIcon } from '@heroicons/react/24/outline';
     
     interface InterviewCardProps {
       title: string;
       description: string;
       icon: React.ReactNode; // 允许传递一个图标组件 (Allow passing an icon component)
       bgColorClass?: string; // 可选的背景颜色类 (Optional background color class)
       textColorClass?: string; // 可选的文本颜色类 (Optional text color class)
       buttonText?: string;
       onButtonClick?: () => void;
     }
     
     const InterviewCard: React.FC<InterviewCardProps> = ({
       title,
       description,
       icon,
       bgColorClass = 'bg-sky-500', // 默认背景色 (Default background color)
       textColorClass = 'text-white', // 默认文本颜色 (Default text color)
       buttonText = "开始体验 (Start Experience)",
       onButtonClick,
     }) => {
       return (
         <div className={`p-6 rounded-lg shadow-lg ${bgColorClass} ${textColorClass} flex flex-col justify-between h-full`}>
           <div>
             <div className="mb-3">{icon}</div>
             <h3 className="text-lg font-semibold mb-2">{title}</h3>
             <p className="text-sm opacity-90 mb-4">{description}</p>
           </div>
           {onButtonClick && (
             <button
               onClick={onButtonClick}
               className={`mt-auto ${textColorClass} ${bgColorClass === 'bg-white' ? 'bg-sky-500 hover:bg-sky-600 text-white' : 'bg-white/20 hover:bg-white/30'} font-medium py-2 px-4 rounded-md flex items-center justify-center text-sm w-full`}
             >
               {buttonText}
               <ArrowRightIcon className="h-4 w-4 ml-2" />
             </button>
           )}
         </div>
       );
     };
     
     export default InterviewCard;
     ```

5. **修改 `frontend/src/pages/HomePage.tsx` 来引入并显示这些模块 (Modify `frontend/src/pages/HomePage.tsx` to import and display these modules):**

   - 操作 (Action):

      打开 

     ```
     frontend/src/pages/HomePage.tsx
     ```

      文件，将其内容修改为： (Open 

     ```
     frontend/src/pages/HomePage.tsx
     ```

      and modify its content to:)

     TypeScript

     ```
     import React from 'react';
     import ResumeSection from '../components/ResumeSection'; // 我们刚修改的 (The one we just modified)
     import PositionSection from '../components/PositionSection'; // 我们刚修改的 (The one we just modified)
     import InterviewCard from '../components/InterviewCard'; // 我们刚修改的 (The one we just modified)
     import { AcademicCapIcon, RocketLaunchIcon } from '@heroicons/react/24/solid'; // 导入图标 (Import icons)
     
     const HomePage: React.FC = () => {
       const handleMockInterviewStart = () => {
         console.log("开始AI模拟面试 (Start AI Mock Interview)");
         // 将来这里会导航到模拟面试页面 (Will navigate to mock interview page in the future)
       };
     
       const handleFormalInterviewStart = () => {
         console.log("开始AI正式面试 (Start AI Formal Interview)");
         // 将来这里会导航到正式面试页面 (Will navigate to formal interview page in the future)
       };
     
       return (
         <div className="space-y-8"> {/* 使用 space-y 来控制模块间的垂直间距 (Use space-y for vertical spacing between modules) */}
           {/* 顶部欢迎可以保留或放到Header (Welcome message can be kept or moved to Header) */}
           {/* <h1 className="text-3xl font-bold text-gray-800">
             欢迎回来, User!
           </h1> */}
     
           <ResumeSection />
           <PositionSection />
     
           <div className="grid grid-cols-1 md:grid-cols-2 gap-6"> {/* 面试卡片用网格布局 (Grid layout for interview cards) */}
             <InterviewCard
               title="AI模拟面试 (AI Mock Interview)"
               description="不限次数，自由练习，AI智能分析，助您从容应对各种面试场景。"
               icon={<AcademicCapIcon className="h-10 w-10 text-white opacity-80" />}
               bgColorClass="bg-gradient-to-br from-sky-500 to-blue-600" // 渐变背景 (Gradient background)
               textColorClass="text-white"
               buttonText="开始模拟 (Start Mock Interview)"
               onButtonClick={handleMockInterviewStart}
             />
             <InterviewCard
               title="AI正式面试 (AI Formal Interview)"
               description="系统辅助，实时建议，助您在真实面试中稳定发挥，斩获心仪Offer。"
               icon={<RocketLaunchIcon className="h-10 w-10 text-sky-600 opacity-80" />} // 不同颜色图标 (Different color icon)
               bgColorClass="bg-white" // 白色背景 (White background)
               textColorClass="text-gray-700" // 深色文本 (Dark text)
               buttonText="开始面试 (Start Interview)"
               onButtonClick={handleFormalInterviewStart}
             />
           </div>
         </div>
       );
     };
     
     export default HomePage;
     ```

6. **保存所有修改过的文件 (Save all modified files).**

7. **查看效果 (Check the Result):**

   - **操作 (Action):** 浏览器中的首页现在应该会显示“简历解析”、“意向岗位”以及两个面试卡片的基础结构和占位内容了。样式可能还需要根据 `design_system.md` 进一步细化，但基本模块已经搭建起来了。 (The homepage in your browser should now display the basic structure and placeholder content for "Resume Analysis", "Target Positions", and the two interview cards. Styles might need further refinement based on `design_system.md`, but the basic modules are now in place.)

------

### Part 4: 底部文案修改 (Modify Footer Text)

目标 (Goal): 将底部文案从 “© 2025 面试君 (MianshiJun). AI助力，面试无忧.” 改为 “© 2025 面试君 AI助力，面试无忧”。

(Change the footer text from "© 2025 面试君 (MianshiJun). AI助力，面试无忧." to "© 2025 面试君 AI助力，面试无忧".)

1. **打开文件 (Open File):**

   - **操作 (Action):** 打开 `frontend/src/components/Footer.tsx` 文件。 (Open the `frontend/src/components/Footer.tsx` file.)

2. **修改文本 (Modify Text):**

   - 操作 (Action):

      找到 

     ```
     <p>
     ```

      标签内的文本： (Find the text inside the 

     ```
     <p>
     ```

      tag:)

     TypeScript

     ```
     <p className="text-sm">&copy; {new Date().getFullYear()} 面试君 (MianshiJun). AI助力，面试无忧.</p>
     ```

   - 将其修改为 (Change it to):

     TypeScript

     ```
     <p className="text-sm">&copy; {new Date().getFullYear()} 面试君  AI助力，面试无忧</p>
     ```

     - 注意： “面试君” 和 “AI助力” 之间是**两个空格**。移除了 `(MianshiJun).` 并且句末的 `.` 也移除了。 (Note: There are **two spaces** between "面试君" and "AI助力". Removed `(MianshiJun).` and the trailing `.`.)

3. **保存文件 (Save File):**

   - **操作 (Action):** 保存 `Footer.tsx` 文件。 (Save the `Footer.tsx` file.)

4. **查看效果 (Check the Result):**

   - **操作 (Action):** 浏览器中的页面页脚文案应该已经更新。 (The footer text in your browser should now be updated.)

------

### Part 5: 提交代码到 GitHub (Commit Code to GitHub)

在完成了这些修改之后，记得把你的代码提交到 GitHub。

1. 检查状态并添加更改 (Check status and add changes):

   - 操作 (Action):

     1. 在终端中，确保你**位于总项目文件夹 `MianshiJun_AI_Project` 的根目录**。 (In the terminal, make sure you are in the **root of your main project directory `MianshiJun_AI_Project`**.)

     2. 输入 `git status` 查看哪些文件被修改或添加了。 (Type `git status` to see which files have been modified or added.)

     3. 将所有更改添加到暂存区： (Add all changes to the staging area:)

        Bash

        ```
        git add .
        ```

2. 提交更改 (Commit changes):

   - 操作 (Action):

     Bash

     ```
     git commit -m "Feat: Implement UI fixes and add homepage module placeholders"
     ```

     (提交信息意思是：“功能：实现UI修复并添加首页模块占位符”) (This commit message means: "Feature: Implement UI fixes and add homepage module placeholders")

3. 推送到 GitHub (Push to GitHub):

   - 操作 (Action):

     Bash

     ```
     git push origin main
     ```

