将项目主页中的“添加岗位”和“上传简历”功能从使用模拟数据改为实体数据，并能实时存储和调用，主要涉及**数据库模型更新、后端API开发和前端页面集成**这三个方面。

以下是详细的操作清单：

------

## 数据库模型定义与迁移 (Database Model Definition and Migration)

首先，我们需要在数据库中创建新的表来存储简历和意向岗位信息。这将在 `local-mianshijun/backend/prisma/schema.prisma` 文件中完成。

1. **打开 Prisma Schema 文件**:

   - 中文: 在 Cursor 编辑器中，打开文件 `local-mianshijun/backend/prisma/schema.prisma`。
   - English: In the Cursor editor, open the file `local-mianshijun/backend/prisma/schema.prisma`.

2. **添加 `Resume` 和 `TargetPosition` 模型**:

   - 中文: 在 `schema.prisma` 文件的末尾，添加以下代码来定义新的模型。

   - English: At the end of the `schema.prisma` file, add the following code to define the new models.

   - 操作 (Action):

     代码段

     ```
     // local-mianshijun/backend/prisma/schema.prisma
     
     // ... (已有的 User 和 UserBalance 模型代码保持不变) ...
     
     model Resume {
       id        String   @id @default(uuid())
       userId    String   // 关联到 User 表的 id
       user      User     @relation(fields: [userId], references: [id], onDelete: Cascade) // 定义与User的关系
       fileName  String   // 上传的文件名
       filePath  String   // 文件在服务器或云存储上的路径
       fileType  String?  // 文件类型 (e.g., application/pdf)
       fileSize  Int?     // 文件大小 (字节)
       jobTitle  String?  // 关联的面试岗位/角色
       uploadTimestamp DateTime @default(now()) // 上传时间戳
       createdAt DateTime @default(now())
       updatedAt DateTime @updatedAt
     
       @@index([userId]) // 为 userId 添加索引以优化查询
       @@map("resumes") // 数据库中的表名
     }
     
     model TargetPosition {
       id        String   @id @default(uuid())
       userId    String   // 关联到 User 表的 id
       user      User     @relation(fields: [userId], references: [id], onDelete: Cascade) // 定义与User的关系
     
       positionName          String   // 岗位名称
       positionRequirements  String?  // 岗位要求
       companyName           String?  // 公司名称
       companyProfile        String?  // 公司简介
       status                String?  // 投递状态或备注
     
       createdAt DateTime @default(now())
       updatedAt DateTime @updatedAt
     
       @@index([userId])
       @@map("target_positions") // 数据库中的表名
     }
     ```

   - 中文: **重要提示**：确保 `User` 模型中添加反向关系。修改 `User` 模型如下：

   - English: **Important**: Ensure you add back-relations to the `User` model. Modify the `User` model as follows:

   - 操作 (Action): 找到 

     ```
     model User { ... }
     ```

      部分，在里面添加：

     代码段

     ```
     // local-mianshijun/backend/prisma/schema.prisma
     
     model User {
       // ... (id, email, password, name, createdAt, updatedAt, balance 这些字段保持不变)
     
       resumes           Resume[]          // 用户可以有多份简历
       targetPositions   TargetPosition[]  // 用户可以有多个意向岗位
     
       // ... sessions 和 orders (如果已存在) 保持不变
     }
     ```

3. **保存 `schema.prisma` 文件**:

   - 中文: 按 `Ctrl+S` (Windows/Linux) 或 `Cmd+S` (Mac)。
   - English: Press `Ctrl+S` (Windows/Linux) or `Cmd+S` (Mac).

4. **创建新的数据库迁移**:

   - 中文: 此命令会根据 `schema.prisma` 的更改生成新的 SQL 迁移文件。

   - English: This command will generate new SQL migration files based on the changes you made in `schema.prisma`.

   - 操作 (Action):

     1. 打开 Cursor 的终端。

     2. 确保您当前在 `local-mianshijun/backend/` 目录下。

     3. 运行命令:

        Bash

        ```
        npx prisma migrate dev --name add_resumes_and_target_positions
        ```

     4. 如果 Prisma 提示输入迁移名称，输入 `add_resumes_and_target_positions` 并按 Enter。

     5. Prisma 会在 `local-mianshijun/backend/prisma/migrations/` 目录下生成一个新的迁移文件夹。

5. **应用数据库迁移**:

   - 中文: `prisma migrate dev` 命令通常会自动应用迁移。
   - English: The `prisma migrate dev` command usually applies the migration automatically.

6. **重新生成 Prisma Client**:

   - 中文: 修改 `schema.prisma` 后，需重新生成 Prisma Client。

   - English: Whenever you modify `schema.prisma`, you need to regenerate Prisma Client.

   - 操作 (Action):

     1. 在终端中 (确保仍在 `local-mianshijun/backend/` 目录下)。

     2. 运行命令:

        Bash

        ```
        npx prisma generate
        ```

   现在数据库已准备好存储简历和意向岗位信息。

------

## 后端API接口开发 (Backend API Development)

我们将为简历和意向岗位创建增删改查 (CRUD) 的基本接口。

### A. 简历相关API (Resume APIs)

根据 `docs/api/resume_upload_api.md` 文件，我们将实现简历的上传、获取和删除功能。

1. 修改 `local-mianshijun/backend/server.ts` 文件

   :

   - 中文: 打开此文件，我们将在这里直接添加简历相关的路由和处理逻辑。

   - English: Open this file. We will add resume-related routes and handling logic here directly.

   - 操作 (Action):

     在 backend/server.ts 中，找到 // --- 意向岗位API路由 (Target Position API Routes) --- 这部分，在其上方添加以下代码：

     TypeScript

     ```
     // local-mianshijun/backend/server.ts
     
     // --- 健康检查 (Health Check) ---
     // ... (保留已有的健康检查代码) ...
     
     // ***** 新增：简历API路由 (New: Resume API Routes) *****
     // 创建简历
     app.post('/api/resumes', async (req: AuthenticatedRequest, res: Response) => {
       if (!req.user?.userId) {
         return res.status(401).json({ message: 'Unauthorized: User ID not found in token' });
       }
       const userId = req.user.userId;
       // 对于文件上传，Vercel Serverless Functions 对 multipart/form-data 的原生支持有限。
       // 更稳健的做法是前端将文件直传到云存储（如 Vercel Blob, S3），然后将返回的 URL 和元数据保存到数据库。
       // 此处简化处理：假设前端已处理文件上传，并将文件信息（如 fileName, filePath）在请求体中传来。
       const { fileName, filePath, fileType, fileSize, jobTitle } = req.body;
     
       if (!fileName || !filePath) {
         return res.status(400).json({ message: 'File name and path are required.' });
       }
     
       try {
         const newResume = await prisma.resume.create({
           data: {
             userId, // userId 现在应该是 number 类型，如果你的 JWT 解码出来是 string，需要转换
             fileName,
             filePath, // 这应该是云存储返回的路径或你自定义的路径
             fileType: fileType || null,
             fileSize: fileSize ? parseInt(fileSize as string, 10) : null, // 确保 fileSize 是数字
             jobTitle: jobTitle || null,
             uploadTimestamp: new Date(), // 使用ISOString格式，确保与 Prisma 字段类型匹配
           },
         });
         return res.status(201).json({ success: true, data: newResume });
       } catch (error) {
         console.error('Error creating resume:', error);
         const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
         return res.status(500).json({ message: 'Internal Server Error creating resume', error: errorMessage });
       }
     });
     
     // 获取用户的所有简历
     app.get('/api/resumes', async (req: AuthenticatedRequest, res: Response) => {
       if (!req.user?.userId) {
         return res.status(401).json({ message: 'Unauthorized: User ID not found in token' });
       }
       const userId = req.user.userId;
     
       try {
         const resumes = await prisma.resume.findMany({
           where: { userId }, // userId 类型需要匹配
           orderBy: { uploadTimestamp: 'desc' },
         });
         return res.status(200).json({ success: true, data: resumes });
       } catch (error) {
         console.error('Error fetching resumes:', error);
         const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
         return res.status(500).json({ message: 'Internal Server Error fetching resumes', error: errorMessage });
       }
     });
     
     // 删除特定简历
     app.delete('/api/resumes/:id', async (req: AuthenticatedRequest, res: Response) => {
       if (!req.user?.userId) {
         return res.status(401).json({ message: 'Unauthorized: User ID not found in token' });
       }
       const userId = req.user.userId;
       const { id } = req.params; // 从路径参数获取简历ID
     
       if (!id) {
         return res.status(400).json({ message: 'Resume ID is required.' });
       }
     
       try {
         // 确保简历存在且属于当前用户
         const resume = await prisma.resume.findFirst({
           where: { id, userId }, // userId 类型需要匹配
         });
     
         if (!resume) { // 如果找不到或者 resume.userId !== userId （理论上 findFirst 已经处理了userId）
           return res.status(404).json({ message: 'Resume not found or you do not have permission to delete it.' });
         }
     
         await prisma.resume.delete({
           where: { id },
         });
         // 注意：您可能还需要从实际的文件存储中删除文件本身。
         return res.status(200).json({ success: true, message: 'Resume deleted successfully.' });
       } catch (error) {
         console.error('Error deleting resume:', error);
         const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
         return res.status(500).json({ message: 'Internal Server Error deleting resume', error: errorMessage });
       }
     });
     
     // --- 意向岗位API路由 (Target Position API Routes) ---
     // ... (保留已有的意向岗位API代码) ...
     ```

   - 中文: **关于文件上传的说明**:

     - Vercel Serverless Functions 对直接处理大型文件上传（`multipart/form-data`）的支持有限。
     - 推荐方案：前端直接将文件上传到文件存储服务（如 Vercel Blob, AWS S3），成功后将返回的文件 URL 或标识符及其他元数据通过 `/api/resumes` (POST) 接口发送给后端保存。
     - 以上代码简化了此过程，假设 `filePath` 为存储服务返回的路径。

   - English: **Note on File Uploads**:

     - Vercel Serverless Functions have limited support for directly handling large file uploads (`multipart/form-data`).
     - Recommended solution: The frontend uploads the file directly to a file storage service (e.g., Vercel Blob, AWS S3). After success, the storage service returns a file URL or identifier, which, along with other metadata, is sent to the backend via the `/api/resumes` (POST) endpoint to be saved.
     - The code above simplifies this, assuming `filePath` is the path returned by the storage service.

### B. 意向岗位相关API (Target Position APIs)

`backend/server.ts` 文件中已经包含了意向岗位的API逻辑（POST, GET, PUT, DELETE）。请检查以下几点：

1. **认证中间件**: 确保 `authenticateToken` 中间件已正确应用到这些路由，或者在每个路由处理器内部都调用了 `authenticate(req)` 并检查了 `userId`。您现有的代码中，`authenticateToken` 中间件已应用于所有 `/api` 路径，但内部有对公共路径的排除。

   - `backend/server.ts`中的`authMiddleware`（您文件中是这个名字）已经应用，并且会从JWT中提取`userId`。

2. **数据校验**: 考虑为创建和更新意向岗位的请求体添加 `zod` 校验，类似于登录和注册接口。

3. **与数据库的交互**:

   - 创建岗位 (

     ```
     POST /api/positions
     ```

     ):

     - 确保从 `req.user.userId` 获取用户ID，并与 `prisma.targetPosition.create` 中的 `userId` 字段类型匹配。
     - 确保请求体中的字段 ( `positionName`, `positionRequirements`, `companyName`, `companyProfile`, `status`) 被正确处理，特别是可选字段。

   - 获取岗位 (

     ```
     GET /api/positions
     ```

     , 

     ```
     GET /api/positions/:id
     ```

     ):

     - 确保 `where` 条件中的 `userId` 类型正确。

   - 更新岗位 (

     ```
     PUT /api/positions/:id
     ```

     ):

     - 先验证岗位是否属于当前用户。
     - 确保更新数据时正确处理可选字段，避免将有值的字段更新为 `null`（除非明确意图）。

   - 删除岗位 (

     ```
     DELETE /api/positions/:id
     ```

     ):

     - 先验证岗位是否属于当前用户。

   您的 backend/server.ts 中已有这些路由的实现。 关键是确保 userId 的类型（通常在Prisma模型中是Int或String，而JWT中可能是number或string）在查询时一致。

   您的 User 模型 id 是 Int，而 Resume 和 TargetPosition 中的 userId 在Prisma模型中最初是 String，后改为 Int，所以请确保 AuthenticatedRequest 接口中 req.user.userId 的类型最终与数据库模型一致 (通常是 number 如果 User ID 是数字)。

   在我提供的 backend/server.ts 文件中，AuthenticatedRequest 接口已将 userId 定义为 number，而您的 PrismaClient 生成的 User 模型中的 id 也是 Int。在API路由中，从 req.user.userId 获取的 userId 将直接用于 Prisma 查询。

   - 在您的 `server.ts` 文件中，`req.user.userId` 的类型是 `number`，而 `PrismaClient` 生成的 `Resume` 和 `TargetPosition` 模型中的 `userId` 字段也应该是 `Int` 类型（与 `User` 模型的 `id` 字段类型一致）。如果之前定义为 `String`，请在 `schema.prisma` 中更正并重新迁移和生成客户端。

   - 上述提供的 `schema.prisma` (在阶段一) 已将 `Resume` 和 `TargetPosition` 中的 `userId` 定义为 `String`，这需要与 `User` 表的 `id` 类型（可能是`Int`）进行匹配。 **请统一 `User` 表 `id` 和关联表中 `userId` 的类型。** 如果 `User.id` 是 `Int` (SERIAL)，那么 `Resume.userId` 和 `TargetPosition.userId` 也应该是 `Int`。如果 `User.id` 是 `String` (UUID)，则它们也应为 `String`。 您的 `schema.prisma` (20250514125711_initial_migration) 中 `User.id` 是 `SERIAL NOT NULL` (即整数)，因此**关联表中的 `userId` 也必须是整数类型。**

   - **操作：请修改 `backend/prisma/schema.prisma` 中 `Resume` 和 `TargetPosition` 模型的 `userId` 字段类型为 `Int`，然后重新运行 `npx prisma migrate dev --name fix_userid_type` 和 `npx prisma generate`。**

     代码段

     ```
     // local-mianshijun/backend/prisma/schema.prisma
     
     model Resume {
       // ...
       userId    Int      // 修改为 Int (Changed to Int)
       user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
       // ...
     }
     
     model TargetPosition {
       // ...
       userId    Int      // 修改为 Int (Changed to Int)
       user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
       // ...
     }
     ```

     请注意，您的 `AuthenticatedRequest` 接口中 `req.user.userId` 已经是 `number` 类型，这是正确的。

------

## 测试API (Testing the APIs)

1. **启动本地开发服务器**:

   - 中文: 在 Cursor 的终端中，确保您位于 `local-mianshijun` 项目的根目录。

   - English: In Cursor's terminal, ensure you are in the root directory of the `local-mianshijun` project.

   - 操作 (Action):

     Bash

     ```
     vercel dev
     ```

   - API 应该可以通过 `http://localhost:3000/api/...` 访问。

2. **获取认证Token**:

   - 中文: 首先，通过 `/api/auth/login`（或注册后登录）获取一个 JWT Token。
   - English: First, obtain a JWT Token via `/api/auth/login` (or register and then login).
   - 将此 Token 放入后续请求的 `Authorization` 头中 (格式: `Bearer <your_token>`)。

3. **测试简历接口**:

   - ```
     POST http://localhost:3000/api/resumes
     ```

     - Body (JSON): `{ "fileName": "my_cv.pdf", "jobTitle": "Software Engineer", "filePath": "user_uploads/my_cv.pdf", "fileType": "application/pdf", "fileSize": 123456 }`
     - Headers: `Authorization: Bearer <your_token>`, `Content-Type: application/json`

   - ```
     GET http://localhost:3000/api/resumes
     ```

     - Headers: `Authorization: Bearer <your_token>`

   - ```
     DELETE http://localhost:3000/api/resumes/<resume_id_from_get_or_post_response>
     ```

      (注意：ID现在是路径参数)

     - Headers: `Authorization: Bearer <your_token>`

4. **测试意向岗位接口**:

   - ```
     POST http://localhost:3000/api/positions
     ```

     - Body (JSON): `{ "positionName": "Frontend Developer", "companyName": "Tech Innovations Inc.", "positionRequirements": "React, TypeScript", "companyProfile": "Web solutions", "status": "Preparing resume" }`
     - Headers: `Authorization: Bearer <your_token>`, `Content-Type: application/json`

   - ```
     GET http://localhost:3000/api/positions
     ```

     - Headers: `Authorization: Bearer <your_token>`

   - ```
     GET http://localhost:3000/api/positions/<position_id>
     ```

     - Headers: `Authorization: Bearer <your_token>`

   - ```
     PUT http://localhost:3000/api/positions/<position_id>
     ```

     - Body (JSON): `{ "positionName": "Senior Frontend Developer", "status": "Applied" }`
     - Headers: `Authorization: Bearer <your_token>`, `Content-Type: application/json`

   - ```
     DELETE http://localhost:3000/api/positions/<position_id>
     ```

     - Headers: `Authorization: Bearer <your_token>`

------

## 前端集成 (Frontend Integration)

前端部分需要修改 API 调用函数并更新组件以使用新的真实数据。

1. **修改API调用函数**:

   - 中文: 更新 `local-mianshijun/frontend/src/lib/api/resumes.ts` 和 `local-mianshijun/frontend/src/lib/api/positions.ts`。

   - English: Update `local-mianshijun/frontend/src/lib/api/resumes.ts` and `local-mianshijun/frontend/src/lib/api/positions.ts`.

   - `resumes.ts` 示例修改:

     TypeScript

     ```
     // frontend/src/lib/api/resumes.ts
     const API_BASE_URL = ''; // 使用相对路径，Vite 会代理到 http://localhost:3000
     
     export interface Resume {
       id: string;
       fileName: string;
       filePath: string;
       fileType?: string | null;
       fileSize?: number | null;
       jobTitle?: string | null;
       uploadTimestamp: string;
       // userId: number; // 根据后端返回，通常不需要在前端展示
       // createdAt: string;
       // updatedAt: string;
     }
     
     export const getUserResumes = async (token: string): Promise<Resume[]> => {
       const response = await fetch(`${API_BASE_URL}/api/resumes`, {
         headers: { 'Authorization': `Bearer ${token}` },
       });
       if (!response.ok) throw new Error('Failed to fetch resumes');
       const result = await response.json();
       return result.data as Resume[];
     };
     
     export const uploadResumeMetadata = async (
       token: string,
       resumeData: {
         fileName: string;
         filePath: string; // 这是文件存储后得到的路径
         fileType?: string;
         fileSize?: number;
         jobTitle?: string;
       }
     ): Promise<Resume> => {
       const response = await fetch(`${API_BASE_URL}/api/resumes`, {
         method: 'POST',
         headers: {
           'Content-Type': 'application/json',
           'Authorization': `Bearer ${token}`,
         },
         body: JSON.stringify(resumeData),
       });
       if (!response.ok) {
         const errorData = await response.json();
         throw new Error(errorData.message || 'Failed to upload resume metadata');
       }
       const result = await response.json();
       return result.data as Resume;
     };
     
     export const deleteResume = async (token: string, resumeId: string): Promise<void> => {
       const response = await fetch(`<span class="math-inline">\{API\_BASE\_URL\}/api/resumes/</span>{resumeId}`, { // ID 作为路径参数
         method: 'DELETE',
         headers: { 'Authorization': `Bearer ${token}` },
       });
       if (!response.ok) {
         const errorData = await response.json();
         throw new Error(errorData.message || 'Failed to delete resume');
       }
     };
     ```

   - `positions.ts` 示例修改:

     TypeScript

     ```
     // frontend/src/lib/api/positions.ts
     const API_BASE_URL = ''; // Vite proxy
     
     export interface TargetPosition {
       id: string;
       positionName: string;
       positionRequirements?: string | null;
       companyName?: string | null;
       companyProfile?: string | null;
       status?: string | null;
       // userId: number;
       // createdAt: string;
       // updatedAt: string;
     }
     
     export interface PositionFormData { // 用于创建和更新
       positionName: string;
       positionRequirements?: string;
       companyName?: string;
       companyProfile?: string;
       status?: string;
     }
     
     export const getUserPositions = async (token: string): Promise<TargetPosition[]> => {
       const response = await fetch(`${API_BASE_URL}/api/positions`, {
         headers: { 'Authorization': `Bearer ${token}` },
       });
       if (!response.ok) throw new Error('Failed to fetch positions');
       const result = await response.json();
       return result.data as TargetPosition[];
     };
     
     export const createPosition = async (token: string, positionData: PositionFormData): Promise<TargetPosition> => {
       const response = await fetch(`${API_BASE_URL}/api/positions`, {
         method: 'POST',
         headers: {
           'Content-Type': 'application/json',
           'Authorization': `Bearer ${token}`,
         },
         body: JSON.stringify(positionData),
       });
       if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || 'Failed to create position');
       }
       const result = await response.json();
       return result.data as TargetPosition;
     };
     
     export const updatePosition = async (token: string, positionId: string, positionData: Partial<PositionFormData>): Promise<TargetPosition> => {
       const response = await fetch(`<span class="math-inline">\{API\_BASE\_URL\}/api/positions/</span>{positionId}`, { // ID 作为路径参数
         method: 'PUT',
         headers: {
           'Content-Type': 'application/json',
           'Authorization': `Bearer ${token}`,
         },
         body: JSON.stringify(positionData),
       });
       if (!response.ok) {
         const errorData = await response.json();
         throw new Error(errorData.message || 'Failed to update position');
       }
       const result = await response.json();
       return result.data as TargetPosition;
     };
     
     export const deletePosition = async (token: string, positionId: string): Promise<void> => {
       const response = await fetch(`<span class="math-inline">\{API\_BASE\_URL\}/api/positions/</span>{positionId}`, { // ID 作为路径参数
         method: 'DELETE',
         headers: { 'Authorization': `Bearer ${token}` },
       });
       if (!response.ok) {
         const errorData = await response.json();
         throw new Error(errorData.message || 'Failed to delete position');
       }
     };
     ```

2. **更新组件状态管理和数据获取逻辑**:

   - 在 `ResumeSection.tsx` 和 `PositionSection.tsx` 中，使用 `useEffect` 配合新的API调用函数从后端获取数据，并用 `useState` 管理这些数据、加载状态和错误状态。
   - 确保在进行API调用时传递认证 `token`。您可以使用 `useAuthStore` 来获取 `token`。
   - `AddPositionModal.tsx` 的保存逻辑应调用 `createPosition` 或 `updatePosition`。
   - `ResumeUploadPage.tsx` 在文件上传成功后（到云存储），应调用 `uploadResumeMetadata`。

3. **用户界面调整**:

   - 移除组件中硬编码的模拟数据。
   - 根据从API获取的真实数据显示列表。
   - 处理加载中和错误状态，向用户提供反馈。

------

## 提交代码与部署 (Commit Code and Deploy)

1. **提交所有更改到 Git**:

   - 操作 (Action):

     Bash

     ```
     git add .
     git commit -m "Feat: Implement real data storage for resumes and positions"
     git push origin main # 或您的主分支名称
     ```

2. **部署到 Vercel**:

   - 中文: 推送到 GitHub 后，Vercel 应该会自动开始新的部署。
   - English: After pushing to GitHub, Vercel should automatically start a new deployment.
   - 检查 Vercel 上的部署日志和线上环境。

这个清单提供了将模拟数据替换为真实后端数据的完整步骤。前端的具体实现会比较繁琐，您可以一步步来，先确保后端API工作正常，再逐步修改前端组件。