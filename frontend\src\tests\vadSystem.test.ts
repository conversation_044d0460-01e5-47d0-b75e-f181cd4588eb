/**
 * VAD系统测试文件
 * 用于验证语音活动检测和智能分片功能
 */

import { VADDetector } from '../utils/vadDetector';
import { AudioBufferManager } from '../utils/audioBufferManager';
import { DynamicSegmenter } from '../utils/dynamicSegmenter';
import { CircularBuffer } from '../utils/circularBuffer';

// 模拟AudioContext
class MockAudioContext {
  sampleRate = 16000;
  state = 'running';
  
  close() {
    this.state = 'closed';
    return Promise.resolve();
  }
}

// 生成测试音频数据
function generateTestAudio(duration: number, frequency: number = 440, amplitude: number = 0.5): Float32Array {
  const sampleRate = 16000;
  const samples = Math.floor(duration * sampleRate);
  const audioData = new Float32Array(samples);
  
  for (let i = 0; i < samples; i++) {
    audioData[i] = amplitude * Math.sin(2 * Math.PI * frequency * i / sampleRate);
  }
  
  return audioData;
}

// 生成静音数据
function generateSilence(duration: number): Float32Array {
  const sampleRate = 16000;
  const samples = Math.floor(duration * sampleRate);
  return new Float32Array(samples);
}

// 生成噪音数据
function generateNoise(duration: number, amplitude: number = 0.1): Float32Array {
  const sampleRate = 16000;
  const samples = Math.floor(duration * sampleRate);
  const audioData = new Float32Array(samples);
  
  for (let i = 0; i < samples; i++) {
    audioData[i] = amplitude * (Math.random() * 2 - 1);
  }
  
  return audioData;
}

describe('VAD System Tests', () => {
  let mockAudioContext: MockAudioContext;
  let vadDetector: VADDetector;
  let bufferManager: AudioBufferManager;
  let segmenter: DynamicSegmenter;

  beforeEach(() => {
    mockAudioContext = new MockAudioContext();
    vadDetector = new VADDetector(mockAudioContext as any);
    bufferManager = new AudioBufferManager();
    segmenter = new DynamicSegmenter(vadDetector, bufferManager);
  });

  afterEach(() => {
    mockAudioContext.close();
  });

  describe('CircularBuffer', () => {
    test('should handle basic operations', () => {
      const buffer = new CircularBuffer<number>(3);
      
      expect(buffer.isEmpty()).toBe(true);
      expect(buffer.getSize()).toBe(0);
      
      buffer.push(1);
      buffer.push(2);
      buffer.push(3);
      
      expect(buffer.isFull()).toBe(true);
      expect(buffer.getSize()).toBe(3);
      expect(buffer.getAll()).toEqual([1, 2, 3]);
      
      buffer.push(4); // 应该覆盖第一个元素
      expect(buffer.getAll()).toEqual([2, 3, 4]);
    });

    test('should handle getLast correctly', () => {
      const buffer = new CircularBuffer<number>(5);
      [1, 2, 3, 4, 5].forEach(n => buffer.push(n));
      
      expect(buffer.getLast(3)).toEqual([3, 4, 5]);
      expect(buffer.getLast(10)).toEqual([1, 2, 3, 4, 5]); // 不应该超过实际大小
    });
  });

  describe('VADDetector', () => {
    test('should detect speech in audio signal', () => {
      const speechAudio = generateTestAudio(0.1, 440, 0.8); // 100ms, 440Hz, 高振幅
      const result = vadDetector.detectVoiceActivity(speechAudio);
      
      expect(result.isSpeech).toBe(true);
      expect(result.energy).toBeGreaterThan(0.1);
      expect(result.confidence).toBeGreaterThan(0.5);
      expect(result.spectralFeatures).toBeDefined();
    });

    test('should not detect speech in silence', () => {
      const silenceAudio = generateSilence(0.1);
      const result = vadDetector.detectVoiceActivity(silenceAudio);
      
      expect(result.isSpeech).toBe(false);
      expect(result.energy).toBeLessThan(0.01);
    });

    test('should handle noise appropriately', () => {
      const noiseAudio = generateNoise(0.1, 0.05); // 低振幅噪音
      const result = vadDetector.detectVoiceActivity(noiseAudio);
      
      // 低振幅噪音不应该被识别为语音
      expect(result.isSpeech).toBe(false);
    });

    test('should adapt threshold over time', () => {
      const config = vadDetector.getState();
      const initialThreshold = config.adaptiveThreshold;
      
      // 发送多个高能量音频样本
      for (let i = 0; i < 20; i++) {
        const highEnergyAudio = generateTestAudio(0.05, 440, 1.0);
        vadDetector.detectVoiceActivity(highEnergyAudio);
      }
      
      const updatedConfig = vadDetector.getState();
      expect(updatedConfig.adaptiveThreshold).toBeGreaterThan(initialThreshold);
    });
  });

  describe('AudioBufferManager', () => {
    test('should trigger on speech detection', () => {
      const speechAudio = generateTestAudio(0.1, 440, 0.8);
      const vadResult = vadDetector.detectVoiceActivity(speechAudio);
      
      // 模拟连续的语音输入
      for (let i = 0; i < 10; i++) {
        const triggerInfo = bufferManager.addAudioChunk(speechAudio, vadResult);
        if (triggerInfo.shouldTrigger) {
          expect(triggerInfo.reason).toMatch(/speech_detected|buffer_full/);
          expect(triggerInfo.confidence).toBeGreaterThan(0);
          break;
        }
      }
    });

    test('should handle silence timeout', () => {
      const speechAudio = generateTestAudio(0.1, 440, 0.8);
      const silenceAudio = generateSilence(0.1);
      
      const speechVAD = vadDetector.detectVoiceActivity(speechAudio);
      const silenceVAD = vadDetector.detectVoiceActivity(silenceAudio);
      
      // 先发送语音
      bufferManager.addAudioChunk(speechAudio, speechVAD);
      
      // 然后发送长时间静音
      for (let i = 0; i < 30; i++) { // 模拟3秒静音
        const triggerInfo = bufferManager.addAudioChunk(silenceAudio, silenceVAD);
        if (triggerInfo.shouldTrigger && triggerInfo.reason === 'silence_timeout') {
          expect(triggerInfo.confidence).toBeGreaterThan(0.8);
          break;
        }
      }
    });

    test('should provide buffer statistics', () => {
      const stats = bufferManager.getBufferStats();
      
      expect(stats).toHaveProperty('shortTerm');
      expect(stats).toHaveProperty('mediumTerm');
      expect(stats).toHaveProperty('longTerm');
      expect(stats).toHaveProperty('speechState');
      
      expect(stats.speechState).toHaveProperty('isInSpeech');
      expect(stats.speechState).toHaveProperty('currentSegmentId');
    });
  });

  describe('DynamicSegmenter', () => {
    test('should create valid audio segments', () => {
      const speechAudio = generateTestAudio(0.2, 440, 0.8); // 200ms语音
      
      let segment = null;
      for (let i = 0; i < 20; i++) {
        segment = segmenter.processAudioChunk(speechAudio);
        if (segment) break;
      }
      
      if (segment) {
        expect(segment.id).toBeDefined();
        expect(segment.audioData).toBeInstanceOf(Float32Array);
        expect(segment.audioData.length).toBeGreaterThan(0);
        expect(segment.confidence).toBeGreaterThan(0);
        expect(segment.segmentType).toMatch(/speech|silence|mixed/);
        expect(segment.duration).toBeGreaterThan(0);
      }
    });

    test('should maintain segment history', () => {
      const speechAudio = generateTestAudio(0.1, 440, 0.8);
      
      // 强制创建几个段
      for (let i = 0; i < 5; i++) {
        segmenter.processAudioChunk(speechAudio);
        segmenter.forceFinalize(); // 强制完成段
      }
      
      const history = segmenter.getSegmentHistory();
      expect(history.length).toBeGreaterThan(0);
      expect(history.length).toBeLessThanOrEqual(5);
    });

    test('should provide segmentation statistics', () => {
      const speechAudio = generateTestAudio(0.1, 440, 0.8);
      
      // 创建一些段
      for (let i = 0; i < 3; i++) {
        segmenter.processAudioChunk(speechAudio);
        segmenter.forceFinalize();
      }
      
      const stats = segmenter.getSegmentationStats();
      expect(stats.totalSegments).toBeGreaterThan(0);
      expect(stats.averageDuration).toBeGreaterThan(0);
      expect(stats.averageConfidence).toBeGreaterThan(0);
      expect(stats.segmentTypeDistribution).toBeDefined();
      expect(stats.triggerReasonDistribution).toBeDefined();
    });

    test('should reset properly', () => {
      const speechAudio = generateTestAudio(0.1, 440, 0.8);
      
      // 创建一些段
      segmenter.processAudioChunk(speechAudio);
      segmenter.forceFinalize();
      
      expect(segmenter.getSegmentHistory().length).toBeGreaterThan(0);
      
      // 重置
      segmenter.reset();
      
      expect(segmenter.getSegmentHistory().length).toBe(0);
      expect(segmenter.getCurrentSegmentInfo()).toBeNull();
    });
  });

  describe('Integration Tests', () => {
    test('should handle realistic audio scenario', () => {
      // 模拟真实场景：静音 -> 语音 -> 静音 -> 语音
      const scenarios = [
        { audio: generateSilence(0.5), description: '500ms静音' },
        { audio: generateTestAudio(1.0, 440, 0.8), description: '1s语音' },
        { audio: generateSilence(0.3), description: '300ms静音' },
        { audio: generateTestAudio(0.8, 880, 0.7), description: '800ms语音' },
        { audio: generateSilence(1.0), description: '1s静音' }
      ];
      
      const segments: any[] = [];
      
      scenarios.forEach(({ audio, description }) => {
        console.log(`Processing: ${description}`);
        
        // 将音频分成小块处理（模拟实时处理）
        const chunkSize = Math.floor(audio.length / 10);
        for (let i = 0; i < audio.length; i += chunkSize) {
          const chunk = audio.slice(i, i + chunkSize);
          const segment = segmenter.processAudioChunk(chunk);
          if (segment) {
            segments.push(segment);
            console.log(`Segment created: ${segment.segmentType}, ${segment.duration}ms`);
          }
        }
      });
      
      // 强制完成最后一个段
      const finalSegment = segmenter.forceFinalize();
      if (finalSegment) {
        segments.push(finalSegment);
      }
      
      expect(segments.length).toBeGreaterThan(0);
      
      // 验证段的质量
      segments.forEach(segment => {
        expect(segment.confidence).toBeGreaterThan(0);
        expect(segment.duration).toBeGreaterThan(0);
        expect(['speech', 'silence', 'mixed']).toContain(segment.segmentType);
      });
    });
  });
});

// 导出测试工具函数，供其他测试使用
export {
  generateTestAudio,
  generateSilence,
  generateNoise,
  MockAudioContext
};
