import React, { createContext, useContext, type ReactNode } from 'react';
import { useToast } from '../hooks/useToast';
import type { UseToastReturn } from '../types/toast';
import ToastContainer from '../components/ui/ToastContainer';

// 创建Toast Context
const ToastContext = createContext<UseToastReturn | undefined>(undefined);

// Toast Provider组件
interface ToastProviderProps {
  children: ReactNode;
}

export const ToastProvider: React.FC<ToastProviderProps> = ({ children }) => {
  const toastMethods = useToast();

  return (
    <ToastContext.Provider value={toastMethods}>
      {children}
      <ToastContainer toasts={toastMethods.toasts} onRemove={toastMethods.removeToast} />
    </ToastContext.Provider>
  );
};

// 使用Toast Context的Hook
export const useToastContext = (): UseToastReturn => {
  const context = useContext(ToastContext);
  if (context === undefined) {
    throw new Error('useToastContext must be used within a ToastProvider');
  }
  return context;
};
