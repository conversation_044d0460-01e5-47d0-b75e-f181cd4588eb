当然，这是根据我们所有讨论细节整合的、最终的、完整的代码重构方案。

------

### **最终代码重构方案**

#### **第一部分：后端重构 (核心智能逻辑)**

后端的职责是成为一个智能服务，全权负责接收前端发送的连续音频流，并管理ASR（语音识别）服务，实现3秒静音检测、60秒自动分段以及最终的长文本拼接。

**1. 修改 `sessionIATManager.ts` (讯飞ASR服务管理器)**

- **文件**: `backend/services/sessionIATManager.ts`

- **目标**: 将`vad_eos`参数从硬编码修改为可配置项，以符合3秒静音检测的需求。

- **实施**: 在初始化ASR服务的配置对象中，将`vad_eos`的值修改为从外部传入，并设置默认值为`3000`。

  TypeScript

  ```
  // backend/services/sessionIATManager.ts (示意代码)
  // 在sendFirstFrame或类似方法中
  private sendFirstFrame(config: { vad_eos?: number; /* ...其他参数 */ }) {
    const frameData = {
      // ...
      business: {
        vad_eos: config.vad_eos || 3000, // 使用传入的配置，默认为3000
        // ... 其他业务参数
      },
      // ...
    };
    this.ws.send(JSON.stringify(frameData));
  }
  ```

**2. 新建 `InterviewSessionManager.ts` (面试会话状态管理器)**

这是本次重构的核心，用于封装单次面试会话的所有复杂状态逻辑。

- **文件**: `backend/services/InterviewSessionManager.ts` (新文件)

- **目标**: 实现60秒“软切分”、结果累加、以及在3秒静音后拼接并发送完整文本的逻辑。

- **类设计**:

  TypeScript

  ```
  // backend/services/InterviewSessionManager.ts
  import { StreamingASRManager } from './streamingASRManager'; // 假设这是现有ASR管理器
  
  export class InterviewSessionManager {
    private accumulatedText: string = '';
    private maxDurationTimeout: NodeJS.Timeout | null = null;
    private asrManager: StreamingASRManager;
    private clientWs: WebSocket;
  
    private readonly MAX_SPEECH_DURATION = 60 * 1000;
  
    constructor(clientWs: WebSocket) {
      this.clientWs = clientWs;
      this.createNewASRManager();
    }
  
    public handleAudioChunk(chunk: Buffer): void {
      if (!this.asrManager) {
        this.createNewASRManager();
      }
      this.asrManager.sendAudio(chunk);
    }
  
    private onFinalResult(finalText: string): void {
      this.stopMaxDurationTimer();
      const completeText = this.accumulatedText + finalText;
      this.sendToLLM(completeText);
      this.accumulatedText = '';
      this.createNewASRManager();
    }
  
    private createNewASRManager(): void {
      if (this.asrManager) this.asrManager.destroy();
      this.asrManager = new StreamingASRManager({ vad_eos: 3000 });
      this.asrManager.on('finalResult', (text) => this.onFinalResult(text));
      this.startMaxDurationTimer();
    }
  
    private startMaxDurationTimer(): void {
      this.stopMaxDurationTimer();
      this.maxDurationTimeout = setTimeout(() => {
        this.handleMaxDuration();
      }, this.MAX_SPEECH_DURATION);
    }
  
    private stopMaxDurationTimer(): void {
      if (this.maxDurationTimeout) {
        clearTimeout(this.maxDurationTimeout);
        this.maxDurationTimeout = null;
      }
    }
  
    private async handleMaxDuration(): Promise<void> {
      const currentText = await this.asrManager.forceEndAndGetResult();
      this.accumulatedText += currentText;
      this.createNewASRManager();
    }
  
    private sendToLLM(text: string): void {
      console.log(`发送给LLM的完整文本: ${text}`);
      this.clientWs.send(JSON.stringify({ type: 'final_transcription', data: text }));
    }
  
    public destroy(): void {
        this.stopMaxDurationTimer();
        if (this.asrManager) this.asrManager.destroy();
    }
  }
  ```

**3. 修改 `interviewWs.ts` (WebSocket入口)**

- **文件**: `backend/websocket/interviewWs.ts`

- **目标**: 为每个WebSocket连接实例化并使用`InterviewSessionManager`。

- 实施

  :

  - 在`wss.on('connection', ...)`中，创建`new InterviewSessionManager(ws)`。
  - 将所有`ws.on('message', ...)`的音频数据交由`sessionManager.handleAudioChunk(message)`处理。
  - 在`ws.on('close', ...)`时调用`sessionManager.destroy()`来清理计时器等资源。

------

#### **第二部分：前端重构 (专注的客户端)**

前端的职责被简化，只负责稳定地采集和发送音频，并渲染后端处理好的最终结果。

**1. 调整 `useInterviewSession.ts` (核心Hook)**

- **文件**: `frontend/src/hooks/useInterviewSession.ts`

- **目标**: 移除所有前端VAD和分段逻辑，简化为纯粹的音频发送和结果接收器。

- **实施步骤**:

  - **简化状态**: 只需保留`isConnected`和`finalTranscripts: string[]`。

  - 改造`startSession`

    :

    - 接收`MediaStream`对象作为参数。
    - 建立WebSocket连接。
    - 使用`ScriptProcessorNode`或`AudioWorkletNode`处理音频，将原始PCM数据转换为`Int16Array`后，不加任何判断地直接通过WebSocket发送。

  - 改造`ws.onmessage`

    :

    - 只处理一种消息类型：`{ type: 'final_transcription', data: '...' }`。
    - 收到消息后，将`data`内容追加到`finalTranscripts`数组中。

- **示意代码**:

  TypeScript

  ```
  // frontend/src/hooks/useInterviewSession.ts
  import { useState, useRef, useCallback } from 'react';
  
  export const useInterviewSession = () => {
    const [isConnected, setIsConnected] = useState(false);
    const [finalTranscripts, setFinalTranscripts] = useState<string[]>([]);
    const wsRef = useRef<WebSocket | null>(null);
    const audioContextRef = useRef<AudioContext | null>(null);
    const scriptProcessorRef = useRef<ScriptProcessorNode | null>(null);
  
    const startSession = useCallback((stream: MediaStream) => {
      const ws = new WebSocket('ws://localhost:8080/interview'); // 使用您的后端地址
      wsRef.current = ws;
      ws.onopen = () => setIsConnected(true);
      ws.onclose = () => setIsConnected(false);
      ws.onmessage = (event) => {
        const message = JSON.parse(event.data);
        if (message.type === 'final_transcription') {
          setFinalTranscripts((prev) => [...prev, message.data]);
        }
      };
  
      const audioContext = new AudioContext();
      audioContextRef.current = audioContext;
      const source = audioContext.createMediaStreamSource(stream);
      const scriptProcessor = audioContext.createScriptProcessor(4096, 1, 1);
      scriptProcessorRef.current = scriptProcessor;
  
      scriptProcessor.onaudioprocess = (audioProcessingEvent) => {
        if (ws.readyState !== WebSocket.OPEN) return;
        const inputData = audioProcessingEvent.inputBuffer.getChannelData(0);
        const pcmData = new Int16Array(inputData.length);
        for (let i = 0; i < inputData.length; i++) {
          let s = Math.max(-1, Math.min(1, inputData[i]));
          pcmData[i] = s < 0 ? s * 0x8000 : s * 0x7FFF;
        }
        ws.send(pcmData.buffer);
      };
  
      source.connect(scriptProcessor);
      scriptProcessor.connect(audioContext.destination);
    }, []);
  
    const stopSession = useCallback(() => {
      wsRef.current?.close();
      scriptProcessorRef.current?.disconnect();
      audioContextRef.current?.close();
      setIsConnected(false);
    }, []);
  
    return { isConnected, finalTranscripts, startSession, stopSession };
  };
  ```

**2. 适配UI组件**

- 文件

  :

  - `frontend/src/pages/LiveInterviewPage.tsx`
  - `frontend/src/components/interview/RealtimeTranscription.tsx`

- **目标**: 使用重构后的Hook，并简化数据显示逻辑。

- 实施

  :

  - 在`LiveInterviewPage.tsx`中，从全局状态获取`MediaStream`，调用`useInterviewSession`，并在`useEffect`中执行`startSession`。
  - 在`RealtimeTranscription.tsx`中，接收`finalTranscripts: string[]`作为props，移除所有复杂的打字机或临时文本逻辑，直接遍历数组并渲染每一条最终文本即可。

这个最终方案将实现一个责任清晰、逻辑健壮、易于维护的实时语音识别系统。