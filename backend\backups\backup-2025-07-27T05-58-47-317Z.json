{"timestamp": "2025-07-27T05:58:56.601Z", "users": [{"id": "cmdhpqxbp0002bo8cqf7g5urg", "email": "<EMAIL>", "password": "", "name": "1", "createdAt": "2025-07-24T18:15:52.117Z", "updatedAt": "2025-07-24T18:15:52.117Z", "phoneNumber": null, "role": "USER", "balance": {"id": 1, "userId": "cmdhpqxbp0002bo8cqf7g5urg", "mockInterviewCredits": 1, "formalInterviewCredits": 2, "createdAt": "2025-07-24T18:15:52.117Z", "updatedAt": "2025-07-26T02:19:59.821Z", "mianshijunBalance": 100}}, {"id": "cmdj453690000bozw7oiailn5", "email": "<EMAIL>", "password": "$2b$12$7QcjKYwo04rsWOwS5MP1U.8N.XQwTqBpdxSGv8a6d.KtjuYkt6HXW", "name": "系统管理员", "createdAt": "2025-07-25T17:46:33.681Z", "updatedAt": "2025-07-25T17:46:33.681Z", "phoneNumber": null, "role": "ADMIN", "balance": {"id": 2, "userId": "cmdj453690000bozw7oiailn5", "mockInterviewCredits": 999, "formalInterviewCredits": 999, "createdAt": "2025-07-25T17:46:33.681Z", "updatedAt": "2025-07-25T17:46:33.681Z", "mianshijunBalance": 999999}}], "orders": [], "redemptionCodes": [{"id": "cmdj4ebj70000boec0lipv15i", "code": "BC34BD9N", "isUsed": true, "createdAt": "2025-07-25T17:53:44.420Z", "updatedAt": "2025-07-25T17:54:12.784Z", "expiresAt": null, "benefitType": "FORMAL_INTERVIEW", "benefitValue": 1, "userId": "cmdhpqxbp0002bo8cqf7g5urg", "codePrefix": null, "createdBy": "cmdj453690000bozw7oiailn5", "description": null, "isActive": true, "usageCount": 1, "usageLimit": 1}, {"id": "cmdj4okii0000boyko1t3xmg4", "code": "I8FXA19Q", "isUsed": false, "createdAt": "2025-07-25T18:01:42.618Z", "updatedAt": "2025-07-25T18:01:42.618Z", "expiresAt": "2025-11-02T18:01:42.019Z", "benefitType": "FORMAL_INTERVIEW", "benefitValue": 1, "userId": null, "codePrefix": null, "createdBy": null, "description": "", "isActive": true, "usageCount": 0, "usageLimit": 100}, {"id": "cmdj4p2ye0001boykzirpz3en", "code": "HR7NHJ98", "isUsed": false, "createdAt": "2025-07-25T18:02:06.518Z", "updatedAt": "2025-07-25T18:02:06.518Z", "expiresAt": "2025-08-04T18:02:06.241Z", "benefitType": "FORMAL_INTERVIEW", "benefitValue": 1, "userId": null, "codePrefix": null, "createdBy": null, "description": "", "isActive": true, "usageCount": 0, "usageLimit": 10}, {"id": "cmdj4p7k60002boykb7lgsplw", "code": "9EV4SIG3", "isUsed": false, "createdAt": "2025-07-25T18:02:12.486Z", "updatedAt": "2025-07-25T18:02:12.486Z", "expiresAt": "2025-08-04T18:02:12.205Z", "benefitType": "FORMAL_INTERVIEW", "benefitValue": 1, "userId": null, "codePrefix": null, "createdBy": null, "description": "", "isActive": true, "usageCount": 0, "usageLimit": 10}, {"id": "cmdj55m660000borw44qrotur", "code": "3IEBW1NK", "isUsed": true, "createdAt": "2025-07-25T18:14:57.918Z", "updatedAt": "2025-07-26T01:42:58.707Z", "expiresAt": null, "benefitType": "POINTS", "benefitValue": 100, "userId": "cmdhpqxbp0002bo8cqf7g5urg", "codePrefix": null, "createdBy": null, "description": "API测试兑换码", "isActive": true, "usageCount": 1, "usageLimit": 1}, {"id": "cmdj4sdqn0003boykg7dr8cso", "code": "PM55OQ3H", "isUsed": false, "createdAt": "2025-07-25T18:04:40.464Z", "updatedAt": "2025-07-26T02:19:57.102Z", "expiresAt": "2025-08-04T18:04:39.829Z", "benefitType": "FORMAL_INTERVIEW", "benefitValue": 1, "userId": null, "codePrefix": null, "createdBy": null, "description": "", "isActive": true, "usageCount": 2, "usageLimit": 10}, {"id": "cmdjqwlg20000boewb53e9dhm", "code": "UURTMO87", "isUsed": false, "createdAt": "2025-07-26T04:23:48.313Z", "updatedAt": "2025-07-26T04:23:48.313Z", "expiresAt": null, "benefitType": "POINTS", "benefitValue": 1, "userId": null, "codePrefix": null, "createdBy": null, "description": "", "isActive": true, "usageCount": 0, "usageLimit": 10}], "usageRecords": [{"id": "cmdj4u8dl000bboyk0tyze0vc", "userId": "cmdhpqxbp0002bo8cqf7g5urg", "type": "FORMAL_INTERVIEW", "amount": -1, "reason": "进入AI正式面试", "createdAt": "2025-07-25T18:06:06.826Z"}, {"id": "cmdj4v5xn000dboykmgkhemaj", "userId": "cmdhpqxbp0002bo8cqf7g5urg", "type": "MOCK_INTERVIEW", "amount": -1, "reason": "进入AI模拟面试", "createdAt": "2025-07-25T18:06:50.315Z"}], "referralCodes": [{"id": "cmdj5s73m0001bo34qzwrl1we", "userId": "test-user-123", "code": "BTU5OM0D", "isActive": true, "createdAt": "2025-07-25T18:32:31.474Z", "updatedAt": "2025-07-25T18:32:31.474Z", "expiresAt": null}, {"id": "cmdjnxm8v0003bobswayjndh3", "userId": "cmdhpqxbp0002bo8cqf7g5urg", "code": "ZDVQX4NY", "isActive": true, "createdAt": "2025-07-26T03:00:37.472Z", "updatedAt": "2025-07-26T03:00:37.472Z", "expiresAt": null}], "referralRelations": [], "referralRewards": []}