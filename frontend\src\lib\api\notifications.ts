import { fetchWithAuth } from './apiService';

// 通知类型定义
export interface Notification {
  id: string;
  title: string;
  content: string;
  type: 'ANNOUNCEMENT' | 'SYSTEM_UPDATE' | 'MAINTENANCE' | 'PROMOTION';
  priority: 'LOW' | 'NORMAL' | 'HIGH' | 'URGENT';
  publishedAt: string;
  expiresAt?: string | null;
}

export interface UserNotification {
  id: string;
  userId: string;
  notificationId: string;
  isRead: boolean;
  readAt?: string | null;
  createdAt: string;
  notification: Notification;
}

// API响应类型
export interface NotificationsResponse {
  success: boolean;
  data: {
    notifications: UserNotification[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      pages: number;
    };
  };
}

export interface UnreadCountResponse {
  success: boolean;
  data: {
    unreadCount: number;
  };
}

export interface NotificationDetailResponse {
  success: boolean;
  data: {
    notification: UserNotification;
  };
}

export interface MarkReadResponse {
  success: boolean;
  message: string;
  data?: {
    userNotification?: UserNotification;
    updatedCount?: number;
  };
}

// 通知API服务
export const notificationService = {
  /**
   * 获取用户通知列表
   */
  getNotifications: async (params?: {
    page?: number;
    limit?: number;
    unreadOnly?: boolean;
  }): Promise<NotificationsResponse> => {
    const queryParams = new URLSearchParams();
    
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());
    if (params?.unreadOnly) queryParams.append('unreadOnly', 'true');

    const endpoint = `/notifications${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return fetchWithAuth<NotificationsResponse>(endpoint, { method: 'GET' });
  },

  /**
   * 获取未读通知数量
   */
  getUnreadCount: async (): Promise<UnreadCountResponse> => {
    return fetchWithAuth<UnreadCountResponse>('/notifications/unread-count', { method: 'GET' });
  },

  /**
   * 获取通知详情
   */
  getNotificationDetail: async (id: string): Promise<NotificationDetailResponse> => {
    return fetchWithAuth<NotificationDetailResponse>(`/notifications/${id}`, { method: 'GET' });
  },

  /**
   * 标记单个通知为已读
   */
  markAsRead: async (id: string): Promise<MarkReadResponse> => {
    return fetchWithAuth<MarkReadResponse>(`/notifications/${id}/read`, { method: 'PUT' });
  },

  /**
   * 标记所有通知为已读
   */
  markAllAsRead: async (): Promise<MarkReadResponse> => {
    return fetchWithAuth<MarkReadResponse>('/notifications/read-all', { method: 'PUT' });
  }
};
