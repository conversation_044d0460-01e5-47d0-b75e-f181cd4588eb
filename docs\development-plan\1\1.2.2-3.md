好的，我们现在开始进行开发计划中的 **任务 1.2: 用户注册功能**。这个任务的目标是让新用户能够通过邮箱和密码成功注册账户。

这是一个涉及前端界面、前端逻辑、后端接口、数据库交互的完整功能。我们会一步一步来。

**重要前提 (Important Prerequisites Reminder):**

- **Node.js 和 npm (或 yarn)**: 已安装。
- **Git**: 已安装，并且您的项目已连接到 GitHub (`new-mianshijun`)。
- **Cursor (或 VS Code)**: 作为您的开发工具。
- **Vercel CLI**: 已安装并已登录 (`npm install -g vercel`, `vercel login`)，用于本地测试后端API。
- **前端开发服务器**: 可以在 `MianshiJun_AI_Project/frontend` 目录下通过 `npm run dev` 启动。
- **后端本地模拟服务器**: 可以在 `MianshiJun_AI_Project` 根目录下通过 `vercel dev` 启动。
- **Neon 数据库**: 已创建，并且 `DATABASE_URL` 已配置在 `MianshiJun_AI_Project/api/.env` 文件和 Vercel 项目的环境变量中。

------

## 任务 1.2: 用户注册功能 - 操作清单

## Task 1.2: User Registration Functionality - Checklist

------

### Part A: 后端准备工作 (Backend Preparation - API & Database)

在开始写前端注册页面之前，我们需要先准备好后端来接收注册请求、验证数据、加密密码，并将用户信息存入数据库。

**A1. 安装后端依赖 (Install Backend Dependencies):**

1. 安装 `bcryptjs` 用于密码加密 (Install `bcryptjs` for password hashing):

   - 操作 (Action):

     1. 打开 Cursor 的终端。 (Open Cursor's terminal.)

     2. 确保你当前在 `api` 文件夹目录下 (If in `MianshiJun_AI_Project`, type `cd api`). (Make sure you are in the `api` directory.)

     3. 运行以下命令 (Run the following command):

        Bash

        ```
        npm install bcryptjs
        ```

2. 安装 `@types/bcryptjs` (TypeScript 类型定义) (Install `@types/bcryptjs` (TypeScript type definitions)):

   - 操作 (Action):

      在终端中 (仍在 

     ```
     api
     ```

      目录下)，运行： (In the terminal (still in the 

     ```
     api
     ```

      directory), run:)

     Bash

     ```
     npm install --save-dev @types/bcryptjs
     ```

3. 安装 Prisma (ORM 工具) (Install Prisma (ORM Tool)):

   - Prisma 将帮助我们更方便地与 Neon 数据库交互。 (Prisma will help us interact with the Neon database more easily.)

   - 操作 (Action):

      在终端中 (仍在 

     ```
     api
     ```

      目录下)，运行： (In the terminal (still in the 

     ```
     api
     ```

      directory), run:)

     Bash

     ```
     npm install prisma --save-dev
     npm install @prisma/client
     ```

     - `prisma` 是 Prisma 的命令行工具。 (`prisma` is the command-line tool for Prisma.)
     - `@prisma/client` 是在你的代码中用来查询数据库的库。 (`@prisma/client` is the library used in your code to query the database.)

**A2. 初始化 Prisma 并定义数据模型 (Initialize Prisma and Define Data Models):**

1. **初始化 Prisma (Initialize Prisma):**

   - 操作 (Action):

      在终端中 (仍在 

     ```
     api
     ```

      目录下)，运行： (In the terminal (still in the 

     ```
     api
     ```

      directory), run:)

     Bash

     ```
     npx prisma init --datasource-provider postgresql
     ```

     - 这个命令会做几件事 (This command will do a few things):
       - 在 `api` 目录下创建一个名为 `prisma` 的新文件夹。 (Create a new folder named `prisma` in your `api` directory.)
       - 在 `api/prisma/` 文件夹中创建一个名为 `schema.prisma` 的文件，这是你定义数据库模型的地方。 (Create a file named `schema.prisma` in your `api/prisma/` folder, where you'll define your database models.)
       - 在 `api` 目录下创建一个 `.env` 文件 (如果它不存在的话)，并添加一个 `DATABASE_URL` 变量——确保这个 `.env` 文件中的 `DATABASE_URL` 指向你的 Neon 数据库连接字符串。 (Create a `.env` file in your `api` directory (if it doesn't exist) and add a `DATABASE_URL` variable – ensure this `DATABASE_URL` in the `.env` file points to your Neon database connection string.)

2. **检查并更新 `api/.env` 文件 (Check and Update `api/.env` file):**

   - 操作 (Action):

      打开 

     ```
     MianshiJun_AI_Project/api/.env
     ```

      文件。确认 

     ```
     DATABASE_URL
     ```

      是你从 Neon 获取的

     完整

     连接字符串。 (Open the 

     ```
     MianshiJun_AI_Project/api/.env
     ```

      file. Confirm that 

     ```
     DATABASE_URL
     ```

      is the 

     complete

      connection string you got from Neon.)

     代码段

     ```
     DATABASE_URL="postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require"
     ```

     (这是你之前提供的示例，请确保它是最新的、正确的。 (This is the example you provided earlier; please ensure it's the latest and correct one.))

3. **定义数据库模型在 `schema.prisma` (Define Database Models in `schema.prisma`):**

   - 根据你的 `development-plan.md` 第8节，我们需要 `users` 表和 `user_balances` 表。 (According to section 8 of your `development-plan.md`, we need `users` and `user_balances` tables.)

   - 操作 (Action):

      打开 

     ```
     api/prisma/schema.prisma
     ```

      文件。将其内容替换为以下代码： (Open the 

     ```
     api/prisma/schema.prisma
     ```

      file. Replace its content with the following:)

     代码段

     ```
     // This is your Prisma schema file,
     // learn more about it in the docs: https://pris.ly/d/prisma-schema
     
     generator client {
       provider = "prisma-client-js"
     }
     
     datasource db {
       provider = "postgresql" // Prisma 会从 .env 文件读取 DATABASE_URL (Prisma will read DATABASE_URL from the .env file)
       url      = env("DATABASE_URL")
     }
     
     model User {
       id        String   @id @default(uuid()) // 使用 String 和 uuid() 作为主键 (Use String and uuid() for primary key)
       email     String   @unique
       password  String // 我们存储的是哈希后的密码 (We store the hashed password)
       name      String? // 可选的名字 (Optional name)
       createdAt DateTime @default(now())
       updatedAt DateTime @updatedAt // Prisma 会自动更新这个时间 (Prisma automatically updates this timestamp)
     
       balance   UserBalance? // 一对一关系到 UserBalance (One-to-one relation to UserBalance)
       // sessions InterviewSession[] // 以后会添加面试会话 (Interview sessions will be added later)
       // orders   PaymentOrder[]   // 以后会添加支付订单 (Payment orders will be added later)
     
       @@map("users") // 将模型映射到数据库中的 "users" 表 (Map model to "users" table in the database)
     }
     
     model UserBalance {
       id        String   @id @default(uuid())
       userId    String   @unique // 关联到 User 表的 id (Relates to User table's id)
       user      User     @relation(fields: [userId], references: [id], onDelete: Cascade) // 定义关系 (Define relation)
     
       mockInterviewCredits    Int      @default(0) // 模拟面试次数 (Mock interview credits)
       formalInterviewCredits  Int      @default(0) // 正式面试次数 (Formal interview credits)
       mianshijinBalance       Int      @default(0) // 面巾余额 (Mianshijin balance)
       lastUpdatedAt           DateTime @default(now()) @updatedAt
     
       @@map("user_balances") // 将模型映射到 "user_balances" 表 (Map model to "user_balances" table)
     }
     
     // 你可以先注释掉其他表，等用到时再取消注释
     // You can comment out other tables for now and uncomment when needed
     /*
     model InterviewSession {
       id            String   @id @default(uuid())
       userId        String
       user          User     @relation(fields: [userId], references: [id])
       titleJobInfo  String?
       status        String   @default("pending")
       createdAt     DateTime @default(now())
       startedAt     DateTime?
       endedAt       DateTime?
       type          String?
     
       transcripts   InterviewTranscript[]
       suggestions   AISuggestion[]
     
       @@map("interview_sessions")
     }
     
     model InterviewTranscript {
       id        String   @id @default(uuid())
       sessionId String
       session   InterviewSession @relation(fields: [sessionId], references: [id])
       speaker   String
       content   String
       timestamp DateTime @default(now())
     
       @@map("interview_transcripts")
     }
     
     model AISuggestion {
       id                       String   @id @default(uuid())
       sessionId                String
       session                  InterviewSession @relation(fields: [sessionId], references: [id])
       originalQuestionSummary  String?
       suggestedResponse        String
       timestamp                DateTime @default(now())
       // transcriptId             String? // TODO: Link to specific question later
       // transcript               InterviewTranscript? @relation(fields: [transcriptId], references: [id])
       metadata                 Json?
     
       @@map("ai_suggestions")
     }
     
     model PaymentOrder {
       id                            String   @id @default(uuid())
       userId                        String
       user                          User     @relation(fields: [userId], references: [id])
       packageName                   String?
       amount                        Decimal
       currency                      String   @default("CNY")
       status                        String   @default("pending")
       paymentGateway                String?
       paymentGatewayTransactionId   String?  @unique
       createdAt                     DateTime @default(now())
       paidAt                        DateTime?
     
       @@map("payment_orders")
     }
     */
     ```

     - 解释 (Explanation):

       - `generator client`: 告诉 Prisma 生成 Prisma Client 代码。 (Tells Prisma to generate Prisma Client code.)

       - `datasource db`: 定义数据库连接。`url = env("DATABASE_URL")` 表示 Prisma 会从 `.env` 文件中读取 `DATABASE_URL`。 (Defines the database connection. `url = env("DATABASE_URL")` means Prisma reads `DATABASE_URL` from the `.env` file.)

       - ```
         model User
         ```

         : 定义了用户数据结构，对应数据库中的 

         ```
         users
         ```

          表。 (Defines the user data structure, corresponding to the 

         ```
         users
         ```

          table in the database.)

         - `@id @default(uuid())`: 将 `id` 字段设为主键，并默认生成 UUID。 (Sets the `id` field as the primary key and generates a UUID by default.)
         - `@unique`: 表示 `email` 字段的值必须是唯一的。 (Indicates the `email` field must have unique values.)
         - `String?`: 表示字段是可选的 (可以为 `null`)。 (Means the field is optional (can be `null`).)
         - `@default(now())`: 设置默认值为当前时间。 (Sets the default value to the current time.)
         - `@updatedAt`: Prisma 会在记录更新时自动更新这个时间戳。 (Prisma will automatically update this timestamp when the record is updated.)
         - `balance UserBalance?`: 定义了与 `UserBalance` 模型的一对一关系。 (Defines a one-to-one relationship with the `UserBalance` model.)
         - `@@map("users")`: 明确告诉 Prisma 这个模型对应数据库中的 `users` 表。 (Explicitly tells Prisma this model maps to the `users` table in the database.)

       - ```
         model UserBalance
         ```

         : 定义了用户余额/权益的数据结构。 (Defines the data structure for user balances/credits.)

         - `@relation(fields: [userId], references: [id], onDelete: Cascade)`: 定义了与 `User` 模型的外键关系。如果对应的 `User` 被删除，相关的 `UserBalance` 记录也会被删除。 (Defines the foreign key relationship with the `User` model. If the corresponding `User` is deleted, the related `UserBalance` record will also be deleted.)

4. **将数据模型同步到数据库 (Sync Data Model to Database):**

   - 这个命令会读取你的 `schema.prisma` 文件，并在你的 Neon 数据库中创建或修改相应的表。 (This command reads your `schema.prisma` file and creates or modifies the corresponding tables in your Neon database.)

   - 操作 (Action):

      在终端中 (仍在 

     ```
     api
     ```

      目录下)，运行： (In the terminal (still in the 

     ```
     api
     ```

      directory), run:)

     Bash

     ```
     npx prisma db push
     ```

     - 它可能会问你是否确定要应用这些更改，输入 `y` 并按 Enter。 (It might ask if you are sure you want to apply these changes; type `y` and press Enter.)
     - **测试要点 (Test Point):** 命令成功执行，没有错误。你可以登录 Neon 控制台查看你的数据库，应该能看到新创建的 `users` 和 `user_balances` 表。 (The command executes successfully without errors. You can log in to the Neon console to check your database; you should see the newly created `users` and `user_balances` tables.)

5. **生成 Prisma Client (Generate Prisma Client):**

   - Prisma Client 是一个类型安全的数据库客户端，你将在后端代码中使用它来读写数据库。 (Prisma Client is a type-safe database client that you'll use in your backend code to read and write to the database.)

   - 操作 (Action):

      在终端中 (仍在 

     ```
     api
     ```

      目录下)，运行： (In the terminal (still in the 

     ```
     api
     ```

      directory), run:)

     Bash

     ```
     npx prisma generate
     ```

     - 这个命令会根据你的 `schema.prisma` 生成 Prisma Client 代码到 `node_modules/.prisma/client`。你不需要直接修改那里的代码。 (This command generates Prisma Client code into `node_modules/.prisma/client` based on your `schema.prisma`. You don't need to modify the code there directly.)

**A3. 创建用户注册 API 端点 (Create User Registration API Endpoint):**

1. **创建 `register.ts` 文件 (Create `register.ts` file):**

   - 操作 (Action):

      在 

     ```
     MianshiJun_AI_Project/api/
     ```

      文件夹内，创建一个名为 

     ```
     auth
     ```

      的新文件夹。然后在 

     ```
     api/auth/
     ```

      文件夹内，创建一个名为 

     ```
     register.ts
     ```

      的文件。 (Inside the 

     ```
     MianshiJun_AI_Project/api/
     ```

      folder, create a new folder named 

     ```
     auth
     ```

     . Then, inside the 

     ```
     api/auth/
     ```

      folder, create a file named 

     ```
     register.ts
     ```

     .)

     - 路径应该是：`MianshiJun_AI_Project/api/auth/register.ts` (The path should be: `MianshiJun_AI_Project/api/auth/register.ts`)

2. **编写注册 API 逻辑 (Write Registration API Logic):**

   - 操作 (Action):

      打开 

     ```
     api/auth/register.ts
     ```

      并粘贴以下代码： (Open 

     ```
     api/auth/register.ts
     ```

      and paste the following code:)

     TypeScript

     ```
     import type { VercelRequest, VercelResponse } from '@vercel/node';
     import { PrismaClient } from '@prisma/client';
     import bcrypt from 'bcryptjs';
     
     const prisma = new PrismaClient();
     
     export default async function handler(
       request: VercelRequest,
       response: VercelResponse,
     ) {
       if (request.method !== 'POST') {
         return response.status(405).json({ message: 'Only POST requests allowed' });
       }
     
       try {
         const { email, password, name } = request.body;
     
         // 1. 基本校验 (Basic Validation)
         if (!email || !password) {
           return response.status(400).json({ message: 'Email and password are required' });
         }
         // 你可以在这里添加更复杂的校验，比如邮箱格式、密码长度等
         // You can add more complex validation here, e.g., email format, password length
     
         // 2. 检查邮箱是否已存在 (Check if email already exists)
         const existingUser = await prisma.user.findUnique({
           where: { email },
         });
     
         if (existingUser) {
           return response.status(409).json({ message: 'Email already in use' }); // 409 Conflict
         }
     
         // 3. 哈希密码 (Hash the password)
         const hashedPassword = await bcrypt.hash(password, 10); // 10 is the salt rounds
     
         // 4. 创建新用户并初始化余额 (Create new user and initialize balance)
         const newUser = await prisma.user.create({
           data: {
             email,
             password: hashedPassword,
             name: name || null, // 如果提供了名字就用，否则为null (Use name if provided, else null)
             balance: { // 同时创建关联的 UserBalance 记录 (Concurrently create the related UserBalance record)
               create: {
                 // 在这里设置初始的面试次数或面巾余额，例如：
                 // Set initial interview credits or Mianshijin balance here, e.g.:
                 mockInterviewCredits: 2, // 赠送2次模拟面试 (Gift 2 mock interview credits)
                 formalInterviewCredits: 1, // 赠送1次正式面试 (Gift 1 formal interview credit)
                 mianshijinBalance: 100,  // 赠送100面巾 (Gift 100 Mianshijin)
               },
             },
           },
           include: { // 同时返回创建的balance信息 (Also return created balance info)
             balance: true,
           }
         });
     
         // 为了安全，不要在响应中返回密码哈希
         // For security, do not return password hash in the response
         const { password: _, ...userWithoutPassword } = newUser;
     
         return response.status(201).json({
           success: true,
           message: 'User registered successfully',
           data: userWithoutPassword,
         });
     
       } catch (error) {
         console.error('Registration error:', error);
         // 检查是否是 Prisma 相关的已知错误 (Check if it's a known Prisma error)
         if (error instanceof Error) {
              return response.status(500).json({ message: 'Internal server error', error: error.message });
         }
         return response.status(500).json({ message: 'Internal server error' });
       } finally {
         await prisma.$disconnect(); // 关闭 Prisma Client 连接 (Close Prisma Client connection)
       }
     }
     ```

     - 解释 (Explanation):

       - 导入必要的模块：Vercel 类型、Prisma Client 和 bcryptjs。 (Import necessary modules: Vercel types, Prisma Client, and bcryptjs.)

       - 创建 PrismaClient 实例。 (Create a PrismaClient instance.)

       - ```
         handler
         ```

          函数处理请求： (The 

         ```
         handler
         ```

          function processes requests:)

         - 检查请求方法是否为 `POST`。 (Checks if the request method is `POST`.)
         - 从请求体中获取 `email`, `password`, `name`。 (Gets `email`, `password`, `name` from the request body.)
         - **基本校验**：确保邮箱和密码已提供。 (Basic validation: ensure email and password are provided.)
         - **检查邮箱是否已存在**：查询数据库看该邮箱是否已被注册。 (Check if email exists: query the database to see if the email is already registered.)
         - **哈希密码**：使用 `bcrypt.hash` 对用户密码进行加密处理。 (Hash password: encrypt the user's password using `bcrypt.hash`.)
         - **创建新用户并初始化余额**：使用 `prisma.user.create` 将用户信息存入 `users` 表，并**同时**通过嵌套写入 (`balance: { create: { ... } }`) 在 `user_balances` 表中创建关联的初始余额记录。 (Create new user and initialize balance: use `prisma.user.create` to save user info to the `users` table and **concurrently** create the associated initial balance record in the `user_balances` table via nested write (`balance: { create: { ... } }`).)
         - 返回成功或错误响应。 (Return success or error response.)
         - `finally { await prisma.$disconnect(); }`: 确保在函数结束时关闭数据库连接，这在 Serverless 环境中很重要。 (Ensures the database connection is closed when the function ends, which is important in a serverless environment.)

------

### Part B: 前端工作 (React - User Registration Page)

**B1. 安装前端表单和校验库 (Install Frontend Form and Validation Libraries):**

1. 安装 `react-hook-form` 和 `zod` (Install `react-hook-form` and `zod`):

   - `react-hook-form` 用于简化表单处理。 (`react-hook-form` is for simplifying form handling.)

   - `zod` 用于定义数据校验规则。 (`zod` is for defining data validation schemas.)

   - 操作 (Action):

     1. 在 Cursor 的终端中，确保你**当前目录是 `frontend` 文件夹**。 (In Cursor's terminal, make sure you are in the `frontend` directory.)

     2. 运行以下命令 (Run the following command):

        Bash

        ```
        npm install react-hook-form zod @hookform/resolvers
        ```

        - `@hookform/resolvers` 是让 `react-hook-form` 能和 `zod` 一起工作的桥梁。 (`@hookform/resolvers` is the bridge to make `react-hook-form` work with `zod`.)

**B2. 创建可复用的表单组件 (Create Reusable Form Components - 简化版/Simplified):**

根据 `design_system.md`，我们需要 `InputField.tsx` 和 `Button.tsx`。我们先创建它们的简化版本。

1. **创建 `frontend/src/components/ui/` 文件夹 (Create `frontend/src/components/ui/` folder):**

   - 为了更好地组织通用的UI组件，我们创建一个 `ui` 子文件夹。 (To better organize common UI components, let's create a `ui` subfolder.)
   - **操作 (Action):** 在 `frontend/src/components/` 目录下创建一个名为 `ui` 的新文件夹。 (In the `frontend/src/components/` directory, create a new folder named `ui`.)

2. **创建 `frontend/src/components/ui/InputField.tsx`:**

   - 操作 (Action):

      在 

     ```
     frontend/src/components/ui/
     ```

      文件夹内创建 

     ```
     InputField.tsx
     ```

      并粘贴以下内容： (Inside 

     ```
     frontend/src/components/ui/
     ```

     , create 

     ```
     InputField.tsx
     ```

      and paste the following:)

     TypeScript

     ```
     import React from 'react';
     
     interface InputFieldProps extends React.InputHTMLAttributes<HTMLInputElement> {
       label: string;
       error?: string; // 用于显示校验错误 (For displaying validation errors)
     }
     
     const InputField = React.forwardRef<HTMLInputElement, InputFieldProps>(
       ({ label, id, error, ...props }, ref) => {
         return (
           <div className="mb-4">
             <label htmlFor={id} className="block text-sm font-medium text-gray-700 mb-1">
               {label}
             </label>
             <input
               id={id}
               ref={ref}
               className={`block w-full px-3 py-2 border ${
                 error ? 'border-red-500' : 'border-gray-300'
               } rounded-md shadow-sm focus:outline-none focus:ring-sky-500 focus:border-sky-500 sm:text-sm`}
               {...props}
             />
             {error && <p className="mt-1 text-xs text-red-600">{error}</p>}
           </div>
         );
       }
     );
     
     InputField.displayName = 'InputField'; // 好习惯，为了React DevTools (Good practice for React DevTools)
     export default InputField;
     ```

3. **创建 `frontend/src/components/ui/Button.tsx`:**

   - 操作 (Action):

      在 

     ```
     frontend/src/components/ui/
     ```

      文件夹内创建 

     ```
     Button.tsx
     ```

      并粘贴以下内容： (Inside 

     ```
     frontend/src/components/ui/
     ```

     , create 

     ```
     Button.tsx
     ```

      and paste the following:)

     TypeScript

     ```
     import React from 'react';
     
     interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
       variant?: 'primary' | 'secondary'; // 按钮样式变体 (Button style variants)
       isLoading?: boolean; // 加载状态 (Loading state)
     }
     
     const Button: React.FC<ButtonProps> = ({
       children,
       className,
       variant = 'primary',
       isLoading = false,
       disabled,
       ...props
     }) => {
       const baseStyle = "font-medium py-2 px-4 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors duration-150";
       const primaryStyle = "bg-sky-500 hover:bg-sky-600 text-white focus:ring-sky-500";
       const secondaryStyle = "bg-gray-200 hover:bg-gray-300 text-gray-800 focus:ring-gray-400";
       const disabledStyle = "disabled:opacity-50 disabled:cursor-not-allowed";
     
       const variantStyle = variant === 'primary' ? primaryStyle : secondaryStyle;
     
       return (
         <button
           className={`${baseStyle} ${variantStyle} ${disabledStyle} ${className || ''}`}
           disabled={disabled || isLoading}
           {...props}
         >
           {isLoading ? (
             <div className="animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-current mx-auto"></div>
           ) : (
             children
           )}
         </button>
       );
     };
     
     export default Button;
     ```

**B3. 实现注册页面 (`RegisterPage.tsx`) (Implement Registration Page):**

1. 修改 `frontend/src/pages/RegisterPage.tsx` (Modify `frontend/src/pages/RegisterPage.tsx`):

   - 操作 (Action):

      打开 

     ```
     frontend/src/pages/RegisterPage.tsx
     ```

      文件，将其内容替换为以下代码： (Open 

     ```
     frontend/src/pages/RegisterPage.tsx
     ```

      and replace its content with the following:)

     TypeScript

     ```
     import React, { useState } from 'react';
     import { useForm, SubmitHandler } from 'react-hook-form';
     import { zodResolver } from '@hookform/resolvers/zod';
     import { z } from 'zod';
     import { Link, useNavigate } from 'react-router-dom'; // 用于跳转 (For navigation)
     import InputField from '../components/ui/InputField'; // 我们创建的输入框组件 (Our InputField component)
     import Button from '../components/ui/Button';     // 我们创建的按钮组件 (Our Button component)
     
     // 定义表单数据的结构和校验规则 (Define form data structure and validation rules)
     const registerSchema = z.object({
       name: z.string().min(1, { message: "名字不能为空 (Name is required)" }).optional(), // 名字可选 (Name is optional)
       email: z.string().min(1, { message: "邮箱不能为空 (Email is required)" }).email({ message: "请输入有效的邮箱地址 (Invalid email address)" }),
       password: z.string().min(6, { message: "密码至少需要6位 (Password must be at least 6 characters)" }),
       confirmPassword: z.string().min(1, { message: "请再次输入密码 (Confirm password is required)" })
     }).refine(data => data.password === data.confirmPassword, { // 校验两次密码是否一致 (Check if passwords match)
       message: "两次输入的密码不一致 (Passwords do not match)",
       path: ["confirmPassword"], // 在哪个字段上显示错误 (Show error on this field)
     });
     
     type RegisterFormValues = z.infer<typeof registerSchema>; // 从 schema 推断类型 (Infer type from schema)
     
     const RegisterPage: React.FC = () => {
       const navigate = useNavigate(); // 用于注册成功后跳转 (For navigating after successful registration)
       const [serverError, setServerError] = useState<string | null>(null); // 用于显示后端返回的错误 (For displaying backend errors)
       const [isLoading, setIsLoading] = useState(false); // 加载状态 (Loading state)
     
       const { register, handleSubmit, formState: { errors } } = useForm<RegisterFormValues>({
         resolver: zodResolver(registerSchema), // 使用 Zod 进行校验 (Use Zod for validation)
       });
     
       const onSubmit: SubmitHandler<RegisterFormValues> = async (data) => {
         setIsLoading(true);
         setServerError(null);
         // console.log("表单数据 (Form data):", data); // 调试用 (For debugging)
     
         try {
           const response = await fetch('/api/auth/register', { // 注意: Vercel dev 会代理这个路径 (Note: Vercel dev will proxy this path)
             method: 'POST',
             headers: {
               'Content-Type': 'application/json',
             },
             body: JSON.stringify({
               email: data.email,
               password: data.password,
               name: data.name || '', // 如果 name 是 undefined，传空字符串 (Send empty string if name is undefined)
             }),
           });
     
           const result = await response.json();
           setIsLoading(false);
     
           if (response.ok && result.success) {
             alert('注册成功！请登录。 (Registration successful! Please log in.)'); // 简单提示 (Simple alert)
             navigate('/login'); // 跳转到登录页 (Navigate to login page)
           } else {
             setServerError(result.message || '注册失败，请稍后再试。 (Registration failed, please try again later.)');
           }
         } catch (error) {
           setIsLoading(false);
           console.error("注册请求错误 (Registration request error):", error);
           setServerError('网络错误或服务器无响应。 (Network error or server not responding.)');
         }
       };
     
       return (
         <div className="flex items-center justify-center min-h-screen bg-gray-50 px-4 sm:px-6 lg:px-8">
           <div className="max-w-md w-full space-y-8 p-8 bg-white shadow-lg rounded-xl">
             <div>
               {/* 你可以在这里放 Logo (You can put your Logo here) */}
               <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
                 创建您的面试君账户 (Create your MianshiJun Account)
               </h2>
               <p className="mt-2 text-center text-sm text-gray-600">
                 已有账户？ (Already have an account?)
                 {' '}
                 <Link to="/login" className="font-medium text-sky-600 hover:text-sky-500">
                   点此登录 (Sign in here)
                 </Link>
               </p>
             </div>
             <form className="mt-8 space-y-6" onSubmit={handleSubmit(onSubmit)}>
               {serverError && ( // 显示后端错误 (Display backend error)
                 <div className="p-3 bg-red-100 border border-red-400 text-red-700 rounded-md text-sm">
                   {serverError}
                 </div>
               )}
               <InputField
                 id="name"
                 label="昵称 (Optional Name)"
                 type="text"
                 autoComplete="name"
                 {...register("name")} // react-hook-form 注册 (Register with react-hook-form)
                 error={errors.name?.message}
               />
               <InputField
                 id="email"
                 label="邮箱地址 (Email address)"
                 type="email"
                 autoComplete="email"
                 required
                 {...register("email")}
                 error={errors.email?.message}
               />
               <InputField
                 id="password"
                 label="密码 (Password)"
                 type="password"
                 autoComplete="new-password"
                 required
                 {...register("password")}
                 error={errors.password?.message}
               />
               <InputField
                 id="confirmPassword"
                 label="确认密码 (Confirm Password)"
                 type="password"
                 autoComplete="new-password"
                 required
                 {...register("confirmPassword")}
                 error={errors.confirmPassword?.message}
               />
               <div>
                 <Button type="submit" className="w-full" isLoading={isLoading} disabled={isLoading}>
                   {isLoading ? '注册中... (Registering...)' : '立即注册 (Sign up)'}
                 </Button>
               </div>
             </form>
           </div>
         </div>
       );
     };
     
     export default RegisterPage;
     ```

     - 解释 (Explanation):

       - 导入了 `react-hook-form`, `zod`, `@hookform/resolvers`, `react-router-dom` 和我们创建的UI组件。 (Imported `react-hook-form`, `zod`, `@hookform/resolvers`, `react-router-dom`, and our UI components.)

       - `registerSchema`: 使用 `zod` 定义了表单字段的校验规则，包括非空、邮箱格式、密码长度以及两次密码输入是否一致。 (Used `zod` to define validation rules for form fields, including non-empty, email format, password length, and password confirmation match.)

       - `useForm`: `react-hook-form` 的核心 Hook，用于管理表单状态、注册输入框和处理提交。 (Core Hook from `react-hook-form` for managing form state, registering inputs, and handling submission.)

       - ```
         onSubmit
         ```

         : 当表单校验通过后被调用的函数。它会： (Function called after form validation passes. It will:)

         - 设置加载状态 `isLoading`。 (Set loading state `isLoading`.)

         - 清除之前的服务器错误 `serverError`。 (Clear previous server errors `serverError`.)

         - 使用 

           ```
           Workspace
           ```

            API 向 

           ```
           /api/auth/register
           ```

            发送 

           ```
           POST
           ```

            请求，请求体包含表单数据。 (Use 

           ```
           Workspace
           ```

            API to send a 

           ```
           POST
           ```

            request to 

           ```
           /api/auth/register
           ```

            with form data in the body.)

           - **重要 (Important):** 当你使用 `vercel dev` 在本地运行后端时，它会自动代理 `/api/*` 的请求到你的 `api` 文件夹下的函数。所以前端可以直接写 `/api/auth/register`。 (When you run the backend locally with `vercel dev`, it automatically proxies requests to `/api/*` to your functions in the `api` folder. So, the frontend can directly use `/api/auth/register`.)

         - 处理后端返回的响应：成功则提示并跳转到登录页，失败则显示错误信息。 (Handle backend response: on success, alert and navigate to login page; on failure, display error message.)

       - **UI 结构**: 使用了 `InputField` 和 `Button` 组件，并用 Tailwind CSS 进行了一些基础的页面美化，使其看起来像一个居中的注册卡片。 (Used `InputField` and `Button` components and some basic page styling with Tailwind CSS to make it look like a centered registration card.)

       - **错误显示**: 前端校验错误会显示在对应输入框下方，后端返回的错误会显示在表单顶部。 (Frontend validation errors are shown below respective input fields, backend errors are shown at the top of the form.)

------

### Part C: 测试用户注册功能 (Test User Registration Functionality)

1. **启动本地开发服务器 (Start Local Development Servers):**
   - 操作 (Action):
     1. 启动前端服务器 (Start Frontend Server):
        - 打开一个终端。 (Open a terminal.)
        - 导航到 `MianshiJun_AI_Project/frontend/` 目录。 (Navigate to `MianshiJun_AI_Project/frontend/` directory.)
        - 运行 `npm run dev`。 (Run `npm run dev`.)
        - 它通常会在 `http://localhost:5173` 启动。 (It usually starts on `http://localhost:5173`.)
     2. 启动后端模拟服务器 (Start Backend Simulation Server):
        - 打开**另一个**新的终端。 (Open **another** new terminal.)
        - 导航到**总项目文件夹 `MianshiJun_AI_Project` 的根目录**。 (Navigate to the **root of your main project directory `MianshiJun_AI_Project`**.)
        - 运行 `vercel dev`。 (Run `vercel dev`.)
        - 它通常会在 `http://localhost:3000` 启动，并且会代理 `/api` 请求到你的 `api` 文件夹。 (It usually starts on `http://localhost:3000` and will proxy `/api` requests to your `api` folder.)
2. **测试注册流程 (Test Registration Flow):**
   - 操作 (Action):
     1. 在浏览器中打开前端地址，例如 `http://localhost:5173/`。 (Open the frontend address in your browser, e.g., `http://localhost:5173/`.)
     2. 导航到注册页面 (通常是 `/register`，如果你在 `Header.tsx` 中添加了链接，可以点击它，或者直接在浏览器地址栏输入 `http://localhost:5173/register`)。 (Navigate to the registration page (usually `/register`; if you added a link in `Header.tsx`, click it, or type `http://localhost:5173/register` directly in the browser's address bar).)
     3. 测试前端校验 (Test Frontend Validation):
        - 不输入任何内容，直接点击“立即注册”按钮，看是否提示错误。 (Click "Sign up" without entering anything to see if errors are shown.)
        - 输入无效的邮箱格式，看是否提示错误。 (Enter an invalid email format to see if errors are shown.)
        - 输入少于6位的密码，看是否提示错误。 (Enter a password less than 6 characters to see if errors are shown.)
        - 输入两次不一致的密码，看是否提示错误。 (Enter two different passwords to see if errors are shown.)
     4. 测试成功注册 (Test Successful Registration):
        - 输入一个有效的、**之前未注册过**的邮箱和符合要求的密码（两次输入一致）。 (Enter a valid, **previously unregistered** email and a compliant password (entered identically twice).)
        - （可选）输入一个昵称。 (Optionally, enter a name.)
        - 点击“立即注册”按钮。 (Click the "Sign up" button.)
        - 预期结果 (Expected Result):
          - 按钮显示加载状态。 (Button shows loading state.)
          - 后端API (`/api/auth/register`) 被调用（你可以在运行 `vercel dev` 的终端中看到请求日志）。 (Backend API (`/api/auth/register`) is called (you can see request logs in the terminal running `vercel dev`).)
          - 数据库中 `users` 表和 `user_balances` 表成功创建记录。你可以登录 Neon 控制台查看数据。 (A record is successfully created in the `users` table and `user_balances` table in your database. You can log in to the Neon console to check the data.)
          - 前端弹出“注册成功！请登录。”的提示，并自动跳转到登录页面 (`/login`)。 (Frontend shows an alert "Registration successful! Please log in." and automatically navigates to the login page (`/login`).)
     5. 测试邮箱已存在的情况 (Test Email Already Exists Scenario):
        - 再次使用刚刚成功注册的邮箱尝试注册。 (Try to register again using the same email you just successfully registered.)
        - **预期结果 (Expected Result):** 表单顶部应该显示类似“Email already in use”的错误信息。 (The form should display an error message like "Email already in use" at the top.)

------

### Part D: 提交代码到 GitHub (Commit Code to GitHub)

完成以上所有步骤并且测试通过后，将你的代码提交到 GitHub。

1. 检查状态并添加更改 (Check status and add changes):

   - 操作 (Action):

     1. 在终端中，确保你位于**总项目文件夹 `MianshiJun_AI_Project` 的根目录**。 (In the terminal, make sure you are in the **root of your main project directory `MianshiJun_AI_Project`**.)

     2. 输入 `git status`。 (Type `git status`.)

     3. 添加所有更改： (Add all changes:)

        Bash

        ```
        git add .
        ```

2. 提交更改 (Commit changes):

   - 操作 (Action):

     Bash

     ```
     git commit -m "Feat(auth): Implement Task 1.2 - User Registration Functionality (frontend & backend)"
     ```

     (提交信息意思是：“功能(认证)：实现任务1.2 - 用户注册功能 (前端和后端)”) (This commit message means: "Feature(auth): Implement Task 1.2 - User Registration Functionality (frontend & backend)")

3. 推送到 GitHub (Push to GitHub):

   - 操作 (Action):

     Bash

     ```
     git push origin main
     ```

------

**恭喜！(Congratulations!)**

完成这些步骤后，你的“面试君”项目就具备了基本的用户注册功能了！这是一个非常重要的里程碑。

**下一步 (Next Step):**

- 根据开发计划，是 **任务 1.3: 用户登录功能**。 (According to the development plan, it's **Task 1.3: User Login Functionality**.)

请您一步步按照清单操作，如果在任何步骤遇到问题或不清楚的地方，随时告诉我！Vibe coding 愉快！

(Please follow the checklist step by step. If you encounter any issues or anything is unclear at any step, feel free to let me know! Happy vibe coding!)