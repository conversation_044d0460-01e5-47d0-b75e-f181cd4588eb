import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useNavigate } from 'react-router-dom';

import Input from '../ui/Input';
import Button from '../ui/Button';
import { registerSchema, RegisterFormValues } from '@new-mianshijun/common';
import { registerUser } from '../../lib/api/auth';

const RegisterForm: React.FC = () => {
  const navigate = useNavigate();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [serverError, setServerError] = useState<string | null>(null);

  const { 
    register, 
    handleSubmit, 
    formState: { errors } 
  } = useForm<RegisterFormValues>({
    resolver: zodResolver(registerSchema),
    defaultValues: {
      email: '',
      password: '',
      confirmPassword: '',
      name: '',
    }
  });

  const onSubmit = async (data: RegisterFormValues) => {
    setIsSubmitting(true);
    setServerError(null);

    try {
      await registerUser(data);
      // 注册成功，重定向到登录页面 (Registration successful, redirect to login page)
      navigate('/login', { 
        state: { 
          message: '注册成功！请登录您的账户。', 
          type: 'success' 
        } 
      });
    } catch (error) {
      // 处理注册错误 (Handle registration error)
      if (error instanceof Error) {
        setServerError(error.message);
      } else {
        setServerError('注册失败，请稍后再试');
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="bg-white p-8 rounded-lg shadow-md w-full max-w-md mx-auto">
      <h2 className="text-2xl font-bold text-center text-gray-800 mb-6">注册账户</h2>
      
      {serverError && (
        <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
          {serverError}
        </div>
      )}
      
      <form onSubmit={handleSubmit(onSubmit)} noValidate>
        <Input
          id="email"
          label="邮箱"
          type="email"
          placeholder="请输入您的邮箱"
          error={errors.email?.message}
          {...register('email')}
        />
        
        <Input
          id="name"
          label="姓名（选填）"
          type="text"
          placeholder="请输入您的姓名"
          error={errors.name?.message}
          {...register('name')}
        />
        
        <Input
          id="password"
          label="密码"
          type="password"
          placeholder="请设置密码"
          error={errors.password?.message}
          {...register('password')}
        />
        
        <Input
          id="confirmPassword"
          label="确认密码"
          type="password"
          placeholder="请再次输入密码"
          error={errors.confirmPassword?.message}
          {...register('confirmPassword')}
        />
        
        <Button
          type="submit"
          fullWidth
          isLoading={isSubmitting}
          className="mt-6"
        >
          注册
        </Button>
        
        <div className="mt-4 text-center text-sm text-gray-600">
          已有账户？{' '}
          <a href="/login" className="text-blue-600 hover:underline">
            立即登录
          </a>
        </div>
      </form>
    </div>
  );
};

export default RegisterForm; 