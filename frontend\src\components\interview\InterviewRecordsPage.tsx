import React, { useState, useEffect } from 'react';
import { Calendar, Clock, FileText, Star, ArrowLeft, Co<PERSON>, Check } from 'lucide-react';
import { getInterviewReview, InterviewReviewData, TranscriptEntry } from '../../lib/api/interview';
import { interviewService, InterviewRecord } from '../../lib/api/apiService';

interface InterviewRecordsPageProps {
  mode?: 'live' | 'mock';
}

const InterviewRecordsPage: React.FC<InterviewRecordsPageProps> = ({ mode = 'live' }) => {
  // 状态管理
  const [currentView, setCurrentView] = useState<'list' | 'detail'>('list');
  const [selectedSessionId, setSelectedSessionId] = useState<string | null>(null);
  const [reviewData, setReviewData] = useState<InterviewReviewData | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [copied, setCopied] = useState<boolean>(false);

  // 面试记录数据
  const [records, setRecords] = useState<InterviewRecord[]>([]);
  const [recordsLoading, setRecordsLoading] = useState<boolean>(true);
  const [recordsError, setRecordsError] = useState<string | null>(null);

  // 保留模拟数据作为备用（用于*****************账号）
  const mockRecords: InterviewRecord[] = [
    {
      id: '1',
      position: 'Java研发工程师',
      company: '浙江飞猪网络技术有限公司',
      date: '2024-12-18',
      duration: '45分钟',
      score: 85,
      status: 'completed'
    },
    {
      id: '2',
      position: '前端开发工程师',
      company: '阿里巴巴集团',
      date: '2024-12-15',
      duration: '38分钟',
      score: 78,
      status: 'completed'
    },
    {
      id: '3',
      position: '产品经理',
      company: '腾讯科技',
      date: '2024-12-12',
      duration: '52分钟',
      score: 92,
      status: 'completed'
    }
  ];

  // 获取面试记录列表
  useEffect(() => {
    const fetchRecords = async () => {
      setRecordsLoading(true);
      setRecordsError(null);

      try {
        const data = await interviewService.getInterviewRecords();
        // 如果API返回空数据，使用模拟数据（保持现有账号数据）
        if (data.length === 0) {
          setRecords(mockRecords);
        } else {
          setRecords(data);
        }
      } catch (err) {
        console.error('Failed to fetch interview records:', err);
        // 如果API调用失败，使用模拟数据作为备用
        setRecords(mockRecords);
        if (err instanceof Error) {
          setRecordsError(`获取面试记录失败: ${err.message}`);
        } else {
          setRecordsError('获取面试记录失败，请稍后重试');
        }
      } finally {
        setRecordsLoading(false);
      }
    };

    fetchRecords();
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-green-600 bg-green-50';
      case 'in-progress':
        return 'text-blue-600 bg-blue-50';
      case 'cancelled':
        return 'text-red-600 bg-red-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return '已完成';
      case 'in-progress':
        return '进行中';
      case 'cancelled':
        return '已取消';
      default:
        return '未知';
    }
  };

  // 处理查看报告按钮点击
  const handleViewReport = async (sessionId: string) => {
    setLoading(true);
    setError(null);
    setSelectedSessionId(sessionId);

    try {
      const data = await getInterviewReview(sessionId);
      setReviewData(data);
      setCurrentView('detail');
    } catch (err) {
      console.error('Failed to fetch interview review:', err);
      if (err instanceof Error) {
        setError(`获取面试回顾失败: ${err.message}`);
      } else {
        setError('获取面试回顾失败，请稍后重试');
      }
    } finally {
      setLoading(false);
    }
  };

  // 返回列表视图
  const handleBackToList = () => {
    setCurrentView('list');
    setSelectedSessionId(null);
    setReviewData(null);
    setError(null);
    setCopied(false);
  };

  // 复制会话ID到剪贴板
  const handleCopySessionId = async (sessionId: string) => {
    try {
      await navigator.clipboard.writeText(sessionId);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000); // 2秒后重置状态
    } catch (err) {
      console.error('Failed to copy session ID:', err);
    }
  };

  // 截断会话ID显示
  const truncateSessionId = (sessionId: string, maxLength: number = 20) => {
    if (sessionId.length <= maxLength) return sessionId;
    return sessionId.substring(0, maxLength) + '...';
  };

  // 渲染详情视图
  const renderDetailView = () => {
    if (loading) {
      return (
        <div className="flex items-center justify-center min-h-96">
          <div className="text-lg text-gray-600">正在加载面试回顾...</div>
        </div>
      );
    }

    if (error) {
      return (
        <div className="flex items-center justify-center min-h-96">
          <div className="text-red-500 text-lg">{error}</div>
        </div>
      );
    }

    if (!reviewData) {
      return (
        <div className="flex items-center justify-center min-h-96">
          <div className="text-gray-500 text-lg">未找到此次面试的回顾数据</div>
        </div>
      );
    }

    return (
      <div className="space-y-6">
        {/* 面试基本信息 */}
        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">面试信息</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <span className="text-gray-500">会话ID:</span>
              <div className="flex items-center gap-2">
                <div
                  className="font-medium cursor-pointer"
                  title={reviewData.sessionId}
                >
                  {truncateSessionId(reviewData.sessionId)}
                </div>
                <button
                  onClick={() => handleCopySessionId(reviewData.sessionId)}
                  className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
                  title="复制会话ID"
                >
                  {copied ? <Check className="w-3 h-3 text-green-500" /> : <Copy className="w-3 h-3" />}
                </button>
              </div>
            </div>
            {reviewData.startedAt && (
              <div>
                <span className="text-gray-500">开始时间:</span>
                <div className="font-medium">{new Date(reviewData.startedAt).toLocaleString()}</div>
              </div>
            )}
            {reviewData.endedAt && (
              <div>
                <span className="text-gray-500">结束时间:</span>
                <div className="font-medium">{new Date(reviewData.endedAt).toLocaleString()}</div>
              </div>
            )}
            {reviewData.status && (
              <div>
                <span className="text-gray-500">状态:</span>
                <div className="font-medium">{reviewData.status}</div>
              </div>
            )}
          </div>

          {/* 岗位和公司信息 */}
          {(reviewData.company || reviewData.position) && (
            <div className="mt-4 pt-4 border-t border-gray-200">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                {reviewData.company && (
                  <div>
                    <span className="text-gray-500">公司:</span>
                    <div className="font-medium">{reviewData.company}</div>
                  </div>
                )}
                {reviewData.position && (
                  <div>
                    <span className="text-gray-500">岗位:</span>
                    <div className="font-medium">{reviewData.position}</div>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        {/* 面试记录和AI建议 */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* 面试记录部分 */}
          <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">面试记录</h2>
            {reviewData.transcripts.length > 0 ? (
              <div className="relative">
                <div className="space-y-4 max-h-[500px] overflow-y-auto pr-2 custom-scrollbar">
                  {reviewData.transcripts.map((item: TranscriptEntry, index: number) => (
                  <div
                    key={`transcript-${index}`}
                    className={`p-3 rounded-md ${
                      item.speaker === 'interviewer'
                        ? 'bg-blue-50 text-blue-800 border-l-4 border-blue-400'
                        : 'bg-green-50 text-green-800 border-l-4 border-green-400'
                    }`}
                  >
                    <div className="flex justify-between items-start mb-1">
                      <strong className="font-medium text-sm">
                        {item.speaker === 'interviewer' ? '面试官' : '候选人'}:
                      </strong>
                      <span className="text-xs text-gray-400">
                        {new Date(item.timestamp).toLocaleTimeString()}
                      </span>
                    </div>
                    <p className="whitespace-pre-wrap text-sm leading-relaxed">{item.content}</p>
                  </div>
                ))}
                </div>
              </div>
            ) : (
              <p className="text-gray-500 text-center py-8">此次面试暂无记录数据</p>
            )}
          </div>

          {/* AI建议部分 */}
          <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">AI建议</h2>
            {/* 为未来的AI建议预留滚动容器 */}
            <div className="relative">
              <div className="max-h-[500px] overflow-y-auto pr-2 custom-scrollbar">
                <div className="text-center py-8">
                  <p className="text-gray-500 mb-2">AI建议功能开发中...</p>
                  <p className="text-xs text-gray-400">敬请期待更智能的面试建议</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="h-full p-8 bg-gray-50">
      <div className="max-w-6xl mx-auto">
        {/* 标题栏 - 根据当前视图显示不同内容 */}
        <div className="mb-6">
          {currentView === 'list' ? (
            <>
              <h1 className="text-2xl font-bold text-gray-900 mb-2">面试记录</h1>
              <p className="text-gray-600">查看您的面试历史记录和表现分析</p>
            </>
          ) : (
            <div className="flex items-center gap-4">
              <button
                onClick={handleBackToList}
                className="flex items-center gap-2 px-3 py-2 text-gray-600 hover:text-gray-900 transition-colors"
              >
                <ArrowLeft className="w-4 h-4" />
                返回列表
              </button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900 mb-2">面试回顾</h1>
                <p className="text-gray-600">详细的面试记录和分析报告</p>
              </div>
            </div>
          )}
        </div>

        {/* 内容区域 - 根据当前视图显示列表或详情 */}
        {currentView === 'list' ? (
          <div className="bg-white rounded-xl shadow-sm border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-semibold text-gray-900">最近面试</h2>
              <div className="flex items-center gap-4">
                <select className="px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                  <option>全部状态</option>
                  <option>已完成</option>
                  <option>进行中</option>
                  <option>已取消</option>
                </select>
                <select className="px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                  <option>最近30天</option>
                  <option>最近7天</option>
                  <option>最近3个月</option>
                </select>
              </div>
            </div>
          </div>

          {/* 加载状态 */}
          {recordsLoading && (
            <div className="p-12 text-center">
              <div className="text-lg text-gray-600">正在加载面试记录...</div>
            </div>
          )}

          {/* 错误状态 */}
          {recordsError && (
            <div className="p-12 text-center">
              <div className="text-red-500 text-lg">{recordsError}</div>
            </div>
          )}

          {/* 面试记录列表 */}
          {!recordsLoading && !recordsError && (
            <div className="max-h-[500px] overflow-y-auto custom-scrollbar">
              <div className="divide-y divide-gray-200">
                {records.map((record) => (
              <div key={record.id} className="p-6 hover:bg-gray-50 transition-colors">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h3 className="text-lg font-medium text-gray-900">{record.company}</h3>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(record.status)}`}>
                        {getStatusText(record.status)}
                      </span>
                    </div>
                    <p className="text-gray-600 mb-3">{record.position}</p>
                    
                    <div className="flex items-center gap-6 text-sm text-gray-500">
                      <div className="flex items-center gap-1">
                        <Calendar className="w-4 h-4" />
                        <span>{record.date}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Clock className="w-4 h-4" />
                        <span>{record.duration}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Star className="w-4 h-4" />
                        <span>评分: {record.score}/100</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-3">
                    <div className="text-right">
                      <div className="text-2xl font-bold text-gray-900">{record.score}</div>
                      <div className="text-sm text-gray-500">总分</div>
                    </div>
                    <button
                      onClick={() => handleViewReport(record.id)}
                      disabled={loading && selectedSessionId === record.id}
                      className="flex items-center gap-2 px-4 py-2 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <FileText className="w-4 h-4" />
                      {loading && selectedSessionId === record.id ? '加载中...' : '查看报告'}
                    </button>
                  </div>
                </div>
              </div>
            ))}

              {records.length === 0 && (
                <div className="p-12 text-center">
                  <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <FileText className="w-8 h-8 text-gray-400" />
                  </div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">暂无面试记录</h3>
                  <p className="text-gray-500">开始您的第一次AI面试吧</p>
                </div>
              )}
              </div>
            </div>
          )}
          </div>
        ) : (
          renderDetailView()
        )}
      </div>
    </div>
  );
};

export default InterviewRecordsPage;
