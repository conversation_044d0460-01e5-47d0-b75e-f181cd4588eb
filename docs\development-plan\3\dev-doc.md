# 讯飞与百度自动语音识别（ASR）服务技术解析报告

## I. 引言：讯飞与百度ASR服务概览

自动语音识别（ASR）技术作为人工智能领域的核心组成部分，已广泛应用于众多行业。讯飞（iFlytek）和百度（Baidu）是中国领先的人工智能公司，均提供全面的ASR服务。这些服务涵盖了从实时语音转写到大规模音频文件批处理的多种应用场景，满足了从语音助手、智能客服到会议记录、内容分析等多样化的需求 1。

开发者通常通过应用程序接口（API）来集成这些ASR能力。针对不同的应用需求，这些API在设计上体现出明显的区分：对于需要即时反馈的交互式应用，如语音输入或实时字幕，平台通常提供基于WebSocket的流式接口，以实现低延迟和高吞吐量的数据传输 1。而对于非实时的大批量音频处理任务，则更多采用RESTful API，支持对已录制音频文件的转写 2。这种API设计的二元性要求开发者在项目初期就根据具体用例选择合适的技术路径，这直接影响到后续的系统架构、客户端实现（例如，选择WebSocket库或HTTP客户端库）、错误处理机制（如实时重连逻辑与批量任务重试策略）乃至音频数据的准备方式（例如，流式音频块与完整音频文件）。

无论是讯飞还是百度，其ASR服务均采用受控的访问模型。开发者在调用任何ASR功能之前，都必须在其各自的开放平台上完成一系列的初始设置步骤，包括创建账户、注册应用程序、获取API密钥（如AppID, API Key, Secret Key等）以及可能的IP白名单配置等 1。这一过程是确保服务安全、进行用量跟踪和计费的基础，也意味着开发者在技术集成之外，还需要关注这些平台管理层面的操作。

本报告旨在深入剖析讯飞和百度两家公司提供的ASR服务的关键技术细节，包括但不限于认证机制、音频格式要求、API协议以及错误处理等方面，为技术开发者提供一份详尽的参考指南。

## II. 讯飞（讯飞）ASR服务深度解析

讯飞开放平台提供了多种语音识别服务，其中实时语音转写和语音听写流式版是开发者常用的流式ASR接口。

### A. 实时语音转写 (RTASR) 与语音听写流式版 API

#### 1. 认证机制：签名算法详解

讯飞的流式ASR服务，特别是基于WebSocket的接口，采用了特定的签名机制进行请求认证，以确保通信安全和用户身份验证。根据现有资料，讯飞针对不同的流式服务可能采用了不只一种认证方案，开发者需仔细甄别。

一种针对实时语音转写（RTASR）的签名方法是生成`signa`参数。其核心算法基于HMAC-SHA1和MD5。具体步骤如下 1：

1. 构造基础字符串（baseString）：由应用的`appid`和当前时间戳`ts`拼接而成。例如，若`appid`为`595f23df`，`ts`为`1512041814`，则`baseString`为`595f23df1512041814`。
2. 对`baseString`进行MD5哈希计算。
3. 使用应用密钥`apiKey`作为HMAC-SHA1的密钥，对上一步得到的MD5哈希值进行加密。
4. 将HMAC-SHA1加密后的结果进行Base64编码，生成最终的`signa`值。 此`apiKey`是在讯飞控制台为应用添加实时语音转写服务时自动生成的，需妥善保管 1。

另一种是针对“语音听写（流式版）”服务的WebSocket握手阶段的认证机制。此方法更为贴近标准的WebSocket安全实践，它要求客户端基于HMAC-SHA256算法计算签名，并将签名信息置于`Authorization` HTTP头部进行发送 6。此过程涉及以下要素：

1. 获取接口密钥`APIKey`和`APISecret`（均为32位字符串，在讯飞开放平台控制台查看）。
2. 构造原始签名串（`signature_origin`）：格式为 `host: $host\ndate: $date\nGET $request_line HTTP/1.1`，其中`$host`是请求的主机名，`$date`是RFC1123格式的GMT（或UTC+0）时间戳，`$request_line`是HTTP请求行（例如 `/v2/iat`）。
3. 使用`APISecret`对原始签名串进行HMAC-SHA256加密。
4. 对加密结果进行Base64编码，得到`signature`。
5. 构造`Authorization`头部的值：格式为 `api_key="$APIKey",algorithm="hmac-sha256",headers="host date request-line",signature="$signature"`。
6. 最后，对此`Authorization`字符串整体再进行Base64编码，作为最终发送的`Authorization`头的值。

时间戳在签名生成中扮演关键角色。例如，语音听写流式服务要求`date`参数与服务器时间的偏差不超过300秒（部分全球接口可能为5分钟），超出此范围的请求将被拒绝 6。这要求客户端设备保持准确的时间同步（如通过NTP服务），以避免因时钟偏移导致的认证失败。

下表总结了讯飞流式ASR服务中观察到的不同认证方案：

**表 II.A.1.1:** 讯飞流式ASR认证方案对比

| **服务类型**           | **认证方法**                         | **关键参数**                                    | **核心算法**                        |
| ---------------------- | ------------------------------------ | ----------------------------------------------- | ----------------------------------- |
| 实时语音转写 (RTASR) 1 | `signa` 参数 (可能在URL或初始消息中) | `appid`, `ts` (时间戳), `apiKey`                | `HmacSHA1(MD5(appid + ts), apiKey)` |
| 语音听写（流式版）6    | `Authorization` HTTP Header          | `APIKey`, `APISecret`, `host`, `date` (RFC1123) | `hmac-sha256`                       |
| 全球实时ASR 13         | `Authorization` HTTP Header          | `APIKey`, `APISecret`, `host`, `date` (RFC1123) | `hmac-sha256`                       |



这两种认证机制并存，意味着开发者在集成时必须严格查阅对应服务的最新API文档，确保使用正确的认证流程、参数名（`apiKey` vs `APIKey`/`APISecret`）和加密算法。混淆认证方案将直接导致连接建立失败，通常表现为HTTP 401或403错误 12。

#### 2. 音频格式要求（支持的编码、采样率、声道数、位深）

讯飞的实时流式ASR服务对其接收的音频数据有严格的格式要求，这与主要用于离线处理的录音文件转写服务有所不同。

- **实时语音转写 (RTASR - 全球版) \**13\**:**

  - 支持音频类型：单声道（mono）、16位（16bit）的PCM（脉冲编码调制）音频。
  - 支持采样率：仅16kHz。
  - 编码格式（`encoding`参数）：`raw`。
  - 格式参数（`format`参数）：`audio/L16;rate=16000`。

- **语音听写（流式版）\**6\**:**

  - 采样率：支持16kHz或8kHz。

  - 位深：16bit。

  - 声道：单声道（mono）。

  - 支持编码（

    ```
    encoding
    ```

    参数）：

    - `raw`：原始PCM音频。
    - `speex`：讯飞定制的Speex压缩格式（用于8kHz采样率）。
    - `speex-wb`：讯飞定制的Speex宽带压缩格式（用于16kHz采样率）。
    - `lame`：MP3格式（仅支持中文普通话和英文识别，其他方言及小语种不支持）。

  - 格式参数（`format`参数）：例如 `audio/L16;rate=16000` 或 `audio/L16;rate=8000`。

相较之下，讯飞的**录音文件转写服务（非实时）**对音频格式的包容性要大得多：

- **大文件ASR (LFASR) \**14\**:** 支持16kHz或8kHz采样率，8bit或16bit位深，单声道及多声道。接受`wav`, `flac`, `opus`, `m4a`, `mp3`等格式，文件大小不超过500MB。
- **新版录音文件转写 \**2\**:** 支持16kHz或8kHz采样率，8bit或16bit位深，单声道及多声道。支持格式非常广泛，包括`mp3`, `wav`, `pcm`, `aac`, `opus`, `flac`, `ogg`, `m4a`, `amr`, `speex（微信）`, `lyb`, `ac3`, `ape`, `m4r`, `mp4`, `acc`, `wma`等，文件大小不超过500MB，时长不超过5小时。

这种差异是符合逻辑的：实时服务为了追求低延迟和稳定的数据流，倾向于采用未压缩或轻度压缩的标准化格式；而批处理服务则可以承担更复杂的解码开销。值得注意的是，语音听写流式版中提及的“讯飞定制speex格式”与“标准开源speex格式”在建议的每40ms发送字节数上有所不同（例如16kHz、压缩等级7时，前者为61B的整数倍，后者为60B的整数倍）6。这意味着如果开发者选择使用Speex压缩以减少带宽占用，必须确认其使用的Speex编码库及参数是否与讯飞服务兼容，否则可能导致解码失败（如错误码10043，“音频解码失败”）13。

**表 II.A.2.1:** 讯飞实时ASR - 音频格式要求

| **服务类型**        | **参数**          | **支持值**                                          | **关键考量**                          |
| ------------------- | ----------------- | --------------------------------------------------- | ------------------------------------- |
| RTASR (全球版) 13   | 采样率            | 16000 Hz                                            | 仅16k                                 |
|                     | 位深              | 16 bit                                              |                                       |
|                     | 声道              | 单声道 (mono)                                       |                                       |
|                     | 编码/格式         | `encoding="raw"`, `format="audio/L16;rate=16000"`   | PCM裸流                               |
| 语音听写（流式版）6 | 采样率            | 16000 Hz, 8000 Hz                                   |                                       |
|                     | 位深              | 16 bit                                              |                                       |
|                     | 声道              | 单声道 (mono)                                       |                                       |
|                     | 编码 (`encoding`) | `raw`, `speex` (8k), `speex-wb` (16k), `lame` (mp3) | MP3仅限中英文；注意Speex版本/定制要求 |
|                     | 格式 (`format`)   | `audio/L16;rate=16000`, `audio/L16;rate=8000`       | 需与`encoding`匹配                    |



#### 3. WebSocket协议：连接与消息传递

讯飞的实时ASR服务广泛采用WebSocket（支持`ws`和`wss`，强烈推荐使用`wss`以增强安全性）进行客户端与服务器之间的双向通信 6。由于服务器IP地址不固定，开发者应始终使用域名进行连接 6。

连接握手：

客户端向指定的WebSocket URL发起GET请求。例如，语音听写流式版的国内接口地址可能为 ws[s]://iat-api.xfyun.cn/v2/iat 6，全球RTASR接口地址为 ws://ist-api-sg.xf-yun.com/v2/ist 13。认证信息（如前述的Authorization头或signa参数）在此阶段提交。握手成功后，服务器返回HTTP 101状态码，表示协议切换成功 6。服务端支持的WebSocket协议版本为13 6。

消息格式（客户端到服务器）：

消息通常为JSON字符串格式，但需注意不同服务或版本间的细微差别。

- **语音听写（流式版）\**6\** 与 全球RTASR \**13\** 的JSON结构：**

  - 首帧（握手成功后发送）：

    - `common`对象：包含公共参数，如 `app_id`（应用ID）。

    - ```
      business
      ```

      对象：包含业务参数，如：

      - `language`: 语种（如 `zh_cn`, `en_us`）。
      - `domain`: 应用领域（如 `iat` 日常用语, `medical` 医疗领域）。
      - `accent`: 方言（如 `mandarin` 普通话）。
      - `vad_eos`: 后端点检测静默时长（毫秒）。
      - `dwa`: 动态修正参数（如 `wpgs` 开启流式结果返回，仅部分语言支持）。
      - `ptt` (或 `punc`): 标点符号添加控制。
      - （针对语音听写）`encoding`: 音频编码类型，如 `raw`, `speex-wb`, `lame`。
      - （针对语音听写）`format`: 音频格式描述，如 `audio/L16;rate=16000`。

  - 后续数据帧（及首帧的`data`部分）：

    - ```
      data
      ```

      对象：

      - `status`: 音频帧状态，`0`表示第一帧音频，`1`表示中间音频帧，`2`表示最后一帧音频。
      - （针对全球RTASR）`format`: 音频采样率，如 `audio/L16;rate=16000`。
      - （针对全球RTASR）`encoding`: 音频编码，如 `raw`。
      - `audio`: Base64编码的音频数据块。

- **RTASR (早期描述) \**1\**:**

  - 此描述称客户端“不断构造binary message发送到服务端，内容是音频的二进制数据”。这与上述JSON封装音频的方式有所不同，可能指特定接口版本或模式。鉴于13（全球RTASR）和6/6（语音听写）均详细描述了JSON封装Base64音频的方案，开发者应优先参考后者的实现方式，除非特定API文档明确指示发送纯二进制音频流。

音频发送策略：

一个关键的实践是按固定间隔发送固定大小的音频数据块。

- 普遍建议每40毫秒发送一次音频数据 1。
- 对于16kHz采样率、16位PCM音频，这通常对应1280字节的数据 (`16000 * 2 * 0.040 = 1280`)。
- 发送过快可能导致引擎错误；发送间隔过长（例如，全球RTASR超过10秒未发送数据，或RTASR早期描述中超过15秒）服务器可能会主动断开连接或因VAD超时而提前结束识别 1。
- 客户端在发送完所有音频数据后，应通过`data.status = 2`的帧来标识音频流结束。

消息格式（服务器到客户端）：

服务器返回的也是JSON格式的消息 6。所有帧类型均为TextMessage (opcode=1) 6。

- 主要字段包括：

  - `sid`: 本次会话的唯一ID。

  - `code`: 结果码，`0`表示成功，非零表示错误。

  - `message`: 错误描述信息（当`code`非零时）。

  - ```
    data
    ```

    : 识别结果数据对象。

    - `status`: 识别结果的状态，`0`表示首个结果片，`1`表示中间结果片，`2`表示最后一个结果片。此状态告知客户端是否期望当前话语段的更多后续结果。

    - ```
      result
      ```

      : 具体的识别结果。

      - `sn`: 结果序号。
      - `ls`: 是否为本句最后一片结果的标记。
      - `ws`: 词列表，每个词包含 `w` (文字)等信息。
      - `pgs` (若开启`dwa=wpgs`): 动态修正方式（`apd`追加, `rpl`替换）。
      - `rg` (若开启`dwa=wpgs`): 动态修正影响的范围。
      - `rl` (若开启角色分离): 角色编号 1。
      - `wb`, `bg` (RTASR 1): 词的开始时间信息（帧单位，1帧=10ms）。

对`status`字段的正确处理至关重要，无论是客户端发送的音频状态，还是服务器返回的结果状态。它管理着语音流的生命周期，确保服务器知道何时完成对特定话语的识别，客户端也知道何时可以拼接完整的转写文本。

**表 II.A.3.1:** 讯飞流式ASR - 客户端至服务器WebSocket消息结构 (JSON封装音频方案)

| **帧类型**      | **主要JSON字段** | **子字段 (示例)**                                            | **用途/描述**                                          |
| --------------- | ---------------- | ------------------------------------------------------------ | ------------------------------------------------------ |
| 首帧 (业务参数) | `common`         | `app_id`                                                     | 应用身份标识                                           |
|                 | `business`       | `language`, `domain`, `accent`, `vad_eos`, `dwa`, `ptt`/`punc`, `encoding`, `format` | 设置识别语言、领域、音频编码、标点、动态修正等业务逻辑 |
|                 | `data`           | `status=0`, `audio` (Base64)                                 | 发送首个音频数据块                                     |
| 后续音频帧      | `data`           | `status=1`, `audio` (Base64)                                 | 发送中间音频数据块                                     |
| 最后音频帧      | `data`           | `status=2`, `audio` (Base64)                                 | 发送最后一个音频数据块，通知服务器音频流结束           |

**表 II.A.3.2:** 讯飞流式ASR - 服务器至客户端WebSocket消息结构

| **主要JSON字段** | **data 子字段 (示例)**                                       | **用途/描述**                                                |
| ---------------- | ------------------------------------------------------------ | ------------------------------------------------------------ |
| `sid`            | -                                                            | 会话ID                                                       |
| `code`           | -                                                            | 结果码，0为成功                                              |
| `message`        | -                                                            | 错误描述 (当`code`非0)                                       |
| `data`           | `status` (0, 1, or 2), `result` (`sn`, `ls`, `ws`, `pgs`, `rl`, `wb`, `bg`) | 包含识别状态（是否最终结果片）和具体的转写内容、词信息、时间戳、动态修正信息、角色信息等 |

#### 4. 关键错误码与故障排除 (重点关注10110及其他)

讯飞开放平台提供了错误码查询文档 15，其全球API文档中也列出了一些特定错误 12。理解这些错误码对于快速诊断和解决集成问题至关重要。

- **错误码 10110 (没有授权许可 / No license):**

  - 含义：应用没有获得相应服务的使用授权，或者授权并发数/次数已用尽 15。
  - 解决方案：
    1. 检查讯飞开放平台控制台，确认对应的ASR服务（如实时语音转写、语音听写）是否已正确添加到当前`AppID`下，并且服务处于可用状态。
    2. 确认账户的服务配额（如并发连接数限制、总调用次数）是否充足。
    3. 如问题持续，需在讯飞控制台提交工单，并提供`AppID`、报错的SDK日志（如`msc.log`）或接口调用信息（如`sid`和完整的错误返回）以便技术支持排查 15。

- **WebSocket握手阶段HTTP错误码 \**12\**:**

  - ```
    401 Unauthorized
    ```

    :

    - 原因：`Authorization`参数缺失；签名参数解析失败；签名校验失败（`api_key`/`api_secret`不正确，签名串构造错误等）。
    - 解决方案：仔细检查`Authorization`头的生成逻辑，确保所有参与签名的参数（`host`, `date`, `request-line`）准确无误，`APIKey`和`APISecret`正确，并且Base64编码步骤无误。

  - ```
    403 Forbidden
    ```

    :

    - 原因：时钟偏移校验失败（客户端与服务器时间差超过允许范围，通常为5分钟）；IP白名单校验失败（如果开启了IP白名单，调用方IP不在许可列表内）。
    - 解决方案：同步客户端设备时间；检查控制台的IP白名单配置，确保配置的是公网出口IP且已生效。

- **API调用常见错误码：**

  - `10043` (音频解码失败 / Audio decoding fails): 通常因为发送的音频数据格式与`business`参数中声明的`encoding`和`format`不一致 13。例如，声明发送PCM但实际发送了MP3数据，或Speex音频损坏/格式不兼容。
  - `10105` (没有权限 / illegal access): `AppID`与`APIKey`不匹配，IP白名单问题，或签名（`checkSum`，针对非WebSocket的HTTP API）错误 15。
  - `10106` (无效的参数 / invalid parameter): 请求参数名错误，参数值不在规定范围，或WebAPI中body参数未进行`urlencode` 15。
  - `10313` (首帧app_id错误 / First frame app_id error): WebSocket首帧消息中`common.app_id`缺失，或与`APIKey`不匹配 13。
  - `11200` (未授权的语种或功能 / Unauthorized language/feature): 尝试使用未在控制台开通或购买的特定语种、方言或高级功能（如医疗领域识别）6。
  - `11201` (appid授权路数不够 / Appid authorize number not enough): 每秒交互次数（QPS）或并发连接数超过了所购服务的上限 13。
  - `10202` (WebSocket连接异常), `10204` (WebSocket写入错误), `10205` (WebSocket读取错误) 16: 这些是底层的WebSocket传输层错误，可能与网络状况、客户端WebSocket库实现或服务器瞬时问题有关。

分析这些错误码可以发现，大量潜在问题源于认证授权（权限、配额）、参数配置的正确性以及客户端环境（网络、时钟），而非ASR引擎本身的转写能力。因此，开发者在集成时应重视这些方面，进行充分的预检和参数校验。

**表 II.A.4.1:** 讯飞流式ASR - 常见错误码与解决方案

| **错误码 (HTTP/自定义)** | **含义**                 | **常见原因**                                                 | **推荐解决方案**                                          |
| ------------------------ | ------------------------ | ------------------------------------------------------------ | --------------------------------------------------------- |
| 10110                    | 没有授权许可             | 服务未开通、授权到期、并发/用量超限                          | 检查控制台服务状态与配额，联系商务续费或扩容，提交工单 15 |
| HTTP 401                 | 认证失败 (WebSocket握手) | `Authorization`头构造错误，`APIKey`/`APISecret`错误，签名算法错误 | 仔细核对签名流程、密钥、参与签名的各参数 12               |
| HTTP 403                 | 禁止访问 (WebSocket握手) | 客户端与服务器时钟严重不同步；IP不在白名单内                 | 同步客户端时间；检查讯飞控制台IP白名单设置 12             |
| 10043                    | 音频解码失败             | 发送的音频格式与声明的`encoding`/`format`不符；音频数据损坏  | 确保音频数据与参数一致，检查音频质量 13                   |
| 10105                    | 无权限访问               | `AppID`/`APIKey`不匹配，IP白名单问题（非WebSocket场景也可能指`checkSum`错误） | 检查凭证，IP白名单配置 15                                 |
| 10313                    | 首帧`app_id`参数错误     | WebSocket首帧消息中`common.app_id`缺失或与`APIKey`不匹配     | 确保首帧正确传递`app_id` 13                               |
| 11200                    | 未授权的语种/方言/领域   | 使用了未在控制台开通或购买的特定识别能力                     | 前往控制台开通或购买相应服务 6                            |
| 11201                    | 并发/QPS超限             | 调用频率或并发连接数超过服务规格                             | 优化调用逻辑降低频率，或升级服务套餐 13                   |



### B. 其他讯飞ASR服务 (音频格式差异概述)

虽然本报告重点关注实时流式ASR，但了解讯飞其他ASR服务（主要指录音文件转写）在音频格式支持上的差异，有助于开发者根据不同场景选择最合适的服务。

如前文II.A.2所述，讯飞的**大文件ASR (LFASR)** 14 和 **新版录音文件转写** 2 服务，专为处理预先录制好的音频文件而设计。它们与实时服务最显著的区别在于对音频格式的广泛支持：

- **支持更多编码格式：** 除了PCM (`wav`)，还广泛支持如`mp3`, `flac`, `opus`, `m4a`, `aac`, `ogg`, `amr`, `wma`等多种主流压缩和无损音频格式。新版录音文件转写服务支持的格式尤为丰富 2。
- **支持多声道音频：** 这两个服务均明确支持单声道和多声道音频，这对于处理会议录音、影视音轨等场景非常重要。实时服务通常严格要求单声道。
- **更大的文件和时长限制：** 支持最大500MB的音频文件，新版录音文件转写服务支持最长5小时的音频时长 2。

这种灵活性源于批处理模式对实时性的要求较低，服务器有更充裕的时间进行复杂格式的解码和处理。因此，当应用场景涉及处理已存在的、格式多样的音频档案，或者需要处理多声道音频时，选用讯飞的录音文件转写服务会更为便捷，可以减少客户端进行音频格式预转换的负担。新版录音文件转写服务因其更广泛的格式支持和如热词定制等高级功能 2，通常是处理录音文件的推荐选择。

## III. 百度（Baidu）ASR服务深度解析

百度AI开放平台同样提供了一系列语音识别服务，包括短语音识别、实时语音识别和音频文件转写等。

### A. 通用API访问与认证 (AppID, API Key, Secret Key, Token获取)

访问百度AI开放平台的各项服务（包括ASR）通常需要经过认证。核心认证机制是基于`access_token`的OAuth 2.0授权 8。

1. 注册与凭证获取：

   开发者首先需要在百度AI开放平台（ai.baidu.com）注册账户，创建应用，并在应用管理后台获取AppID、API Key（对应OAuth2.0中的client_id）和Secret Key（对应OAuth2.0中的client_secret）8。这些凭证是后续获取access_token和调用API的基础。

2. 获取access_token：

   access_token是调用百度AI服务接口的临时身份凭证，具有一定的有效期（例如，文档中提及默认为30天）8。获取access_token的步骤如下：

   - 向授权服务地址 `https://aip.baidubce.com/oauth/2.0/token` 发起HTTP POST请求（推荐）8。

   - 请求参数（通常在POST body中以

     ```
     application/x-www-form-urlencoded
     ```

     格式传递）：

     - `grant_type`: 固定值为 `client_credentials`。
     - `client_id`: 应用的`API Key`。
     - `client_secret`: 应用的`Secret Key`。

   - 成功响应：服务器将返回一个JSON对象，其中包含`access_token`和`expires_in`（有效期，单位秒）等信息。

   客户端应用需要妥善管理`access_token`的生命周期，包括安全存储`API Key`和`Secret Key`，在`access_token`过期前或过期时及时刷新，以保证服务的持续可用性。这种基于`access_token`的认证方式是行业标准，相较于每次请求都使用静态API密钥，能提供更好的安全性，例如通过令牌限定权限范围和缩短泄露风险窗口。

   百度也提及存在一种“API Key鉴权机制”，可能用于简化调用或跨产品场景，但`access_token`机制是其AI平台主要的、推荐的认证方式 8。

**表 III.A.1.1:** 百度ASR - Access Token 获取流程

| **参数**        | **值/描述**                   | **来源**             | **OAuth 端点URL**                          | **HTTP 方法** | **典型响应**                                |
| --------------- | ----------------------------- | -------------------- | ------------------------------------------ | ------------- | ------------------------------------------- |
| `grant_type`    | `client_credentials` (固定值) | -                    | `https://aip.baidubce.com/oauth/2.0/token` | POST (推荐)   | `{"access_token": "...", "expires_in":...}` |
| `client_id`     | 应用的 `API Key`              | 百度AI开放平台控制台 |                                            |               |                                             |
| `client_secret` | 应用的 `Secret Key`           | 百度AI开放平台控制台 |                                            |               |                                             |

### B. 音频格式规范 (支持格式、采样率、声道、位深)

百度ASR服务对输入的音频格式有明确规定，遵循这些规范是保证识别准确率和性能的前提。

- **通用建议与核心参数：**
  - **首选格式：** 强烈推荐直接上传PCM（原始脉冲编码调制）格式的音频文件。因为百度ASR的底层识别引擎使用的是PCM格式，如果上传其他格式（如MP3, WAV封装的非PCM数据, AMR, M4A），服务器端会先进性转码操作，这会增加API调用的整体耗时 8。
  - **采样率 (Rate)：** 主流服务普遍支持并推荐使用16000 Hz (16kHz) 8。对于8000 Hz (8kHz) 采样率的支持通常是有限的，可能仅限于特定模型（如短语音识别的普通话模型）或特定服务（如呼叫中心音频文件转写）8。
  - **位深 (Bit Depth)：** 通常使用16 bits（16位）进行编码 8。音频数据一般为小端序（Little-endian）。
  - **声道 (Channels)：** 绝大多数百度ASR服务仅支持单声道（mono）音频 8。
- **具体服务类型的音频要求 \**8\**:**
  - 短语音识别（标准版和极速版）：
    - 支持格式：`pcm`, `wav`, `amr`, `m4a`。
    - 编码要求：16000 Hz采样率，16 bit位深，单声道。若使用8000 Hz采样率，仅支持普通话模型。
  - 实时语音识别：
    - 支持格式：**仅支持PCM格式**的原始音频数据。
    - 编码要求：16000 Hz采样率，16 bits位深，单声道，小端序。
    - 时长限制：单次调用接口的音频时长目前不超过1小时。
  - 音频文件转写 (16k)：
    - 支持格式：`pcm`, `wav`, `amr`, `m4a`。
    - 编码要求：16000 Hz采样率，16 bits位深，单声道，小端序。
  - 呼叫中心语音 - 音频文件转写 (8k)：
    - 支持格式：`pcm`。
    - 编码要求：8000 Hz采样率，16 bits位深，单声道，小端序。

对于开发者而言，这意味着为了获得最佳性能（尤其是低延迟的实时识别），应尽可能在客户端或应用服务器端将音频预处理为16kHz、16bit、单声道的PCM裸流或WAV文件。如果处理的是8kHz的电话录音等音频，则需要确认所选用的百度ASR服务或模型是否支持该采样率，否则可能需要进行重采样。

**表 III.B.1:** 百度ASR - 各服务音频格式规范

| **百度ASR服务类型**      | **支持格式**               | **要求采样率 (Hz)**    | **要求位深** | **要求声道** | **关键说明**             |
| ------------------------ | -------------------------- | ---------------------- | ------------ | ------------ | ------------------------ |
| 短语音识别 (标准/极速版) | `pcm`, `wav`, `amr`, `m4a` | 16000 (8000仅限普通话) | 16 bit       | 单声道       | PCM推荐；8kHz支持受限    |
| 实时语音识别             | `pcm` (仅原始数据)         | 16000                  | 16 bit       | 单声道       | 必须为PCM裸流；小端序    |
| 音频文件转写 (16k)       | `pcm`, `wav`, `amr`, `m4a` | 16000                  | 16 bit       | 单声道       | PCM推荐；小端序          |
| 呼叫中心音频转写 (8k)    | `pcm`                      | 8000                   | 16 bit       | 单声道       | 专为8kHz场景设计；小端序 |

### C. 实时语音识别：WebSocket API (连接、消息、认证)

百度实时语音识别服务支持通过WebSocket API进行集成，以实现音频流的即时转写，达到“边说边出文字”的效果 4。这种方式适用于长句语音输入、音视频字幕生成、会议实时记录等场景。

- **技术特点：**

  - 基于Deep Peak2端到端建模，支持中英文及方言的音频流实时识别 4。
  - 首包响应时间达到毫秒级，并能实时展示中间文字结果 4。
  - 识别结果支持时间戳，并能进行智能语言处理，如对中间结果进行智能纠错、根据内容和停顿智能匹配标点符号 4。

- 连接与认证：

  虽然具体的WebSocket连接参数（如URL、特定头部）和消息格式（JSON结构）在当前可查阅的摘要信息中未详细列出 4，但通常的WebSocket API集成流程会涉及以下步骤，这与讯飞的流程有相似之处，也与百度实时语音翻译API的WebSocket逻辑一致 7：

  1. **建立WebSocket连接：** 客户端向百度实时语音识别服务指定的WebSocket端点发起连接请求。
  2. **认证：** 在握手阶段或连接建立后的首个消息中，客户端需要传递有效的`access_token`（获取方式见III.A节）以完成身份验证。
  3. **发送开始指令：** 连接成功后，客户端可能需要发送一个“开始识别”的指令性消息，其中可能包含识别参数（如语种、领域、是否开启标点等）。
  4. **发送音频流：** 客户端将采集到的音频数据分块（通常是PCM裸流），按一定的时间间隔（如每几十毫秒）通过WebSocket连接持续发送给服务器。
  5. **接收识别结果：** 服务器会实时或准实时地将识别结果（包括中间结果和最终结果）通过WebSocket连接推送给客户端。这些结果通常是JSON格式，包含文字内容、时间戳、句子结束标记等信息。
  6. **发送结束指令：** 音频流发送完毕后，客户端发送一个“结束识别”的指令。
  7. **关闭连接：** 服务器确认所有结果发送完毕后，或客户端主动关闭WebSocket连接。

  由于缺乏百度实时语音识别WebSocket API协议的详细规格说明，开发者在实际集成时，必须查阅百度AI开放平台官方提供的最新、最完整的技术文档（通常在`ai.baidu.com`的开发者文档中心或`ai-doc`子域下）。这些文档会明确定义WebSocket的URL、必要的请求头、认证参数的传递方式、客户端与服务器之间交互的JSON消息的具体字段和结构，以及错误处理机制。

### D. 关键错误码与故障排除 (重点关注3311及其他)

理解并正确处理API返回的错误码是确保ASR应用稳定运行的关键环节。

- 关于错误码 3311：

  在所提供的资料中，未能找到百度ASR服务关于错误码3311的具体含义和解决方案。

  - 百度语音SDK错误码列表（源自一份较为详细的PDF文档 8）中，并未包含`3311`。该列表将错误码按类型（如1xxx网络超时、2xxx其他网络错误、3xxx音频相关错误、4xxx服务端错误、5xxx客户端错误等）进行了分类，但`3311`不在其中。
  - 其他提及错误码的文档，如 10，主要涉及的是百度翻译（Fanyi）API的错误码，虽然部分平台通用错误（如认证、频率限制）可能相似，但它们并非针对ASR服务。错误码`3311`也未在这些翻译API的错误列表中出现。
  - 其他如 8 等是对PDF文档的文本匹配，内容与`3311`错误码的解释无关。
  - 一份可能包含此信息的文档 22 无法访问。

  鉴于此，如果开发者在对接百度ASR服务时遇到错误码`3311`，建议优先检查是否为HTTP状态码的误读，或查阅百度AI开放平台最新的、完整的错误码文档，或直接联系百度技术支持获取帮助。此错误码可能属于更底层的百度云平台错误、特定场景下的罕见错误，或者在当前资料中未被覆盖。

- 百度语音SDK常见错误码 (主要依据 8)：

  百度语音SDK的错误码分类有助于定位问题根源：

  - 网络错误 (1xxx, 2xxx系列):
    - `1000`: DNS连接超时。
    - `2000`: 网络连接失败。
    - `2100`: 本地网络不可用。
    - 解决方案：检查客户端网络连接、防火墙设置、DNS解析及网络权限。
  - 音频错误 (3xxx系列):
    - `3001`: 录音机打开失败。
    - `3003`: 录音机不可用（可能被占用或无权限）。
    - `3101`: 长时间未检测到有效语音。
    - `3102`: 检测到语音，但过短。
    - 解决方案：检查麦克风硬件、录音权限、是否有其他应用占用音频设备、音频输入是否清晰有效。
  - 服务端/认证错误 (4xxx系列):
    - `4001`: 服务端参数错误。
    - `4003`: 服务端识别错误。
    - `4004`: 服务端鉴权失败（通常与`access_token`无效或权限不足有关）。
    - 解决方案：检查请求参数是否符合API文档要求，确认`access_token`是否有效、未过期且具有调用该ASR服务的权限，`cuid`等参数是否正确传递。
  - 客户端错误 (5xxx系列):
    - `5002`: 客户端识别参数错误。
    - `5003`: 客户端获取`token`失败。
    - 解决方案：检查SDK初始化参数、调用逻辑是否符合SDK文档规范，确认`access_token`获取流程是否正确实现。

  这种分类方式为开发者提供了一个结构化的排错思路。例如，3xxx系列错误指向音频输入问题，而4xxx或5xxx错误则提示检查认证、请求参数或SDK集成逻辑。

**表 III.D.1:** 百度ASR - 部分常见错误码与解决方案 (基于 8)

| **错误码分类** | **示例错误码** | **含义 (摘自 )**      | **可能原因/解决方案 (据  或推断)**                           |
| -------------- | -------------- | --------------------- | ------------------------------------------------------------ |
| 网络错误       | 2000           | 网络连接失败          | 检查客户端网络，防火墙，代理设置                             |
| 音频错误       | 3001           | 录音机打开失败        | 检查麦克风权限，设备是否被占用                               |
|                | 3101           | 未检测到有效语音      | 确保有清晰的语音输入，调整麦克风                             |
| 服务端/认证    | 4004           | 服务端鉴权失败        | `access_token`无效、过期或权限不足；检查应用配置             |
| 客户端错误     | 5002           | 客户端识别参数错误    | 核对API文档，检查传递给SDK的识别参数（如语种、采样率等）是否正确 |
|                | 5003           | 客户端获取`token`失败 | 检查`API Key`/`Secret Key`是否正确，网络是否能通达百度认证服务器，`token`获取逻辑 |



*注意：错误码3311未在所提供的ASR专用错误文档中找到。*

### E. SDK与代码示例 (Node.js/JavaScript 重点)

百度AI开放平台为其各项服务提供了多种语言的SDK，以方便开发者集成。

- 官方声明支持Node.js SDK 23。NPM上也存在一个名为`bce-sdk-js`的百度云引擎JavaScript SDK，它同时适用于浏览器和Node.js环境，可以用于调用百度云的各项服务 24。
- 然而，在当前提供的资料中，**并未直接找到针对百度语音识别（ASR）服务，特别是使用`bce-sdk-js`的Node.js或JavaScript具体代码示例**。多个针对`bce-sdk-js`的文档和代码库的检索结果（如 24）均表示信息不可用或未包含ASR的特定用例。这意味着，虽然通用的JS SDK存在，但其在ASR场景下的快速上手示例在这些材料中是缺失的。
- 开发者若希望使用Node.js或JavaScript集成百度ASR，首选途径应是访问百度AI开放平台的官方网站（`ai.baidu.com`），在其开发者中心或SDK下载区域查找专门为ASR服务提供的最新SDK和代码示例 29。如果官方JS SDK对ASR的支持不够突出或示例缺乏，开发者可能需要考虑直接基于HTTP REST API（用于文件转写）或WebSocket API（用于实时识别）进行集成，这就更依赖于对API协议细节的掌握。

## IV. 跨平台ASR集成：最佳实践与工具

无论是集成讯飞还是百度的ASR服务，一些通用的最佳实践和工具可以帮助开发者提高集成效率和识别效果。

### A. 音频格式转换：从WebM到ASR适用格式 (如PCM, WAV)

WebM是一种常见的网络音频格式，尤其在浏览器通过`MediaRecorder` API录制音频时。然而，大多数ASR服务，特别是实时接口，对输入音频格式有严格要求，通常偏好PCM或WAV（封装的PCM）格式，并指定采样率（如16kHz）、位深（如16bit）和单声道。因此，将WebM转换为ASR服务接受的格式是一个常见的需求。

- 服务器端转换 (使用FFmpeg)：

  FFmpeg是一个功能强大的跨平台音视频处理命令行工具，非常适合在服务器端进行音频格式转换。

  - 示例命令 (将WebM转换为16kHz, 16bit, 单声道 PCM WAV):

    Bash

    ```
    ffmpeg -i input.webm -acodec pcm_s16le -ac 1 -ar 16000 output.wav
    ```

    这个命令的参数解释如下 

    30

    ：

    

    - `-i input.webm`: 指定输入文件。
    - `-acodec pcm_s16le`: 设置音频编解码器为16位小端PCM。
    - `-ac 1`: 设置音频通道数为1（单声道）。
    - `-ar 16000`: 设置音频采样率为16000 Hz。
    - `output.wav`: 指定输出文件名。

- 客户端转换 (JavaScript库)：

  对于需要在浏览器端直接进行转换的Web应用，可以使用JavaScript库。webm-to-wav-converter是一个为此目的设计的库 32。

  - 安装：

    Bash

    ```
    npm i webm-to-wav-converter
    ```

  - 使用示例 **33**：

    JavaScript

    ```
    import { WavRecorder, getWaveBlob } from 'webm-to-wav-converter';
    
    // 方案一：使用WavRecorder直接录制并获取WAV Blob
    const wavRecorder = new WavRecorder();
    // wavRecorder.start(); // 开始录制
    //... 录制一段时间...
    // wavRecorder.stop(); // 停止录制
    // const wavBlob = wavRecorder.getBlob(); // 获取16-bit WAV Blob
    // const wavBlob32bit = wavRecorder.getBlob(true); // 获取32-bit WAV Blob
    
    // 方案二：处理已有的MediaRecorder录制的WebM数据块 (dataChunks)
    // const dataChunks =; // 假设这是从MediaRecorder的ondataavailable事件收集的WebM数据块数组
    // const wavBlobFromChunks = getWaveBlob(dataChunks, false); // false表示16-bit
    ```

    

  该库可以直接在浏览器中将`MediaRecorder`产生的`audio/webm` Blob转换为`audio/wav`格式的Blob，便于后续发送给ASR API。

选择服务器端还是客户端转换取决于应用的具体架构。服务器端转换（如使用FFmpeg）更为灵活和强大，适合后端处理流程。客户端转换则可以减轻服务器负担，减少上传数据量（如果目标格式更小或ASR服务不接受WebM），并可能为实时应用带来更快的响应，因为它确保了发送给ASR服务的是其原生支持的格式，避免了ASR服务端可能存在的转码延迟。对于像百度实时ASR那样严格要求PCM输入的服务，客户端转换尤为有益 8。

**表 IV.A.1:** WebM 至 PCM/WAV 转换示例

| **方法**                     | **示例命令/代码片段**                                        | **关键参数/选项**                         | **输出格式**   | **用途场景**     |
| ---------------------------- | ------------------------------------------------------------ | ----------------------------------------- | -------------- | ---------------- |
| FFmpeg (命令行)              | `ffmpeg -i input.webm -acodec pcm_s16le -ac 1 -ar 16000 output.wav` | `-acodec pcm_s16le`, `-ac 1`, `-ar 16000` | WAV (PCM)      | 服务器端批量转换 |
| `webm-to-wav-converter` (JS) | `import { getWaveBlob } from 'webm-to-wav-converter'; const wavBlob = getWaveBlob(webmChunks, false);` (其中`webmChunks`是`MediaRecorder`数据, `false`指16-bit) | `is32bit` (第二个参数, `false`为16-bit)   | WAV Blob (PCM) | 客户端浏览器转换 |

### B. 推荐的音频参数配置以优化识别效果

ASR系统的识别准确率在很大程度上取决于输入音频的质量和参数配置。“输入差则输出差”（Garbage In, Garbage Out）的原则在此表现得淋漓尽致。

- 音频格式与编码：
  - **无损格式优先：** 尽可能使用无损音频格式，如PCM（通常封装在WAV文件中）。有损压缩编解码器（如MP3）可能会引入失真，降低识别质量 37。这与百度对PCM的推荐一致 8。
- 采样率 (Sampling Rate)：
  - **16kHz为佳：** 对于高质量ASR，通常建议最低采样率为16kHz 37。讯飞和百度的主流ASR服务均主要支持或偏好16kHz 1。
  - **避免不必要的重采样：** 如果原始音频已经是16kHz，则直接使用。如果必须重采样（例如从8kHz到16kHz），应使用高质量的重采样算法以减少音质损失 37。
- 位深 (Bit Depth)：
  - **16-bit是标准：** 大多数ASR服务（包括讯飞和百度）期望16位深度的音频 6。
- 声道 (Channels)：
  - **单声道 (Mono)：** 绝大多数ASR服务（特别是实时服务）要求或仅支持单声道输入 6。如果原始音频是立体声或其他多声道格式，应在发送给ASR服务前将其混合（mix down）为单声道。
- 录音环境与说话方式：
  - **安静环境：** ASR在安静环境下表现最佳，背景噪音会严重影响识别效果 8。
  - **清晰发音：** 说话人应保持正常语速，发音清晰。避免多人同时说话 8。
  - **麦克风距离：** 说话人应靠近麦克风，以获得更好的信噪比 8。
- 领域适配与定制化：
  - **热词/自定义词库：** 对于包含特定领域术语、人名、产品名等非常用词汇的音频，应利用平台提供的定制化功能，如讯飞的热词功能 2 或百度的语音自训练平台 4。这些功能可以显著提升对这些特定词汇的识别准确率 37。
- 其他特性：
  - **标点符号：** 许多ASR服务能自动添加标点符号，如讯飞的`ptt=1`参数 6 或百度的智能标点功能 4。根据应用需求启用或调整此功能。

遵循这些音频参数配置建议，是最大化ASR服务识别准确率的基础。对于特定领域的应用，探索并使用平台提供的模型定制化工具，是进一步提升性能的关键步骤，这通常超出了基础API集成的范畴，但对最终效果至关重要。

**表 IV.B.1:** ASR音频参数最佳实践清单

| **参数/方面** | **推荐**                                                 | **原因/对ASR的影响**                       |
| ------------- | -------------------------------------------------------- | ------------------------------------------ |
| 音频格式      | PCM (WAV封装) 等无损格式                                 | 减少压缩失真，保证原始音频信息 8           |
| 采样率        | 16kHz                                                    | 提供足够的声音频率信息，主流ASR服务标准 37 |
| 位深          | 16 bit                                                   | 动态范围适中，行业标准 6                   |
| 声道          | 单声道 (Mono)                                            | 大多数ASR模型基于单声道训练 6              |
| 噪音水平      | 尽可能低，避免背景噪音                                   | 提高信噪比，减少干扰 8                     |
| 说话风格      | 发音清晰，正常语速，避免多人同时说                       | 有利于模型准确切分和识别音素 8             |
| 热词/领域定制 | 针对专业词汇使用平台提供的定制功能（如热词、自训练模型） | 显著提升特定领域词汇的识别准确率 2         |



### C. 调试ASR服务连接与WebSocket消息

调试与ASR服务的WebSocket连接是集成过程中的常见挑战。这需要从网络层、协议层到应用消息层的多维度排查。

- WebSocket连接建立（握手阶段）：

  - WebSocket连接始于一个HTTP/1.1的握手过程。客户端发送一个包含`Upgrade: websocket`等头部的HTTP GET请求。服务器若同意升级，则返回HTTP 101状态码，此后连接转为持久的WebSocket双向通信 38。

  - 常见握手失败原因 **38**：

    

    - **URL与协议错误：** 确保使用正确的WebSocket URL（`ws://` 或 `wss://`），且服务器地址无误。对于加密连接（`wss://`），服务器SSL证书必须配置正确且有效。
    - **配置问题：** 服务端配置错误，或客户端参数不符合服务端要求。
    - **网络防火墙：** 防火墙可能阻止WebSocket连接或特定端口。
    - **认证问题：** 如前述，讯飞和百度的ASR服务在握手阶段或紧随其后的首条消息中进行认证。API密钥错误、签名无效、`access_token`过期或IP白名单限制等都会导致握手失败，通常返回HTTP 401（未授权）或403（禁止访问）错误 12。

- 使用浏览器开发者工具调试：

  - 现代浏览器（如Chrome, Firefox, Edge）的开发者工具是调试WebSocket的利器 38。
  - 打开“网络”（Network）面板，通常可以筛选出“WS” (WebSocket)类型的连接。
  - 点击目标WebSocket连接，可以查看：
    - **Headers：** 握手请求和响应的头部信息，用于确认握手是否成功（状态码101）以及认证参数是否正确传递。
    - **Messages/Frames：** 客户端与服务器之间发送和接收的实际消息负载。可以检查消息格式（如JSON是否有效）、内容是否符合API规范。

- 专用WebSocket测试工具：

  - 工具如Postman、WebSocket King等，允许开发者手动建立WebSocket连接，发送自定义消息，并查看服务器响应，非常适合在不编写完整客户端代码的情况下测试API端点和消息交互 38。

- 消息传递与格式：

  - **格式一致性：** 确保客户端和服务端对消息格式（如JSON、纯文本）及其内部结构（schema）有共同的约定。格式不匹配会导致解析错误和消息被忽略 38。
  - **消息丢失或延迟：** 可能由网络拥塞、服务器队列积压或过载引起 38。

- 连接维护与稳定性：

  - **Ping/Pong机制：** WebSocket协议支持Ping/Pong帧用于心跳检测，以保持连接活跃并检测无响应的连接。例如，Speechmatics建议Ping/Pong超时至少60秒，发送间隔20-60秒 39。这有助于区分是网络临时中断还是连接已彻底断开。
  - **自动重连逻辑：** 客户端应实现自动重连机制，最好带有指数退避策略（exponential backoff），以避免在连接失败时频繁冲击服务器 38。
  - **管理空闲连接：** 长时间无数据交互的WebSocket连接应考虑由客户端或服务端主动关闭，以释放资源 38。

- 日志记录：

  - 在客户端和（如果可能）服务端记录详细的日志，包括时间戳、请求ID（如果API支持）、发送和接收的消息摘要、错误码和错误信息，对于事后分析问题至关重要 40。

对于ASR服务，由于认证通常在握手或初始消息阶段完成，因此当WebSocket连接无法建立时，首要排查方向应集中在认证凭证的正确性、签名算法的实现（针对讯飞的某些方案）、`access_token`的有效性（针对百度）以及任何IP白名单或网络策略。

### D. 通用ASR API错误处理策略

一个健壮的ASR应用不仅能处理成功的转写，更能优雅地应对各种API错误。

- 遵循HTTP状态码语义：

  - 对于基于HTTP的交互（如获取`access_token`，或某些ASR服务可能提供的RESTful接口），严格使用标准的HTTP状态码。`4xx`系列表示客户端错误（如`400 Bad Request`表示请求格式错误，`401 Unauthorized`表示认证失败，`403 Forbidden`表示无权限访问资源，`404 Not Found`表示资源不存在，`429 Too Many Requests`表示频率超限）。`5xx`系列表示服务端错误（如`500 Internal Server Error`表示服务器内部发生未知错误）40。

- 结构化错误响应：

  - API应返回结构化的错误信息，通常是JSON格式，便于客户端程序解析 

    40

    。一个良好的错误响应体应包含：

    

    - **唯一的错误码 (`error.code`)：** 机器可读的、标识特定错误类型的代码（例如："INVALID_AUDIO_FORMAT", "RATE_LIMIT_EXCEEDED"）。
    - **清晰的错误消息 (`error.message`)：** 人类可读的、对错误的简明描述，最好能提供解决问题的线索。避免使用含糊不清或过于技术性的术语。
    - **详细信息 (`error.details`) (可选)：** 提供关于错误的更多上下文，例如，在参数校验失败时，可以指明哪个字段出错、当前值以及期望的约束。
    - **请求ID (`requestId`)：** 一个唯一的标识符，用于跟踪特定请求，方便在日志中查找和与服务商沟通问题。
    - **文档链接 (可选)：** 指向相关错误码或API文档的链接，帮助开发者快速找到解决方案。

- 错误处理最佳实践 **40**：

  

  - **一致性：** 所有API端点应使用统一的错误响应格式和术语。
  - **明确性：** 错误消息应清晰、具体，并尽可能提供可操作的建议。
  - **安全性：** 不要在错误响应中暴露敏感信息，如服务器内部路径、堆栈跟踪或原始数据库错误。对认证失败等安全相关错误，使用通用的提示信息。
  - **帮助性：** 在可能的情况下，提示用户如何修正错误，或提供重试建议（如`Retry-After`头部）。

- 特定场景处理：

  - **频率限制：** 当收到表示频率超限的错误时（如HTTP 429，或讯飞11201，百度54003），客户端应遵守`Retry-After`头部（如果提供）的指示，或实现带有指数退避的重试逻辑，避免加剧服务器负载。
  - **输入校验：** 如果API支持，一次性返回所有输入参数的校验错误，而不是让客户端逐个修正 40。

- 超越API调用错误的“错误处理”：

  - **转写结果校正：** ASR的输出本身可能包含错误。高级的错误处理可以扩展到对转写文本的后处理。例如，使用外部的错误校正模型（可能基于大型语言模型）来修正识别结果中的语法或词汇错误 42。
  - **置信度分数：** 一些ASR系统会提供词或句子的置信度分数。这些分数可以用来标识可能不准确的转写部分，提示用户进行人工核查或触发特定的业务逻辑。但需要注意，置信度分数与实际错误的关联度可能并不完美 43。

通过实施这些错误处理策略，开发者可以构建出更稳定、更易于调试、用户体验更好的ASR应用。

### E. ASR API测试与验证工具

在正式编码集成之前或在调试过程中，使用API提供商的测试工具或在线沙箱可以极大地提高效率。

- 讯飞（iFlytek）：
  - 讯飞的全球站点（global.xfyun.cn）为其语音转写服务（包括实时ASR）提供了“演示”（Demonstration）或“接口示例”（Interface demo）功能 5。其中，实时ASR的文档页明确提到可以下载部分开发语言的示例代码，其他语言则需参考接口文档自行实现 13。这些资源可以作为初步测试和理解API行为的沙箱环境。
- 百度（Baidu）：
  - 百度AI开放平台的一份技术文档（PDF）中提及，可以通过“API在线调试页面”获取更多示例代码和进行在线调试 8。这表明百度也提供了类似的在线工具，帮助开发者测试API调用和验证参数。
- 通用Web Speech API演示：
  - 谷歌提供了一个Web Speech API的在线演示页面 (`google.com/intl/en/chrome/demos/speech.html`) 44。虽然这并非针对讯飞或百度的特定API，但它可以用于快速测试客户端的麦克风设置和浏览器对基本语音识别能力的支持情况。
- 第三方ASR服务提供商的演示：
  - 一些独立的ASR服务商（如Voicegain 45）也在其官网上提供“试用演示”（Try Demo）功能。这些可以作为了解通用ASR在线测试工具形态的参考。

这些官方或第三方的测试工具和演示对于开发者来说非常有价值。它们能够：

1. **快速验证API连通性和认证：** 无需编写完整客户端代码，即可测试API密钥、签名或`access_token`是否有效，网络是否通畅。
2. **理解请求与响应格式：** 直观地看到API期望的请求结构和实际返回的响应数据，帮助理解API协议。
3. **初步评估识别效果：** 上传或实时输入少量样本音频，对ASR的识别准确率和响应速度有一个初步的感性认识。
4. **辅助调试：** 当自己编写的客户端代码遇到问题时，可以使用这些工具发送相同的请求，对比结果，从而定位问题是在客户端实现还是对API的理解上。

利用这些工具进行迭代测试和学习，是集成任何新API时的推荐做法。

## V. 比较概览与关键差异点

基于前述对讯飞和百度ASR服务在特定方面的分析，可以总结出一些在所查阅资料范围内显现的关键差异点：

- **认证机制：**
  - **讯飞：** 针对流式服务，资料显示了至少两种认证方式：一种是实时语音转写（RTASR）使用的基于`appid`、时间戳`ts`和`apiKey`，通过`HmacSHA1(MD5(...))`生成`signa`参数的机制 1；另一种是语音听写流式版和全球RTASR采用的、更贴近标准WebSocket握手认证的方式，即使用`APIKey`、`APISecret`、`host`和`date`，通过`hmac-sha256`生成签名并置于`Authorization` HTTP头部 6。
  - **百度：** 主要采用OAuth 2.0的`access_token`模型。客户端使用`API Key` (`client_id`) 和 `Secret Key` (`client_secret`) 向特定端点换取有时效性的`access_token`，然后在API调用时携带此`token` 8。
  - *差异影响：* 讯飞的多重认证机制要求开发者仔细核对具体服务接口的文档。百度的`access_token`机制是行业标准，但比直接使用API密钥多了一个获取令牌的步骤。
- **实时音频格式要求：**
  - **讯飞：** 全球RTASR明确要求16kHz、16bit、单声道PCM (`raw`编码) 13。语音听写流式版也支持8kHz，以及讯飞定制的Speex压缩格式和有限的MP3格式（仅中英文）6。
  - **百度：** 实时语音识别服务强烈推荐并主要支持16kHz、16bit、单声道的PCM原始音频数据 8。
  - *差异影响：* 两者对PCM的偏好相似。讯飞语音听写流式版在压缩格式（Speex, MP3）上提供了更多选择，可能有助于在特定场景下优化带宽，但需注意其“定制Speex”和MP3的语种限制。
- **WebSocket协议细节的明确性：**
  - **讯飞：** 在所查阅的资料中，讯飞的文档（如 6）对WebSocket消息的具体JSON结构（包括命令参数、业务参数、音频数据封装方式）以及认证参数在握手阶段的传递方式，提供了相对更详尽的描述。
  - **百度：** 虽然确认了实时ASR支持WebSocket 4，但当前资料中缺乏其WebSocket连接参数、消息格式和认证细节的深入说明。这些信息可能存在于更底层的官方API文档中，但未在本次分析的材料中充分展现。
  - *差异影响：* 对于希望直接通过WebSocket协议（而非SDK）集成的开发者，讯飞当前材料中提供的协议细节可能更易于上手。百度用户则需确保能从其官方文档中找到同等详细的WebSocket协议规范。
- **特定错误码文档：**
  - **讯飞：** 针对用户查询的错误码10110（无授权许可），在资料中有明确的解释和解决方案指引 15。
  - **百度：** 针对用户查询的错误码3311，在所提供的ASR专用错误码列表 8 中未能找到。
  - *差异影响：* 这可能表明错误码3311并非百度ASR的常见或公开错误码，或者相关文档未包含在本次分析范围内。开发者遇到此类未文档化的错误码时，通常需要依赖更广泛的排查或寻求官方支持。
- **SDK示例 (Node.js/JavaScript ASR专用)：**
  - **讯飞：** 资料中提及讯飞提供示例代码下载 13，但未详细列出针对Node.js/JavaScript的流式ASR具体示例。
  - **百度：** 存在通用的百度云JavaScript SDK (`bce-sdk-js`) 24，但当前资料中缺乏使用该SDK进行ASR（特别是实时ASR）的具体代码示例。
  - *差异影响：* 对于寻求Node.js/JavaScript快速上手ASR SDK的开发者，可能需要更深入地挖掘两家平台的开发者门户网站以找到最合适的示例代码。
- **批处理音频格式支持：**
  - **讯飞：** 其“新版录音文件转写”服务支持的音频格式列表非常广泛，包括多种主流压缩和无损格式，以及多声道音频 2。
  - **百度：** 音频文件转写服务在资料中主要提及支持`pcm`, `wav`, `amr`, `m4a` 8。
  - *差异影响：* 在处理格式多样的存量音频文件时，讯飞新版录音文件转写服务在当前资料中展现出更大的格式兼容性。

这些差异点是基于当前分析材料的观察，具体选择应始终以最新的官方文档和实际测试为准。

## VI. 结论与战略建议

本报告对讯飞和百度两家主流供应商的自动语音识别（ASR）服务进行了技术层面的梳理与分析。研究表明，两家公司均提供了功能强大的ASR能力，但开发者在集成过程中，必须对认证协议、音频格式规范（尤其针对实时服务）、API交互细节以及错误处理机制有精准的理解和细致的实施。

**核心结论：**

1. **认证是前提：** 无论是讯飞的多样化签名机制还是百度的OAuth 2.0 `access_token`模型，正确实现认证是成功调用API的第一步。时间戳同步、密钥安全管理是其中的关键。
2. **音频质量是基石：** ASR的准确率高度依赖于输入音频的质量。16kHz采样率、16位深、单声道的PCM或WAV格式是实时服务普遍推荐的“黄金标准”。背景噪音、说话人发音习惯、麦克风质量等因素亦不容忽视。
3. **WebSocket主导实时交互：** 对于实时ASR，WebSocket是主流技术选择，开发者需掌握其握手、消息分帧、状态管理及连接维护（如心跳、重连）等机制。
4. **错误处理需全面：** 健壮的应用必须能够处理从网络连接、认证失败到参数错误、配额超限乃至音频解码失败等各类API错误。结构化的错误响应和详细的日志记录对调试至关重要。
5. **文档与工具是助力：** 充分利用官方提供的API文档、SDK、在线测试工具和示例代码，可以显著加速开发和调试进程。

**战略建议：**

1. **优先参考官方最新文档：** 对于本文未能详尽或存在疑问之处（如百度WebSocket具体协议细节、特定未列出错误码等），应始终以讯飞和百度官方发布的最新API文档为最终依据。技术细节可能随版本迭代而更新。
2. **严格遵循认证规范：** 针对所选用的具体ASR服务接口，务必仔细核对并正确实现其认证流程。特别是讯飞平台可能存在的多种认证方案，需避免混淆。确保客户端时间与服务器时间同步，以防范因时钟偏移导致的签名验证失败。
3. **重视音频预处理：** 根据目标ASR服务的要求，实施必要的客户端或服务器端音频预处理。包括将常见的WebM等格式转换为PCM/WAV，确保采样率、位深、声道符合规范。对于实时应用，这有助于降低延迟，提升识别效果。
4. **构建详尽的错误处理与日志机制：** 客户端应能捕获并区分不同类型的错误（网络、认证、业务逻辑等），并根据错误性质采取相应措施（如重试、提示用户、记录日志）。日志中应包含请求ID、时间戳、错误码和上下文信息，便于追踪和解决问题。
5. **采用迭代测试方法：** 在全面集成SDK或自行实现API调用前，利用官方提供的在线演示或测试工具进行小范围、快速的API功能验证。逐步增加测试音频的复杂性（如不同口音、噪音环境），以评估ASR在真实场景下的表现。
6. **积极探索与利用定制化功能：** 对于有特定领域词汇（如医疗、金融、游戏等行业术语）识别需求的应用，应积极调研并利用平台提供的ASR定制化功能，如热词库、用户词典上传、自训练语言模型等。这往往是提升特定场景识别准确率的关键手段。

通过遵循上述建议，开发者可以更高效、更可靠地将讯飞和百度的ASR技术集成到其应用中，从而充分发挥语音识别技术在提升用户体验、优化业务流程方面的潜力。







# Node.js SDK

更新时间：2024-01-30 11:41:07

[产品详情](https://ai.aliyun.com/nls)

[我的收藏](https://help.aliyun.com/my_favorites.html)

本文介绍如何使用智能语音交互一句话识别的Node.js SDK，包括SDK的安装方法及SDK代码示例等。

## 前提条件

- 在使用SDK前，请先阅读接口说明，详情请参见[接口说明](https://help.aliyun.com/zh/isi/developer-reference/api-reference-1#topic-1917909)。
- 请确认已经安装nodejs&npm环境，并完成基本配置。
- SDK支持nodev14及以上版本。

## 下载安装

1. 下载并安装SDK。

   通过以下命令完成SDK下载和安装。

    

   ```nodejs
   npm install alibabacloud-nls
   ```

2. 导入SDK。

   在代码中使用require或者import导入SDK。

    

   ```nodejs
   const Nls = require('alibabacloud-nls')
   //Nls内部含SpeechRecognition, SpeechTranscription, SpeechSynthesizer
   //以下为使用import导入SDK
   //import { SpeechRecognition } from "alibabacloud-nls"
   //import { SpeechTranscription } from "alibabacloud-nls"
   //import { SpeechSynthesizer } from "alibabacloud-nls"
   ```

## 一句话识别

### Class: SpeechRecognition

SpeechRecognition类用于进行一句话识别。

- 构造函数参数说明：

  |      |      |      |
  | ---- | ---- | ---- |
  |      |      |      |

  | **参数** | **类型** | **参数说明**   |
  | -------- | -------- | -------------- |
  | config   | Object   | 连接配置对象。 |

- config object说明：

  |      |      |      |
  | ---- | ---- | ---- |
  |      |      |      |

  | **参数** | **类型** | **参数说明**                                                 |
  | -------- | -------- | ------------------------------------------------------------ |
  | url      | String   | 服务URL地址。                                                |
  | token    | String   | 访问Token，详情可参见[获取Token概述](https://help.aliyun.com/zh/isi/overview-of-obtaining-an-access-token#587dee8029x7r)。 |
  | appkey   | String   | 对应项目Appkey。获取Appkey请前往[控制台](https://nls-portal.console.aliyun.com/applist)。 |

### defaultStartParams()

返回一个默认的推荐参数，其中Format为PCM，采样率为16000 Hz，中间结果、标点预测和ITN均为打开状态。您在拿到默认对象后可以根据自身需求，结合[接口说明](https://help.aliyun.com/zh/isi/developer-reference/api-reference-1#topic-1917909)中的参数列表来添加和修改参数。

- 参数说明：无。

- 返回值：

  object类型对象，字段如下：

   

  ```javascript
  {
      "format": "pcm",
      "sample_rate": 16000,
      "enable_intermediate_result": true,
      "enable_punctuation_predition": true,
      "enable_inverse_text_normalization": true
  }
  ```

### on(which, handler)

设置事件回调。

- 参数说明：

  |      |      |      |
  | ---- | ---- | ---- |
  |      |      |      |

  | **参数** | **类型** | **参数说明** |
  | -------- | -------- | ------------ |
  | which    | String   | 事件名称。   |
  | handler  | Function | 回调函数。   |

  支持的回调事件如下：

  |      |      |      |      |
  | ---- | ---- | ---- | ---- |
  |      |      |      |      |

  | **事件名称** | **事件说明**         | **回调函数参数个数** | **回调函数参数说明**       |
  | ------------ | -------------------- | -------------------- | -------------------------- |
  | started      | 一句话识别开始。     | 1                    | String类型，开始信息。     |
  | changed      | 一句话识别中间结果。 | 1                    | String类型，中间结果信息。 |
  | completed    | 一句话识别完成。     | 1                    | String类型，完成信息。     |
  | closed       | 连接关闭。           | 0                    | 无。                       |
  | failed       | 错误。               | 1                    | String类型，错误信息。     |

- 返回值：无。

### async start(param, enablePing, pingInterval)

根据param发起一次一句话识别，param可以参考defaultStartParams方法的返回，具体参数见[接口说明](https://help.aliyun.com/zh/isi/developer-reference/api-reference-1#topic-1917909)。

- 参数说明：

  |      |      |      |
  | ---- | ---- | ---- |
  |      |      |      |

  | **参数**     | **类型** | **参数说明**                                                 |
  | ------------ | -------- | ------------------------------------------------------------ |
  | param        | Object   | 一句话识别参数。                                             |
  | enablePing   | Boolean  | 是否自动向云端发送ping请求，默认false。true：发送。false：不发送。 |
  | pingInterval | Number   | 发ping请求间隔时间，默认6000，单位为毫秒。                   |

- 返回值： Promise对象，当started事件发生后触发resolve，并携带started信息；当任何错误发生后触发reject，并携带异常信息。

### async close(param)

停止一句话识别。

- 参数说明：

  |      |      |      |
  | ---- | ---- | ---- |
  |      |      |      |

  | **参数** | **类型** | **参数说明**         |
  | -------- | -------- | -------------------- |
  | param    | Object   | 一句话识别结束参数。 |

- 返回值：

  Promise对象，当completed事件发生后触发resolve，并携带completed信息；当任何错误发生后触发reject，并携带异常信息。

### shutdown()

强制断开连接。

- 参数说明：无。
- 返回值：无。

### sendAudio(data)

发送音频，音频格式必须和参数中一致。

- 参数说明：

  |      |      |      |
  | ---- | ---- | ---- |
  |      |      |      |

  | **参数** | **类型** | **参数说明**     |
  | -------- | -------- | ---------------- |
  | data     | Buffer   | 二进制音频数据。 |

- 返回值：无。

## 代码示例

 

```nodejs
"use strict"

const Nls = require("alibabacloud-nls")
const fs = require("fs")
const sleep = (waitTimeInMs) => new Promise(resolve => setTimeout(resolve, waitTimeInMs))

const URL = "wss://nls-gateway.cn-shanghai.aliyuncs.com/ws/v1"
const APPKEY = "Your Appkey"      //获取Appkey请前往控制台：https://nls-portal.console.aliyun.com/applist
const TOKEN = "Your Token"      //获取Token具体操作，请参见：https://help.aliyun.com/document_detail/450514.html

let audioStream = fs.createReadStream("test1.pcm", {
  encoding: "binary",
  highWaterMark: 1024
})
let b1 = []

audioStream.on("data", (chunk) => {
  let b = Buffer.from(chunk, "binary")
  b1.push(b)
})

audioStream.on("close", async ()=>{
  while (true) {
    let sr = new Nls.SpeechRecognition({
      url: URL,
      appkey:APPKEY,
      token:TOKEN
    })

    sr.on("started", (msg)=>{
      console.log("Client recv started:", msg)
    })

    sr.on("changed", (msg)=>{
      console.log("Client recv changed:", msg)
    })

    sr.on("completed", (msg)=>{
      console.log("Client recv completed:", msg)
    })

    sr.on("closed", () => {
      console.log("Client recv closed")
    })

    sr.on("failed", (msg)=>{
      console.log("Client recv failed:", msg)
    })

    try {
      await sr.start(sr.defaultStartParams(), true, 6000)
    } catch(error) {
      console.log("error on start:", error)
      continue
    }

    try {
      for (let b of b1) {
        if (!sr.sendAudio(b)) {
          throw new Error("send audio failed")
        }
        await sleep(20)
      }
    } catch(error) {
      console.log("sendAudio failed:", error)
      continue
    }

    try {
      console.log("close...")
      await sr.close()
    } catch(error) {
      console.log("error on close:", error)
    }
    await sleep(2000)
  }
})
```

# 语音听写（流式版）WebAPI 文档

## [#](https://www.xfyun.cn/doc/asr/voicedictation/API.html#接口说明)接口说明

语音听写流式接口，用于1分钟内的即时语音转文字技术，支持实时返回识别结果，达到一边上传音频一边获得识别文本的效果。
高阶功能-动态修正现在免费开放！多方言免切能力已上线！

**语音听写流式API JAVA-SDK-DEMO调用视频说明**请点击 **[这里](https://www.xfyun.cn/doc/asr/voicedictation/API.html#调用示例)** 观看。

**动态修正**：可到这里 [动态修正效果 ](https://www.xfyun.cn/services/voicedictation)在线体验

- **未开启动态修正**：实时返回识别结果，每次返回的结果都是对之前结果的追加；
- **开启动态修正**：实时返回识别结果，每次返回的结果有可能是对之前结果的追加，也有可能是要替换之前某次返回的结果（即修正）；
- 开启动态修正，相较于未开启，返回结果的颗粒度更小，视觉冲击效果更佳；
- 使用动态修正功能直接设置**相应参数**方可使用，参数设置方法详见 [业务参数说明](https://www.xfyun.cn/doc/asr/voicedictation/API.html#业务参数) ；
- 动态修正功能仅 **中文** 支持；
- 未开启与开启返回的结果格式不同，详见 [动态修正返回结果](https://www.xfyun.cn/doc/asr/voicedictation/API.html#动态修正返回参数) ；

**小语种**

- 支持的语种请到[语音听写 ](https://www.xfyun.cn/services/voicedictation)页面或控制台查看；
- 使用少数民族语言和小语种时，**URL和中英文URL不同**，详见 [接口要求](https://www.xfyun.cn/doc/asr/voicedictation/API.html#接口要求) ；
- 小语种参数设置方法详见 [业务参数说明](https://www.xfyun.cn/doc/asr/voicedictation/API.html#业务参数) ；

**多方言免切**

- 支持四川话、河南话、东北话、粤语、闽南话、山东话、贵州话等在内的23种方言免切换识别，具体请到[业务参数说明](https://www.xfyun.cn/doc/asr/voicedictation/API.html#业务参数)查看；
- 参数设置方法详见 [业务参数说明](https://www.xfyun.cn/doc/asr/voicedictation/API.html#业务参数) ；

该语音能力是通过Websocket API的方式给开发者提供一个通用的接口。Websocket API具备流式传输能力，适用于需要流式数据传输的AI服务场景，比如边说话边识别。相较于SDK，API具有轻量、跨语言的特点；相较于HTTP API，Websocket API协议有原生支持跨域的优势。

原WebAPI普通版本接口(http[s]: //api.xfyun.cn/v1/service/v1/iat) 不再对外开放，已经使用WebAPI普通版本的用户仍可使用，同时也欢迎体验新版流式接口并尽快完成迁移~

## [#](https://www.xfyun.cn/doc/asr/voicedictation/API.html#接口demo)接口Demo

**示例demo**请点击 **[这里](https://www.xfyun.cn/doc/asr/voicedictation/API.html#调用示例)** 下载。
目前仅提供部分开发语言的demo，其他语言请参照下方接口文档进行开发。
也欢迎热心的开发者到 [讯飞开放平台社区 ](https://developer.xfyun.cn/)分享你们的demo。

## [#](https://www.xfyun.cn/doc/asr/voicedictation/API.html#接口要求)接口要求

集成语音听写流式API时，需按照以下要求。

| 内容     | 说明                                                         |
| :------- | :----------------------------------------------------------- |
| 请求协议 | ws[s]（为提高安全性，强烈推荐wss）                           |
| 请求地址 | 中英文(**推荐使用**)：ws[s]: //iat-api.xfyun.cn/v2/iat 中英文：ws[s]: //ws-api.xfyun.cn/v2/iat **小语种**：ws[s]: //iat-niche-api.xfyun.cn/v2/iat *注：服务器IP不固定，为保证您的接口稳定，请勿通过指定IP的方式调用接口，使用域名方式调用* |
| 请求行   | GET /v2/iat HTTP/1.1                                         |
| 接口鉴权 | 签名机制，详情请参照下方[接口鉴权](https://www.xfyun.cn/doc/asr/voicedictation/API.html#接口鉴权) |
| 字符编码 | UTF-8                                                        |
| 响应格式 | 统一采用JSON格式                                             |
| 开发语言 | 任意，只要可以向讯飞云服务发起Websocket请求的均可            |
| 操作系统 | 任意                                                         |
| 音频属性 | 采样率16k或8K、位长16bit、单声道                             |
| 音频格式 | pcm speex（8k） speex-wb（16k） mp3（仅中文普通话和英文支持，其他方言及小语种敬请期待） 样例音频请参照[音频样例](https://www.xfyun.cn/doc/asr/voicedictation/API.html#音频样例) |
| 音频长度 | 最长60s                                                      |
| 语言种类 | 中文、英文、小语种以及中文方言，可在控制台-语音听写（流式版）-方言/语种处添加试用或购买 |

## [#](https://www.xfyun.cn/doc/asr/voicedictation/API.html#接口调用流程)接口调用流程

- 通过接口密钥基于hmac-sha256计算签名，向服务器端发送Websocket协议握手请求。详见下方 [接口鉴权](https://www.xfyun.cn/doc/asr/voicedictation/API.html#接口鉴权) 。
- 握手成功后，客户端通过Websocket连接同时上传和接收数据。数据上传完毕，客户端需要上传一次数据结束标识。详见下方 [接口数据传输与接收](https://www.xfyun.cn/doc/asr/voicedictation/API.html#接口数据传输与接收) 。
- 接收到服务器端的结果全部返回标识后断开Websocket连接。

*注： Websocket使用注意事项如下*

1. 服务端支持的websocket-version 为13，请确保客户端使用的框架支持该版本。
2. 服务端返回的所有的帧类型均为TextMessage，对应于原生websocket的协议帧中opcode=1，请确保客户端解析到的帧类型一定为该类型，如果不是，请尝试升级客户端框架版本，或者更换技术框架。
3. 如果出现分帧问题，即一个json数据包分多帧返回给了客户端，导致客户端解析json失败。出现这种问题大部分情况是客户端的框架对websocket协议解析存在问题，如果出现请先尝试升级框架版本，或者更换技术框架。
4. 客户端会话结束后如果需要关闭连接，尽量保证传给服务端的错误码为websocket错误码1000（如果客户端框架没有提供关闭时传错误码的接口。则无需关注本条）。

### [#](https://www.xfyun.cn/doc/asr/voicedictation/API.html#白名单)白名单

默认关闭IP白名单，即该服务不限制调用IP。
在调用该业务接口时

- 若关闭IP白名单，接口认为IP不限，不会校验IP。
- 若打开IP白名单，则服务端会检查调用方IP是否在讯飞开放平台配置的IP白名单中，对于没有配置到白名单中的IP发来的请求，服务端会拒绝服务。

IP白名单规则

- 在 控制台-相应服务的IP白名单处编辑，保存后五分钟左右生效。
- 不同Appid的不同服务都需要分别设置IP白名单。
- IP白名单需设置为外网IP，请勿设置局域网IP。
- 如果握手阶段返回{"message":"Your IP address is not allowed"}，则表示由于IP白名单配置有误或还未生效，服务端拒绝服务。

### [#](https://www.xfyun.cn/doc/asr/voicedictation/API.html#接口鉴权)接口鉴权

在握手阶段，请求方需要对请求进行签名，服务端通过签名来校验请求的合法性。

#### [#](https://www.xfyun.cn/doc/asr/voicedictation/API.html#鉴权方法)鉴权方法

通过在请求地址后面加上鉴权相关参数的方式。示例url：

```text
wss://iat-api.xfyun.cn/v2/iat?authorization=YXBpX2tleT0ia2V5eHh4eHh4eHg4ZWUyNzkzNDg1MTlleHh4eHh4eHgiLCBhbGdvcml0aG09ImhtYWMtc2hhMjU2IiwgaGVhZGVycz0iaG9zdCBkYXRlIHJlcXVlc3QtbGluZSIsIHNpZ25hdHVyZT0iSHAzVHk0WmtTQm1MOGpLeU9McFFpdjlTcjVudm1lWUVIN1dzTC9aTzJKZz0i&date=Wed%2C%2010%20Jul%202019%2007%3A35%3A43%20GMT&host=iat-api.xfyun.cn
```

鉴权参数：

| 参数          | 类型   | 必须 | 说明                                                  | 示例                              |
| :------------ | :----- | :--- | :---------------------------------------------------- | :-------------------------------- |
| host          | string | 是   | 请求主机                                              | iat-api.xfyun.cn                  |
| date          | string | 是   | 当前时间戳，RFC1123格式                               | Wed, 10 Jul 2019 07:35:43 GMT     |
| authorization | string | 是   | 使用base64编码的签名相关信息(签名基于hmac-sha256计算) | 参考下方authorization参数生成规则 |

**· date参数生成规则**

date必须是UTC+0或GMT时区，RFC1123格式(Wed, 10 Jul 2019 07:35:43 GMT)。
服务端会对Date进行时钟偏移检查，最大允许300秒的偏差，超出偏差的请求都将被拒绝。

**· authorization参数生成规则**

1）获取接口密钥APIKey 和 APISecret。
在讯飞开放平台控制台，创建WebAPI平台应用并添加语音听写（流式版）服务后即可查看，均为32位字符串。

2）参数authorization base64编码前（authorization_origin）的格式如下。

```text
api_key="$api_key",algorithm="hmac-sha256",headers="host date request-line",signature="$signature"
```

其中 api_key 是在控制台获取的APIKey，algorithm 是加密算法（仅支持hmac-sha256），headers 是参与签名的参数（见下方注释）。
signature 是使用加密算法对参与签名的参数签名后并使用base64编码的字符串，详见下方。

***注：\* headers是参与签名的参数，请注意是固定的参数名（"host date request-line"），而非这些参数的值。**

3）signature的原始字段(signature_origin)规则如下。

signature原始字段由 host，date，request-line三个参数按照格式拼接成，
拼接的格式为(\n为换行符,’:’后面有一个空格)：

```text
host: $host\ndate: $date\n$request-line
```

假设

```text
请求url = wss://iat-api.xfyun.cn/v2/iat
date = Wed, 10 Jul 2019 07:35:43 GMT
```

那么 signature原始字段(signature_origin)则为：

```text
host: iat-api.xfyun.cn
date: Wed, 10 Jul 2019 07:35:43 GMT
GET /v2/iat HTTP/1.1
```

4）使用hmac-sha256算法结合apiSecret对signature_origin签名，获得签名后的摘要signature_sha。

```text
signature_sha=hmac-sha256(signature_origin,$apiSecret)
```

其中 apiSecret 是在控制台获取的APISecret

5）使用base64编码对signature_sha进行编码获得最终的signature。

```text
signature=base64(signature_sha)
```

假设

```text
APISecret = secretxxxxxxxx2df7900c09xxxxxxxx	
date = Wed, 10 Jul 2019 07:35:43 GMT
```

则signature为

```text
signature=Hp3Ty4ZkSBmL8jKyOLpQiv9Sr5nvmeYEH7WsL/ZO2Jg=
```

6）根据以上信息拼接authorization base64编码前（authorization_origin）的字符串，示例如下。

```text
api_key="keyxxxxxxxx8ee279348519exxxxxxxx", algorithm="hmac-sha256", headers="host date request-line", signature="Hp3Ty4ZkSBmL8jKyOLpQiv9Sr5nvmeYEH7WsL/ZO2Jg="
```

*注：* headers是参与签名的参数，请注意是固定的参数名（"host date request-line"），而非这些参数的值。

7）最后再对authorization_origin进行base64编码获得最终的authorization参数。

```text
authorization = base64(authorization_origin)
示例：
authorization=YXBpX2tleT0ia2V5eHh4eHh4eHg4ZWUyNzkzNDg1MTlleHh4eHh4eHgiLCBhbGdvcml0aG09ImhtYWMtc2hhMjU2IiwgaGVhZGVycz0iaG9zdCBkYXRlIHJlcXVlc3QtbGluZSIsIHNpZ25hdHVyZT0iSHAzVHk0WmtTQm1MOGpLeU9McFFpdjlTcjVudm1lWUVIN1dzTC9aTzJKZz0i
```

#### [#](https://www.xfyun.cn/doc/asr/voicedictation/API.html#鉴权url示例-golang)鉴权url示例(golang)

```go
    //@hosturl :  like  wss://iat-api.xfyun.cn/v2/iat
    //@apikey : apiKey
    //@apiSecret : apiSecret
    func assembleAuthUrl(hosturl string, apiKey, apiSecret string) string {
        ul, err := url.Parse(hosturl)
        if err != nil {
            fmt.Println(err)
        }
        //签名时间
        date := time.Now().UTC().Format(time.RFC1123)
        //参与签名的字段 host ,date, request-line
        signString := []string{"host: " + ul.Host, "date: " + date, "GET " + ul.Path + " HTTP/1.1"}
        //拼接签名字符串
        sgin := strings.Join(signString, "\n")
        //签名结果
        sha := HmacWithShaTobase64("hmac-sha256", sgin, apiSecret)
        //构建请求参数 此时不需要urlencoding
        authUrl := fmt.Sprintf("api_key=\"%s\", algorithm=\"%s\", headers=\"%s\", signature=\"%s\"", apiKey,
            "hmac-sha256", "host date request-line", sha)
        //将请求参数使用base64编码
        authorization:= base64.StdEncoding.EncodeToString([]byte(authUrl))
        v := url.Values{}
        v.Add("host", ul.Host)
        v.Add("date", date)
        v.Add("authorization", authorization)
        //将编码后的字符串url encode后添加到url后面
        callurl := hosturl + "?" + v.Encode()
        return callurl
    }
```

#### [#](https://www.xfyun.cn/doc/asr/voicedictation/API.html#鉴权结果)鉴权结果

如果握手成功，会返回HTTP 101状态码，表示协议升级成功；如果握手失败，则根据不同错误类型返回不同HTTP Code状态码，同时携带错误描述信息，详细错误说明如下：

| HTTP Code | 说明                  | 错误描述信息                                                 | 解决方法                                                     |
| :-------- | :-------------------- | :----------------------------------------------------------- | :----------------------------------------------------------- |
| 401       | 缺少authorization参数 | {“message”:”Unauthorized”}                                   | 检查是否有authorization参数，详情见[authorization参数详细生成规则](https://www.xfyun.cn/doc/asr/voicedictation/API.html#鉴权方法) |
| 401       | 签名参数解析失败      | {“message”:”HMAC signature cannot be verified”}              | 检查签名的各个参数是否有缺失是否正确，特别确认下复制的**api_key**是否正确 |
| 401       | 签名校验失败          | {“message”:”HMAC signature does not match”}                  | 签名验证失败，可能原因有很多。 1. 检查api_key,api_secret 是否正确 2.检查计算签名的参数host，date，request-line是否按照协议要求拼接。 3. 检查signature签名的base64长度是否正常(正常44个字节)。 |
| 403       | 时钟偏移校验失败      | {“message”:”HMAC signature cannot be verified, a valid date or x-date header is required for HMAC Authentication”} | 检查服务器时间是否标准，相差5分钟以上会报此错误              |
| 403       | IP白名单校验失败      | {"message":"Your IP address is not allowed"}                 | 可在控制台关闭IP白名单，或者检查IP白名单设置的IP地址是否为本机外网IP地址 |

握手失败返回示例：

```text
    HTTP/1.1 401 Forbidden
    Date: Thu, 06 Dec 2018 07:55:16 GMT
    Content-Length: 116
    Content-Type: text/plain; charset=utf-8
    {
        "message": "HMAC signature does not match"
    }
```

### [#](https://www.xfyun.cn/doc/asr/voicedictation/API.html#接口数据传输与接收)接口数据传输与接收

握手成功后客户端和服务端会建立Websocket连接，客户端通过Websocket连接可以同时上传和接收数据。
当服务端有识别结果时，会通过Websocket连接推送识别结果到客户端。

发送数据时，如果间隔时间太短，可能会导致引擎识别有误。
建议每次发送音频间隔40ms，每次发送音频字节数（即java示例demo中的frameSize）为一帧音频大小的整数倍。

```java
//连接成功，开始发送数据
int frameSize = 1280; //每一帧音频大小的整数倍，请注意不同音频格式一帧大小字节数不同，可参考下方建议
int intervel = 40;
int status = 0;  // 音频的状态
try (FileInputStream fs = new FileInputStream(file)) {
    byte[] buffer = new byte[frameSize];
    // 发送音频
```

请注意不同音频格式一帧大小的字节数不同，我们建议：

1. 未压缩的PCM格式，每次发送音频间隔40ms，每次发送音频字节数1280B；
2. 讯飞定制speex格式，每次发送音频间隔40ms，假如16k的压缩等级为7，则每次发送61B的整数倍；
3. 标准开源speex格式，每次发送音频间隔40ms，假如16k的压缩等级为7，则每次发送60B的整数倍；

| 讯飞定制speex（压缩等级） | 0    | 1    | 2    | 3    | 4    | 5    | 6    | 7    | 8    | 9    | 10   |
| :------------------------ | :--- | :--- | :--- | :--- | :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| speex 8k                  | 7    | 11   | 16   | 21   | 21   | 29   | 29   | 39   | 39   | 47   | 63   |
| speex-wb 16k              | 11   | 16   | 21   | 26   | 33   | 43   | 53   | 61   | 71   | 87   | 107  |

| 标准开源speex（压缩等级） | 0    | 1    | 2    | 3    | 4    | 5    | 6    | 7    | 8    | 9    | 10   |
| :------------------------ | :--- | :--- | :--- | :--- | :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| speex 8k                  | 6    | 10   | 15   | 20   | 20   | 28   | 28   | 38   | 38   | 46   | 62   |
| speex-wb 16k              | 10   | 15   | 20   | 25   | 32   | 42   | 52   | 60   | 70   | 86   | 106  |

speex相关说明详见[speex编码](https://www.xfyun.cn/doc/asr/voicedictation/Audio.html#speex编码)

整个会话时长最多持续60s，或者超过10s未发送数据，服务端会主动断开连接。
数据上传完毕，客户端需要上传一次数据结束标识表示会话已结束，详见下方data参数说明。

#### [#](https://www.xfyun.cn/doc/asr/voicedictation/API.html#请求参数)请求参数

请求数据均为json字符串

| 参数名   | 类型   | 必传 | 描述                                                         |
| :------- | :----- | :--- | :----------------------------------------------------------- |
| common   | object | 是   | 公共参数，仅在握手成功后首帧请求时上传，详见下方             |
| business | object | 是   | 业务参数，仅在握手成功后首帧请求时上传，详见下方             |
| data     | object | 是   | 业务数据流参数，在握手成功后的所有请求中都需要上传，详见下方 |

##### [#](https://www.xfyun.cn/doc/asr/voicedictation/API.html#公共参数说明)公共参数说明

common

| 参数名 | 类型   | 必传 | 描述                  |
| :----- | :----- | :--- | :-------------------- |
| app_id | string | 是   | 在平台申请的APPID信息 |

##### [#](https://www.xfyun.cn/doc/asr/voicedictation/API.html#业务参数)业务参数

business

| 参数名     | 类型   | 必传 | 描述                                                         | 示例       |
| ---------- | ------ | ---- | ------------------------------------------------------------ | ---------- |
| language   | string | 是   | 语种 zh_cn：中文（支持简单的英文识别） en_us：英文 其他小语种：可到控制台-语音听写（流式版）-方言/语种处添加试用或购买，添加后会显示该小语种参数值，若未授权无法使用会报错11200。 另外，小语种接口URL与中英文不同，详见[接口要求](https://www.xfyun.cn/doc/asr/voicedictation/API.html#接口要求)。 | "zh_cn"    |
| domain     | string | 是   | 应用领域 iat：日常用语 xfime-mianqie：方言免切（支持23种方言+中文普通话混合识别） medical：医疗 gov-seat-assistant：政务坐席助手 seat-assistant：金融坐席助手 gov-ansys：政务语音分析 gov-nav：政务语音导航 fin-nav：金融语音导航 fin-ansys：金融语音分析 *注*：除日常用语领域外其他领域若未授权无法使用，可到控制台-语音听写（流式版）-高级功能处添加试用或购买；方言免切需在方言/语种处添加使用或购买；若未授权无法使用会报错11200。 坐席助手、语音导航、语音分析相关垂直领域仅适用于8k采样率的音频数据，另外三者的区别详见下方。 方言免切23种方言：四川话、河南话、东北话、粤语、闽南话、山东话、贵州话、云南话、客家话、天津话、河北话、太原话、上海话、合肥话、南京话、皖北话、台湾话、甘肃话、陕西话、宁夏话、长沙话、南昌话、武汉话。 | "iat"      |
| accent     | string | 是   | 方言，当前仅在language为中文时，支持方言选择。 mandarin：中文普通话、其他语种 其他方言：可到控制台-语音听写（流式版）-方言/语种处添加试用或购买，添加后会显示该方言参数值；方言若未授权无法使用会报错11200。 | "mandarin" |
| vad_eos    | int    | 否   | 用于设置后端点检测的静默时间，单位是毫秒。 即静默多长时间后引擎认为音频结束。 默认2000（小语种除外，小语种不设置该参数默认为未开启VAD）。 | 3000       |
| dwa        | string | 否   | （仅中文普通话支持）动态修正 wpgs：开启流式结果返回功能      | "wpgs"     |
| pd         | string | 否   | （仅中文支持）领域个性化参数 game：游戏 health：健康 shopping：购物 trip：旅行 | "game"     |
| ptt        | int    | 否   | （仅中文支持）是否开启标点符号添加 1：开启（默认值） 0：关闭 | 0          |
| pcm        | int    | 否   | 标点返回位置控制，开启后标点会缓存到下一句句首返回(返回标点更准确) 1：开启（默认值） 0：关闭 注：关闭之后标点会显示在上一句句尾 | 1          |
| rlang      | string | 否   | （仅中文支持）字体 zh-cn :简体中文（默认值） zh-hk :繁体香港 | "zh-cn"    |
| vinfo      | int    | 否   | 返回子句结果对应的起始和结束的端点帧偏移值。端点帧偏移值表示从音频开头起已过去的帧长度。 0：关闭（默认值） 1：开启 开启后返回的结果中会增加data.result.vad字段，详见下方返回结果。 *注：若使用了动态修正功能，则该功能无法使用。* | 1          |
| nunum      | int    | 否   | （中文普通话和日语支持）将返回结果的数字格式规则为阿拉伯数字格式，默认开启 0：关闭 1：开启 | 0          |
| speex_size | int    | 否   | speex音频帧长，仅在speex音频时使用 1 当speex编码为标准开源speex编码时必须指定 2 当speex编码为讯飞定制speex编码时不要设置 注：标准开源speex以及讯飞定制SPEEX编码工具请参考这里 [speex编码 ](https://www.xfyun.cn/doc/asr/voicedictation/Audio.html)。 | 70         |
| nbest      | int    | 否   | 取值范围[1,5]，通过设置此参数，获取在发音相似时的句子多侯选结果。设置多候选会影响性能，响应时间延迟200ms左右。 | 3          |
| wbest      | int    | 否   | 取值范围[1,5]，通过设置此参数，获取在发音相似时的词语多侯选结果。设置多候选会影响性能，响应时间延迟200ms左右。 | 5          |

*注：* 多候选效果是由引擎决定的，并非绝对的。即使设置了多候选，如果引擎并没有识别出候选的词或句，返回结果也还是单个。
*注：* 以上common和business参数只需要在握手成功后的第一帧请求时带上。
*注：*
坐席助手：电话坐席助手，一般用于人与人对话的场景。
语音导航：电话语音导航，一般用于机器与人对话的场景。
语音分析：基于大量存量的电话客服录音做质检，即事后音频转文字的场景(识别率会优于前两者)。

##### [#](https://www.xfyun.cn/doc/asr/voicedictation/API.html#业务数据流参数)业务数据流参数

data

| 参数名   | 类型   | 必传 | 描述                                                         |
| :------- | :----- | :--- | :----------------------------------------------------------- |
| status   | int    | 是   | 音频的状态 0 :第一帧音频 1 :中间的音频 2 :最后一帧音频，最后一帧必须要发送 |
| format   | string | 是   | 音频的采样率支持16k和8k 16k音频：audio/L16;rate=16000 8k音频：audio/L16;rate=8000 |
| encoding | string | 是   | 音频数据格式 raw：原生音频（支持单声道的pcm） speex：speex压缩后的音频（8k） speex-wb：speex压缩后的音频（16k） 请注意压缩前也必须是采样率16k或8k单声道的pcm。 lame：mp3格式（仅中文普通话和英文支持，方言及小语种暂不支持） 样例音频请参照[音频样例](https://www.xfyun.cn/doc/asr/voicedictation/API.html#音频样例) |
| audio    | string | 是   | 音频内容，采用base64编码                                     |

请求参数示例：

```json
    {  
        "common":{
           // 公共请求参数
           "app_id":"123456"  
        },
        "business":{
            "language":"zh_cn",
            "domain":"iat",
            "accent":"mandarin"
        },
        "data":{
                "status":0,
                "format":"audio/L16;rate=16000",
                "encoding":"raw",
                "audio":"exSI6ICJlbiIsCgkgICAgInBvc2l0aW9uIjogImZhbHNlIgoJf..."    
        }
    }
```

数据上传结束标识示例：

```json
    {
    "data":{
      "status":2
        }
    }
```

#### [#](https://www.xfyun.cn/doc/asr/voicedictation/API.html#返回参数)返回参数

| 参数                                      | 类型       | 描述                                                         |
| ----------------------------------------- | ---------- | ------------------------------------------------------------ |
| sid                                       | string     | 本次会话的id，只在握手成功后第一帧请求时返回                 |
| code                                      | int        | 返回码，0表示成功，其它表示异常，详情请参考[错误码](https://www.xfyun.cn/doc/asr/voicedictation/API.html#错误码) |
| message                                   | string     | 错误描述                                                     |
| data                                      | object     | 听写结果信息                                                 |
| data.status                               | int        | 识别结果是否结束标识： 0：识别的第一块结果 1：识别中间结果 2：识别最后一块结果 |
| data.result                               | object     | 听写识别结果                                                 |
| data.result.sn                            | int        | 返回结果的序号                                               |
| data.result.ls                            | bool       | 是否是最后一片结果                                           |
| data.result.bg                            | int        | 保留字段，无需关心                                           |
| data.result.ed                            | int        | 保留字段，无需关心                                           |
| data.result.ws                            | array      | 听写结果                                                     |
| data.result.ws.bg                         | int        | 起始的端点帧偏移值，单位：帧（1帧=10ms） 注：以下两种情况下bg=0，无参考意义： 1)返回结果为标点符号或者为空；2)本次返回结果过长。 |
| data.result.ws.cw                         | array      | 中文分词                                                     |
| data.result.ws.cw.w                       | string     | 字词                                                         |
| data.result.ws.cw.其他字段 sc/wb/wc/we/wp | int/string | 均为保留字段，无需关心。如果解析sc字段，建议float与int数据类型都做兼容 |

##### [#](https://www.xfyun.cn/doc/asr/voicedictation/API.html#动态修正返回参数)动态修正返回参数

若开通了动态修正功能并设置了dwa=wpgs（仅中文支持），还有如下字段返回：
*注：动态修正结果解析可参考页面下方的java demo。*

| 参数            | 类型   | 描述                                                         |
| --------------- | ------ | ------------------------------------------------------------ |
| data.result.pgs | string | 开启wpgs会有此字段 取值为 "apd"时表示该片结果是追加到前面的最终结果；取值为"rpl" 时表示替换前面的部分结果，替换范围为rg字段 |
| data.result.rg  | array  | 替换范围，开启wpgs会有此字段 假设值为[2,5]，则代表要替换的是第2次到第5次返回的结果 |

##### [#](https://www.xfyun.cn/doc/asr/voicedictation/API.html#vinfo返回参数)vinfo返回参数

若设置了vinfo=1，还有如下字段返回（若同时开通并设置了dwa=wpgs，则vinfo失效）：

| 参数               | 类型   | 描述                                     |
| ------------------ | ------ | ---------------------------------------- |
| data.result.vad    | object | 端点帧偏移值信息                         |
| data.result.vad.ws | array  | 端点帧偏移值结果                         |
| data.result.vad.bg | int    | 起始的端点帧偏移值，单位：帧（1帧=10ms） |
| data.result.vad.ed | int    | 结束的端点帧偏移值，单位：帧（1帧=10ms） |
| data.result.vad.eg | number | 无需关心                                 |

返回参数示例（动态修正dwa=wpgs）
*注：动态修正结果解析可参考页面下方的java demo。*

```json
	{
	  "code": 0,
	  "message": "success",
	  "sid": "iatxxxxxxxxxxxxx",
	  "data": {
	    "result": {
	      "bg": 0,
	      "ed": 0,
	      "ls": false,
	      "pgs": "rpl",
	      "rg": [
	        1,
	        1
	      ],
	      "sn": 2,
	      "ws": [
	        {
	          "bg": 0,
	          "cw": [
	            {
	              "sc": 0,
	              "w": "测试"
	            }
	          ]
	        },
	        {
	          "bg": 0,
	          "cw": [
	            {
	              "sc": 0,
	              "w": "一下"
	            }
	          ]
	        }
	      ]
	    },
	    "status": 1
	  }
	}
```

返回参数示例（vinfo=1）

```json
{
  "code": 0,
  "message": "success",
  "sid": "iatxxxxxxxxxxxxxx",
  "data": {
    "result": {
      "bg": 0,
      "ed": 0,
      "ls": false,
      "sn": 1,
      "vad": {
        "ws": [
          {
            "bg": 40,
            "ed": 366,
            "eg": 63.58
          }
        ]
      },
      "ws": [
        {
          "bg": 53,
          "cw": [
            {
              "sc": 0,
              "w": "4月"
            }
          ]
        },
        {...},
        {
          "bg": 293,
          "cw": [
            {
              "sc": 0,
              "w": "选手"
            }
          ]
        }
      ]
    },
    "status": 1
  }
}
```

返回参数示例（句子多候选nbest）

```json
{
  "code": 0,
  "message": "success",
  "sid": "iatxxxxxxxxxxxxx",
  "data": {
    "result": {
      "bg": 0,
      "ed": 0,
      "ls": false,
      "sn": 1,
      "ws": [
        {
          "bg": 35,
          "cw": [
            {
              "sc": 0,
              "w": "打电话给梁玉生"
            },
            {
              "sc": 0,
              "w": "打电话给梁玉升"
            }
          ]
        }
      ]
    },
    "status": 0
  }
}
```

返回参数示例（词级多候选wbest）

```json
{
  "code": 0,
  "message": "success",
  "sid": "iatxxxxxxxxxxxxxx",
  "data": {
    "result": {
      "bg": 0,
      "ed": 0,
      "ls": false,
      "sn": 1,
      "ws": [
        {...},
        {
          "bg": 159,
          "cw": [
            {
              "sc": 0,
              "w": "梁"
            }
          ]
        },
        {
          "bg": 191,
          "cw": [
            {
              "sc": 0,
              "w": "玉"
            },
            {
              "sc": 0,
              "w": "育"
            }
          ]
        },
        {
          "bg": 215,
          "cw": [
            {
              "sc": 0,
              "w": "生"
            },
            {
              "sc": 0,
              "w": "升"
            }
          ]
        }
      ]
    },
    "status": 0
  }
}
```

## [#](https://www.xfyun.cn/doc/asr/voicedictation/API.html#错误码)错误码

备注：如出现下述列表中没有的错误码，可到 [这里 ](https://www.xfyun.cn/document/error-code)查询。

| 错误码 | 错误描述                                                | 说明                         | 处理方式                                                     |
| ------ | ------------------------------------------------------- | ---------------------------- | ------------------------------------------------------------ |
| 10005  | licc fail                                               | appid授权失败                | 确认appid是否正确，是否开通了听写服务                        |
| 10006  | Get audio rate fail                                     | 获取某个参数失败             | 检查报错信息中的参数是否正确上传                             |
| 10007  | get invalid rate                                        | 参数值不合法                 | 检查报错信息中的参数值是否在取值范围内                       |
| 10010  | AIGES_ERROR_NO_LICENSE                                  | 引擎授权不足                 | 请到控制台提交工单联系技术人员                               |
| 10014  | AIGES_ERROR_TIME_OUT                                    | 会话超时                     |                                                              |
| 10019  | service read buffer timeout, session timeout            | session超时                  | 检查是否数据发送完毕但未关闭连接                             |
| 10043  | Syscall AudioCodingDecode error                         | 音频解码失败                 | 检查aue参数，如果为speex，请确保音频是speex音频并分段压缩且与帧大小一致 |
| 10101  | engine inavtive                                         | 引擎会话已结束               | 检查是否引擎已结束会话但客户端还在发送数据，比如音频数据虽然发送完毕但并未关闭websocket连接，还在发送空的音频等 |
| 10114  | session timeout                                         | 会话超时                     | 检查整个会话是否已经超过了60s                                |
| 10139  | invalid param                                           | 参数错误                     | 引擎编解码错误                                               |
| 10313  | appid cannot be empty                                   | appid不能为空                | 检查common参数是否正确上传，或common中的app_id参数是否正确上传或是否为空 |
| 10317  | invalid version                                         | 版本非法                     | 联系技术人员                                                 |
| 11200  | auth no license                                         | 没有权限                     | 检查是否使用了未授权的功能，或者总的调用次数已超越上限       |
| 11201  | auth no enough license                                  | 日流控超限                   | 可联系商务提高每日调用次数                                   |
| 10160  | parse request json error                                | 请求数据格式非法             | 检查请求数据是否是合法的json                                 |
| 10161  | parse base64 string error                               | base64解码失败               | 检查发送的数据是否使用了base64编码                           |
| 10163  | param validate error:/common 'app_id' param is required | 缺少必传参数，或者参数不合法 | 检查报错信息中的参数是否正确上传                             |
| 10165  | invalid handle                                          | 无效的句柄                   | 检查下传入第一帧音频时，是否上传了status=0                   |
| 10200  | read data timeout                                       | 读取数据超时                 | 检查是否累计10s未发送数据并且未关闭连接                      |

## [#](https://www.xfyun.cn/doc/asr/voicedictation/API.html#调用示例)调用示例

*注: demo只是一个简单的调用示例，不适合直接放在复杂多变的生产环境使用*

[语音听写流式API demo java语言](https://xfyun-doc.xfyun.cn/1602142850219788/iat_ws_java_demo.zip)

[语音听写流式API demo python3语言](https://xfyun-doc.xfyun.cn/static/16732428966724729/iat_ws_python3_demo.zip)

[语音听写流式API demo js语言](https://xfyun-doc.xfyun.cn/static/16902656744143549/iat-js-demo.zip)

[语音听写流式API demo go语言](https://xfyun-doc.xfyun.cn/1602142800879963/iat_ws_go_demo.zip)

[语音听写流式API demo nodejs语言](https://xfyun-doc.xfyun.cn/1568629859354753/iat_ws_nodejs_demo.zip)

[语音听写流式API JAVA-SDK-DEMO](https://github.com/iFLYTEK-OP/websdk-java-demo)

语音听写流式API JAVA-SDK-DEMO 麦克风版本一分钟调用视频如下：

<video width="640" height="360" controls="controls" id="iat_xlliu24_202504161112" __idm_id__="2211843" style="color: rgb(44, 62, 80); font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, Roboto, Oxygen, Ubuntu, Cantarell, &quot;Fira Sans&quot;, &quot;Droid Sans&quot;, &quot;Helvetica Neue&quot;, sans-serif; font-size: 14px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: start; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial;"></video>



语音听写流式API JAVA-SDK-DEMO一分钟调用视频如下：

<video width="640" height="360" controls="controls" id="iat_xlliu24_202504161113" __idm_id__="2211844" style="color: rgb(44, 62, 80); font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, Roboto, Oxygen, Ubuntu, Cantarell, &quot;Fira Sans&quot;, &quot;Droid Sans&quot;, &quot;Helvetica Neue&quot;, sans-serif; font-size: 14px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: start; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial;"></video>



讯飞开放平台AI能力-JAVASDK: [Github地址](https://github.com/iFLYTEK-OP/websdk-java)

*注：* 其他开发语言请参照 [接口调用流程](https://www.xfyun.cn/doc/asr/voicedictation/API.html#接口调用流程) 进行开发，也欢迎热心的开发者到 [讯飞开放平台社区 ](https://developer.xfyun.cn/)分享你们的demo。

## [#](https://www.xfyun.cn/doc/asr/voicedictation/API.html#音频样例)音频样例

[语音听写流式 音频样例 中文普通话 PCM文件 采样率16k](https://xfyun-doc.xfyun.cn/1556089516788301/iat_pcm_16k.pcm)

[语音听写流式 音频样例 中文普通话 PCM文件 采样率8k](https://xfyun-doc.xfyun.cn/1556089531011321/iat_pcm_8k.pcm)

[语音听写流式 音频样例 中文普通话 MP3文件 采样率16k](https://xfyun-doc.xfyun.cn/1597644669753474/iat_mp3_16k.mp3)

[语音听写流式 音频样例 中文普通话 MP3文件 采样率8k](https://xfyun-doc.xfyun.cn/1597648949406450/iat_mp3_8k.mp3)

[语音听写流式 音频样例 中文普通话 SPEEX文件（标准开源SPEEX编码） 采样率8k 7级压缩](https://xfyun-doc.xfyun.cn/1578556853425635/iat_speex_wideband_8k.zip)

[语音听写流式 音频样例 中文普通话 SPEEX文件（标准开源SPEEX编码） 采样率16k 7级压缩](https://xfyun-doc.xfyun.cn/1578556879996420/iat_speex_wideband_16k.zip)

[语音听写流式 音频样例 中文普通话 SPEEX文件（讯飞定制SPEEX编码） 采样率16k 7级压缩](https://xfyun-doc.xfyun.cn/1556089561054645/iat_speex-wb_16k.speex)

[语音听写流式 音频样例 中文普通话 SPEEX文件（讯飞定制SPEEX编码） 采样率8k 7级压缩](https://xfyun-doc.xfyun.cn/1556089604172854/iat_speex_8k.speex)

*注：* 音频文件的录制和格式确认（推荐使用Cool Edit Pro工具），以及讯飞定制SPEEX编码工具请参考这里： [音频格式说明](https://www.xfyun.cn/doc/asr/voicedictation/Audio.html)

## [#](https://www.xfyun.cn/doc/asr/voicedictation/API.html#视频教程)视频教程

[语音听写-WebAPI接口详解](https://www.aidaxue.com/course/courseDetail?id=544&ch=WZsp544)

## [#](https://www.xfyun.cn/doc/asr/voicedictation/API.html#常见问题)常见问题

#### [#](https://www.xfyun.cn/doc/asr/voicedictation/API.html#语音听写的apikey在哪里查询到)语音听写的APIKey在哪里查询到？

> 答：控制台--我的应用---找到对应应用的语音听写（流式）服务---即能查看到。

#### [#](https://www.xfyun.cn/doc/asr/voicedictation/API.html#webapi流式听写能获取到语音听写结果为空或错误内容或者不全-原因是什么)webapi流式听写能获取到语音听写结果为空或错误内容或者不全，原因是什么？

> 答：原因可能如下；
> 1、音频格式不正确，请使用Cool Edit Pro工具（网页搜索下载即可）查看音频格式，webapi听写流式版：支持的格式是pcm、speex、speex-wb，其中中文普通话和英文还支持mp3格式
> 音频采样率要是 16k 或者 8k、采样精度16 位、单声道音频。样例音频请参照[音频样例](https://www.xfyun.cn/doc/asr/voicedictation/API.html#音频样例)
> 2、音频中间有静音或者杂音音频超过了后端点（不设置默认为2000ms）的设置，此时请使用Cool Edit Pro工具查看音频内容，并且设置后端点（vad_eos）为最大值10000ms
> 包含超过后端点最大值的静音或者杂音时，音频识别不完整是正常的

#### [#](https://www.xfyun.cn/doc/asr/voicedictation/API.html#语音听写webapi支持的音频格式有哪些)语音听写WebAPI支持的音频格式有哪些？

> 答：支持8k和16k采样率、16bit、单声道的pcm、mp3、speex格式的音频。需注意mp3格式的音频仅支持中文普通话和英文。

#### [#](https://www.xfyun.cn/doc/asr/voicedictation/API.html#语音听写最长支持多少秒之内的音频)语音听写最长支持多少秒之内的音频？

> 答：听写支持识别60s之内的音频。

#### [#](https://www.xfyun.cn/doc/asr/voicedictation/API.html#语音听写webapi支持多少路并发-如何提高并发)语音听写Webapi支持多少路并发？如何提高并发？

> 答：默认支持50路并发，如需更多并发可提交工单进行咨询。

#### [#](https://www.xfyun.cn/doc/asr/voicedictation/API.html#听写报错10163-length-of-data-audio-must-be-between-0-13000是什么原因)听写报错10163，length of $.data.audio must be between 0,13000是什么原因？

> 答：听写frameSize传的音频大小base64编码后不能超出13000B，默认传1280B不建议传值过大。

#### [#](https://www.xfyun.cn/doc/asr/voicedictation/API.html#为什么每隔一段时间不发送数据就会断开连接)为什么每隔一段时间不发送数据就会断开连接？

> 答：听写vad_eos为支持的最长静音时间，超过这个时间会认为音频结束自动断开连接。

#### [#](https://www.xfyun.cn/doc/asr/voicedictation/API.html#最多支持多少热词-是否可以扩容)最多支持多少热词，是否可以扩容？

> 答：控制台最多支持2000个应用级热词，暂不支持扩容。

#### [#](https://www.xfyun.cn/doc/asr/voicedictation/API.html#如何限制ip)如何限制IP？

> 答：可通过在IP白名单设置自己服务的IP地址，限制其他IP地址访问。


阿里调用示例（Node.js）安装Node.js SDK。建议使用npm完成Node.js依赖模块的安装，所有阿里官方的Node.js SDK都位于@alicloud下。假设Node.js SDK下载后的路径为/path/to/aliyun-openapi-Node.js-sdk。当基于SDK核心库进行开发时，请执行以下命令安装@alicloud/pop-core模块。命令中的--save会将模块写入应用的package.json文件中，作为依赖模块。放大查看复制代码 npm install @alicloud/pop-core --save说明您也可以从GitHub上下载SDK，请参见GitHub下载SDK。调用服务。示例代码如下：放大查看复制代码var RPCClient = require('@alicloud/pop-core').RPCClient;var client = new RPCClient({  accessKeyId: process.env.ALIYUN_AK_ID,  accessKeySecret: process.env.ALIYUN_AK_SECRET,  endpoint: 'http://nls-meta.cn-shanghai.aliyuncs.com',  apiVersion: '2019-02-28'});// => returns Promise// => request(Action, params, options)client.request('CreateToken').then((result) => {    console.log(result.Token)    console.log("token = " + result.Token.Id)    console.log("expireTime = " + result.Token.ExpireTime)});