# Implementation Plan

- [ ] 1. 数据库模型和迁移设置
  - 更新 Prisma schema 文件，添加 ReferralCode、ReferralRelation、ReferralReward 模型
  - 为 User 和 Order 模型添加必要的关联关系
  - 生成并执行数据库迁移文件
  - 验证数据库表结构和索引创建正确
  - _Requirements: 1.1, 2.4, 3.1, 3.2_

- [ ] 2. 后端核心服务实现
  - [ ] 2.1 创建 ReferralService 邀请服务类
    - 实现邀请码生成算法（8位字母数字组合，确保唯一性）
    - 实现邀请码验证逻辑（检查存在性、有效性、是否过期）
    - 实现邀请关系创建和查询功能
    - 实现邀请统计数据计算（总邀请数、成功充值数、累计奖励）
    - _Requirements: 1.1, 1.2, 2.1, 2.2, 4.1, 4.4_

  - [ ] 2.2 创建 RewardService 奖励服务类
    - 实现奖励发放核心逻辑（检查首次充值、防重复发放）
    - 实现用户余额更新功能（增加面巾值）
    - 实现奖励记录创建和状态管理
    - 实现异常处理和事务回滚机制
    - _Requirements: 3.1, 3.2, 3.3, 3.4, 5.1, 5.3_

- [ ] 3. 后端API端点实现
  - [ ] 3.1 实现邀请码管理API
    - GET /api/referral/code - 获取或生成用户邀请码
    - POST /api/referral/validate - 验证邀请码有效性
    - 添加JWT认证中间件和参数验证
    - 实现错误处理和响应格式标准化
    - _Requirements: 1.1, 1.2, 2.2, 2.3_

  - [ ] 3.2 实现邀请统计和历史API
    - GET /api/referral/stats - 获取用户邀请统计数据
    - GET /api/referral/history - 获取邀请记录（支持分页）
    - 实现数据脱敏处理（邮箱部分隐藏）
    - 添加查询优化和缓存机制
    - _Requirements: 4.1, 4.2, 4.3, 4.5_

  - [ ] 3.3 实现奖励处理API
    - POST /api/referral/reward - 内部奖励发放接口
    - 集成到现有订单支付成功回调流程
    - 实现并发安全和重复处理检测
    - 添加详细的操作日志记录
    - _Requirements: 3.1, 3.2, 3.3, 5.1, 5.3_

- [ ] 4. 注册流程集成
  - [ ] 4.1 增强用户注册API
    - 修改注册接口支持邀请码参数
    - 在用户注册成功后创建邀请关系记录
    - 实现邀请码验证和错误处理
    - 确保注册流程向后兼容（邀请码可选）
    - _Requirements: 2.1, 2.2, 2.4, 2.5_

  - [ ] 4.2 更新前端注册页面
    - 在注册表单中添加邀请码输入字段
    - 实现URL参数自动填充邀请码功能
    - 添加邀请码实时验证和错误提示
    - 保持现有注册页面UI风格一致性
    - _Requirements: 2.1, 2.2, 2.3, 6.1, 6.3_

- [ ] 5. 订单支付回调集成
  - [ ] 5.1 增强订单处理逻辑
    - 修改订单状态更新流程，添加邀请奖励检查
    - 实现首次充值检测逻辑
    - 在支付成功后触发邀请奖励发放
    - 添加奖励发放失败的重试机制
    - _Requirements: 3.1, 3.2, 3.4, 5.3_

  - [ ] 5.2 实现通知服务集成
    - 创建奖励发放成功通知模板
    - 集成到现有通知系统（邮件/站内信）
    - 实现通知发送失败的重试逻辑
    - 添加通知发送状态跟踪
    - _Requirements: 3.3, 5.1_

- [ ] 6. 前端分享有礼页面实现
  - [ ] 6.1 创建主页面组件 ReferralRewardsPage
    - 实现页面整体布局和导航
    - 集成邀请码生成、统计展示、历史记录等子组件
    - 实现页面加载状态和错误处理
    - 确保响应式设计和深色模式适配
    - _Requirements: 1.1, 4.1, 6.1, 6.4, 6.5_

  - [ ] 6.2 实现邀请码生成和分享组件
    - 创建 InviteCodeGenerator 组件显示邀请码和链接
    - 实现一键复制功能（邀请码和邀请链接）
    - 添加多平台分享功能（微信、QQ等）
    - 实现分享成功反馈和错误处理
    - _Requirements: 1.1, 1.3, 1.4, 1.5, 6.3_

  - [ ] 6.3 实现统计数据展示组件
    - 创建 ReferralStats 组件展示邀请统计
    - 实现数据可视化（图表或卡片形式）
    - 添加数据实时更新和加载动画
    - 确保数据展示的准确性和美观性
    - _Requirements: 4.1, 4.4, 6.1, 6.2_

  - [ ] 6.4 实现邀请历史记录组件
    - 创建 ReferralHistory 组件展示邀请记录
    - 实现分页加载和无限滚动
    - 添加状态筛选和时间排序功能
    - 实现数据脱敏显示和状态标识
    - _Requirements: 4.2, 4.3, 4.5, 6.1_

- [ ] 7. 路由和导航集成
  - [ ] 7.1 添加分享有礼页面路由
    - 在前端路由配置中添加 /referral-rewards 路由
    - 实现路由守卫确保用户已登录
    - 添加页面标题和面包屑导航
    - 确保路由与现有导航系统集成
    - _Requirements: 6.1, 6.2_

  - [ ] 7.2 更新主导航菜单
    - 在主导航中添加"分享有礼"菜单项
    - 实现菜单项的权限控制和状态管理
    - 确保菜单样式与现有设计一致
    - 添加菜单项的活跃状态指示
    - _Requirements: 6.1, 6.2_

- [ ] 8. 错误处理和日志记录
  - [ ] 8.1 实现全局错误处理
    - 定义邀请相关错误类型和错误码
    - 实现前端错误边界和用户友好提示
    - 添加后端异常捕获和错误响应标准化
    - 实现错误恢复和重试机制
    - _Requirements: 2.3, 5.1, 5.3, 6.3_

  - [ ] 8.2 添加操作日志记录
    - 实现邀请码生成和使用的日志记录
    - 添加奖励发放过程的详细日志
    - 实现异常操作的监控和告警
    - 确保日志信息的完整性和可追溯性
    - _Requirements: 5.1, 5.2, 5.3_

- [ ] 9. 安全性和防护机制
  - [ ] 9.1 实现防刷机制
    - 添加邀请码生成频率限制
    - 实现异常邀请行为检测
    - 添加IP和设备指纹跟踪
    - 实现可疑操作的自动暂停机制
    - _Requirements: 5.1, 5.2_

  - [ ] 9.2 数据安全和权限控制
    - 实现用户数据访问权限验证
    - 添加敏感信息脱敏处理
    - 实现API接口的访问频率限制
    - 确保数据传输和存储的安全性
    - _Requirements: 5.2, 5.4_

- [ ] 10. 测试实现
  - [ ] 10.1 编写单元测试
    - 为 ReferralService 和 RewardService 编写单元测试
    - 测试邀请码生成、验证和奖励发放逻辑
    - 实现API端点的单元测试覆盖
    - 确保测试覆盖率达到90%以上
    - _Requirements: 1.1, 1.2, 2.1, 2.2, 3.1, 3.2_

  - [ ] 10.2 编写集成测试
    - 实现完整邀请流程的端到端测试
    - 测试支付回调和奖励发放的集成
    - 验证前端组件的交互和数据流
    - 测试异常情况和错误恢复机制
    - _Requirements: 2.4, 3.1, 3.2, 4.1, 4.2_

- [ ] 11. 性能优化和监控
  - [ ] 11.1 数据库查询优化
    - 优化邀请统计查询的性能
    - 添加必要的数据库索引
    - 实现查询结果缓存机制
    - 监控慢查询和性能瓶颈
    - _Requirements: 4.1, 4.4, 5.4_

  - [ ] 11.2 前端性能优化
    - 实现组件懒加载和代码分割
    - 优化数据获取和状态管理
    - 添加加载状态和骨架屏
    - 实现图片和资源的优化加载
    - _Requirements: 6.1, 6.2, 6.4_

- [ ] 12. 文档和部署准备
  - [ ] 12.1 编写技术文档
    - 创建API接口文档和使用说明
    - 编写数据库变更和迁移指南
    - 撰写功能使用手册和故障排除指南
    - 更新系统架构和部署文档
    - _Requirements: 5.2, 5.4_

  - [ ] 12.2 部署配置和验证
    - 配置生产环境的数据库迁移
    - 验证所有API端点的正常工作
    - 测试前端页面的完整功能
    - 进行生产环境的压力测试和监控设置
    - _Requirements: 5.1, 5.4_