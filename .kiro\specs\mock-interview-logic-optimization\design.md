# Design Document

## Overview

本设计文档描述了AI模拟面试系统核心逻辑优化的技术方案。通过引入状态机驱动的架构，重构消息类型系统，实现AI面试官先提问、用户后回答的正确面试流程。设计重点解决当前系统中角色定位混乱、流程逻辑错误、ASR生命周期管理不当等核心问题，同时确保与正式面试功能的完全隔离。

### 核心设计原则

1. **状态驱动**: 使用有限状态机管理复杂的面试流程，确保状态转换的可预测性
2. **角色明确**: AI明确充当面试官角色，用户充当求职者角色
3. **功能隔离**: 模拟面试与正式面试功能完全独立，互不影响
4. **性能优先**: 优化响应时间和用户体验，确保流畅的面试过程
5. **容错设计**: 完善的错误处理和恢复机制，提供稳定的服务

## Architecture

### 整体架构

```mermaid
graph TB
    subgraph "Frontend Layer"
        UI[Mock Interview UI]
        SM[State Machine]
        ASR[ASR Manager]
        WS[WebSocket Client]
    end
    
    subgraph "Backend Layer"
        Router[Message Router]
        MockHandler[Mock Interview Handler]
        FormalHandler[Formal Interview Handler]
        AI[AI Question Generator]
        DB[(Database)]
    end
    
    UI --> SM
    SM --> ASR
    SM --> WS
    WS --> Router
    Router --> MockHandler
    Router --> FormalHandler
    MockHandler --> AI
    MockHandler --> DB
```

### 状态机架构

系统采用有限状态机(FSM)管理面试流程，确保状态转换的确定性和可预测性：

```mermaid
stateDiagram-v2
    [*] --> INITIALIZING
    INITIALIZING --> WAITING_FOR_AI_QUESTION: 会话开始
    WAITING_FOR_AI_QUESTION --> LISTENING_FOR_ANSWER: 收到AI问题
    LISTENING_FOR_ANSWER --> PROCESSING_ANSWER: 用户回答完毕
    PROCESSING_ANSWER --> WAITING_FOR_AI_QUESTION: AI生成新问题
    PROCESSING_ANSWER --> COMPLETED: 面试结束
    
    INITIALIZING --> ERROR: 初始化失败
    WAITING_FOR_AI_QUESTION --> ERROR: AI问题生成失败
    LISTENING_FOR_ANSWER --> ERROR: ASR服务失败
    PROCESSING_ANSWER --> ERROR: 回答处理失败
    
    ERROR --> WAITING_FOR_AI_QUESTION: 错误恢复
    ERROR --> [*]: 会话终止
    COMPLETED --> [*]: 会话结束
```

**设计决策理由**: 状态机架构能够清晰地定义每个状态下允许的操作和状态转换条件，避免了当前系统中流程混乱的问题。通过明确的状态定义，可以确保AI只在合适的时机提问，ASR只在需要时启动。

## Components and Interfaces

### 1. 状态机管理器 (State Machine Manager)

负责管理面试流程的状态转换和业务逻辑控制。

```typescript
interface MockInterviewStateMachine {
  currentState: MockInterviewState;
  context: InterviewContext;
  
  // 状态转换方法
  transition(event: StateEvent): Promise<void>;
  
  // 状态查询
  canTransition(event: StateEvent): boolean;
  getCurrentState(): MockInterviewState;
  
  // 错误处理
  handleError(error: InterviewError): Promise<void>;
  reset(): void;
}

enum MockInterviewState {
  INITIALIZING = 'INITIALIZING',
  WAITING_FOR_AI_QUESTION = 'WAITING_FOR_AI_QUESTION',
  LISTENING_FOR_ANSWER = 'LISTENING_FOR_ANSWER',
  PROCESSING_ANSWER = 'PROCESSING_ANSWER',
  COMPLETED = 'COMPLETED',
  ERROR = 'ERROR'
}
```

### 2. 消息类型系统

重构后的消息类型系统，完全独立于正式面试功能。

```typescript
// 模拟面试专用消息类型
interface MockInterviewMessage {
  type: 'interviewer' | 'user-answer';
  timestamp: number;
  sessionId: string;
}

interface InterviewerMessage extends MockInterviewMessage {
  type: 'interviewer';
  content: string;
  questionId: string;
  questionType: 'introduction' | 'technical' | 'behavioral' | 'closing';
  difficulty: 'easy' | 'medium' | 'hard';
  expectedDuration: number; // 预期回答时长(秒)
  context: {
    companyName: string;
    positionName: string;
    previousQuestions: string[];
  };
}

interface UserAnswerMessage extends MockInterviewMessage {
  type: 'user-answer';
  content: string;
  questionId: string;
  isFinal: boolean;
  duration: number; // 实际回答时长(秒)
  confidence: number; // ASR置信度
}
```

**设计决策理由**: 独立的消息类型系统确保了模拟面试和正式面试功能的完全隔离，避免了类型混乱。新的消息结构包含了面试场景所需的所有关键信息，支持个性化问题生成和智能分析。

### 3. ASR生命周期管理器

精确控制语音识别的启动、监听和停止。

```typescript
interface ASRLifecycleManager {
  // ASR控制
  startListening(config: ASRConfig): Promise<void>;
  stopListening(): Promise<string>;
  
  // 状态查询
  isListening(): boolean;
  getStatus(): ASRStatus;
  
  // 事件处理
  onSilenceDetected(callback: (duration: number) => void): void;
  onTranscriptionUpdate(callback: (text: string, isFinal: boolean) => void): void;
  onError(callback: (error: ASRError) => void): void;
  
  // 资源管理
  cleanup(): void;
}

interface ASRConfig {
  silenceThreshold: number; // 静音检测阈值(秒)
  maxDuration: number; // 最大录音时长(秒)
  language: string;
  fallbackProvider?: string; // 备用ASR提供商
}
```

### 4. 个性化问题生成器

基于用户信息和面试进展生成个性化问题。

```typescript
interface QuestionGenerator {
  generateInitialQuestion(context: InterviewContext): Promise<InterviewerMessage>;
  generateFollowUpQuestion(
    context: InterviewContext, 
    previousAnswer: UserAnswerMessage
  ): Promise<InterviewerMessage>;
  
  // 问题去重和质量控制
  validateQuestion(question: string, history: string[]): boolean;
  getFallbackQuestion(questionType: string): InterviewerMessage;
}

interface InterviewContext {
  companyName: string;
  positionName: string;
  userProfile?: UserProfile;
  questionHistory: InterviewerMessage[];
  answerHistory: UserAnswerMessage[];
  interviewDuration: number;
}
```

## Data Models

### 1. 面试会话模型

```typescript
interface MockInterviewSession {
  id: string;
  userId: string;
  companyName: string;
  positionName: string;
  status: 'active' | 'completed' | 'error';
  startTime: Date;
  endTime?: Date;
  
  // 状态信息
  currentState: MockInterviewState;
  currentQuestionId?: string;
  
  // 面试内容
  questions: InterviewerMessage[];
  answers: UserAnswerMessage[];
  
  // 统计信息
  totalQuestions: number;
  totalDuration: number;
  averageResponseTime: number;
}
```

### 2. 问题模板库

```typescript
interface QuestionTemplate {
  id: string;
  type: 'introduction' | 'technical' | 'behavioral' | 'closing';
  difficulty: 'easy' | 'medium' | 'hard';
  template: string; // 支持变量替换的模板
  variables: string[]; // 可替换的变量列表
  tags: string[]; // 标签，用于分类和筛选
  usage_count: number;
  success_rate: number;
}
```

### 3. 错误日志模型

```typescript
interface InterviewErrorLog {
  id: string;
  sessionId: string;
  errorType: 'ASR_FAILURE' | 'AI_GENERATION_FAILURE' | 'WEBSOCKET_ERROR' | 'STATE_TRANSITION_ERROR';
  errorMessage: string;
  stackTrace?: string;
  context: any; // 错误发生时的上下文信息
  timestamp: Date;
  resolved: boolean;
  resolution?: string;
}
```

## Error Handling

### 错误分类和处理策略

1. **ASR服务错误**
   - 自动切换到备用ASR提供商
   - 提供手动输入选项作为最后备选
   - 记录错误日志用于服务质量监控

2. **AI问题生成错误**
   - 使用预设的问题模板库
   - 根据面试进展选择合适的备用问题
   - 确保面试流程不中断

3. **WebSocket连接错误**
   - 自动重连机制，最多重试3次
   - 保存会话状态，重连后恢复
   - 向用户显示连接状态

4. **状态转换错误**
   - 记录详细的状态转换日志
   - 提供状态重置和恢复机制
   - 必要时回滚到安全状态

### 错误恢复机制

```typescript
interface ErrorRecoveryStrategy {
  canRecover(error: InterviewError): boolean;
  recover(error: InterviewError, context: InterviewContext): Promise<RecoveryResult>;
  
  // 预定义恢复策略
  recoverFromASRFailure(): Promise<void>;
  recoverFromAIFailure(): Promise<void>;
  recoverFromConnectionFailure(): Promise<void>;
}

interface RecoveryResult {
  success: boolean;
  newState?: MockInterviewState;
  userMessage?: string;
  requiresUserAction?: boolean;
}
```

**设计决策理由**: 分层的错误处理策略确保了系统的稳定性。通过预定义的恢复策略，系统能够自动处理大部分常见错误，只在必要时才需要用户干预。

## Testing Strategy

### 1. 单元测试

- **状态机测试**: 验证所有状态转换的正确性和边界条件
- **消息处理测试**: 确保消息类型系统的正确路由和处理
- **ASR管理器测试**: 模拟各种ASR场景和错误情况
- **问题生成器测试**: 验证个性化问题生成的质量和去重逻辑

### 2. 集成测试

- **端到端流程测试**: 完整的面试流程从开始到结束
- **WebSocket通信测试**: 前后端消息传输的可靠性
- **错误恢复测试**: 各种错误场景下的系统恢复能力
- **性能测试**: 响应时间和并发处理能力

### 3. 兼容性测试

- **正式面试功能测试**: 确保修改不影响现有功能
- **浏览器兼容性测试**: 支持主流浏览器和设备
- **网络环境测试**: 不同网络条件下的系统表现

### 4. 用户验收测试

- **用户体验测试**: 面试流程的自然性和流畅性
- **功能完整性测试**: 所有需求的实现验证
- **边界情况测试**: 异常情况下的用户体验

### 测试数据和环境

```typescript
interface TestScenario {
  name: string;
  description: string;
  initialState: MockInterviewState;
  events: StateEvent[];
  expectedFinalState: MockInterviewState;
  expectedMessages: MockInterviewMessage[];
  errorConditions?: ErrorCondition[];
}

// 预定义测试场景
const TEST_SCENARIOS: TestScenario[] = [
  {
    name: "正常面试流程",
    description: "从开始到结束的完整面试流程",
    // ... 详细配置
  },
  {
    name: "ASR服务失败恢复",
    description: "ASR服务失败时的自动恢复",
    // ... 详细配置
  },
  // ... 更多测试场景
];
```

**设计决策理由**: 全面的测试策略确保了系统的质量和稳定性。通过预定义的测试场景，可以系统性地验证各种情况下的系统行为，特别是错误处理和恢复机制的有效性。

### 性能指标

- AI问题生成时间: < 3秒
- ASR启动时间: < 1秒  
- 用户回答处理时间: < 2秒
- WebSocket消息延迟: < 500ms
- 系统稳定运行时间: > 30分钟
- 错误恢复时间: < 5秒

这些性能指标确保了用户能够获得流畅、自然的面试体验，同时保证系统的可靠性和稳定性。