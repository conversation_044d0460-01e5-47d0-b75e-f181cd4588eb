import React from 'react';
import { <PERSON>, <PERSON>, <PERSON>, Check } from 'lucide-react';
import useUserStore, { ThemeMode } from '../../stores/userStore';

const ThemeSettings: React.FC = () => {
  const { themeMode, setThemeMode } = useUserStore();

  const themes = [
    {
      id: 'light' as ThemeMode,
      name: '浅色模式',
      description: '经典的浅色界面，适合白天使用',
      icon: Sun,
      preview: 'bg-white border-gray-200',
    },
    {
      id: 'dark' as ThemeMode,
      name: '深色模式',
      description: '深色界面，减少眼部疲劳，适合夜间使用',
      icon: Moon,
      preview: 'bg-gray-900 border-gray-700',
    },
    {
      id: 'system' as ThemeMode,
      name: '跟随系统',
      description: '自动适配您的操作系统主题设置',
      icon: Monitor,
      preview: 'bg-gradient-to-r from-white to-gray-900 border-gray-400',
    },
  ];

  const handleThemeChange = (mode: ThemeMode) => {
    setThemeMode(mode);
  };

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">选择主题</h3>
        <p className="text-gray-600 dark:text-gray-300 mb-6">
          选择您喜欢的界面外观。主题设置会自动保存并在下次访问时生效。
        </p>

        <div className="grid gap-4">
          {themes.map((theme) => {
            const Icon = theme.icon;
            const isSelected = themeMode === theme.id;

            return (
              <div
                key={theme.id}
                className={`relative p-4 border-2 rounded-xl cursor-pointer transition-all duration-300 hover:shadow-md hover:scale-[1.02] ${
                  isSelected
                    ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 dark:border-blue-400 shadow-lg'
                    : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500 bg-white dark:bg-gray-800'
                }`}
                onClick={() => handleThemeChange(theme.id)}
              >
                <div className="flex items-center gap-4">
                  {/* 主题预览 */}
                  <div className={`w-12 h-12 rounded-lg border-2 ${theme.preview} flex items-center justify-center transition-transform duration-300 ${isSelected ? 'scale-110' : ''}`}>
                    <Icon className={`w-6 h-6 transition-colors duration-300 ${
                      theme.id === 'dark' ? 'text-white' :
                      theme.id === 'system' ? 'text-gray-600 dark:text-gray-400' : 'text-gray-900 dark:text-gray-100'
                    }`} />
                  </div>

                  {/* 主题信息 */}
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <h4 className="font-semibold text-gray-900 dark:text-white">{theme.name}</h4>
                      {isSelected && (
                        <div className="flex items-center gap-1 px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded-full text-xs font-medium animate-pulse">
                          <Check className="w-3 h-3" />
                          已选择
                        </div>
                      )}
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">{theme.description}</p>
                  </div>

                  {/* 选择指示器 */}
                  <div className={`w-5 h-5 rounded-full border-2 flex items-center justify-center transition-all duration-300 ${
                    isSelected
                      ? 'border-blue-500 bg-blue-500 dark:border-blue-400 dark:bg-blue-400 scale-110'
                      : 'border-gray-300 dark:border-gray-600'
                  }`}>
                    {isSelected && <Check className="w-3 h-3 text-white animate-bounce" />}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* 主题说明 */}
      <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 transition-colors">
        <h4 className="font-medium text-gray-900 dark:text-white mb-2">主题说明</h4>
        <ul className="text-sm text-gray-600 dark:text-gray-300 space-y-1">
          <li>• <strong>浅色模式</strong>：传统的白色背景界面，适合在光线充足的环境中使用</li>
          <li>• <strong>深色模式</strong>：深色背景界面，可以减少眼部疲劳，特别适合在暗光环境中使用</li>
          <li>• <strong>跟随系统</strong>：自动检测您的操作系统主题设置，并相应地切换浅色或深色模式</li>
        </ul>
      </div>

      {/* 当前系统主题检测 */}
      {themeMode === 'system' && (
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-4 transition-all duration-300">
          <h4 className="font-medium text-blue-900 dark:text-blue-200 mb-2">系统主题检测</h4>
          <p className="text-sm text-blue-700 dark:text-blue-300">
            当前系统偏好：
            <span className="font-medium ml-1">
              {window.matchMedia('(prefers-color-scheme: dark)').matches ? '深色模式' : '浅色模式'}
            </span>
          </p>
          <p className="text-xs text-blue-600 dark:text-blue-400 mt-1">
            当您的系统主题发生变化时，界面会自动更新
          </p>
        </div>
      )}
    </div>
  );
};

export default ThemeSettings;
