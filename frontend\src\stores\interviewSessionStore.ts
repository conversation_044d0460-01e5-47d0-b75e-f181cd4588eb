// 面试会话状态管理 Store
import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { SessionState, SessionPhase, SessionProgress, ResourceStatus } from '../managers/InterviewSessionManager';

export interface InterviewSessionStore {
  // 状态
  sessionState: SessionState;
  isInitialized: boolean;
  lastError: string | null;
  
  // 操作
  updateSessionState: (newState: SessionState) => void;
  updatePhase: (phase: SessionPhase) => void;
  updateProgress: (resource: keyof SessionProgress, progress: number) => void;
  updateResourceStatus: (resource: keyof SessionState['resources'], status: ResourceStatus) => void;
  setError: (error: string | null) => void;
  reset: () => void;
  
  // 计算属性
  getOverallProgress: () => number;
  isReady: () => boolean;
  hasError: () => boolean;
  getReadyResources: () => string[];
  getPendingResources: () => string[];
}

const initialSessionState: SessionState = {
  phase: 'idle',
  progress: {
    webSocket: 0,
    audio: 0,
    credits: 0,
    overall: 0
  },
  resources: {
    webSocket: { status: 'idle', lastUpdated: Date.now() },
    audio: { status: 'idle', lastUpdated: Date.now() },
    credits: { status: 'idle', lastUpdated: Date.now() }
  }
};

export const useInterviewSessionStore = create<InterviewSessionStore>()(
  devtools(
    (set, get) => ({
      // 初始状态
      sessionState: initialSessionState,
      isInitialized: false,
      lastError: null,

      // 更新整个会话状态
      updateSessionState: (newState: SessionState) => {
        set(
          (state) => ({
            sessionState: newState,
            isInitialized: true,
            lastError: null
          }),
          false,
          'updateSessionState'
        );
      },

      // 更新阶段
      updatePhase: (phase: SessionPhase) => {
        set(
          (state) => ({
            sessionState: {
              ...state.sessionState,
              phase
            }
          }),
          false,
          'updatePhase'
        );
      },

      // 更新进度
      updateProgress: (resource: keyof SessionProgress, progress: number) => {
        set(
          (state) => {
            const newProgress = {
              ...state.sessionState.progress,
              [resource]: progress
            };
            
            // 计算总体进度
            const overall = Math.round(
              (newProgress.webSocket + newProgress.audio + newProgress.credits) / 3
            );
            newProgress.overall = overall;

            return {
              sessionState: {
                ...state.sessionState,
                progress: newProgress
              }
            };
          },
          false,
          'updateProgress'
        );
      },

      // 更新资源状态
      updateResourceStatus: (resource: keyof SessionState['resources'], status: ResourceStatus) => {
        set(
          (state) => ({
            sessionState: {
              ...state.sessionState,
              resources: {
                ...state.sessionState.resources,
                [resource]: {
                  ...status,
                  lastUpdated: Date.now()
                }
              }
            }
          }),
          false,
          'updateResourceStatus'
        );
      },

      // 设置错误
      setError: (error: string | null) => {
        set(
          (state) => ({
            lastError: error,
            sessionState: error ? {
              ...state.sessionState,
              phase: 'error' as SessionPhase
            } : state.sessionState
          }),
          false,
          'setError'
        );
      },

      // 重置状态
      reset: () => {
        set(
          () => ({
            sessionState: initialSessionState,
            isInitialized: false,
            lastError: null
          }),
          false,
          'reset'
        );
      },

      // 计算总体进度
      getOverallProgress: () => {
        const { progress } = get().sessionState;
        return Math.round((progress.webSocket + progress.audio + progress.credits) / 3);
      },

      // 检查是否就绪
      isReady: () => {
        const { resources } = get().sessionState;
        return Object.values(resources).every(resource => resource.status === 'ready');
      },

      // 检查是否有错误
      hasError: () => {
        const state = get();
        return state.sessionState.phase === 'error' || state.lastError !== null;
      },

      // 获取已就绪的资源
      getReadyResources: () => {
        const { resources } = get().sessionState;
        return Object.entries(resources)
          .filter(([_, resource]) => resource.status === 'ready')
          .map(([name, _]) => name);
      },

      // 获取待处理的资源
      getPendingResources: () => {
        const { resources } = get().sessionState;
        return Object.entries(resources)
          .filter(([_, resource]) => resource.status === 'preparing')
          .map(([name, _]) => name);
      }
    }),
    {
      name: 'interview-session-store',
      // 只在开发环境启用devtools
      enabled: process.env.NODE_ENV === 'development'
    }
  )
);

// 选择器函数，用于优化性能
export const selectSessionPhase = (state: InterviewSessionStore) => state.sessionState.phase;
export const selectProgress = (state: InterviewSessionStore) => state.sessionState.progress;
export const selectResources = (state: InterviewSessionStore) => state.sessionState.resources;
export const selectOverallProgress = (state: InterviewSessionStore) => state.getOverallProgress();
export const selectIsReady = (state: InterviewSessionStore) => state.isReady();
export const selectHasError = (state: InterviewSessionStore) => state.hasError();
export const selectLastError = (state: InterviewSessionStore) => state.lastError;

// Hook for easy access to specific parts of the state
export const useSessionPhase = () => useInterviewSessionStore(selectSessionPhase);
export const useSessionProgress = () => useInterviewSessionStore(selectProgress);
export const useSessionResources = () => useInterviewSessionStore(selectResources);
export const useOverallProgress = () => useInterviewSessionStore(selectOverallProgress);
export const useIsSessionReady = () => useInterviewSessionStore(selectIsReady);
export const useSessionError = () => useInterviewSessionStore(selectHasError);
export const useLastError = () => useInterviewSessionStore(selectLastError);

// 实用工具函数
export const getSessionManager = () => {
  // 这里可以返回SessionManager实例的引用
  // 避免循环依赖，可以通过全局变量或其他方式获取
  return (window as any).__interviewSessionManager__;
};

// 调试工具（仅开发环境）
if (process.env.NODE_ENV === 'development') {
  (window as any).__interviewSessionStore__ = useInterviewSessionStore;
}
