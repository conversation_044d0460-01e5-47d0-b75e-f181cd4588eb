const API_BASE_URL = process.env.NODE_ENV === 'production'
  ? 'https://mianshijun.xyz' // 生产环境 API URL (使用完整域名)
  : ''; // 开发环境使用相对路径，Vite proxy

export interface TargetPosition {
  id: string;
  positionName: string;
  positionRequirements?: string | null;
  companyName?: string | null;
  companyProfile?: string | null;
  status?: string | null;
  // userId: number;
  // createdAt: string;
  // updatedAt: string;
}

export interface PositionFormData { // 用于创建和更新
  positionName: string;
  positionRequirements?: string;
  companyName?: string;
  companyProfile?: string;
  status?: string;
}

export const getUserPositions = async (token: string): Promise<TargetPosition[]> => {
  const response = await fetch(`${API_BASE_URL}/api/positions`, {
    headers: { 'Authorization': `Bearer ${token}` },
  });
  if (!response.ok) throw new Error('Failed to fetch positions');
  const result = await response.json();
  return result as TargetPosition[];
};

export const createPosition = async (token: string, positionData: PositionFormData): Promise<TargetPosition> => {
  const response = await fetch(`${API_BASE_URL}/api/positions`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`,
    },
    body: JSON.stringify(positionData),
  });
  if (!response.ok) {
     const errorData = await response.json();
     throw new Error(errorData.message || 'Failed to create position');
  }
  const result = await response.json();
  return result as TargetPosition;
};

export const updatePosition = async (token: string, positionId: string, positionData: Partial<PositionFormData>): Promise<TargetPosition> => {
  const response = await fetch(`${API_BASE_URL}/api/positions/${positionId}`, { // ID 作为路径参数
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`,
    },
    body: JSON.stringify(positionData),
  });
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Failed to update position');
  }
  const result = await response.json();
  return result as TargetPosition;
};

export const deletePosition = async (token: string, positionId: string): Promise<void> => {
  const response = await fetch(`${API_BASE_URL}/api/positions/${positionId}`, { // ID 作为路径参数
    method: 'DELETE',
    headers: { 'Authorization': `Bearer ${token}` },
  });
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Failed to delete position');
  }
};