# 面试君 (MianshiJun) - MVP 开发计划文档

版本: 1.1 (MVP Aligned - React Frontend)

日期: 2025年5月10日

状态: 修订稿 (React 技术栈对齐)

## 1. 项目概述

面试君 (MianshiJun) 是一款AI实时辅助面试SaaS工具，旨在通过屏幕共享、实时语音识别技术以及大语言模型的智能分析能力，为求职者在面试过程中提供实时回答建议和问题解读，帮助用户缓解紧张情绪，提升面试表现，并提供面试复盘支持，助力用户获得心仪的职位。

本项目将严格遵循MVP（最小可行产品）原则，快速迭代，小步快跑，确保每个核心功能模块在完成1-2天的开发任务后即可部署至Vercel平台进行功能测试。

## 2. MVP目标及范围

### 2.1. MVP核心目标

- 验证核心用户流程：用户能够顺利完成从注册登录到使用AI实时辅助面试并查看简单记录的完整体验。
- 验证核心技术可行性：确保语音识别、AI建议生成、实时通讯等关键技术模块能够稳定运行并满足基本性能要求。
- 收集早期用户反馈：为后续产品迭代和功能优化提供数据支持。

### 2.2. MVP 功能范围 (P0 优先级)

1. 用户认证模块

   :

   - 用户注册 (邮箱 + 密码)
   - 用户登录 (邮箱 + 密码)

2. 面试准备模块

   :

   - 输入面试岗位/角色信息
   - 麦克风设备授权与检测
   - 屏幕共享授权与选择 (用于捕获面试官音视频)

3. 实时面试辅助模块

   :

   - 实时捕获面试官语音 (通过屏幕共享的系统音频或麦克风)
   - 实时语音转文字 (ASR)
   - 实时展示AI生成的回答建议 (LLM)
   - 基础的面试状态显示与控制 (如结束面试)

4. 简单面试记录查看模块

   :

   - 展示本次面试的转录问题和AI建议列表。

5. (P0 后期 / P1 早期) 支付收款模块 (初步规划)

   :

   - 套餐展示 (基础套餐、高级套餐、超级套餐)
   - 面巾包展示
   - 支付宝支付渠道集成 (初步)
   - 用户面试次数/面巾余额管理

### 2.3. MVP 暂不包含的功能 (P1 及以后)

- 更详细的面试评估与AI反馈报告
- 完整的用户中心 (详细个人资料修改、详细面试历史、账户设置等)
- 模拟面试功能
- 分享有礼等增长裂变功能
- 微信支付、Stripe等其他支付渠道
- 复杂的AI上下文理解与多轮对话跟踪优化
- 视频分析与反馈 (需用户明确授权)
- 团队协作功能

## 3. 技术栈

- 前端

  :

  - 框架: **React (v18+)**
  - UI库: Tailwind CSS v3.x
  - **状态管理**: **Context API / `useReducer` (MVP初期优先) / Zustand (按需引入)**
  - 路由: **React Router v6+**
  - 构建工具: Vite
  - 语言: TypeScript

- 后端

  :

  - 运行时: Node.js (LTS版本)
  - 部署平台: Vercel Serverless Functions / Edge Functions (用于WebSocket)
  - API框架: Express.js (或直接使用Vercel的请求处理函数)

- 数据库

  :

  - Neon (Serverless PostgreSQL)
  - ORM/查询构建器: Prisma (推荐，或轻量级如 `pg` 库直接操作)

- AI 服务

  :

  - 语音识别 (ASR): 百度ASR API (优先)
  - 大语言模型 (LLM): DeepSeek API (优先)

- **版本控制**: Git

- **代码托管**: GitHub

## 4. 核心模块详细开发计划 (总预计6周)

**迭代原则**: 每个编号任务群组（如1.1.x）目标在1-2天内完成并部署到Vercel进行功能测试。

------

### **第一周：基础架构与用户认证**

#### **任务 1.1: 项目初始化与基础配置 (1-2天)**

- **任务描述**: 初始化前后端项目，配置开发环境、版本控制，搭建基础部署流程。

- 前端工作 (React)

  :

  - 使用Vite创建**React项目 (TypeScript模板)**。
  - 集成Tailwind CSS。
  - 配置**React Router v6+**基础路由 (首页、登录、注册页骨架 - 例如 `HomePage.tsx`, `LoginPage.tsx`, `RegisterPage.tsx`)。
  - 创建基础布局组件 (例如 `Layout.tsx` 包含导航栏 `Navbar.tsx`、页脚 `Footer.tsx` - 极简版，核心样式参考`design_system.md`)。

- 后端工作

  :

  - 初始化Node.js项目 (Express.js)。
  - 设置Vercel Serverless Functions基本结构。
  - 配置Vercel环境变量 (占位符)。

- 数据库相关

  :

  - 在Neon创建数据库实例。
  - 获取数据库连接字符串，配置到Vercel环境变量。

- 可交付成果/测试要点

  :

  - 前后端项目骨架代码提交到GitHub。
  - Vercel成功部署前端静态骨架页面 (如带Logo的空白首页)。
  - Vercel成功部署一个简单的后端API (如 `/api/health` 返回 `{"status": "ok"}`)。

#### **任务 1.2: 用户注册功能 (2-3天)**

- **任务描述**: 实现用户邮箱密码注册功能。

- 前端工作 (React)

  :

  - 创建注册页面 (`RegisterPage.tsx`)，包含邮箱、密码、确认密码输入框和注册按钮 (核心组件基于`design_system.md`简化实现，例如 `InputField.tsx`, `Button.tsx`)。
  - 表单基础校验 (非空、邮箱格式、密码一致性 - 可使用如 `React Hook Form` 配合 `Zod` 或 `Yup`)。
  - 调用后端注册API，处理响应 (成功跳转登录页/失败提示)。

- 后端工作 (Node.js/Vercel Functions)

  :

  - 创建 `/api/auth/register` 接口。
  - 接收注册信息，进行数据校验。
  - 密码使用 `bcrypt` 哈希加密。
  - 将用户信息存入Neon数据库 `users` 表。

- 数据库相关

  :

  - 实现 `users` 表的创建 (DDL见第8节)。
  - 实现用户数据插入逻辑。

- 可交付成果/测试要点

  :

  - 用户可以在注册页面输入信息。
  - 点击注册按钮，前端进行基本校验。
  - 后端API能接收到数据，并成功在数据库创建用户记录。
  - 注册成功后前端能收到成功提示或跳转。
  - Vercel部署后，可完成新用户注册流程。

#### **任务 1.3: 用户登录功能 (1-2天)**

- **任务描述**: 实现用户邮箱密码登录功能。

- 前端工作 (React)

  :

  - 创建登录页面 (`LoginPage.tsx`)，包含邮箱、密码输入框和登录按钮。
  - 调用后端登录API。
  - 登录成功后，本地存储JWT (例如，通过**Context API / `useReducer` (如AuthContext) 或 Zustand store** 管理认证状态，并配合 `localStorage` 或安全Cookie存储Token)，并跳转到面试准备页 (骨架)。
  - 处理登录失败提示。

- 后端工作 (Node.js/Vercel Functions)

  :

  - 创建 `/api/auth/login` 接口。
  - 校验用户凭证 (邮箱是否存在，密码是否匹配)。
  - 成功后生成JWT返回给前端。

- 数据库相关

  :

  - 查询 `users` 表验证用户信息。

- 可交付成果/测试要点

  :

  - 已注册用户可以使用邮箱和密码登录。
  - 登录成功后，前端能获取并存储JWT。
  - 登录成功后跳转到指定页面。
  - Vercel部署后，可完成用户登录流程。

------

### **第二周：面试准备与核心权限**

#### **任务 2.1: 面试准备页面基础 (1天)**

- **任务描述**: 创建面试准备页面骨架，允许用户输入面试岗位信息。

- 前端工作 (React)

  :

  - 创建 `InterviewSetupPage.tsx` 页面。
  - 添加文本输入框，允许用户输入“面试岗位/角色”。
  - 将输入信息暂存到**组件状态 (`useState`) 或提升至 Context / Zustand store**。
  - 添加“下一步”或“开始面试”按钮 (初始禁用)。

- **后端工作**: (此阶段可能不需要后端交互，信息暂存前端)

- **数据库相关**: (此阶段不需要数据库交互)

- 可交付成果/测试要点

  :

  - 面试准备页面可访问。
  - 用户可以输入岗位信息。
  - Vercel部署后，页面功能符合预期。

#### **任务 2.2: 麦克风设备授权与检测 (1-2天)**

- **任务描述**: 在面试准备页面实现麦克风授权和基础检测功能。

- 前端工作 (React)

  :

  - 在 `InterviewSetupPage.tsx` 中添加“授权麦克风”按钮。
  - 使用 `navigator.mediaDevices.getUserMedia({ audio: true })` 请求麦克风权限。
  - 显示麦克风权限状态 (未授权/已授权/已拒绝)。
  - (可选MVP) 授权成功后，显示简单的音量指示（如一个变化的图标或文本提示“麦克风工作中”）。
  - 将麦克风授权状态更新到**组件状态 (`useState`) 或 Context / Zustand store**。

- **后端工作**: (无)

- **数据库相关**: (无)

- 可交付成果/测试要点

  :

  - 点击按钮能触发浏览器麦克风授权请求。
  - 界面能正确显示麦克风权限状态。
  - Vercel部署后，麦克风授权流程正常。

#### **任务 2.3: 屏幕共享授权与选择 (1-2天)**

- **任务描述**: 在面试准备页面实现屏幕共享授权和内容选择功能。

- 前端工作 (React)

  :

  - 在 `InterviewSetupPage.tsx` 中添加“选择共享内容”按钮。
  - 使用 `navigator.mediaDevices.getDisplayMedia({ video: true, audio: true })` 请求屏幕共享权限 (注意捕获系统音频)。
  - 显示屏幕共享状态 (未就绪/已就绪)。
  - 将屏幕共享状态及选择的媒体流 (MediaStream) 暂存到**组件状态 (`useState`) 或 Context / Zustand store**。
  - 当所有准备条件满足 (岗位输入、麦克风授权、屏幕共享就绪)，“开始面试”按钮变为可用。

- **后端工作**: (无)

- **数据库相关**: (无)

- 可交付成果/测试要点

  :

  - 点击按钮能触发浏览器屏幕共享选择器。
  - 界面能正确显示屏幕共享状态。
  - “开始面试”按钮状态根据条件正确更新。
  - Vercel部署后，屏幕共享授权流程正常。

------

### **第三周：实时面试辅助 - WebSocket与ASR初步集成**

#### **任务 3.1: 实时面试辅助页面骨架与WebSocket连接 (1-2天)**

- **任务描述**: 创建实时面试辅助页面，并建立与后端的WebSocket连接。

- 前端工作 (React)

  :

  - 创建 `LiveInterviewPage.tsx` 页面。
  - 从 `InterviewSetupPage.tsx` 页面跳转时，携带必要的面试信息 (如岗位，通过React Router的state或URL参数传递)。
  - 页面加载时 (例如，在 `useEffect` Hook中)，初始化WebSocket连接到后端 (Vercel Edge Function/Serverless Function)。
  - 实现基础的WebSocket消息发送和接收逻辑 (可使用如 `react-use-websocket` Hook)。
  - 显示连接状态。

- 后端工作 (Node.js/Vercel Edge Function)

  :

  - 创建WebSocket服务接入点 (如 `/ws/interview/:sessionId`)。
  - 处理新的WebSocket连接请求，管理连接。
  - 实现简单的消息回显 (echo) 功能用于测试连接。
  - 创建 `interview_sessions` 表记录。

- 数据库相关

  :

  - 实现 `interview_sessions` 表的创建 (DDL见第8节)。
  - 后端在WebSocket连接建立或面试开始时，创建一条新的面试会话记录。

- 可交付成果/测试要点

  :

  - 从准备页点击“开始面试”能跳转到实时辅助页。
  - 前端能成功建立到后端WebSocket服务的连接。
  - 连接状态在前端正确显示。
  - Vercel部署后，WebSocket连接可建立。

#### **任务 3.2: 音频流处理与发送 (前端) (1-2天)**

- **任务描述**: 从屏幕共享的音频轨道 (面试官声音) 或麦克风获取音频流，进行分片并通过WebSocket发送到后端。

- 前端工作 (React)

  :

  - 在 `LiveInterviewPage.tsx` 中获取之前存储的屏幕共享MediaStream。
  - 提取音频轨道 (AudioTrack)。
  - 使用Web Audio API (`AudioContext`, `MediaStreamAudioSourceNode`) 处理音频。
  - (MVP简化) 将音频流通过 `MediaRecorder` API 录制成短片段 (如每秒1-2个片段，格式如WebM/Opus)，然后通过WebSocket发送 `Blob` 数据。
  - (备选方案，更复杂) 实现更精细的音频分片逻辑，发送原始PCM数据或压缩数据 (可封装在自定义Hook如 `useAudioStreamer.ts` 中)。

- 后端工作

  :

  - WebSocket服务端能接收前端发送的音频数据片段。
  - (初步) 简单记录收到的数据类型和大小，用于调试。

- **数据库相关**: (无)

- 可交付成果/测试要点

  :

  - 前端能够从指定MediaStream捕获音频。
  - 音频数据能够通过WebSocket成功发送到后端。
  - 后端能接收到音频数据。
  - Vercel部署后，音频流可传输。

#### **任务 3.3: 后端ASR集成与文本返回 (2-3天)**

- **任务描述**: 后端接收音频数据，调用百度ASR API进行语音转文字，并将识别结果通过WebSocket返回给前端。

- 前端工作 (React)

  :

  - 在 `LiveInterviewPage.tsx` 中接收后端通过WebSocket发送的ASR识别结果 (转录文本)。
  - 将转录文本显示在界面特定区域 (如“面试官问题”区域，可能是 `TranscriptDisplay.tsx` 组件)。
  - 实现文本的追加显示和自动滚动。

- 后端工作 (Node.js/Vercel Function/Edge Function)

  :

  - WebSocket服务端接收音频片段。
  - 将音频片段发送给百度ASR API (处理API认证、请求格式等)。
  - 接收ASR服务返回的识别结果。
  - 将识别结果通过WebSocket推送给对应的前端客户端。
  - 初步将识别的文本存入 `interview_transcripts` 表。

- 数据库相关

  :

  - 实现 `interview_transcripts` 表的创建 (DDL见第8节)。
  - 后端将ASR识别的文本内容及说话人（'interviewer'）存入数据库。

- 可交付成果/测试要点

  :

  - 前端发送的音频，后端能成功调用百度ASR并获取结果。
  - ASR识别的文本能通过WebSocket实时返回给前端并显示。
  - 数据库中能查到对应的转录记录。
  - Vercel部署后，可完成语音到文字的实时转换和显示。

------

### **第四周：LLM集成与AI建议展示**

#### **任务 4.1: 后端LLM集成与建议生成 (2-3天)**

- **任务描述**: 后端接收ASR识别的文本，调用DeepSeek LLM API生成回答建议，并将建议通过WebSocket返回前端。

- 前端工作 (React)

  :

  - 在 `LiveInterviewPage.tsx` 中接收后端通过WebSocket发送的LLM生成的AI建议。
  - 将AI建议显示在界面特定区域 (如“AI回答建议”区域，可能是 `AISuggestionDisplay.tsx` 组件)。
  - 建议可以先以简单文本形式展示。

- 后端工作 (Node.js/Vercel Function/Edge Function)

  :

  - 当收到新的ASR转录文本 (面试官问题) 后，构造Prompt (包含当前问题、面试岗位信息等上下文)。
  - 调用DeepSeek LLM API。
  - 接收LLM返回的回答建议。
  - 将AI建议通过WebSocket推送给对应的前端客户端。
  - 初步将AI建议存入 `ai_suggestions` 表。

- 数据库相关

  :

  - 实现 `ai_suggestions` 表的创建 (DDL见第8节)。
  - 后端将LLM生成的建议及关联的问题摘要存入数据库。

- 可交付成果/测试要点

  :

  - 后端能根据转录文本成功调用DeepSeek LLM并获取建议。
  - AI建议能通过WebSocket实时返回给前端并显示。
  - 数据库中能查到对应的AI建议记录。
  - Vercel部署后，可完成从问题到AI建议的完整流程。

#### **任务 4.2: AI建议前端结构化展示 (1-2天)**

- **任务描述**: 优化前端AI建议的展示方式，使其更易读、更结构化 (如要点列表)。

- 前端工作 (React)

  :

  - 在 `LiveInterviewPage.tsx` (或其子组件 `AISuggestionDisplay.tsx`) 中，对接收到的AI建议进行解析 (如果后端返回的是结构化数据，如JSON)。
  - 将建议以核心要点列表 (bullet points，使用 `ul` 和 `li` 标签)、关键词 (使用 `Tag.tsx` 组件)等形式展示。
  - (可选MVP) 实现简单的“复制建议”功能。
  - UI样式遵循 `design_system.md` 的核心组件规范。

- **后端工作**: (可能需要配合调整LLM Prompt或对LLM输出做一些后处理，以生成更易于前端结构化展示的格式)

- **数据库相关**: (无)

- 可交付成果/测试要点

  :

  - AI建议在前端以更清晰、结构化的方式展示。
  - Vercel部署后，建议展示效果符合预期。

#### **任务 4.3: 实时面试状态与基础控制 (1天)**

- **任务描述**: 在实时面试辅助页面添加面试状态显示和“结束面试”功能。

- 前端工作 (React)

  :

  - 在 `LiveInterviewPage.tsx` 中显示系统状态 (如“正在聆听”、“AI思考中”、“已连接”等，可通过 `useState` 管理)。
  - 添加“结束面试”按钮。点击后，关闭WebSocket连接，并使用React Router的 `useNavigate` Hook 跳转到“简单面试记录查看”页面 (`InterviewReviewPage.tsx`)。

- 后端工作 (Node.js/Vercel Function/Edge Function)

  :

  - WebSocket服务端处理客户端断开连接的逻辑。
  - 更新 `interview_sessions` 表中的会话状态为 `completed` 及 `ended_at` 时间。

- 数据库相关

  :

  - 更新对应面试会话的状态和结束时间。

- 可交付成果/测试要点

  :

  - 前端能显示基本的系统运行状态。
  - 点击“结束面试”按钮能断开WebSocket并跳转页面。
  - 后端能正确更新会话状态。
  - Vercel部署后，可完整结束一次面试辅助流程。

------

### **第五周：简单面试记录与前端优化**

#### **任务 5.1: 简单面试记录查看页面 (2-3天)**

- **任务描述**: 创建页面展示本次面试的转录问题列表和AI建议列表。

- 前端工作 (React)

  :

  - 创建 `InterviewReviewPage.tsx` 页面。
  - 页面加载时 (例如，在 `useEffect` Hook中)，根据会话ID (从路由参数获取) 从后端获取该次面试的转录记录和AI建议。
  - 按时间顺序或问答对的形式展示问题和对应的AI建议 (使用 `.map()` 渲染列表)。
  - 提供“返回”或“开始新的面试”的导航。

- 后端工作 (Node.js/Vercel Functions)

  :

  - 创建 `/api/interviews/:sessionId/review` 接口。
  - 根据 `sessionId` 从 `interview_transcripts` 和 `ai_suggestions` 表中查询数据。
  - 返回格式化后的数据给前端。

- 数据库相关

  :

  - 实现从相关表查询面试记录的逻辑。

- 可交付成果/测试要点

  :

  - 结束面试后，跳转到回顾页面能看到本次面试的问题和AI建议。
  - 数据展示清晰。
  - Vercel部署后，可查看简单面试记录。

#### **任务 5.2: 前端核心组件样式与响应式初步调整 (2天)**

- **任务描述**: 根据 `design_system.md`，对已实现的核心页面和组件进行样式细化和基础的响应式布局调整。

- 前端工作 (React & Tailwind CSS)

  :

  - 重点关注登录/注册页 (`LoginPage.tsx`, `RegisterPage.tsx`)、面试准备页 (`InterviewSetupPage.tsx`)、实时辅助页 (`LiveInterviewPage.tsx`)、记录查看页 (`InterviewReviewPage.tsx`)。
  - 应用 `design_system.md` 中定义的核心颜色、排版、间距规范。
  - 确保在移动端和桌面端有基本可用的布局 (使用Tailwind CSS的响应式修饰符)。

- **后端工作**: (无)

- **数据库相关**: (无)

- 可交付成果/测试要点

  :

  - 核心页面UI有明显改善，更接近设计稿。
  - 在不同尺寸屏幕上基本可用。
  - Vercel部署后，UI和响应式效果符合预期。

------

### **第六周：支付功能初步集成 (P0后期) 与MVP整体测试**

#### **任务 6.1: 支付套餐与面巾展示页面 (1-2天)**

- **任务描述**: 创建页面展示不同面试套餐和面巾包的价格及内容。

- 前端工作 (React)

  :

  - 创建 `PricingPage.tsx` 或类似页面。
  - 静态展示您提供的套餐信息和面巾包信息。
    - 基础套餐: 模拟面试2次, 正式面试2次, 附赠100面巾, ¥68
    - 高级套餐: 模拟面试4次, 正式面试4次, 附赠400面巾, ¥138
    - 超级套餐: 模拟面试10次, 正式面试8次, 附赠800面巾, ¥298
    - 面巾包: 100面巾/¥30; 200面巾/¥60; 1000面巾/¥90
    - 兑换规则: 100面巾/模拟面试; 200面巾/正式面试
  - 为每个套餐/面巾包添加“购买”按钮 (初始可不处理点击事件，或仅跳转到占位支付页)。

- **后端工作**: (无，信息静态展示)

- **数据库相关**: (无)

- 可交付成果/测试要点

  :

  - 套餐和面巾包信息在前端正确展示。
  - Vercel部署后，页面可访问。

#### **任务 6.2: 用户余额/次数管理表结构与API基础 (1-2天)**

- **任务描述**: 设计并创建用户余额/面试次数相关的数据表，并提供基础的查询API。

- **前端工作**: (暂无，或在用户相关页面 (`DashboardPage.tsx` 或 `ProfilePage.tsx`) 预留显示位置)

- 后端工作 (Node.js/Vercel Functions)

  :

  - 创建 `/api/user/balance` 接口 (GET)，用于查询当前用户面试次数/面巾余额。
  - (MVP简化) 注册新用户时，在 `user_balances` 表中初始化一条记录 (如赠送少量体验次数/面巾，或默认为0)。

- 数据库相关

  :

  - 实现 `user_balances` 表和 `payment_orders` 表的创建 (DDL见第8节)。
  - 实现用户余额初始化和查询逻辑。

- 可交付成果/测试要点

  :

  - 后端API能返回用户（模拟的或初始化的）余额信息。
  - 数据库表结构正确创建。
  - Vercel部署后，API可调用。

#### **任务 6.3: (可选，根据进度) 支付宝支付流程前端引导与后端订单创建 (2-3天)**

- 任务描述

  : 用户选择套餐后，引导至支付宝支付，后端创建预支付订单。

  - **注意**: 此任务复杂度较高，如果时间紧张，可延至P1。MVP阶段可采用手动后台充值。

- 前端工作 (React)

  :

  - 在套餐购买按钮点击后，调用后端API创建订单。
  - 获取后端返回的支付宝支付链接或二维码数据，引导用户支付。
  - 创建简单的支付状态等待/结果页面。

- 后端工作 (Node.js/Vercel Functions)

  :

  - 创建 `/api/payments/create-order` 接口。接收用户选择的套餐ID。
  - 在 `payment_orders` 表中创建一条状态为 `pending` 的订单记录。
  - 调用支付宝SDK/API生成支付参数或支付二维码信息，返回给前端。
  - 创建支付宝回调接口 `/api/payments/alipay-notify` (处理异步通知，更新订单状态和用户余额)。**此回调接口的健壮性和安全性要求极高。**

- 数据库相关

  :

  - 订单数据写入 `payment_orders` 表。
  - 支付成功后，更新 `payment_orders` 状态，并更新 `user_balances` 表。

- 可交付成果/测试要点

  :

  - 用户选择套餐能生成支付订单。
  - 能跳转到支付宝支付页面（或显示二维码）。
  - (沙箱环境) 支付成功后，后端能收到回调并更新订单和用户余额。
  - Vercel部署后，（沙箱）支付流程可部分走通。

#### **任务 6.4: MVP整体功能串联测试与Bug修复 (1-2天)**

- **任务描述**: 对P0阶段已开发的所有核心功能进行全面的串联测试，修复发现的Bug。

- 前端&后端&测试

  :

  - 完整测试用户注册 -> 登录 -> 面试准备 -> 实时辅助 -> 查看记录 的流程。
  - 关注各模块间的衔接和数据传递。
  - 重点测试实时辅助的稳定性和延迟。
  - 记录并修复测试过程中发现的Bug。

- 可交付成果/测试要点

  :

  - MVP核心流程基本顺畅。
  - 主要功能模块按预期工作。
  - 已知严重Bug得到修复。
  - 产品达到可供早期用户体验的状态。

------

## 5. 关键里程碑

- **第一周末**: 完成基础架构搭建和用户认证模块核心功能，可注册登录。
- **第二周末**: 完成面试准备模块，用户可完成设备授权。
- **第三周末**: 完成实时语音转文字功能，用户可以看到面试官问题的实时转录。
- **第四周末**: 完成AI建议生成与展示，用户可以看到针对问题的实时AI建议。
- **第五周末**: 完成简单面试记录查看功能，核心页面样式得到初步优化。
- **第六周末**: (若包含支付) 支付功能初步集成，完成MVP整体测试和Bug修复，产品达到P0可交付状态。

## 6. 部署与测试流程

- **部署平台**: Vercel。

- **部署频率**: 每个细化任务群组 (1-2天开发量) 完成后，即合并到主开发分支 (如 `develop` 或 `main`)，自动触发Vercel部署到预览环境或生产环境。

- 测试类型 (MVP阶段)

  :

  - **功能测试**: 开发人员完成模块后进行自测。团队成员之间进行交叉测试。确保每个P0功能点按需求文档和设计文档实现。
  - **手动探索性测试**: 模拟真实用户场景进行操作，发现潜在问题。

- 测试环境

  :

  - Vercel提供每次部署的预览URL，可用于测试。
  - 最终部署到生产URL。

- **Bug跟踪**: 使用GitHub Issues进行Bug记录、分配和状态跟踪。

## 7. 团队协作约定

- **版本控制**: 使用Git。所有代码更改通过分支进行，完成后发起Pull Request (PR) 到主开发分支。

- **代码仓库**: GitHub。

- 分支策略 (建议)

  :

  - `main`: 生产环境分支，只合并经过充分测试的稳定版本。
  - `develop`: 主要开发分支，合并各个功能分支。
  - `feature/<feature-name>`: 具体功能开发分支，从 `develop` 创建，完成后合并回 `develop`。
  - `fix/<bug-name>`: Bug修复分支。

- 代码规范

  :

  - **前端React**: 遵循推荐的React编码规范 (如Airbnb JavaScript Style Guide，或团队自行约定)，可配合ESLint (已包含`typescript-eslint`, `eslint-plugin-react-hooks`, `eslint-plugin-react-refresh`) 和 Prettier 进行代码格式化和检查。
  - 后端Node.js: 遵循通用的JavaScript/Node.js编码规范，同样可使用ESLint和Prettier。
  - 注释: 对关键逻辑、复杂函数、API接口等添加清晰的注释。

- Pull Request (PR)流程

  :

  - PR至少需要一名其他团队成员Review通过后方可合并。
  - PR描述清晰说明本次更改的内容和目的。

- **每日站会 (可选，根据团队规模)**: 简短同步进度、遇到的问题和计划。

- **沟通工具**: 根据团队习惯选择 (如Slack, 钉钉, 微信等)。

## 8. 数据库设计 (MVP简化版 - Neon PostgreSQL DDL)

SQL

```
CREATE EXTENSION IF NOT EXISTS "uuid-ossp"; -- 用于 uuid_generate_v4()

-- 用户表
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  email VARCHAR(255) NOT NULL UNIQUE,
  password_hash VARCHAR(255) NOT NULL,
  name VARCHAR(100),
  created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);
CREATE INDEX idx_users_email ON users(email);

-- 用户余额/权益表
CREATE TABLE user_balances (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL UNIQUE REFERENCES users(id) ON DELETE CASCADE,
    mock_interview_credits INTEGER DEFAULT 0, -- 模拟面试次数
    formal_interview_credits INTEGER DEFAULT 0, -- 正式面试次数
    mianshijin_balance INTEGER DEFAULT 0, -- 面巾余额
    last_updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);
CREATE INDEX idx_user_balances_user_id ON user_balances(user_id);

-- 面试会话表
CREATE TABLE interview_sessions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  title_job_info VARCHAR(255), -- 面试岗位/角色信息
  status VARCHAR(30) NOT NULL DEFAULT 'pending', -- 例如: pending, active, processing_asr, processing_llm, completed, error
  created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
  started_at TIMESTAMPTZ,
  ended_at TIMESTAMPTZ
);
CREATE INDEX idx_interview_sessions_user_id ON interview_sessions(user_id);

-- 面试转录文本表
CREATE TABLE interview_transcripts (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  session_id UUID NOT NULL REFERENCES interview_sessions(id) ON DELETE CASCADE,
  speaker VARCHAR(20) NOT NULL CHECK (speaker IN ('interviewer', 'user_input')), -- 'interviewer' 代表识别的语音, 'user_input' 代表用户自己的语音/笔记（如果捕获）
  content TEXT NOT NULL,
  timestamp TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);
CREATE INDEX idx_interview_transcripts_session_id ON interview_transcripts(session_id);

-- AI建议表
CREATE TABLE ai_suggestions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  session_id UUID NOT NULL REFERENCES interview_sessions(id) ON DELETE CASCADE,
  original_question_summary TEXT, -- 问题摘要，对应某条转录或综合理解
  suggested_response TEXT NOT NULL, -- AI生成的建议
  timestamp TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);
CREATE INDEX idx_ai_suggestions_session_id ON ai_suggestions(session_id);

-- 支付订单表 (P0后期/P1)
CREATE TABLE payment_orders (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  package_name VARCHAR(100), -- 套餐名称或面巾包名称
  amount DECIMAL(10, 2) NOT NULL, -- 订单金额
  currency VARCHAR(10) NOT NULL DEFAULT 'CNY',
  status VARCHAR(30) NOT NULL DEFAULT 'pending', -- 例如: pending, completed, failed, refunded
  payment_gateway VARCHAR(50), -- 例如: alipay
  payment_gateway_transaction_id VARCHAR(255) UNIQUE, -- 支付网关交易号
  created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
  paid_at TIMESTAMPTZ
);
CREATE INDEX idx_payment_orders_user_id ON payment_orders(user_id);
CREATE INDEX idx_payment_orders_status ON payment_orders(status);
```

## 9. 风险与应对

1. **WebSocket在Vercel Serverless Functions上的稳定性风险**:

   - **风险描述**: Serverless Functions可能有执行时长限制或空闲超时，可能导致实时WebSocket连接中断，影响用户体验。

   - 应对措施

     :

     - 优先尝试Vercel Edge Functions部署WebSocket服务，其更适合低延迟长连接。
     - 在开发过程中密切监控和测试连接稳定性。
     - 实现健壮的前后端心跳机制和自动重连逻辑。
     - 如果问题持续存在且严重，评估迁移到第三方专业WebSocket服务 (如Pusher, Ably) 或更传统的WebSocket服务器部署方案 (可能增加运维成本)。此调整需团队讨论并更新计划。

2. **ASR和LLM API响应延迟或不稳定性**:

   - **风险描述**: 第三方AI服务可能出现网络波动、API限流或服务降级，导致语音识别慢、AI建议生成慢或失败。

   - 应对措施

     :

     - 在调用API时实现合理的超时控制和指数退避重试机制。
     - 在前端清晰展示AI处理状态 (如“AI思考中...”, “语音识别中...”)，管理用户预期。
     - 监控API调用成功率和平均响应时间，设置告警。
     - 准备备选API供应商 (如ASR有阿里/讯飞，LLM有OpenAI) 的技术预案，虽然MVP阶段只集成首选，但需了解其接入方式。
     - 对于LLM，优化Prompt以减少Token消耗和提高响应速度。

3. **前端性能**:

   - **风险描述**: **React**应用如果组件设计不当或状态管理混乱，可能导致页面卡顿，尤其在实时更新较多的面试辅助页面。

   - 应对措施

     :

     - 遵循**React性能优化最佳实践 (如合理使用 `React.memo`, `useMemo`, `useCallback`，避免不必要的组件重渲染，优化Context API的使用或采用Zustand等库)**。
     - 对实时更新的列表 (如转录文本、AI建议) 考虑虚拟滚动技术 (如果列表非常长，例如使用 `react-window` 或 `react-virtualized`)。
     - 代码Review时关注性能影响点。
     - 使用Vite等现代构建工具的优化功能。

4. **第三方支付集成复杂度 (P0后期/P1)**:

   - **风险描述**: 支付流程涉及资金安全，集成和测试通常比较复杂，回调处理需要非常严谨。

   - 应对措施

     :

     - 优先在沙箱环境充分测试。
     - 严格处理支付回调通知，确保幂等性和数据一致性。
     - 考虑MVP初期采用手动收款和后台充值的方式，以降低初期风险，快速上线核心功能。
     - 详细阅读并遵循支付平台官方文档。

5. **迭代周期过短导致开发压力**:

   - **风险描述**: 1-2天的开发任务并立即部署测试，可能会给开发团队带来较大压力，尤其是在处理复杂功能或未知技术问题时。

   - 应对措施

     :

     - 任务拆分时，确保每个子任务确实可以在1-2天内完成一个可测试的最小闭环。
     - 允许在某些复杂任务点适当延长迭代周期，例如某个后端API的复杂逻辑实现可能需要2-3天才能达到可测试状态。
     - 保持团队内部沟通顺畅，及时暴露风险和调整计划。
     - 在计划中预留一定的缓冲时间 (Buffer Time) 以应对突发情况。

6. **设计系统实现与前端开发同步问题**:

   - **风险描述**: `design_system.md` 非常详细，但MVP阶段只选取核心组件实现。可能存在前端开发时，所需组件尚未完全按设计系统规范实现的情况。

   - 应对措施

     :

     - 在每周初规划本周要开发的前端页面和所需的核心组件。
     - 优先实现这些核心组件的基础版本，满足功能需求，样式上尽量贴近设计系统。
     - 允许在MVP阶段，部分非核心UI元素的样式与设计系统有细微偏差，P1阶段再统一优化。
     - 确保核心交互和信息层级符合设计原则。

7. **数据安全与隐私**:

   - **风险描述**: 面试内容（语音、文本）、AI建议、用户个人信息等属于敏感数据，若处理不当可能导致隐私泄露。

   - 应对措施

     :

     - 严格遵守相关数据隐私法规 (如GDPR, PIPL - 如果适用地区有要求)。
     - 对敏感数据在传输 (HTTPS/WSS) 和存储 (数据库字段加密，如用户密码已哈希) 过程中进行加密。
     - 明确告知用户数据收集和使用策略（隐私政策）。
     - 后端API接口进行严格的权限校验，确保用户只能访问自己的数据。
     - 定期进行安全审计和漏洞扫描 (P1及以后)。
     - 考虑对面试录音/文本数据设置合理的保留期限和自动清理机制。

## 10. 附录 (可选)

- **API接口详细文档链接** (可指向Swagger/OpenAPI文档)
- **设计稿链接** (可指向Figma等设计工具)
- **项目联系人列表**