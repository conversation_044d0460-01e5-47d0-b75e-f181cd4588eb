您好！根据您提供的图片和Vercel的部署日志，我们来详细分析并解决 “Serverless Functions 数量超出 Hobby Plan 限制” 的问题。

**问题根源分析：**

Vercel部署日志中的错误 `Error: No more than 12 Serverless Functions can be added to a Deployment on the Hobby plan.` 非常明确地指出了问题所在。

结合您提供的图片：

- `image_66af23.png` 显示了您的 `MianshiJun_AI_Project/api/` 文件夹结构。
- `image_66af43.png` 显示了 `MianshiJun_AI_Project/api/auth/` 文件夹结构。
- `image_66af02.png` 显示了 Vercel 项目设置中的 "Root Directory" 是**空的**，这通常意味着Vercel会使用仓库的根目录作为项目根目录，这是**正确**的设置，以便Vercel能同时扫描到 `frontend` 和 `api` 目录。

从 `image_66af23.png` 中，我看到 `api` 文件夹下除了我们期望的 `health.ts` 和 `auth/register.ts` 之外，还有一些**额外的 `.js` 文件和不必要的文件夹**，这些很可能是导致函数数量超限的罪魁祸首：

- `local-server.js`
- `test-register.js`
- `generated` 文件夹 (通常是 Prisma Client 生成的地方，不应该直接放在 `api` 根目录被Vercel当作函数处理)

Vercel 会尝试将 `api` 目录（及其子目录）下的每个可执行文件（通常是 `.js`, `.ts`）视为一个独立的 Serverless Function。如果 `api` 目录下结构不清晰，或者包含了非 Serverless Function 用途的文件，就可能导致这个问题。

**解决方案清单 (中文 + English):**

我们将一步步清理 `api` 目录，并优化Vercel的配置，确保只有真正的API端点被部署为Serverless Functions。

------

### 步骤 1: 清理 `api` 文件夹 (Clean up the `api` folder)

目标 (Goal): 移除 api 文件夹中不应被Vercel部署为Serverless Function的文件和文件夹。

(Remove files and folders from the api directory that should not be deployed by Vercel as Serverless Functions.)

1. **删除 `api/local-server.js` (Delete `api/local-server.js`):**

   - 这个文件听起来像是用于本地Express服务器测试的，不应该作为Vercel Serverless Function部署。 (This file sounds like it was for local Express server testing and should not be deployed as a Vercel Serverless Function.)
   - **操作 (Action):** 在 Cursor (或文件管理器) 中，直接删除 `MianshiJun_AI_Project/api/local-server.js` 文件。 (In Cursor (or your file explorer), delete the `MianshiJun_AI_Project/api/local-server.js` file.)

2. **删除 `api/test-register.js` (Delete `api/test-register.js`):**

   - 这个文件看起来也是一个测试脚本，不应部署。 (This file also looks like a test script and should not be deployed.)
   - **操作 (Action):** 删除 `MianshiJun_AI_Project/api/test-register.js` 文件。 (Delete the `MianshiJun_AI_Project/api/test-register.js` file.)

3. **处理 `api/generated` 文件夹 (Handle the `api/generated` folder):**

   - Prisma Client 默认会生成到 `node_modules/.prisma/client`。如果因为某些原因它生成在了 `api/generated`，并且这个文件夹被Vercel扫描到，就可能出问题。 (Prisma Client, by default, generates into `node_modules/.prisma/client`. If for some reason it generated into `api/generated` and this folder is being scanned by Vercel, it could cause issues.)

   - 确认 `prisma generate` 的输出位置 (Confirm `prisma generate` output location):

     - 打开 `api/prisma/schema.prisma` 文件。 (Open the `api/prisma/schema.prisma` file.)

     - 检查 

       ```
       generator client { ... }
       ```

        部分是否有自定义的 

       ```
       output
       ```

       路径。默认情况下，它应该是这样的： (Check the 

       ```
       generator client { ... }
       ```

        section for any custom 

       ```
       output
       ```

        path. By default, it should be like this:)

       代码段

       ```
       generator client {
         provider = "prisma-client-js"
       }
       ```

       如果这里有 

       ```
       output = "../generated/client"
       ```

        之类的，那就是问题所在。理想情况下，让它生成到默认的 

       ```
       node_modules
       ```

        位置。 (If there's something like 

       ```
       output = "../generated/client"
       ```

       , that could be an issue. Ideally, let it generate to the default 

       ```
       node_modules
       ```

        location.)

     - **如果 `api/generated` 确实是 Prisma Client 的输出，并且你不确定如何修改，可以先尝试在 `.vercelignore` 文件中忽略它（见下一步）。** (If `api/generated` is indeed Prisma Client's output and you are unsure how to change it, you can first try ignoring it in a `.vercelignore` file (see next step).)

4. **清理后 `api` 文件夹应有的结构 (Expected structure of the `api` folder after cleanup):**

   - 理想情况下，`api` 文件夹根目录应该只包含直接的 Serverless Function 文件（如 `health.ts`）或包含这些文件的子目录（如 `auth/register.ts`）。 (Ideally, the root of the `api` folder should only contain direct Serverless Function files (like `health.ts`) or subdirectories containing them (like `auth/register.ts`).)

   - 你的 

     ```
     api
     ```

      文件夹在清理后，主要的可执行文件应该是： (Your 

     ```
     api
     ```

      folder, after cleanup, should primarily have these executable files:)

     ```
     MianshiJun_AI_Project/
     └── api/
         ├── health.ts
         ├── auth/
         │   └── register.ts
         ├── .env               (这个不会被部署，很好 - This won't be deployed, good)
         ├── .gitignore         (这个是Git的，没问题 - This is for Git, fine)
         ├── package.json
         ├── package-lock.json
         ├── tsconfig.json
         └── prisma/            (Prisma schema 和 migrations，没问题 - Prisma schema and migrations, fine)
             └── schema.prisma
             └── migrations/
     ```

     - `node_modules` 和 Prisma 生成的 client（通常在 `node_modules/.prisma/client`）由 `npm install` 和 `prisma generate` 处理，不应手动放在 `api` 根目录让Vercel扫描。 (`node_modules` and the Prisma generated client (usually in `node_modules/.prisma/client`) are handled by `npm install` and `prisma generate` and should not be manually placed in the `api` root for Vercel to scan.)

------

### 步骤 2: 创建并配置 `.vercelignore` 文件 (Create and Configure `.vercelignore` file)

目标 (Goal): 明确告诉 Vercel 在构建和部署 Serverless Functions 时应该忽略哪些文件或文件夹，即使它们在 api 目录下。

(Explicitly tell Vercel which files or folders to ignore when building and deploying Serverless Functions, even if they are in the api directory.)

1. **创建 `.vercelignore` 文件 (Create `.vercelignore` file):**

   - **操作 (Action):** 在你的**项目根目录 `MianshiJun_AI_Project/`** (与 `frontend` 和 `api` 文件夹同级) 创建一个名为 `.vercelignore` 的新文件。 (In your **project root directory `MianshiJun_AI_Project/`** (at the same level as `frontend` and `api` folders), create a new file named `.vercelignore`.)

2. **添加忽略规则 (Add ignore rules):**

   - 操作 (Action):

      打开 

     ```
     .vercelignore
     ```

      文件并粘贴以下内容。我们会忽略一些通常不需要作为独立函数部署的内容，特别是如果它们位于 

     ```
     api
     ```

      目录下。 (Open the 

     ```
     .vercelignore
     ```

      file and paste the following content. We will ignore some content that is typically not needed for deployment as separate functions, especially if they reside in the 

     ```
     api
     ```

      directory.)

     ```
     # 忽略 api 目录下的 node_modules, Prisma schema/migrations, .env, .gitignore, package files, tsconfig
     # (Ignore node_modules, Prisma schema/migrations, .env, .gitignore, package files, tsconfig within the api directory
     # if Vercel is trying to treat them as functions. Usually Vercel is smart enough,
     # but this adds explicitness if needed based on its behavior with your specific structure.)
     
     api/node_modules
     api/prisma
     api/.env
     api/.gitignore
     api/package.json
     api/package-lock.json
     api/tsconfig.json
     
     # 如果你的 Prisma Client 确实生成在 api/generated，并想忽略它
     # If your Prisma Client is indeed generated in api/generated and you want to ignore it
     api/generated
     
     # 确保前端的 node_modules 和 dist 目录也被忽略（尽管构建设置通常会处理这个）
     # Ensure frontend's node_modules and dist are also ignored (though build settings usually handle this)
     frontend/node_modules
     frontend/dist
     ```

   - 解释 (Explanation):

     - Vercel 在构建 Serverless Functions 时，会查看 `api` 目录。`.vercelignore` 文件可以告诉 Vercel 在这个过程中忽略某些路径。 (When Vercel builds Serverless Functions, it looks into the `api` directory. The `.vercelignore` file tells Vercel to ignore certain paths during this process.)
     - **重要 (Important):** `api/package.json`, `api/tsconfig.json` 对于 Vercel 构建 `api` 目录下的 TypeScript 函数**可能是有用的**。如果添加这些到 `.vercelignore` 后导致后端函数构建失败，则需要将它们从 `.vercelignore` 中移除。这里的目的是确保它们本身不被错误地识别为*单独的函数端点*。

------

### 步骤 3: 检查 Vercel 构建配置 (Verify Vercel Build Configuration)

目标 (Goal): 确保 Vercel 仪表盘中的项目设置能正确处理你的 monorepo 结构（即 frontend 和 api 文件夹在同一个仓库）。

(Ensure the project settings in the Vercel dashboard correctly handle your monorepo structure (i.e., frontend and api folders in the same repository).)

1. **登录 Vercel 并进入项目设置 (Log in to Vercel and go to Project Settings):**

   - 操作 (Action):
     1. 访问 [vercel.com](https://vercel.com/) 并登录。 (Go to [vercel.com](https://vercel.com/) and log in.)
     2. 选择你的 `new-mianshijun` 项目。 (Select your `new-mianshijun` project.)
     3. 转到 "Settings" (设置) 标签页，然后选择 "General" (常规)。 (Go to the "Settings" tab, then select "General".)

2. **确认根目录 (Confirm Root Directory):**

   - 操作 (Action):
     - 找到 "Root Directory" (根目录) 设置。 (Find the "Root Directory" setting.)
     - **确保它是空的 (或者明确设置为仓库根)**。正如您图片 `image_66af02.png` 所示，空的是正确的，这意味着 Vercel 会使用你 Git 仓库的根目录 (`MianshiJun_AI_Project`) 作为项目的根。 (Ensure it is empty (or explicitly set to the repository root). As your image `image_66af02.png` shows, empty is correct, meaning Vercel uses your Git repository's root (`MianshiJun_AI_Project`) as the project root.)

3. **配置/确认前端的构建设置 (Configure/Confirm Frontend Build Settings):**

   - 因为你的前端 React 项目在 `frontend` 子文件夹中，Vercel 需要知道在哪里找到它以及如何构建它。 (Since your frontend React project is in the `frontend` subdirectory, Vercel needs to know where to find it and how to build it.)

   - 操作 (Action):

      在 "Build & Development Settings" (构建和开发设置) 部分： (In the "Build & Development Settings" section:)

     1. 点击 "Framework Preset" (框架预设) 右侧的 "Override" (覆盖)。 (Click "Override" next to "Framework Preset".)

     2. **Build Command (构建命令):** 设置为 `cd frontend && npm run build` (Set "Build Command" to `cd frontend && npm run build`)

     3. **Output Directory (输出目录):** 设置为 `frontend/dist` (Set "Output Directory" to `frontend/dist`)

     4. Install Command (安装命令):

         设置为 

        ```
        cd frontend && npm install && cd .. && cd api && npm install && cd ..
        ```

        (或者更简洁的方式可能是 

        ```
        npm install --prefix frontend && npm install --prefix api
        ```

        ，但这取决于你的 

        ```
        package.json
        ```

         结构。最保险的方式是分开 

        ```
        cd
        ```

         并安装。如果你的 

        ```
        api
        ```

         目录的 

        ```
        package.json
        ```

         依赖很少或没有，可以简化为 

        ```
        cd frontend && npm install && cd ..
        ```

        ，然后Vercel会自动处理 

        ```
        api
        ```

         目录的依赖（如果它识别了函数）。但为了明确，我们先写全一点，或者Vercel在构建时也会自动为API函数安装依赖。) (A more concise way might be 

        ```
        npm install --prefix frontend && npm install --prefix api
        ```

        , but this depends on your 

        ```
        package.json
        ```

         structure. The safest approach is to 

        ```
        cd
        ```

         and install separately. If your 

        ```
        api
        ```

         directory's 

        ```
        package.json
        ```

         has few or no dependencies, it can be simplified to 

        ```
        cd frontend && npm install && cd ..
        ```

        , and Vercel will automatically handle dependencies for the 

        ```
        api
        ```

         directory if it recognizes functions. But to be explicit, let's write it more fully for now, or Vercel might also automatically install dependencies for API functions during build.)

        - 更简单且通常有效的方式 (Simpler and often effective way):
          - **Install Command (安装命令):** `cd frontend && npm install` (Vercel 通常会自动检测并为 `api` 目录中的函数安装其自身的 `package.json` 依赖。) (Vercel usually auto-detects and installs dependencies from the `api` directory's own `package.json` for its functions.)

4. **服务器函数的区域 (Serverless Function Region):**

   - **操作 (Action):** 在 "Serverless Function Region" 设置中，确保选择了你想要的区域，例如 "Washington, D.C., USA (East) – iad1"。 (In the "Serverless Function Region" settings, ensure your desired region is selected, e.g., "Washington, D.C., USA (East) – iad1".)

5. **保存更改 (Save Changes):**

   - **操作 (Action):** 如果你做了任何修改，点击 "Save" (保存) 按钮。 (If you made any changes, click the "Save" button.)

------

### 步骤 4: 提交更改到 GitHub 并触发重新部署 (Commit Changes to GitHub and Trigger Redeployment)

1. **提交本地更改 (Commit local changes):**

   - 操作 (Action):

     1. 在 Cursor 的终端中，确保你位于**总项目文件夹 `MianshiJun_AI_Project` 的根目录**。 (In Cursor's terminal, make sure you are in the **root of your main project directory `MianshiJun_AI_Project`**.)

     2. 运行 `git status` 检查你的更改 (包括删除的文件和新的 `.vercelignore`)。 (Run `git status` to check your changes (including deleted files and the new `.vercelignore`).)

     3. 添加所有更改： (Add all changes:)

        Bash

        ```
        git add .
        ```

     4. 提交更改，写一个清晰的提交信息： (Commit the changes with a clear message:)

        Bash

        ```
        git commit -m "Refactor: Clean up api directory and add .vercelignore to fix function limit"
        ```

        (提交信息意思是：“重构：清理api目录并添加.vercelignore以解决函数数量限制问题”) (This commit message means: "Refactor: Clean up api directory and add .vercelignore to fix function limit")

2. **推送到 GitHub (Push to GitHub):**

   - 操作 (Action):

     Bash

     ```
     git push origin main
     ```

3. **在 Vercel 上检查部署 (Check deployment on Vercel):**

   - 操作 (Action):
     1. 推送到 GitHub后，Vercel应该会自动开始一个新的部署。 (After pushing to GitHub, Vercel should automatically start a new deployment.)
     2. 去你的 Vercel 项目仪表盘查看部署状态和日志。 (Go to your Vercel project dashboard to check the deployment status and logs.)
     3. **特别关注日志中关于 Serverless Functions 的部分，看是否还有数量超限的错误。** (Pay close attention to the logs related to Serverless Functions to see if the "function count limit" error still appears.)

------

**如果问题仍然存在 (If the problem persists):**

- **仔细检查 `api` 目录**：确保没有任何隐藏的 `.js` 或 `.ts` 文件，或者任何可能被Vercel错误地解释为单独函数的子目录结构。 (Double-check the `api` directory: ensure there are no hidden `.js` or `.ts` files, or any subdirectory structures that Vercel might misinterpret as separate functions.)
- **Vercel Support 或 Community**: 如果上述步骤都不能解决，你可能需要在Vercel的官方文档中查找更具体的monorepo配置，或者在Vercel社区寻求帮助，提供你的部署日志和项目结构。 (If none of the above steps work, you might need to look for more specific monorepo configurations in Vercel's official documentation or seek help in the Vercel community, providing your deployment logs and project structure.)
- **考虑将后端函数合并**: 如果你的API端点逻辑简单且相关，可以考虑将多个逻辑合并到一个Serverless Function文件中，通过请求路径或参数来区分不同的操作。但这会增加该单个文件的复杂性。 (If your API endpoint logic is simple and related, consider merging multiple logics into a single Serverless Function file, distinguishing different operations by request path or parameters. However, this increases the complexity of that single file.)

我相信通过清理 `api` 目录和正确配置 Vercel 的构建设置，这个问题应该能得到解决。请您先尝试这些步骤，特别是清理 `api` 目录和在Vercel仪表盘上**确认/修改构建命令和输出目录**。

告诉我您的操作结果！