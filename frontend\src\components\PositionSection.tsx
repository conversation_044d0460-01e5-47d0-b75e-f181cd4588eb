import React, { useState, useEffect } from 'react';
import { Briefcase, Plus, ChevronRight } from 'lucide-react';
import type { Position } from '@new-mianshijun/common';
import AddPositionModal, { PositionFormData } from './dashboard/AddPositionModal';
import { targetPositionService, TargetPosition, TargetPositionCreateInput, TargetPositionUpdateInput } from '../lib/api/apiService';
import { useToast } from '../hooks/useToast';
import ToastContainer from './ui/ToastContainer';
import useAuthStore from '../stores/authStore';
import { usePositions, usePositionsLoading, usePositionsError, usePositionActions, usePositionStore } from '../stores/positionStore';

interface PositionSectionProps {
  positions: Position[] | TargetPosition[];
  maxPositions: number;
}

const PositionSection: React.FC<PositionSectionProps> = ({
  // 暂时不使用传入的positions，而是通过API获取
  // positions: initialPositions,
  maxPositions
}) => {
  // 🎯 检查 Zustand persist 是否已经水合完成（简化版本）
  const hasHydrated = (usePositionStore as any).persist?.hasHydrated?.() ?? true;

  // 🎯 使用全局状态管理替代本地状态
  const positions = usePositions(); // 现在 usePositions 钩子内部已经确保返回数组
  const isLoading = usePositionsLoading();
  const error = usePositionsError();
  const { fetchPositions, addPosition, updatePosition, removePosition } = usePositionActions();

  // 本地UI状态
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingPosition, setEditingPosition] = useState<TargetPosition | null>(null);
  const [isOperating, setIsOperating] = useState<boolean>(false); // 区分数据加载和操作加载

  // Toast功能
  const { toasts, removeToast, showSuccess, showError } = useToast();

  // 认证状态
  const { isAuthenticated } = useAuthStore();

  // 🎯 如果还没有水合完成，显示加载状态
  if (!hasHydrated) {
    return (
      <div className="bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-800 dark:to-gray-900 rounded-xl p-6 shadow-lg">
        <div className="flex items-center space-x-3 mb-4">
          <span className="bg-blue-100 dark:bg-blue-900 p-2 rounded-lg">
            <Briefcase className="w-5 h-5 text-sky-600 dark:text-sky-400" />
          </span>
          <div>
            <h3 className="text-lg font-bold text-gray-800 dark:text-white">意向岗位</h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">正在加载数据...</p>
          </div>
        </div>
        <div className="bg-white bg-opacity-60 backdrop-blur-sm rounded-lg p-4 text-center h-[122px] flex items-center justify-center">
          <div className="text-gray-600">正在初始化岗位数据...</div>
        </div>
      </div>
    );
  }

  // 🎯 智能数据加载：只在需要时获取
  useEffect(() => {
    if (isAuthenticated) {
      // 使用全局状态管理的智能缓存
      fetchPositions().catch(error => {
        console.error('Failed to fetch positions in PositionSection:', error);
        // 错误已经在store中处理，这里只需要记录日志
      });
    }
  }, [isAuthenticated, fetchPositions]);

  // 🎯 移除本地加载函数，使用全局状态管理

  const handleOpenModal = () => {
    setEditingPosition(null);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => setIsModalOpen(false);

  const handleEditPosition = (position: TargetPosition) => {
    // 🔍 调试：检查编辑的岗位数据
    console.log('🔍 编辑岗位数据:', position);

    // 🛡️ 防御性编程：验证岗位对象
    if (!position) {
      console.error('编辑失败：岗位对象为空');
      showError('编辑失败：岗位信息无效');
      return;
    }

    if (!position.id) {
      console.error('编辑失败：岗位ID为空', position);
      showError('编辑失败：岗位ID无效');
      return;
    }

    setEditingPosition(position);
    setIsModalOpen(true);
  };

  const handleSavePosition = async (data: PositionFormData) => {
    console.log('岗位数据 (Position Data):', data);
    setIsOperating(true);

    try {
      // 准备API调用的数据
      const positionData: TargetPositionCreateInput = {
        positionName: data.positionName,
        companyName: data.companyName,
      };

      // 只添加非空的可选字段
      if (data.positionRequirements) {
        positionData.positionRequirements = data.positionRequirements;
      }

      if (data.companyProfile) {
        positionData.companyProfile = data.companyProfile;
      }

      // 添加默认状态
      positionData.status = '待处理';

      if (editingPosition) {
        // 🎯 更新岗位：API + 全局状态同步
        const updatedPosition = await targetPositionService.updateTargetPosition(
          editingPosition.id,
          positionData as TargetPositionUpdateInput
        );

        // 更新全局状态
        updatePosition(editingPosition.id, updatedPosition);

        console.log(`岗位 "${data.positionName}" 已更新!`);
        showSuccess(`岗位 "${data.positionName}" 更新成功！`);
      } else {
        // 🎯 添加新岗位：API + 全局状态同步
        const newPosition = await targetPositionService.createTargetPosition(
          positionData as TargetPositionCreateInput
        );

        // 更新全局状态
        addPosition(newPosition);

        console.log(`岗位 "${data.positionName}" 已添加!`);
        showSuccess(`岗位 "${data.positionName}" 添加成功！`);
      }

      handleCloseModal();
    } catch (err: any) {
      console.error('保存岗位失败:', err);
      showError(err.message || '保存岗位失败，请稍后重试');
    } finally {
      setIsOperating(false);
    }
  };

  const handleDeletePosition = async (position: TargetPosition) => {
    console.log('删除岗位:', position);

    // 🛡️ 防御性编程：验证岗位对象和ID
    if (!position) {
      console.error('删除失败：岗位对象为空');
      showError('删除失败：岗位信息无效');
      return;
    }

    if (!position.id) {
      console.error('删除失败：岗位ID为空', position);
      showError('删除失败：岗位ID无效');
      return;
    }

    setIsOperating(true);

    try {
      // 🎯 删除岗位：API + 全局状态同步
      await targetPositionService.deleteTargetPosition(position.id);
      console.log(`岗位 "${position.positionName}" 已删除!`);

      // 更新全局状态
      removePosition(position.id);

      showSuccess(`岗位 "${position.positionName}" 删除成功！`);
      handleCloseModal();
    } catch (err: any) {
      console.error('删除岗位失败:', err);
      showError(err.message || '删除岗位失败，请稍后重试');
    } finally {
      setIsOperating(false);
    }
  };

  return (
    <>
      {/* Toast容器 */}
      <ToastContainer toasts={toasts} onRemoveToast={removeToast} />

      <div className="bg-gradient-to-br from-sky-50 to-indigo-50 dark:from-gray-800 dark:to-gray-700 rounded-xl p-4 relative overflow-hidden flex-shrink-0 flex-grow-0 transition-colors" style={{ minHeight: '187px', height: '187px', flexBasis: '187px' }}>
        {isLoading && (
          <div className="absolute inset-0 bg-white dark:bg-gray-800 bg-opacity-50 dark:bg-opacity-50 flex items-center justify-center z-10">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-sky-500 dark:border-sky-400"></div>
          </div>
        )}
        <div className="absolute top-1/2 right-0 w-64 h-64 bg-gradient-to-br from-sky-200 to-indigo-200 dark:from-gray-600 dark:to-gray-500 rounded-full blur-3xl opacity-20 transform -translate-y-1/2 translate-x-1/2"></div>

        <div className="relative h-full">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <span className="p-2 bg-sky-100 dark:bg-sky-900/30 rounded-xl">
                <Briefcase className="w-5 h-5 text-sky-600 dark:text-sky-400" />
              </span>
              <div>
                <h3 className="text-lg font-bold text-gray-800 dark:text-white">意向岗位</h3>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  已添加 {positions.length}/{maxPositions} 个岗位
                </p>
              </div>
              {error && (
                <div className="text-xs text-red-500 dark:text-red-400 ml-2">{error}</div>
              )}
            </div>

            <button
              onClick={handleOpenModal}
              className="px-4 py-2 bg-sky-500 dark:bg-sky-600 text-white rounded-lg font-medium flex items-center gap-2 hover:bg-sky-600 dark:hover:bg-sky-700 transition-all"
            >
              <Plus className="w-4 h-4" />
              添加岗位
            </button>
          </div>

          {positions.length === 0 ? (
            <div className="bg-white bg-opacity-60 backdrop-blur-sm rounded-lg p-4 text-center h-full">
              <p className="text-gray-600 mb-3">开始添加你感兴趣的岗位</p>
              <div className="flex flex-wrap gap-2 justify-center">
                {['前端工程师', '产品经理', 'UI 设计师'].map((suggestion, index) => (
                  <button
                    key={index}
                    className="px-3 py-1.5 bg-white rounded-lg text-sm text-gray-600 hover:bg-sky-50 hover:text-sky-600 transition-colors flex items-center gap-1"
                  >
                    {suggestion}
                    <ChevronRight className="w-4 h-4" />
                  </button>
                ))}
              </div>
            </div>
          ) : (
            // 岗位列表容器，固定高度为三个岗位时的高度
            <div className={`grid gap-2 overflow-y-auto pr-1 custom-scrollbar h-[122px] min-h-[122px]`} style={{ height: '122px', minHeight: '122px' }}>
              {positions.map((position, index) => {
                // 🔍 调试：检查岗位数据结构
                if (!position.id) {
                  console.warn('⚠️ 发现无效岗位数据（缺少ID）:', position);
                }

                return (
                  <div
                    key={position.id || `position-${index}`}
                    className="bg-white rounded-lg p-3 flex items-center justify-between h-[47px]"
                    data-component-name="PositionSection"
                  >
                    <span className="font-medium text-gray-800 truncate max-w-[80%]">{position.positionName}</span>
                    <button
                      className="text-gray-400 hover:text-gray-600"
                      onClick={() => handleEditPosition(position)}
                      disabled={isLoading || isOperating}
                    >
                      <ChevronRight className="w-5 h-5" />
                    </button>
                  </div>
                );
              })}
            </div>
          )}
        </div>
      </div>

      {/* 添加/编辑岗位弹窗 */}
      <AddPositionModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        onSave={handleSavePosition}
        onDelete={editingPosition && editingPosition.id ? () => handleDeletePosition(editingPosition) : undefined}
        position={editingPosition}
        isLoading={isOperating}
      />
    </>
  );
};

export default PositionSection;