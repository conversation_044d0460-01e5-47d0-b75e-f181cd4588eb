// VAD (Voice Activity Detection) 相关类型定义

export interface VADResult {
  isSpeech: boolean;
  energy: number;
  confidence: number;
  timestamp: number;
  spectralFeatures?: SpectralFeatures;
  isEndOfSpeech?: boolean;
  speechContinuity?: number;
  segmentQuality?: number;
}

export interface SpectralFeatures {
  spectralCentroid: number;
  zeroCrossingRate: number;
  spectralRolloff: number;
  mfcc?: number[];
}

export interface VADConfig {
  energyThreshold: number;
  silenceThreshold: number;
  minSpeechDuration: number; // ms
  minSilenceDuration: number; // ms
  adaptiveThreshold: boolean;
  sampleRate: number;
  endOfSpeechTimeout: number;
  continuityThreshold: number;
  qualityThreshold: number;
  maxSpeechDuration: number;
}

export interface VADState {
  isInSpeech: boolean;
  speechStartTime: number;
  silenceStartTime: number;
  recentEnergyHistory: number[];
  adaptiveThreshold: number;
  speechDuration: number;
  lastSpeechQuality: number;
  continuityScore: number;
  pendingEndDetection: boolean;
}

export const DEFAULT_VAD_CONFIG: VADConfig = {
  energyThreshold: 0.01,
  silenceThreshold: 0.005,
  minSpeechDuration: 300,
  minSilenceDuration: 800,
  adaptiveThreshold: true,
  sampleRate: 16000,
  endOfSpeechTimeout: 1500,
  continuityThreshold: 0.7,
  qualityThreshold: 0.6,
  maxSpeechDuration: 55000
};
