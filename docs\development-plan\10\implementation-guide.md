# AI模拟面试逻辑优化 - 技术实施指南

## 实施概览

本文档为专家团队提供详细的技术实施指南，包括具体的代码修改方案、架构设计和实施步骤。

## 核心架构设计

### 1. 消息类型系统重构

#### 当前问题
```typescript
// ❌ 当前错误的设计 - frontend/src/stores/mockInterviewStore.ts
export interface MockMessage {
  type: 'interviewer' | 'ai-suggestion' | 'user-answer'; // ai-suggestion不应该存在
}
```

#### 解决方案
```typescript
// ✅ 正确的设计
export interface MockMessage {
  id: string;
  content: string;
  type: 'interviewer' | 'user-answer'; // 只有面试官问题和用户回答
  timestamp: number;
  questionId?: string; // AI问题的唯一标识
  isFinal?: boolean; // 用户回答是否为最终版本
}

// 为了更好的类型安全，建议使用联合类型
export type MockInterviewerMessage = {
  type: 'interviewer';
  questionId: string;
  questionType: string;
  difficulty: string;
  expectedDuration: number;
  context: string;
} & Omit<MockMessage, 'type'>;

export type MockUserAnswerMessage = {
  type: 'user-answer';
  questionId: string;
  isFinal: boolean;
} & Omit<MockMessage, 'type'>;
```

### 2. 状态机驱动的流程控制

#### 状态定义
```typescript
export enum MockInterviewState {
  INITIALIZING = 'initializing',           // 初始化中
  WAITING_FOR_AI_QUESTION = 'waiting_for_ai_question', // 等待AI问题
  DISPLAYING_QUESTION = 'displaying_question',         // 显示问题中
  LISTENING_FOR_ANSWER = 'listening_for_answer',       // 监听用户回答
  PROCESSING_ANSWER = 'processing_answer',             // 处理用户回答
  GENERATING_NEXT_QUESTION = 'generating_next_question', // 生成下一个问题
  COMPLETED = 'completed',                             // 面试完成
  ERROR = 'error'                                      // 错误状态
}
```

#### 状态转换逻辑
```typescript
export class MockInterviewStateMachine {
  private state: MockInterviewState = MockInterviewState.INITIALIZING;
  
  transition(event: string, data?: any): MockInterviewState {
    switch (this.state) {
      case MockInterviewState.INITIALIZING:
        if (event === 'SESSION_STARTED') {
          return this.setState(MockInterviewState.WAITING_FOR_AI_QUESTION);
        }
        break;
        
      case MockInterviewState.WAITING_FOR_AI_QUESTION:
        if (event === 'AI_QUESTION_RECEIVED') {
          return this.setState(MockInterviewState.DISPLAYING_QUESTION);
        }
        break;
        
      case MockInterviewState.DISPLAYING_QUESTION:
        if (event === 'START_LISTENING') {
          return this.setState(MockInterviewState.LISTENING_FOR_ANSWER);
        }
        break;
        
      case MockInterviewState.LISTENING_FOR_ANSWER:
        if (event === 'USER_ANSWER_COMPLETED') {
          return this.setState(MockInterviewState.PROCESSING_ANSWER);
        }
        break;
        
      case MockInterviewState.PROCESSING_ANSWER:
        if (event === 'ANSWER_SENT_TO_AI') {
          return this.setState(MockInterviewState.GENERATING_NEXT_QUESTION);
        }
        break;
        
      case MockInterviewState.GENERATING_NEXT_QUESTION:
        if (event === 'NEXT_QUESTION_READY') {
          return this.setState(MockInterviewState.WAITING_FOR_AI_QUESTION);
        } else if (event === 'INTERVIEW_COMPLETED') {
          return this.setState(MockInterviewState.COMPLETED);
        }
        break;
    }
    
    return this.state;
  }
  
  private setState(newState: MockInterviewState): MockInterviewState {
    console.log(`🔄 State transition: ${this.state} → ${newState}`);
    this.state = newState;
    return newState;
  }
}
```

### 3. 后端服务重构

#### MockInterviewService 优化
```typescript
// backend/websocket/handlers/mockInterviewService.ts

export class MockInterviewService {
  private stateMachine = new Map<string, MockInterviewStateMachine>();
  
  async handleSessionStart(sessionId: string, ws: WebSocket, config: MockInterviewConfig) {
    // 初始化状态机
    this.stateMachine.set(sessionId, new MockInterviewStateMachine());
    
    // 发送会话开始消息
    const sessionStartMessage = {
      type: 'session_started',
      sessionId,
      timestamp: Date.now()
    };
    ws.send(JSON.stringify(sessionStartMessage));
    
    // 🔥 立即发送第一个问题（不延迟）
    await this.sendFirstQuestion(sessionId, ws, config);
  }
  
  private async sendFirstQuestion(sessionId: string, ws: WebSocket, config: MockInterviewConfig) {
    const session = this.sessions.get(sessionId);
    if (!session) return;
    
    // 状态转换
    const stateMachine = this.stateMachine.get(sessionId);
    stateMachine?.transition('SESSION_STARTED');
    
    // 生成个性化的自我介绍问题
    const introQuestion = this.generatePersonalizedIntroQuestion(
      config.companyName, 
      config.positionName
    );
    
    const questionMessage = {
      type: 'mock_interview_question',
      mode: 'mock',
      sessionId,
      questionId: `q_${Date.now()}`,
      questionText: introQuestion,
      questionType: 'behavioral',
      difficulty: 'easy',
      expectedDuration: 120,
      context: '自我介绍环节',
      timestamp: Date.now()
    };
    
    console.log('🤖 Sending first question:', introQuestion);
    ws.send(JSON.stringify(questionMessage));
    
    // 状态转换
    stateMachine?.transition('AI_QUESTION_RECEIVED');
  }
  
  private generatePersonalizedIntroQuestion(companyName: string, positionName: string): string {
    const templates = [
      `您好，欢迎参加${companyName}的${positionName}岗位面试。请先做一个简单的自我介绍，包括您的教育背景、工作经验以及为什么对这个职位感兴趣。`,
      `很高兴见到您！我是今天的面试官。请您简单介绍一下自己，特别是与${positionName}相关的经验和技能。`,
      `欢迎来到${companyName}！请您用2-3分钟的时间介绍一下自己，重点说说您的专业背景和对${positionName}这个岗位的理解。`,
      `您好！感谢您对${companyName}的${positionName}职位的关注。请先自我介绍一下，让我们了解您的背景和优势。`
    ];
    
    const randomIndex = Math.floor(Math.random() * templates.length);
    return templates[randomIndex];
  }
  
  async handleUserAnswer(sessionId: string, answerData: any) {
    const session = this.sessions.get(sessionId);
    const stateMachine = this.stateMachine.get(sessionId);
    
    if (!session || !stateMachine) return;
    
    // 状态转换
    stateMachine.transition('USER_ANSWER_COMPLETED');
    
    // 保存用户回答
    session.answers.push({
      id: `a_${Date.now()}`,
      questionId: answerData.questionId,
      answerText: answerData.answerText,
      timestamp: answerData.timestamp
    });
    
    // 状态转换
    stateMachine.transition('ANSWER_SENT_TO_AI');
    
    // 生成下一个问题
    await this.generateNextQuestion(sessionId);
  }
  
  private async generateNextQuestion(sessionId: string) {
    const session = this.sessions.get(sessionId);
    const stateMachine = this.stateMachine.get(sessionId);
    
    if (!session || !stateMachine) return;
    
    // 检查是否应该结束面试
    if (session.answers.length >= 5) { // 假设5个问题后结束
      stateMachine.transition('INTERVIEW_COMPLETED');
      return;
    }
    
    // 使用AI生成下一个问题
    const nextQuestion = await this.llmService.generateInterviewQuestion({
      companyName: session.companyName,
      positionName: session.positionName,
      previousQuestions: session.questions.map(q => q.text),
      previousAnswers: session.answers.map(a => a.answerText),
      currentQuestionIndex: session.answers.length
    });
    
    // 发送新问题
    const questionMessage = {
      type: 'mock_interview_question',
      mode: 'mock',
      sessionId,
      questionId: `q_${Date.now()}`,
      questionText: nextQuestion.questionText,
      questionType: nextQuestion.questionType,
      difficulty: nextQuestion.difficulty,
      expectedDuration: nextQuestion.expectedDuration,
      context: nextQuestion.context,
      timestamp: Date.now()
    };
    
    const ws = this.getWebSocketBySessionId(sessionId);
    if (ws) {
      ws.send(JSON.stringify(questionMessage));
      stateMachine.transition('NEXT_QUESTION_READY');
    }
  }
}
```

### 4. 前端钩子重构

#### useInterviewSession 优化
```typescript
// frontend/src/hooks/useInterviewSession.ts

export function useInterviewSession(mode: 'mock' | 'formal') {
  const [stateMachine] = useState(() => new MockInterviewStateMachine());
  const [currentQuestionId, setCurrentQuestionId] = useState<string | null>(null);
  
  // WebSocket消息处理
  const handleWebSocketMessage = useCallback((event: MessageEvent) => {
    const data = JSON.parse(event.data);
    
    switch (data.type) {
      case 'mock_interview_question':
        if (mode === 'mock') {
          console.log('🤖 Received AI mock interview question:', data.questionText);
          
          // 添加AI问题到消息列表
          addMessage({
            id: data.questionId,
            content: data.questionText,
            type: 'interviewer',
            timestamp: data.timestamp || Date.now(),
            questionId: data.questionId
          });
          
          // 更新当前问题ID
          setCurrentQuestionId(data.questionId);
          
          // 状态转换
          stateMachine.transition('AI_QUESTION_RECEIVED');
          
          // 🔥 收到AI问题后，自动启动ASR监听
          if (!isRecording && recordingIntent) {
            console.log('🎤 Starting recording after receiving AI question');
            setTimeout(() => {
              forceStartRecording();
              stateMachine.transition('START_LISTENING');
            }, 500); // 短暂延迟确保消息已显示
          }
        }
        break;
        
      // 其他消息类型处理...
    }
  }, [mode, isRecording, recordingIntent, addMessage, forceStartRecording]);
  
  // ASR结果处理
  const handleASRResult = useCallback((data: any) => {
    if (data.isFinal && mode === 'mock') {
      // 状态转换
      stateMachine.transition('USER_ANSWER_COMPLETED');
      
      // 创建用户回答消息
      const newMessage: MockUserAnswerMessage = {
        id: `msg_${Date.now()}`,
        content: data.text,
        type: 'user-answer',
        timestamp: Date.now(),
        questionId: currentQuestionId || 'unknown',
        isFinal: true
      };
      
      // 添加到消息列表
      addMessage(newMessage);
      
      // 🔥 发送用户回答给后端，触发AI生成下一个问题
      if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
        const answerMessage = {
          type: 'mock_interview_answer',
          questionId: currentQuestionId,
          answerText: data.text,
          timestamp: Date.now()
        };
        
        console.log('📤 Sending user answer to backend:', answerMessage);
        wsRef.current.send(JSON.stringify(answerMessage));
        
        // 状态转换
        stateMachine.transition('ANSWER_SENT_TO_AI');
      }
    }
  }, [mode, currentQuestionId, addMessage, wsRef]);
  
  // 连接建立后的处理
  const handleWebSocketOpen = useCallback(() => {
    if (mode === 'mock') {
      // 🔥 模拟面试模式：不立即启动录音，等待AI问题
      console.log('🎤 Mock interview mode: Waiting for AI question before starting recording');
      stateMachine.transition('SESSION_STARTED');
    }
  }, [mode, stateMachine]);
  
  return {
    // ... 其他返回值
    currentState: stateMachine.state,
    currentQuestionId
  };
}
```

## 实施步骤

### 阶段1：基础架构重构（2-3天）
1. 重构消息类型系统
2. 实现状态机
3. 分离模拟面试和正式面试的处理逻辑

### 阶段2：流程逻辑修复（2-3天）
1. 修复AI问题发送时机
2. 修复ASR启动时机
3. 实现用户回答的自动处理

### 阶段3：优化和测试（2-3天）
1. 优化问题生成策略
2. 完善错误处理
3. 全面测试和调优

## 关键测试用例

### 功能测试
```javascript
describe('Mock Interview Flow', () => {
  test('should send AI question immediately after entering page', async () => {
    // 进入页面
    // 验证AI立即发送问题
    // 验证问题显示为蓝色气泡
  });
  
  test('should start ASR after receiving AI question', async () => {
    // 收到AI问题
    // 验证ASR自动启动
    // 验证录音状态正确
  });
  
  test('should generate next question after user answer', async () => {
    // 用户回答完毕
    // 验证回答发送给后端
    // 验证AI生成新问题
    // 验证问题不重复
  });
});
```

这个实施指南为专家团队提供了详细的代码级别的解决方案，确保能够准确实施所需的修复。
