import { PrismaClient } from '@prisma/client';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const prisma = new PrismaClient();

async function backupData() {
  console.log('🔄 开始备份重要数据...');

  const backupDir = path.join(__dirname, '../backups');
  if (!fs.existsSync(backupDir)) {
    fs.mkdirSync(backupDir, { recursive: true });
  }
  
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const backupFile = path.join(backupDir, `backup-${timestamp}.json`);
  
  try {
    // 备份用户数据
    console.log('📊 备份用户数据...');
    const users = await prisma.user.findMany({
      include: {
        balance: true
      }
    });
    
    // 备份订单数据
    console.log('📊 备份订单数据...');
    const orders = await prisma.order.findMany();
    
    // 备份兑换码数据
    console.log('📊 备份兑换码数据...');
    const redemptionCodes = await prisma.redemptionCode.findMany();
    
    // 备份使用记录
    console.log('📊 备份使用记录...');
    const usageRecords = await prisma.usageRecord.findMany();
    
    // 备份邀请数据
    console.log('📊 备份邀请数据...');
    const referralCodes = await prisma.referralCode.findMany();
    const referralRelations = await prisma.referralRelation.findMany();
    const referralRewards = await prisma.referralReward.findMany();
    
    const backupData = {
      timestamp: new Date().toISOString(),
      users,
      orders,
      redemptionCodes,
      usageRecords,
      referralCodes,
      referralRelations,
      referralRewards
    };
    
    fs.writeFileSync(backupFile, JSON.stringify(backupData, null, 2));
    
    console.log(`✅ 数据备份完成: ${backupFile}`);
    console.log(`📊 备份统计:`);
    console.log(`   - 用户: ${users.length}`);
    console.log(`   - 订单: ${orders.length}`);
    console.log(`   - 兑换码: ${redemptionCodes.length}`);
    console.log(`   - 使用记录: ${usageRecords.length}`);
    console.log(`   - 邀请码: ${referralCodes.length}`);
    console.log(`   - 邀请关系: ${referralRelations.length}`);
    console.log(`   - 邀请奖励: ${referralRewards.length}`);
    
  } catch (error) {
    console.error('❌ 备份失败:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

backupData().catch(console.error);
