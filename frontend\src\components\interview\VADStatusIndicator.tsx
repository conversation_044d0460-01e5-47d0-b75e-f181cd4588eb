import React, { useState, useEffect } from 'react';
import { VADStatus } from '../../hooks/useVAD';

interface VADStatusIndicatorProps {
  vadStatus: VADStatus;
  className?: string;
  showDetails?: boolean;
  showAnimation?: boolean;
  compact?: boolean;
}

/**
 * VAD状态指示器组件
 * 显示语音活动检测的当前状态
 */
export const VADStatusIndicator: React.FC<VADStatusIndicatorProps> = ({
  vadStatus,
  className = '',
  showDetails = false,
  showAnimation = true,
  compact = false
}) => {
  const [isAnimating, setIsAnimating] = useState(false);
  const [lastProcessingTime, setLastProcessingTime] = useState<number>(0);

  useEffect(() => {
    if (vadStatus.isProcessing) {
      setIsAnimating(true);
      setLastProcessingTime(Date.now());
    } else {
      // 延迟停止动画，让用户看到处理完成
      const timer = setTimeout(() => setIsAnimating(false), 500);
      return () => clearTimeout(timer);
    }
  }, [vadStatus.isProcessing]);
  const getStatusColor = () => {
    if (vadStatus.error) return 'text-red-500';
    if (!vadStatus.isInitialized) return 'text-gray-400';
    if (vadStatus.isProcessing) return 'text-blue-500';
    return 'text-green-500';
  };

  const getStatusText = () => {
    if (vadStatus.error) return '错误';
    if (!vadStatus.isInitialized) return '未初始化';
    if (vadStatus.isProcessing) return '处理中';
    return '就绪';
  };

  const getStatusIcon = () => {
    if (vadStatus.error) return '❌';
    if (!vadStatus.isInitialized) return '⏳';
    if (vadStatus.isProcessing) return '🎤';
    return '✅';
  };

  const getAnimationClass = () => {
    if (!showAnimation) return '';
    if (isAnimating) return 'animate-pulse';
    return '';
  };

  const getProcessingInfo = () => {
    if (!vadStatus.currentSegmentInfo) return null;

    const duration = vadStatus.currentSegmentInfo.duration || 0;
    const confidence = vadStatus.currentSegmentInfo.confidence || 0;

    return {
      duration: Math.round(duration),
      confidence: Math.round(confidence * 100),
      type: vadStatus.currentSegmentInfo.segmentType
    };
  };

  if (compact) {
    return (
      <div className={`inline-flex items-center gap-1 ${className}`}>
        <span className={`text-sm ${getAnimationClass()}`}>{getStatusIcon()}</span>
        <span className={`text-xs ${getStatusColor()}`}>
          {getStatusText()}
        </span>
      </div>
    );
  }

  return (
    <div className={`vad-status-indicator bg-white rounded-lg shadow-sm border p-3 ${className}`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <span className={`text-lg ${getAnimationClass()}`}>{getStatusIcon()}</span>
          <div>
            <span className={`text-sm font-medium ${getStatusColor()}`}>
              VAD: {getStatusText()}
            </span>
            {vadStatus.isProcessing && (
              <div className="text-xs text-gray-500 mt-1">
                正在处理语音...
              </div>
            )}
          </div>
        </div>

        {vadStatus.isInitialized && !vadStatus.error && (
          <div className="text-right">
            <div className={`w-2 h-2 rounded-full ${vadStatus.isProcessing ? 'bg-green-400 animate-pulse' : 'bg-gray-300'}`}></div>
          </div>
        )}
      </div>

      {showDetails && (
        <div className="mt-3 space-y-2">
          {/* 当前段信息 */}
          {vadStatus.currentSegmentInfo && (
            <div className="bg-gray-50 rounded p-2">
              <div className="text-xs font-medium text-gray-700 mb-1">当前音频段</div>
              <div className="grid grid-cols-3 gap-2 text-xs">
                <div>
                  <span className="text-gray-500">类型:</span>
                  <span className={`ml-1 font-medium ${
                    vadStatus.currentSegmentInfo.segmentType === 'speech' ? 'text-green-600' :
                    vadStatus.currentSegmentInfo.segmentType === 'silence' ? 'text-gray-500' :
                    'text-blue-600'
                  }`}>
                    {vadStatus.currentSegmentInfo.segmentType === 'speech' ? '语音' :
                     vadStatus.currentSegmentInfo.segmentType === 'silence' ? '静音' : '混合'}
                  </span>
                </div>
                <div>
                  <span className="text-gray-500">置信度:</span>
                  <span className="ml-1 font-medium">
                    {Math.round((vadStatus.currentSegmentInfo.confidence || 0) * 100)}%
                  </span>
                </div>
                <div>
                  <span className="text-gray-500">时长:</span>
                  <span className="ml-1 font-medium">
                    {Math.round(vadStatus.currentSegmentInfo.duration || 0)}ms
                  </span>
                </div>
              </div>
            </div>
          )}

          {/* 统计信息 */}
          {vadStatus.stats && (
            <div className="bg-blue-50 rounded p-2">
              <div className="text-xs font-medium text-blue-700 mb-1">处理统计</div>
              <div className="grid grid-cols-2 gap-2 text-xs">
                <div>
                  <span className="text-blue-600">总段数:</span>
                  <span className="ml-1 font-medium">{vadStatus.stats.totalSegments}</span>
                </div>
                <div>
                  <span className="text-blue-600">平均时长:</span>
                  <span className="ml-1 font-medium">
                    {vadStatus.stats.averageDuration?.toFixed(0)}ms
                  </span>
                </div>
                <div>
                  <span className="text-blue-600">平均置信度:</span>
                  <span className="ml-1 font-medium">
                    {Math.round((vadStatus.stats.averageConfidence || 0) * 100)}%
                  </span>
                </div>
                <div>
                  <span className="text-blue-600">处理时间:</span>
                  <span className="ml-1 font-medium">
                    {lastProcessingTime > 0 ? `${Date.now() - lastProcessingTime}ms` : '-'}
                  </span>
                </div>
              </div>
            </div>
          )}

          {/* 错误信息 */}
          {vadStatus.error && (
            <div className="bg-red-50 rounded p-2">
              <div className="text-xs font-medium text-red-700 mb-1">错误信息</div>
              <div className="text-xs text-red-600">{vadStatus.error}</div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default VADStatusIndicator;
