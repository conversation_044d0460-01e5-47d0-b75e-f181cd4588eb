/**
 * 循环缓冲区实现
 * 用于高效管理音频数据流
 */
export class CircularBuffer<T> {
  private buffer: T[];
  private head: number = 0;
  private tail: number = 0;
  private size: number = 0;
  private readonly capacity: number;

  constructor(capacity: number) {
    this.capacity = capacity;
    this.buffer = new Array(capacity);
  }

  /**
   * 添加元素到缓冲区
   */
  push(item: T): void {
    this.buffer[this.tail] = item;
    this.tail = (this.tail + 1) % this.capacity;
    
    if (this.size < this.capacity) {
      this.size++;
    } else {
      // 缓冲区满，移动头指针
      this.head = (this.head + 1) % this.capacity;
    }
  }

  /**
   * 获取所有元素（按时间顺序）
   */
  getAll(): T[] {
    const result: T[] = [];
    for (let i = 0; i < this.size; i++) {
      const index = (this.head + i) % this.capacity;
      result.push(this.buffer[index]);
    }
    return result;
  }

  /**
   * 获取最近的N个元素
   */
  getLast(count: number): T[] {
    const actualCount = Math.min(count, this.size);
    const result: T[] = [];
    
    for (let i = actualCount - 1; i >= 0; i--) {
      const index = (this.tail - 1 - i + this.capacity) % this.capacity;
      result.unshift(this.buffer[index]);
    }
    
    return result;
  }

  /**
   * 获取最早的N个元素
   */
  getFirst(count: number): T[] {
    const actualCount = Math.min(count, this.size);
    const result: T[] = [];
    
    for (let i = 0; i < actualCount; i++) {
      const index = (this.head + i) % this.capacity;
      result.push(this.buffer[index]);
    }
    
    return result;
  }

  /**
   * 获取指定范围的元素
   */
  getRange(start: number, end: number): T[] {
    const actualStart = Math.max(0, Math.min(start, this.size - 1));
    const actualEnd = Math.max(actualStart, Math.min(end, this.size - 1));
    const result: T[] = [];
    
    for (let i = actualStart; i <= actualEnd; i++) {
      const index = (this.head + i) % this.capacity;
      result.push(this.buffer[index]);
    }
    
    return result;
  }

  /**
   * 查看最新元素（不移除）
   */
  peek(): T | undefined {
    if (this.size === 0) return undefined;
    const index = (this.tail - 1 + this.capacity) % this.capacity;
    return this.buffer[index];
  }

  /**
   * 查看最早元素（不移除）
   */
  peekFirst(): T | undefined {
    if (this.size === 0) return undefined;
    return this.buffer[this.head];
  }

  /**
   * 移除并返回最早的元素
   */
  shift(): T | undefined {
    if (this.size === 0) return undefined;
    
    const item = this.buffer[this.head];
    this.head = (this.head + 1) % this.capacity;
    this.size--;
    
    return item;
  }

  /**
   * 清空缓冲区
   */
  clear(): void {
    this.head = 0;
    this.tail = 0;
    this.size = 0;
  }

  /**
   * 检查缓冲区是否已满
   */
  isFull(): boolean {
    return this.size === this.capacity;
  }

  /**
   * 检查缓冲区是否为空
   */
  isEmpty(): boolean {
    return this.size === 0;
  }

  /**
   * 获取当前大小
   */
  getSize(): number {
    return this.size;
  }

  /**
   * 获取容量
   */
  getCapacity(): number {
    return this.capacity;
  }

  /**
   * 获取使用率
   */
  getUsageRatio(): number {
    return this.size / this.capacity;
  }

  /**
   * 转换为数组（调试用）
   */
  toArray(): T[] {
    return this.getAll();
  }

  /**
   * 获取缓冲区统计信息
   */
  getStats(): {
    size: number;
    capacity: number;
    usageRatio: number;
    isFull: boolean;
    isEmpty: boolean;
  } {
    return {
      size: this.size,
      capacity: this.capacity,
      usageRatio: this.getUsageRatio(),
      isFull: this.isFull(),
      isEmpty: this.isEmpty()
    };
  }
}

/**
 * 音频专用循环缓冲区
 * 提供音频数据特定的操作方法
 */
export class AudioCircularBuffer extends CircularBuffer<Float32Array> {
  private totalSamples: number = 0;
  private sampleRate: number;

  constructor(capacity: number, sampleRate: number = 16000) {
    super(capacity);
    this.sampleRate = sampleRate;
  }

  /**
   * 添加音频数据并更新统计
   */
  push(audioData: Float32Array): void {
    super.push(audioData);
    this.totalSamples += audioData.length;
  }

  /**
   * 获取合并的音频数据
   */
  getCombinedAudio(): Float32Array {
    const chunks = this.getAll();
    if (chunks.length === 0) {
      return new Float32Array(0);
    }

    const totalLength = chunks.reduce((sum, chunk) => sum + chunk.length, 0);
    const combined = new Float32Array(totalLength);
    
    let offset = 0;
    for (const chunk of chunks) {
      combined.set(chunk, offset);
      offset += chunk.length;
    }
    
    return combined;
  }

  /**
   * 获取增量音频数据（从指定偏移量开始）
   */
  getIncrementalAudio(offset: number): Float32Array | null {
    const chunks = this.getAll();
    if (chunks.length === 0) {
      return null;
    }

    // 计算总样本数
    const currentTotalSamples = chunks.reduce((sum, chunk) => sum + chunk.length, 0);
    
    if (offset >= currentTotalSamples) {
      return null; // 没有新数据
    }
    
    const incrementalLength = currentTotalSamples - offset;
    if (incrementalLength <= 0) {
      return null;
    }
    
    // 合并所有音频块
    const combined = new Float32Array(currentTotalSamples);
    let combinedOffset = 0;
    
    for (const chunk of chunks) {
      combined.set(chunk, combinedOffset);
      combinedOffset += chunk.length;
    }
    
    // 返回从指定偏移量开始的增量数据
    return combined.slice(offset, currentTotalSamples);
  }

  /**
   * 获取指定时长的音频数据
   */
  getAudioByDuration(durationMs: number): Float32Array {
    const samplesNeeded = Math.floor((durationMs / 1000) * this.sampleRate);
    const chunks = this.getAll();
    
    if (chunks.length === 0) {
      return new Float32Array(0);
    }

    let totalSamples = 0;
    const selectedChunks: Float32Array[] = [];
    
    // 从最新的数据开始收集
    for (let i = chunks.length - 1; i >= 0 && totalSamples < samplesNeeded; i--) {
      selectedChunks.unshift(chunks[i]);
      totalSamples += chunks[i].length;
    }
    
    // 合并选中的音频块
    const combined = new Float32Array(Math.min(totalSamples, samplesNeeded));
    let offset = 0;
    
    for (const chunk of selectedChunks) {
      const copyLength = Math.min(chunk.length, combined.length - offset);
      combined.set(chunk.subarray(0, copyLength), offset);
      offset += copyLength;
      
      if (offset >= combined.length) break;
    }
    
    return combined;
  }

  /**
   * 获取音频统计信息
   */
  getAudioStats(): {
    totalDurationMs: number;
    totalSamples: number;
    averageChunkSize: number;
    sampleRate: number;
  } {
    const chunks = this.getAll();
    const currentTotalSamples = chunks.reduce((sum, chunk) => sum + chunk.length, 0);
    
    return {
      totalDurationMs: (currentTotalSamples / this.sampleRate) * 1000,
      totalSamples: currentTotalSamples,
      averageChunkSize: chunks.length > 0 ? currentTotalSamples / chunks.length : 0,
      sampleRate: this.sampleRate
    };
  }

  /**
   * 清空并重置统计
   */
  clear(): void {
    super.clear();
    this.totalSamples = 0;
  }
}
