import levenshtein from 'fast-levenshtein';
import crypto from 'crypto';

/**
 * 文本相似度检测器
 */
export class TextSimilarityDetector {
  constructor(config = {}) {
    this.editDistanceThreshold = config.editDistanceThreshold || 0.7;
    this.cosineSimilarityThreshold = config.cosineSimilarityThreshold || 0.6;
    this.minTextLength = config.minTextLength || 2;
    this.enableLogging = config.enableLogging || false;
  }

  /**
   * 计算两个文本的相似度
   */
  calculateSimilarity(text1, text2) {
    if (!text1 || !text2) {
      return { isDuplicate: false, reason: 'Empty text' };
    }

    const cleanText1 = this.cleanText(text1);
    const cleanText2 = this.cleanText(text2);

    // 处理短文本
    if (cleanText1.length < this.minTextLength || cleanText2.length < this.minTextLength) {
      return { isDuplicate: false, reason: 'Text too short' };
    }

    // 完全相同
    if (cleanText1 === cleanText2) {
      return { isDuplicate: true, reason: 'Exact match', editSimilarity: 1, cosineSimilarity: 1 };
    }

    // 计算编辑距离相似度
    const editDistance = levenshtein.get(cleanText1, cleanText2);
    const maxLength = Math.max(cleanText1.length, cleanText2.length);
    const editSimilarity = maxLength === 0 ? 1 : (1 - (editDistance / maxLength));

    // 计算余弦相似度
    const cosineSimilarity = this.calculateCosineSimilarity(cleanText1, cleanText2);

    // 动态调整阈值
    const adjustedEditThreshold = this.adjustThreshold(cleanText1, cleanText2, this.editDistanceThreshold);
    const adjustedCosineThreshold = this.adjustThreshold(cleanText1, cleanText2, this.cosineSimilarityThreshold);

    const isDuplicate = editSimilarity >= adjustedEditThreshold || cosineSimilarity >= adjustedCosineThreshold;

    const result = {
      editDistance,
      editSimilarity,
      cosineSimilarity,
      isDuplicate,
      adjustedEditThreshold,
      adjustedCosineThreshold,
      reason: isDuplicate ? 'Similarity match' : 'No match'
    };

    if (this.enableLogging) {
      console.log('Text similarity analysis:', result);
    }

    return result;
  }

  /**
   * 清理文本
   */
  cleanText(text) {
    return text
      .toLowerCase()
      .replace(/[^\u4e00-\u9fa5a-zA-Z0-9\s]/g, '')
      .replace(/\s+/g, ' ')
      .trim();
  }

  /**
   * 生成文本指纹
   */
  generateTextFingerprint(text) {
    const cleanText = this.cleanText(text);
    return crypto.createHash('sha256').update(cleanText).digest('hex').substring(0, 16);
  }

  /**
   * 计算余弦相似度（改进版）
   */
  calculateCosineSimilarity(text1, text2) {
    const words1 = this.tokenize(text1);
    const words2 = this.tokenize(text2);

    if (words1.length === 0 || words2.length === 0) {
      return 0;
    }

    // 创建词汇表
    const vocabulary = new Set([...words1, ...words2]);
    
    if (vocabulary.size === 0) {
      return 0;
    }

    // 创建词频向量
    const vector1 = Array.from(vocabulary).map(word => words1.filter(w => w === word).length);
    const vector2 = Array.from(vocabulary).map(word => words2.filter(w => w === word).length);

    // 计算余弦相似度
    const dotProduct = vector1.reduce((sum, a, i) => sum + a * vector2[i], 0);
    const magnitude1 = Math.sqrt(vector1.reduce((sum, a) => sum + a * a, 0));
    const magnitude2 = Math.sqrt(vector2.reduce((sum, a) => sum + a * a, 0));

    if (magnitude1 === 0 || magnitude2 === 0) {
      return 0;
    }

    return dotProduct / (magnitude1 * magnitude2);
  }

  /**
   * 分词
   */
  tokenize(text) {
    // 中文字符分割 + 英文单词分割
    const chineseChars = text.match(/[\u4e00-\u9fa5]/g) || [];
    const englishWords = text.match(/[a-zA-Z0-9]+/g) || [];
    return [...chineseChars, ...englishWords].filter(token => token.length > 0);
  }

  /**
   * 动态调整阈值
   */
  adjustThreshold(text1, text2, baseThreshold) {
    const avgLength = (text1.length + text2.length) / 2;
    
    // 短文本提高阈值，长文本降低阈值
    if (avgLength < 10) {
      return Math.min(baseThreshold + 0.1, 0.9);
    } else if (avgLength > 50) {
      return Math.max(baseThreshold - 0.1, 0.5);
    }
    
    return baseThreshold;
  }

  /**
   * 检查是否为增量文本（包含关系）
   */
  isIncrementalText(shortText, longText) {
    const cleanShort = this.cleanText(shortText);
    const cleanLong = this.cleanText(longText);
    
    if (cleanShort.length >= cleanLong.length) {
      return false;
    }
    
    // 检查短文本是否为长文本的子集
    return cleanLong.includes(cleanShort);
  }

  /**
   * 批量去重
   */
  batchDeduplicate(texts, threshold = 0.8) {
    const uniqueTexts = [];
    const duplicateGroups = [];

    for (const text of texts) {
      let foundGroup = false;
      
      for (const group of duplicateGroups) {
        const similarity = this.calculateSimilarity(text, group[0]);
        if (similarity.isDuplicate) {
          group.push(text);
          foundGroup = true;
          break;
        }
      }
      
      if (!foundGroup) {
        uniqueTexts.push(text);
        duplicateGroups.push([text]);
      }
    }

    return {
      uniqueTexts,
      duplicateGroups: duplicateGroups.filter(group => group.length > 1),
      totalDuplicates: texts.length - uniqueTexts.length
    };
  }
}

export default TextSimilarityDetector;