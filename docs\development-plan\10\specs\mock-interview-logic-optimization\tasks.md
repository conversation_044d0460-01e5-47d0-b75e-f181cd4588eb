# Implementation Plan

## 状态机核心重构

- [x] 1. 更新状态机状态定义以匹配设计文档



  - 修改 `frontend/src/machines/mockInterviewMachine.ts` 中的状态定义
  - 将状态从当前的 `idle, initializing, requestingMicPermission, connecting, ready, active` 更新为设计文档中的 `INITIALIZING, WAITING_FOR_AI_QUESTION, LISTENING_FOR_ANSWER, PROCESSING_ANSWER, COMPLETED, ERROR`
  - 更新状态转换逻辑以符合设计文档中的状态机图
  - _Requirements: 6.1, 6.2, 6.3, 6.4, 6.5_

- [x] 2. 实现正确的面试流程状态转换



  - 修改状态机以确保 AI 先提问，然后用户回答的流程
  - 实现 `WAITING_FOR_AI_QUESTION -> LISTENING_FOR_ANSWER -> PROCESSING_ANSWER -> WAITING_FOR_AI_QUESTION` 循环
  - 移除页面加载时立即启动 ASR 的逻辑
  - _Requirements: 2.1, 2.2, 2.3, 2.4, 2.5_

## ASR 生命周期管理系统





- [x] 3. 创建 ASR 生命周期管理器
  - ✅ 创建了 `frontend/src/services/ASRLifecycleManager.ts` 文件
  - ✅ 创建了 `frontend/src/services/AudioRecordingManager.ts` 音频录制管理器
  - ✅ 创建了 `frontend/src/utils/EventEmitter.ts` 事件发射器工具类
  - ✅ 实现了 `ASRLifecycleManager` 类，包含完整的生命周期管理方法
  - ✅ 实现了静音检测、转录更新和错误处理的事件监听器
  - ✅ 添加了 ASR 服务失败时的备用提供商切换逻辑和指数退避重试
  - _Requirements: 4.1, 4.2, 4.3, 4.4, 4.6_

- [x] 4. 集成 ASR 管理器到状态机
  - ✅ 修改了 `mockInterviewMachine.ts` 以使用真实的 ASR 生命周期管理器
  - ✅ 替换了占位符 `audioRecordingService` 为实际的 `asrLifecycleService`
  - ✅ 创建了 `useMockInterviewStateMachine.ts` Hook 集成所有管理器
  - ✅ 实现了 AI 问题发送后自动启动 ASR 的逻辑
  - ✅ 实现了基于静音检测的自动 ASR 停止和状态同步
  - ✅ 集成了 WebSocket 连接管理器和错误恢复机制
  - _Requirements: 4.1, 4.2, 4.3, 4.4_

## 消息类型系统重构

- [x] 5. 验证和完善消息类型隔离
  - ✅ 检查并完善了 `backend/websocket/handlers/messageHandler.ts` 中的消息路由逻辑
  - ✅ 确保模拟面试和正式面试消息类型完全隔离，添加了严格的模式验证
  - ✅ 验证了 `mock_interview_question` 和 `mock_interview_answer` 消息类型的正确处理
  - ✅ 添加了消息类型验证函数，自动拒绝不匹配当前模式的消息
  - ✅ 实现了完整的消息类型分类：通用、正式面试专用、模拟面试专用
  - _Requirements: 5.1, 5.2, 5.3, 5.4, 5.5_

- [x] 6. 增强消息结构以支持个性化问题生成
  - ✅ 已实现 `MockInterviewQuestionMessage` 接口，包含 `questionType`, `difficulty`, `expectedDuration` 字段
  - ✅ 已实现 `MockInterviewAnswerMessage` 接口，包含 `duration`, `confidence` 字段
  - ✅ 后端消息生成逻辑已包含这些字段
  - _Requirements: 3.1, 3.2, 3.3, 3.4_

## 个性化问题生成系统

- [x] 7. 实现个性化问题生成逻辑
  - ✅ `MockInterviewService` 已实现基于公司名称和岗位名称的第一个问题生成
  - ✅ 已实现基于用户回答内容的后续问题动态生成（使用LLM）
  - ✅ 已实现问题去重逻辑，确保不重复已问的问题
  - _Requirements: 3.1, 3.2, 3.3, 3.4_

- [x] 8. 创建问题模板库和备用机制
  - ✅ `MockInterviewService` 中已有完整的 `questionTemplates` 数组
  - ✅ 已实现按问题类型（behavioral, technical, situational）分类的模板
  - ✅ 已实现问题生成失败时的备用问题选择逻辑
  - ✅ 已实现问题质量验证和LLM降级机制
  - _Requirements: 3.5, 9.2_

## 错误处理和恢复机制

- [x] 9. 实现全面的错误处理系统
  - ✅ 在状态机中添加了 `ERROR` 状态和错误恢复转换
  - ✅ 实现了 ASR 服务失败的自动恢复机制，包括指数退避重试和备用提供商切换
  - ✅ 实现了 AI 问题生成失败的备用处理，包括网络错误重试和模板问题降级
  - ✅ 实现了 WebSocket 连接断开的自动重连和状态恢复，包括心跳检测和消息队列
  - ✅ 创建了友好的错误提示界面组件，支持错误分类、重试操作和状态显示
  - _Requirements: 9.1, 9.2, 9.3, 9.4, 9.5_

- [x] 10. 创建错误日志和监控系统
  - ✅ 创建了 `backend/services/InterviewErrorLogger.ts` 服务，支持错误分类、日志记录和统计
  - ✅ 实现了前端 `ErrorRecoveryManager.ts` 错误恢复策略管理器
  - ✅ 创建了 `ErrorMonitoringDashboard.tsx` 监控仪表板组件
  - ✅ 集成了错误日志记录到 MockInterviewService 中
  - ✅ 实现了自动错误恢复策略选择和执行
  - _Requirements: 9.5, 9.6_

## 性能优化和用户体验

- [x] 11. 优化 AI 问题生成性能



  - 在 `MockInterviewService` 中实现问题生成缓存机制
  - 优化 LLM 调用以确保 3 秒内完成问题生成
  - 实现问题预生成策略以减少等待时间
  - 添加问题生成超时处理和备用机制



  - _Requirements: 8.1, 8.2, 8.3_

- [x] 12. 优化 WebSocket 消息传输性能


  - 检查和优化 WebSocket 消息大小和频率
  - 实现消息压缩和批处理机制
  - 添加网络延迟监控和自适应调整


  - 确保消息传输延迟低于 500ms
  - _Requirements: 8.4, 8.5_

## 系统兼容性和隔离性验证





- [ ] 13. 验证正式面试功能完全不受影响
  - 运行正式面试功能的完整测试流程
  - 验证 AI 建议功能在正式面试中正常工作
  - 确保模拟面试和正式面试的状态完全隔离
  - 检查 WebSocket 消息路由的正确性
  - _Requirements: 7.1, 7.2, 7.3, 7.4, 7.5_



- [ ] 14. 实现模式检测和消息路由验证
  - 在 `messageHandler.ts` 中添加面试模式检测逻辑
  - 实现基于模式的消息路由和验证
  - 添加不匹配模式消息的拒绝和日志记录
  - 确保一个模式的错误不影响另一个模式


  - _Requirements: 7.4, 7.5_

## 测试和质量保证

- [ ] 15. 创建状态机单元测试
  - 创建 `frontend/src/machines/__tests__/mockInterviewMachine.test.ts`
  - 测试所有状态转换的正确性和边界条件
  - 测试错误状态和恢复机制
  - 验证状态机与设计文档的一致性
  - _Requirements: 10.1, 10.4_

- [ ] 16. 创建 ASR 生命周期管理器测试
  - 创建 `frontend/src/services/__tests__/ASRLifecycleManager.test.ts`
  - 模拟各种 ASR 场景和错误情况
  - 测试静音检测和自动停止功能
  - 验证备用 ASR 提供商切换逻辑
  - _Requirements: 10.1, 10.4_

- [ ] 17. 创建端到端集成测试
  - 创建完整面试流程的自动化测试
  - 测试 AI 提问 -> ASR 启动 -> 用户回答 -> AI 分析 -> 生成新问题的完整循环
  - 验证个性化问题生成的正确性
  - 测试各种错误场景下的系统恢复能力
  - _Requirements: 10.2, 10.3, 10.4_

## 最终验证和优化

- [ ] 18. 性能基准测试和优化
  - 验证 AI 问题生成时间 < 3 秒
  - 验证 ASR 启动时间 < 1 秒
  - 验证用户回答处理时间 < 2 秒
  - 验证 WebSocket 消息延迟 < 500ms
  - 验证系统稳定运行时间 > 30 分钟
  - _Requirements: 8.1, 8.2, 8.3, 8.4, 8.5_

- [ ] 19. 用户体验验证和边界情况测试
  - 测试页面初始加载后 AI 主动提问的流程
  - 测试长时间面试会话的稳定性
  - 测试网络断开重连后的状态恢复
  - 验证所有错误情况下的友好提示
  - _Requirements: 2.6, 8.6, 9.6, 10.5_

- [ ] 20. 代码质量和文档完善
  - 添加关键函数和类的 TypeScript 类型注释
  - 完善错误处理的代码注释和文档
  - 优化代码结构和命名规范
  - 确保所有新增代码符合项目编码标准
  - _Requirements: 10.6_