好的，我们来完成开发计划中的屏幕共享相关功能。

**核心目标：**

1. **显示屏幕共享状态**：在 UI 中明确告知用户当前的屏幕共享状态（未开始、请求中、已共享、已拒绝/失败）。
2. **暂存状态和媒体流**：将屏幕共享的状态以及用户选择的媒体流（`MediaStream`）保存到组件的 `useState` 或者全局的 Zustand store 中。
3. **启用“开始面试”按钮**：当所有条件（岗位输入、麦克风授权、屏幕共享就绪）都满足时，“开始面试”按钮才变为可用。

我们将主要修改 `frontend/src/components/interview/InterviewConfigForm.tsx` 文件，并参考您提供的 `formal.html` 中的相关逻辑和 `AI_Interview_Page_Task.md` 的描述。

## 操作清单 (中文 + English)

------

### 1. 更新 Zustand Store (interviewStore.ts) 添加屏幕共享状态

(Update Zustand Store (interviewStore.ts) to include screen sharing state)

首先，我们需要在 `interviewStore` 中添加管理屏幕共享状态的字段和方法。

- **中文**: 打开 `frontend/src/stores/interviewStore.ts` 文件。

- **English**: Open the `frontend/src/stores/interviewStore.ts` file.

- 操作 (Action):

  修改 InterviewConfig 接口和 defaultConfig，并添加相应的 setter 方法。

  TypeScript

  ```
  // frontend/src/stores/interviewStore.ts
  import { create } from 'zustand';
  
  export type ScreenShareStatus = 'idle' | 'pending' | 'sharing' | 'denied'; // 新增屏幕共享状态类型
  
  export interface InterviewConfig {
    selectedPositionId: string | null;
    interviewLanguage: 'chinese' | 'english';
    answerStyle: 'keywords_conversational' | 'conversational';
    secondaryScreen: boolean; // 这个字段可以用于控制是否启用副屏特定UI
    audioCollection: boolean; // 这个字段现在可以代表“系统音频”是否已随屏幕共享获取
    screenShareStatus: ScreenShareStatus; // 新增：屏幕共享状态
    sharedStream: MediaStream | null; // 新增：存储共享的媒体流
  }
  
  interface InterviewState {
    config: InterviewConfig;
    setSelectedPosition: (positionId: string | null) => void;
    setInterviewLanguage: (language: 'chinese' | 'english') => void;
    setAnswerStyle: (style: 'keywords_conversational' | 'conversational') => void;
    setSecondaryScreen: (enabled: boolean) => void;
    setAudioCollection: (enabled: boolean) => void; // 这个方法可以保留，或者其逻辑合并到屏幕共享处理中
    setScreenShareStatus: (status: ScreenShareStatus) => void; // 新增
    setSharedStream: (stream: MediaStream | null) => void; // 新增
    resetConfig: () => void;
  }
  
  const defaultConfig: InterviewConfig = {
    selectedPositionId: null,
    interviewLanguage: 'chinese',
    answerStyle: 'keywords_conversational',
    secondaryScreen: false,
    audioCollection: false, // 初始为false
    screenShareStatus: 'idle', // 初始为idle
    sharedStream: null, // 初始为null
  };
  
  const useInterviewStore = create<InterviewState>((set, get) => ({
    config: defaultConfig,
    setSelectedPosition: (positionId) =>
      set((state) => ({
        config: { ...state.config, selectedPositionId: positionId }
      })),
    setInterviewLanguage: (language) =>
      set((state) => ({
        config: { ...state.config, interviewLanguage: language }
      })),
    setAnswerStyle: (style) =>
      set((state) => ({
        config: { ...state.config, answerStyle: style }
      })),
    setSecondaryScreen: (enabled) =>
      set((state) => ({
        config: { ...state.config, secondaryScreen: enabled }
      })),
    setAudioCollection: (enabled) => // 如果audioCollection代表屏幕共享的音频，则此方法可能被setSharedStream替代或联动
      set((state) => ({
        config: { ...state.config, audioCollection: enabled }
      })),
    setScreenShareStatus: (status) => // 新增
      set((state) => ({
        config: { ...state.config, screenShareStatus: status }
      })),
    setSharedStream: (stream) => { // 新增，并包含清理逻辑
      const oldStream = get().config.sharedStream;
      if (oldStream && oldStream !== stream) { // 如果有旧的流且不是同一个流
        oldStream.getTracks().forEach(track => track.stop());
        console.log('Old shared stream stopped in store setter.');
      }
      set((state) => ({
        config: {
          ...state.config,
          sharedStream: stream,
          // 当成功获取到流时，可以认为音频已采集（如果流包含音频）
          // 如果流为null（例如用户停止共享），则 audioCollection 应为 false
          audioCollection: !!(stream && stream.getAudioTracks().length > 0)
        }
      }));
    },
    resetConfig: () => {
      const currentStream = get().config.sharedStream;
      if (currentStream) {
        currentStream.getTracks().forEach(track => track.stop());
        console.log('Shared stream stopped on resetConfig.');
      }
      set({ config: { ...defaultConfig, sharedStream: null } });
    }
  }));
  
  export default useInterviewStore;
  ```

  **中文解释**：

  - 在 `InterviewConfig` 接口中增加了 `screenShareStatus` (类型为 `ScreenShareStatus`) 和 `sharedStream` (类型为 `MediaStream | null`)。
  - 在 `defaultConfig` 中为这两个新字段设置了初始值。
  - 添加了 `setScreenShareStatus` 和 `setSharedStream` 两个新的 action。
  - `setSharedStream` 在设置新的流之前，会先检查并停止旧的流（如果存在且不同）。同时，它会根据新的流是否包含音轨来更新 `audioCollection` 状态。
  - `resetConfig` 也会停止当前的 `sharedStream`。
  - `audioCollection` 字段现在可以更直接地反映共享流中是否包含音频。

------

### 2. 更新 `InterviewConfigForm.tsx` 实现屏幕共享逻辑

(Update `InterviewConfigForm.tsx` to implement screen sharing logic)

现在我们将修改表单组件来处理屏幕共享的请求、状态显示和媒体流存储。

- **中文**: 打开 `frontend/src/components/interview/InterviewConfigForm.tsx` 文件。

- **English**: Open the `frontend/src/components/interview/InterviewConfigForm.tsx` file.

- 操作 (Action):

  进行以下修改：

  TypeScript

  ```
  // frontend/src/components/interview/InterviewConfigForm.tsx
  import React, { useState, useEffect, useCallback } from 'react'; // 添加 useCallback
  import { ChevronDown, Upload, FileText, Monitor, CheckCircle, XCircle, AlertTriangle, Power, PowerOff } from 'lucide-react'; // 添加 Power, PowerOff 图标
  import { useNavigate } from 'react-router-dom';
  import useResumeStore from '../../stores/resumeStore';
  import useInterviewStore, { ScreenShareStatus } from '../../stores/interviewStore'; // 导入 ScreenShareStatus
  import useAuthStore from '../../stores/authStore';
  import { targetPositionService, type TargetPosition } from '../../lib/api/apiService';
  import { checkMicrophonePermission, type AudioPermissionResult } from '../../utils/audioPermissions'; // 假设 requestScreenShare 也在此文件
  import { useToast } from '../../hooks/useToast';
  
  const InterviewConfigForm: React.FC = () => {
    const navigate = useNavigate();
    const { showSuccess, showError } = useToast();
    const { uploadedResume } = useResumeStore();
    const { isAuthenticated } = useAuthStore();
    const {
      config,
      setSelectedPosition,
      setInterviewLanguage,
      setAnswerStyle,
      setSecondaryScreen,
      // setAudioCollection, // 这个可能不再直接由用户切换，而是由屏幕共享状态决定
      setScreenShareStatus,
      setSharedStream,
    } = useInterviewStore();
  
    const [positions, setPositions] = useState<TargetPosition[]>([]);
    const [loadingPositions, setLoadingPositions] = useState(false);
    const [isStartingInterview, setIsStartingInterview] = useState(false);
    const [micPermissionResult, setMicPermissionResult] = useState<AudioPermissionResult | null>(null);
  
    // 加载用户岗位列表
    useEffect(() => {
      const loadPositions = async () => {
        if (!isAuthenticated) {
          console.log('用户未登录，跳过加载岗位列表');
          return;
        }
        try {
          setLoadingPositions(true);
          const userPositions = await targetPositionService.getTargetPositions();
          setPositions(userPositions);
        } catch (error) {
          console.error('Failed to load positions:', error);
          if (error instanceof Error && error.message.includes('Unauthorized')) {
            showError('请先登录后再访问岗位列表');
          } else {
            showError('加载岗位列表失败');
          }
        } finally {
          setLoadingPositions(false);
        }
      };
      loadPositions();
    }, [isAuthenticated, showError]);
  
    // 检查麦克风权限 (可以在需要时调用，或者在用户点击“开始面试”前统一检查)
    const verifyMicrophone = useCallback(async () => {
      const permission = await checkMicrophonePermission();
      setMicPermissionResult(permission);
      if (!permission.granted) {
        showError(permission.error || '麦克风权限未授予');
      } else {
        showSuccess('麦克风已授权');
      }
      return permission.granted;
    }, [showError, showSuccess]);
  
  
    // 处理屏幕共享请求
    const handleRequestScreenShare = useCallback(async () => {
      if (config.screenShareStatus === 'sharing') { // 如果已在共享，则停止
        if (config.sharedStream) {
          config.sharedStream.getTracks().forEach(track => track.stop());
          console.log('Screen share stopped by user action.');
        }
        setSharedStream(null);
        setScreenShareStatus('idle');
        // setAudioCollection(false); // Zustand store 的 setSharedStream 会自动处理
        showInfo('屏幕共享已停止');
        return;
      }
  
      setScreenShareStatus('pending');
      try {
        const stream = await navigator.mediaDevices.getDisplayMedia({
          video: true, // 必须请求视频以共享屏幕
          audio: true  // 同时请求系统音频
        });
  
        // 监听用户手动停止共享的事件 (例如通过浏览器UI)
        if (stream.getVideoTracks()[0]) {
          stream.getVideoTracks()[0].onended = () => {
            console.log('Screen share ended by browser UI.');
            setSharedStream(null); // 清理store中的流
            setScreenShareStatus('idle');
            // setAudioCollection(false); // store 会处理
            showInfo('屏幕共享已结束');
          };
        }
  
        setSharedStream(stream); // 保存流到Zustand
        setScreenShareStatus('sharing');
        // setAudioCollection(stream.getAudioTracks().length > 0); // store 会处理
        showSuccess('屏幕和系统音频已开始共享');
      } catch (error) {
        console.error('获取屏幕共享失败:', error);
        setSharedStream(null); // 确保失败时清除流
        setScreenShareStatus('denied');
        // setAudioCollection(false); // store 会处理
        if (error instanceof Error && error.name === 'NotAllowedError') {
          showError('您已取消或拒绝屏幕共享权限');
        } else {
          showError('获取屏幕共享失败，请重试');
        }
      }
    }, [config.screenShareStatus, config.sharedStream, setSharedStream, setScreenShareStatus, showSuccess, showError, showInfo]);
  
  
    const allPrerequisitesMet =
      isAuthenticated &&
      uploadedResume &&
      config.selectedPositionId &&
      micPermissionResult?.granted && // 麦克风已授权
      config.screenShareStatus === 'sharing' && // 屏幕已共享
      config.audioCollection; // 并且共享流中包含音频
  
    const handleStartInterview = async () => {
      if (!uploadedResume) {
        showError('请先上传简历');
        return;
      }
      if (!config.selectedPositionId) {
        showError('请选择面试岗位');
        return;
      }
  
      // 再次确认麦克风权限
      const micOK = await verifyMicrophone();
      if (!micOK) {
        showError('麦克风权限是必需的');
        return;
      }
  
      if (config.screenShareStatus !== 'sharing' || !config.audioCollection) {
        showError('请先成功共享屏幕和系统音频');
        return;
      }
  
      setIsStartingInterview(true);
      showSuccess('所有准备就绪，即将开始面试...');
      console.log('Starting interview with config:', config);
      // 实际的跳转和面试开始逻辑
      // navigate('/interview/session'); // 跳转到面试会话页面
      setTimeout(() => {
        alert(`面试开始！岗位: ${config.selectedPositionId}, 语言: ${config.interviewLanguage}`);
        setIsStartingInterview(false);
      }, 1500);
    };
  
    // 清理函数：组件卸载时停止屏幕共享
    useEffect(() => {
      return () => {
        if (config.sharedStream) {
          config.sharedStream.getTracks().forEach(track => track.stop());
          console.log('InterviewConfigForm unmounted, shared stream stopped.');
          // 在卸载时不重置store中的流，除非是导航到完全无关的页面
          // useInterviewStore.getState().resetConfig(); // 或者只清除流和状态
        }
      };
    }, [config.sharedStream]);
  
  
    const getScreenShareButtonTextAndIcon = () => {
      switch (config.screenShareStatus) {
        case 'sharing':
          return { text: '停止共享系统音频', icon: <PowerOff className="w-5 h-5 mr-2 text-red-500" />, color: 'red' };
        case 'pending':
          return { text: '正在请求共享...', icon: <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-orange-500 mr-2"></div>, color: 'orange' };
        case 'denied':
          return { text: '共享被拒绝, 点击重试', icon: <AlertTriangle className="w-5 h-5 mr-2 text-red-500" />, color: 'red' };
        case 'idle':
        default:
          return { text: '共享屏幕和系统音频', icon: <Monitor className="w-5 h-5 mr-2" />, color: 'orange' };
      }
    };
    const screenShareButtonInfo = getScreenShareButtonTextAndIcon();
  
    return (
      <div className="max-w-[520px] w-full bg-white px-6 py-5 rounded-xl flex flex-col shadow-sm overflow-hidden">
        {/* ... (简历上传, 岗位选择, 面试语言, 答案风格, 面试副屏部分代码保持不变) ... */}
        {/* 简历上传状态 */}
        <h2 className="text-xl font-semibold mb-4 text-gray-800">个人简历</h2>
        <div className="mb-4">
          {uploadedResume ? (
            <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg bg-gray-50">
              <div className="flex items-center gap-3">
                <FileText className="w-5 h-5 text-gray-600" />
                <span className="text-sm text-gray-800 truncate max-w-[200px] font-medium">
                  {uploadedResume.fileName}
                </span>
              </div>
              <button
                onClick={() => navigate('/resume-upload')}
                className="text-blue-600 text-sm hover:text-blue-700 transition-colors font-medium"
              >
                更换
              </button>
            </div>
          ) : (
            <button
              onClick={() => navigate('/resume-upload')}
              className="w-full bg-orange-500 text-white py-3 rounded-lg hover:bg-orange-600 transition-colors text-sm font-medium flex items-center justify-center gap-2"
            >
              <Upload className="w-5 h-5" />
              上传简历
            </button>
          )}
        </div>
  
        {/* 岗位选择 */}
        <div className="mb-6">
          <label className="block text-sm font-semibold text-gray-700 mb-2">岗位选择</label>
          <div className="relative">
            <select
              value={config.selectedPositionId || ''}
              onChange={(e) => setSelectedPosition(e.target.value || null)}
              className="w-full px-3 py-2.5 bg-white border border-gray-200 rounded-lg text-sm appearance-none focus:outline-none focus:border-blue-500 focus:ring-2 focus:ring-blue-100 transition-all"
              disabled={loadingPositions}
            >
              <option value="">请选择</option>
              {positions.map((position) => (
                <option key={position.id} value={position.id} title={`<span class="math-inline">\{position\.positionName\}</span>{position.companyName ? ` - ${position.companyName}` : ''}`}>
                  {position.positionName}
                  {position.companyName && ` - ${position.companyName}`}
                </option>
              ))}
            </select>
            <ChevronDown className="absolute right-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400 pointer-events-none" />
          </div>
        </div>
        {/* 面试语言 */}
        <div className="mb-4">
          <label className="block text-sm font-semibold text-gray-700 mb-2">面试语言</label>
          <div className="relative">
            <select
              value={config.interviewLanguage}
              onChange={(e) => setInterviewLanguage(e.target.value as 'chinese' | 'english')}
              className="w-full px-3 py-2.5 bg-white border border-gray-200 rounded-lg text-sm appearance-none focus:outline-none focus:border-blue-500 focus:ring-2 focus:ring-blue-100 transition-all"
            >
              <option value="chinese">中文</option>
              <option value="english">英文</option>
            </select>
            <ChevronDown className="absolute right-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400 pointer-events-none" />
          </div>
        </div>
  
        {/* 答案风格 */}
        <div className="mb-4">
          <label className="block text-sm font-semibold text-gray-700 mb-2">答案风格</label>
          <div className="relative">
            <select
              value={config.answerStyle}
              onChange={(e) => setAnswerStyle(e.target.value as 'keywords_conversational' | 'conversational')}
              className="w-full px-3 py-2.5 bg-white border border-gray-200 rounded-lg text-sm appearance-none focus:outline-none focus:border-blue-500 focus:ring-2 focus:ring-blue-100 transition-all"
            >
              <option value="keywords_conversational">关键词提示 + 口语化答案</option>
              <option value="conversational">口语化答案</option>
            </select>
            <ChevronDown className="absolute right-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400 pointer-events-none" />
          </div>
        </div>
  
        {/* 面试副屏 */}
        <div className="mb-4 py-2 flex items-center">
          <div className="flex-1">
            <div className="mb-1">
              <label className="text-sm font-semibold text-gray-700">面试副屏</label>
            </div>
            <p className="text-xs text-gray-500">面试副屏专为需要屏幕共享的面试场景设计</p>
          </div>
          <label className="relative inline-flex items-center cursor-pointer ml-4">
            <input
              type="checkbox"
              checked={config.secondaryScreen}
              onChange={(e) => setSecondaryScreen(e.target.checked)}
              className="sr-only peer"
            />
            <div className="w-9 h-5 bg-gray-200 rounded-full peer peer-checked:after:translate-x-full after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-blue-500"></div>
          </label>
        </div>
  
        {/* 屏幕共享区域 (修改部分) */}
        <div className="mb-4">
          <div className="flex items-center justify-between mb-1">
              <h3 className="text-sm font-semibold text-gray-700">屏幕与音频共享</h3>
              {config.screenShareStatus === 'sharing' && config.audioCollection && (
                  <div className="flex items-center text-xs text-green-600">
                      <CheckCircle size={14} className="mr-1" />
                      已共享并采集到音频
                  </div>
              )}
              {config.screenShareStatus === 'sharing' && !config.audioCollection && (
                  <div className="flex items-center text-xs text-yellow-600">
                      <AlertTriangle size={14} className="mr-1" />
                      已共享但未采集到音频
                  </div>
              )}
          </div>
          <p className="text-xs text-gray-500 mb-3 leading-relaxed">
            面试君通过获取您选择共享的屏幕内容及系统音频，来辅助您进行面试。
          </p>
          <button
            onClick={handleRequestScreenShare}
            disabled={config.screenShareStatus === 'pending'}
            className={`flex items-center justify-center w-full py-2.5 rounded-lg text-sm font-medium transition-colors
              ${screenShareButtonInfo.color === 'orange' ? 'bg-orange-50 text-orange-600 border border-orange-200 hover:bg-orange-100' : ''}
              ${screenShareButtonInfo.color === 'red' ? 'bg-red-50 text-red-600 border border-red-200 hover:bg-red-100' : ''}
            `}
          >
            {screenShareButtonInfo.icon}
            {screenShareButtonInfo.text}
          </button>
           {config.screenShareStatus === 'denied' && (
              <p className="text-xs text-red-500 mt-1">请在浏览器设置中允许屏幕共享，或尝试选择其他窗口/屏幕。</p>
          )}
        </div>
  
        {/* 底部间距 */}
        <div className="flex-grow mt-2"></div>
  
        {/* 开始面试按钮 (修改disabled条件) */}
        <button
          onClick={handleStartInterview}
          disabled={isStartingInterview || !allPrerequisitesMet}
          className="w-full bg-gray-800 text-white py-3 rounded-lg text-base font-semibold hover:bg-gray-700 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed shadow-sm mt-2"
        >
          {isStartingInterview ? '正在启动...' : '开始面试'}
        </button>
      </div>
    );
  };
  
  export default InterviewConfigForm;
  ```

  **中文解释与关键修改**：

  - Zustand Store 使用

    ：

    - 从 `useInterviewStore` 中获取 `config` 对象以及 `setScreenShareStatus`, `setSharedStream` 方法。
    - `config.screenShareStatus` 和 `config.sharedStream` 现在是屏幕共享状态和媒体流的“单一数据源”。

  - `handleRequestScreenShare` 函数

    ：

    - 这是核心的屏幕共享处理函数。

    - 如果当前已在共享 (

      ```
      config.screenShareStatus === 'sharing'
      ```

      )，再次点击按钮会停止共享：

      - 调用 `config.sharedStream.getTracks().forEach(track => track.stop())` 来停止所有媒体轨道。
      - 调用 `setSharedStream(null)` 和 `setScreenShareStatus('idle')` 更新 Zustand store。

    - 如果未共享，则请求屏幕共享权限：

      - 调用 `navigator.mediaDevices.getDisplayMedia({ video: true, audio: true })`。**这里明确请求 `audio: true` 以尝试捕获系统音频。**
      - 成功后，将获取到的 `MediaStream` 通过 `setSharedStream(stream)` 保存到 Zustand store，并将状态设为 `sharing`。
      - 添加了一个监听器 `stream.getVideoTracks()[0].onended`，当用户通过浏览器UI停止共享时，会自动更新状态。
      - 失败后，将状态设为 `denied`，并清空流。

  - 按钮状态与文本 (`getScreenShareButtonTextAndIcon`)

    ：

    - 根据 `config.screenShareStatus` 动态显示按钮文本（例如“共享屏幕和系统音频”、“停止共享系统音频”、“正在请求共享...”等）和图标。
    - 按钮颜色也会根据状态变化（橙色系表示可操作或警告，红色系表示停止或错误）。

  - “开始面试”按钮的可用条件 (`allPrerequisitesMet`)

    ：

    - 现在检查 `isAuthenticated` (用户已登录)。
    - `uploadedResume` (简历已上传)。
    - `config.selectedPositionId` (岗位已选择)。
    - `micPermissionResult?.granted` (麦克风已授权)。
    - `config.screenShareStatus === 'sharing'` (屏幕已开始共享)。
    - `config.audioCollection` (共享的流中包含音频)。**这个状态现在由 `setSharedStream` 自动根据流的内容更新，表示系统音频是否被成功捕获。**

  - **`useEffect` 清理函数**：添加了一个 `useEffect` 的返回函数，用于在组件卸载时确保停止任何正在进行的屏幕共享流，防止资源泄露。

  - 移除了原 `config.audioCollection` 的直接切换逻辑，因为音频采集现在与屏幕共享流绑定。

------

### 4. 测试 (Testing)

- 中文

  :

  1. **启动开发服务器**: 确保前端 (`npm run dev`) 和后端 (`vercel dev` 或 `npm run dev --workspace=backend`) 都在运行。

  2. **登录系统**。

  3. 导航到包含 `InterviewConfigForm` 的页面 (例如 `/ai-interview` 或 `/interview-setup`，取决于您的路由)。

  4. 测试屏幕共享按钮

     :

     - 初始状态应该是“共享屏幕和系统音频”。

     - 点击按钮，浏览器应弹出屏幕/窗口选择器。

       - 选择一个包含音频播放的窗口/屏幕并共享

         ：

         - 按钮应变为“停止共享系统音频”。
         - 状态指示（如果有）应显示共享成功并采集到音频。
         - 检查 Zustand store (`config.screenShareStatus` 为 `sharing`, `config.sharedStream` 有值, `config.audioCollection` 为 `true`)。

       - 选择一个不播放音频的窗口/屏幕并共享

          (如果浏览器允许分别选择)：

         - `config.audioCollection` 可能为 `false`。

       - 取消选择或关闭选择器

         ：

         - 按钮应恢复或显示错误/拒绝状态。
         - Zustand store 状态应为 `idle` 或 `denied`。

     - 再次点击“停止共享系统音频”按钮，共享应停止，按钮文本恢复。

  5. **测试麦克风授权**: 确保麦克风授权逻辑仍然有效（通过 `verifyMicrophone`）。

  6. 测试“开始面试”按钮的可用性

     :

     - 只有当简历上传、岗位选择、麦克风授权、屏幕共享成功（且采集到音频）时，按钮才可用。
     - 缺少任一条件，按钮都应禁用。

  7. **测试用户手动停止共享**: 通过浏览器自带的停止共享按钮停止屏幕共享，检查UI状态是否更新为"idle"或相应提示。

- English

  :

  1. **Start development servers**: Ensure both frontend (`npm run dev`) and backend (`vercel dev` or `npm run dev --workspace=backend`) are running.

  2. **Log into the system**.

  3. Navigate to the page containing `InterviewConfigForm` (e.g., `/ai-interview` or `/interview-setup`, depending on your routes).

  4. Test the screen share button

     :

     - Initial state should be "Share Screen and System Audio".

     - Click the button; the browser should show the screen/window picker.

       - Select a window/screen that is playing audio and share it

         :

         - The button should change to "Stop Sharing System Audio".
         - Status indicators (if any) should show successful sharing and audio capture.
         - Check Zustand store (`config.screenShareStatus` is `sharing`, `config.sharedStream` has a value, `config.audioCollection` is `true`).

       - Select a window/screen not playing audio and share it

          (if the browser allows separate selection):

         - `config.audioCollection` might be `false`.

       - Cancel selection or close the picker

         :

         - The button should revert or show an error/denied state.
         - Zustand store status should be `idle` or `denied`.

     - Click the "Stop Sharing System Audio" button again; sharing should stop, and button text should revert.

  5. **Test microphone authorization**: Ensure the microphone authorization logic (via `verifyMicrophone`) still works.

  6. Test "Start Interview" button availability

     :

     - It should only be enabled when resume is uploaded, position is selected, microphone is authorized, and screen is successfully shared (with audio captured).
     - If any condition is missing, the button should be disabled.

  7. **Test user manually stopping screen share**: Use the browser's native stop sharing button and check if the UI status updates to "idle" or the appropriate message.

------

### 5. 提交代码 (Commit Code)

- **中文**: 测试通过后，提交您的代码到 GitHub。

- **English**: After testing passes, commit your code to GitHub.

- 操作 (Action)

  :

  Bash

  ```
  git add .
  git commit -m "Feat: Implement screen sharing status and media stream storage"
  git push origin main # 或您的主分支 (or your main branch)
  ```

------

这些步骤应该可以帮助您实现所需的功能。请注意，`navigator.mediaDevices.getDisplayMedia` 的 `audio: true` 选项行为可能因浏览器而异，有时用户需要明确选择共享系统音频的选项。

If you encounter any issues or have further questions during implementation, feel free to ask!