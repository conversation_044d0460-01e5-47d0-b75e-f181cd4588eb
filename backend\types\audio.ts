// 音频处理相关类型定义

export enum AudioFormat {
  WEBM = 'webm',
  OGG = 'ogg',
  MP4 = 'mp4',
  PCM = 'pcm',
  WAV = 'wav',
  MP3 = 'mp3',
  UNKNOWN = 'unknown'
}

export interface AudioBuffer {
  chunks: Buffer[];
  totalSize: number;
  lastChunkTime: number;
}

export interface PcmAudioBuffer extends AudioBuffer {
  isProcessing: boolean;
}

export interface AudioProcessingOptions {
  sampleRate: number;
  channels: number;
  bitDepth: number;
  format: AudioFormat;
}

export interface AudioConversionResult {
  buffer: Buffer;
  format: AudioFormat;
  sampleRate: number;
  channels: number;
  duration: number;
}

export interface FFmpegProcessInfo {
  process: any; // ChildProcessWithoutNullStreams
  stdin: any; // Writable
  onPcmData: (pcmChunk: Buffer) => void;
  onError: (error: Error) => void;
}

export interface AudioSegmentInfo {
  id: string;
  duration: number;
  confidence: number;
  segmentType: 'speech' | 'silence' | 'mixed';
  triggerReason: string;
  timestamp: number;
}

export interface VADInfo {
  isSpeech: boolean;
  energy: number;
  confidence: number;
  timestamp: number;
  isEndOfSpeech?: boolean;
  spectralFeatures?: {
    spectralCentroid: number;
    zeroCrossingRate: number;
    spectralRolloff: number;
  };
}

export interface AudioProcessorConfig {
  maxBufferSize: number;
  processingTimeout: number;
  ffmpegPath?: string;
  tempDir?: string;
}

export interface AudioQualityMetrics {
  snr: number; // Signal-to-noise ratio
  volume: number;
  clarity: number;
  stability: number;
}

export interface AudioProcessingError extends Error {
  code: string;
  audioFormat?: AudioFormat;
  bufferSize?: number;
  processingStep?: string;
}
