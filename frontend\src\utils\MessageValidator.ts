// frontend/src/utils/MessageValidator.ts
import { Message } from '../hooks/useInterviewSession';

export interface ValidationResult {
  isValid: boolean;
  issues: string[];
  warnings: string[];
}

export interface RepairResult {
  repairedMessages: Message[];
  repairActions: string[];
  originalCount: number;
  repairedCount: number;
}

/**
 * 消息验证器 - 验证和修复消息数组的完整性
 * 确保消息数组的一致性和正确性
 */
export class MessageValidator {
  constructor() {
    console.log('🔧 MessageValidator initialized');
  }

  /**
   * 验证消息数组的完整性
   * @param messages 消息数组
   * @returns 验证结果
   */
  validate(messages: Message[]): ValidationResult {
    const issues: string[] = [];
    const warnings: string[] = [];

    // 基本检查
    if (!Array.isArray(messages)) {
      issues.push('Messages is not an array');
      return { isValid: false, issues, warnings };
    }

    if (messages.length === 0) {
      warnings.push('Messages array is empty');
      return { isValid: true, issues, warnings };
    }

    // 检查消息结构
    for (let i = 0; i < messages.length; i++) {
      const msg = messages[i];
      const msgPrefix = `Message[${i}]`;

      if (!msg) {
        issues.push(`${msgPrefix}: Message is null or undefined`);
        continue;
      }

      if (!msg.id) {
        issues.push(`${msgPrefix}: Missing id`);
      }

      if (!msg.content && msg.content !== '') {
        issues.push(`${msgPrefix}: Missing content`);
      }

      if (!msg.type) {
        issues.push(`${msgPrefix}: Missing type`);
      } else if (!['interviewer', 'ai-suggestion'].includes(msg.type)) {
        issues.push(`${msgPrefix}: Invalid type "${msg.type}"`);
      }

      if (!msg.timestamp) {
        issues.push(`${msgPrefix}: Missing timestamp`);
      } else if (typeof msg.timestamp !== 'number') {
        issues.push(`${msgPrefix}: Invalid timestamp type`);
      }
    }

    // 检查时间戳顺序
    for (let i = 1; i < messages.length; i++) {
      if (messages[i].timestamp < messages[i-1].timestamp) {
        issues.push(`Timestamp order violation: Message[${i}] (${messages[i].timestamp}) < Message[${i-1}] (${messages[i-1].timestamp})`);
      }
    }

    // 检查重复ID
    const ids = messages.map(m => m.id).filter(id => id);
    const uniqueIds = new Set(ids);
    if (ids.length !== uniqueIds.size) {
      const duplicates = ids.filter((id, index) => ids.indexOf(id) !== index);
      issues.push(`Duplicate message IDs detected: ${[...new Set(duplicates)].join(', ')}`);
    }

    // 检查消息类型分布
    const transcriptionMessages = messages.filter(m => m.type === 'interviewer');
    const aiMessages = messages.filter(m => m.type === 'ai-suggestion');

    if (transcriptionMessages.length === 0 && messages.length > 0) {
      warnings.push('No transcription messages found');
    }

    // 检查时间戳合理性
    const now = Date.now();
    const oneHourAgo = now - 60 * 60 * 1000;
    const futureMessages = messages.filter(m => m.timestamp > now + 60000); // 1分钟容错
    const tooOldMessages = messages.filter(m => m.timestamp < oneHourAgo);

    if (futureMessages.length > 0) {
      warnings.push(`${futureMessages.length} messages have future timestamps`);
    }

    if (tooOldMessages.length > 0) {
      warnings.push(`${tooOldMessages.length} messages are older than 1 hour`);
    }

    // 检查内容长度
    const longMessages = messages.filter(m => m.content && m.content.length > 5000);
    if (longMessages.length > 0) {
      warnings.push(`${longMessages.length} messages have very long content (>5000 chars)`);
    }

    // 检查空内容
    const emptyMessages = messages.filter(m => !m.content || m.content.trim().length === 0);
    if (emptyMessages.length > 0) {
      warnings.push(`${emptyMessages.length} messages have empty content`);
    }

    console.log('🔍 MessageValidator: Validation completed:', {
      totalMessages: messages.length,
      issues: issues.length,
      warnings: warnings.length,
      transcriptionCount: transcriptionMessages.length,
      aiMessageCount: aiMessages.length
    });

    return {
      isValid: issues.length === 0,
      issues,
      warnings
    };
  }

  /**
   * 修复消息数组
   * @param messages 原始消息数组
   * @returns 修复结果
   */
  repair(messages: Message[]): RepairResult {
    const repairActions: string[] = [];
    let repairedMessages: Message[] = [];

    if (!Array.isArray(messages)) {
      repairActions.push('Converted non-array to empty array');
      repairedMessages = [];
    } else {
      repairedMessages = [...messages];
    }

    const originalCount = repairedMessages.length;

    // 修复1：移除无效消息
    const validMessages = repairedMessages.filter(msg => {
      if (!msg) {
        repairActions.push('Removed null/undefined message');
        return false;
      }
      if (!msg.id) {
        repairActions.push('Removed message without ID');
        return false;
      }
      if (!msg.type || !['interviewer', 'ai-suggestion'].includes(msg.type)) {
        repairActions.push(`Removed message with invalid type: ${msg.type}`);
        return false;
      }
      return true;
    });

    repairedMessages = validMessages;

    // 修复2：去重（保留最新的）
    const uniqueMessages = new Map<string, Message>();
    repairedMessages.forEach(msg => {
      const existing = uniqueMessages.get(msg.id);
      if (!existing || msg.timestamp > existing.timestamp) {
        uniqueMessages.set(msg.id, msg);
      }
    });

    const beforeDedup = repairedMessages.length;
    repairedMessages = Array.from(uniqueMessages.values());
    if (beforeDedup !== repairedMessages.length) {
      repairActions.push(`Removed ${beforeDedup - repairedMessages.length} duplicate messages`);
    }

    // 修复3：修复缺失的时间戳
    repairedMessages.forEach((msg, index) => {
      if (!msg.timestamp || typeof msg.timestamp !== 'number') {
        msg.timestamp = Date.now() - (repairedMessages.length - index) * 1000;
        repairActions.push(`Fixed missing timestamp for message ${msg.id}`);
      }
    });

    // 修复4：重新排序（按时间戳）
    const beforeSort = repairedMessages.map(m => m.id).join(',');
    repairedMessages.sort((a, b) => a.timestamp - b.timestamp);
    const afterSort = repairedMessages.map(m => m.id).join(',');
    
    if (beforeSort !== afterSort) {
      repairActions.push('Reordered messages by timestamp');
    }

    // 修复5：修复空内容
    repairedMessages.forEach(msg => {
      if (!msg.content) {
        msg.content = msg.type === 'interviewer' ? '[转录内容]' : '[AI建议]';
        repairActions.push(`Fixed empty content for message ${msg.id}`);
      }
    });

    const repairedCount = repairedMessages.length;

    console.log('🔧 MessageValidator: Repair completed:', {
      originalCount,
      repairedCount,
      repairActions: repairActions.length,
      actions: repairActions
    });

    return {
      repairedMessages,
      repairActions,
      originalCount,
      repairedCount
    };
  }

  /**
   * 快速验证（只检查关键问题）
   * @param messages 消息数组
   * @returns 是否通过快速验证
   */
  quickValidate(messages: Message[]): boolean {
    if (!Array.isArray(messages)) return false;
    if (messages.length === 0) return true;

    // 检查基本结构
    for (const msg of messages) {
      if (!msg || !msg.id || !msg.type || !msg.timestamp) {
        return false;
      }
    }

    // 检查ID唯一性
    const ids = messages.map(m => m.id);
    const uniqueIds = new Set(ids);
    if (ids.length !== uniqueIds.size) {
      return false;
    }

    return true;
  }

  /**
   * 获取消息统计信息
   * @param messages 消息数组
   * @returns 统计信息
   */
  getStatistics(messages: Message[]): {
    total: number;
    transcriptionCount: number;
    aiMessageCount: number;
    averageContentLength: number;
    timeSpan: number;
    oldestTimestamp: number;
    newestTimestamp: number;
  } {
    if (!Array.isArray(messages) || messages.length === 0) {
      return {
        total: 0,
        transcriptionCount: 0,
        aiMessageCount: 0,
        averageContentLength: 0,
        timeSpan: 0,
        oldestTimestamp: 0,
        newestTimestamp: 0
      };
    }

    const transcriptionCount = messages.filter(m => m.type === 'interviewer').length;
    const aiMessageCount = messages.filter(m => m.type === 'ai-suggestion').length;
    const totalContentLength = messages.reduce((sum, m) => sum + (m.content?.length || 0), 0);
    const averageContentLength = Math.round(totalContentLength / messages.length);

    const timestamps = messages.map(m => m.timestamp).filter(t => t);
    const oldestTimestamp = Math.min(...timestamps);
    const newestTimestamp = Math.max(...timestamps);
    const timeSpan = newestTimestamp - oldestTimestamp;

    return {
      total: messages.length,
      transcriptionCount,
      aiMessageCount,
      averageContentLength,
      timeSpan,
      oldestTimestamp,
      newestTimestamp
    };
  }
}
