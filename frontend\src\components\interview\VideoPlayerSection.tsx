import React from 'react';
import { Play } from 'lucide-react';

interface VideoPlayerSectionProps {
  mode?: 'live' | 'mock';
}

const VideoPlayerSection: React.FC<VideoPlayerSectionProps> = ({ mode = 'live' }) => {
  return (
    <div className="flex-none w-3/5 bg-gradient-to-br from-purple-400 via-purple-500 to-purple-600 flex items-center justify-center rounded-xl h-full mr-6">
      <div className="text-center text-white flex flex-col items-center justify-center">
        <h1 className="text-4xl font-bold mb-2">3分钟学会使用</h1>
        <h2 className="text-3xl font-bold mb-8">面试君</h2>

        <button className="w-20 h-20 bg-white bg-opacity-20 rounded-full flex items-center justify-center hover:bg-opacity-30 transition-all duration-300 backdrop-blur-sm mx-auto">
          <Play className="w-8 h-8 text-white ml-1" fill="currentColor" />
        </button>
      </div>
    </div>
  );
};

export default VideoPlayerSection;
