import { VADDetector } from './vadDetector';
import { DynamicSegmenter, AudioSegment } from './dynamicSegmenter';
import { AudioBufferManager } from './audioBufferManager';
import { VADResult, VADConfig } from '../types/vadTypes';

export interface EnhancedAudioConfig {
  vadConfig?: Partial<VADConfig>;
  segmentationConfig?: {
    minSegmentDuration: number;
    maxSegmentDuration: number;
    confidenceThreshold: number;
  };
  bufferConfig?: {
    maxBufferSize: number;
    sendInterval: number;
    qualityThreshold: number;
  };
}

export interface AudioProcessingResult {
  segment?: AudioSegment | null;
  vadResult: VADResult;
  shouldSend: boolean;
  audioData?: Float32Array;
  metadata: {
    processingTime: number;
    segmentCount: number;
    quality: number;
    continuity: number;
  };
}

/**
 * 🔥 增强音频处理器
 * 集成VAD检测、智能分段和缓冲管理的统一音频处理解决方案
 */
export class EnhancedAudioProcessor {
  private vadDetector: VADDetector;
  private dynamicSegmenter: DynamicSegmenter;
  private bufferManager: AudioBufferManager;
  private audioContext: AudioContext;
  
  private config: EnhancedAudioConfig;
  private isProcessing: boolean = false;
  private segmentCount: number = 0;
  private lastProcessingTime: number = 0;
  
  // 性能监控
  private performanceMetrics = {
    totalChunks: 0,
    processedSegments: 0,
    averageProcessingTime: 0,
    lastQuality: 0,
    lastContinuity: 0
  };

  constructor(audioContext: AudioContext, config: EnhancedAudioConfig = {}) {
    this.audioContext = audioContext;
    this.config = {
      vadConfig: {
        energyThreshold: 0.01,
        silenceThreshold: 0.003,
        minSpeechDuration: 300,
        minSilenceDuration: 800,
        endOfSpeechTimeout: 1500,
        maxSpeechDuration: 15000,
        qualityThreshold: 0.4,
        continuityThreshold: 0.3,
        ...config.vadConfig
      },
      segmentationConfig: {
        minSegmentDuration: 500,
        maxSegmentDuration: 15000,
        confidenceThreshold: 0.3,
        ...config.segmentationConfig
      },
      bufferConfig: {
        maxBufferSize: 20,
        sendInterval: 1000,
        qualityThreshold: 0.4,
        ...config.bufferConfig
      }
    };

    // 初始化组件
    this.vadDetector = new VADDetector(audioContext, this.config.vadConfig);
    this.bufferManager = new AudioBufferManager();
    this.dynamicSegmenter = new DynamicSegmenter(
      this.vadDetector,
      this.bufferManager,
      this.config.segmentationConfig
    );

    console.log('🔥 Enhanced Audio Processor initialized with config:', this.config);
  }

  /**
   * 🔥 主要音频处理方法
   * 统一处理音频数据，返回处理结果
   */
  processAudioChunk(audioData: Float32Array): AudioProcessingResult {
    const startTime = performance.now();
    this.performanceMetrics.totalChunks++;

    try {
      // 1. VAD检测
      const vadResult = this.vadDetector.detectVoiceActivity(audioData);
      
      // 2. 动态分段处理
      const segment = this.dynamicSegmenter.processAudioChunk(audioData);
      
      // 3. 获取语音段信息
      const speechInfo = this.vadDetector.getSpeechSegmentInfo();
      
      // 4. 决定是否应该发送音频数据
      const shouldSend = this.shouldSendAudio(vadResult, speechInfo, segment);
      
      // 5. 准备发送的音频数据
      let audioDataToSend: Float32Array | undefined;
      if (shouldSend && segment) {
        audioDataToSend = segment.audioData;
        this.performanceMetrics.processedSegments++;
        this.segmentCount++;
      }

      // 6. 更新性能指标
      const processingTime = performance.now() - startTime;
      this.updatePerformanceMetrics(processingTime, vadResult);

      // 7. 构建结果
      const result: AudioProcessingResult = {
        segment: segment || null,
        vadResult,
        shouldSend,
        audioData: audioDataToSend,
        metadata: {
          processingTime,
          segmentCount: this.segmentCount,
          quality: speechInfo.quality,
          continuity: speechInfo.continuity
        }
      };

      // 8. 日志输出（仅在重要事件时）
      if (segment || vadResult.isEndOfSpeech) {
        console.log('🎯 Audio processing result:', {
          hasSegment: !!segment,
          shouldSend,
          vadResult: {
            isSpeech: vadResult.isSpeech,
            isEndOfSpeech: vadResult.isEndOfSpeech,
            confidence: vadResult.confidence.toFixed(2),
            quality: vadResult.segmentQuality?.toFixed(2),
            continuity: vadResult.speechContinuity?.toFixed(2)
          },
          speechInfo: {
            isActive: speechInfo.isActive,
            duration: speechInfo.duration,
            shouldForceEnd: speechInfo.shouldForceEnd
          },
          performance: {
            processingTime: processingTime.toFixed(2) + 'ms',
            totalChunks: this.performanceMetrics.totalChunks,
            processedSegments: this.performanceMetrics.processedSegments
          }
        });
      }

      return result;

    } catch (error) {
      console.error('❌ Enhanced audio processing error:', error);
      
      // 返回错误结果
      return {
        vadResult: {
          isSpeech: false,
          energy: 0,
          confidence: 0,
          timestamp: Date.now(),
          spectralFeatures: {
            spectralCentroid: 0,
            zeroCrossingRate: 0,
            spectralRolloff: 0
          },
          isEndOfSpeech: false,
          speechContinuity: 0,
          segmentQuality: 0
        },
        shouldSend: false,
        metadata: {
          processingTime: performance.now() - startTime,
          segmentCount: this.segmentCount,
          quality: 0,
          continuity: 0
        }
      };
    }
  }

  /**
   * 🔥 智能发送决策
   */
  private shouldSendAudio(
    vadResult: VADResult, 
    speechInfo: any, 
    segment?: AudioSegment | null
  ): boolean {
    // 必须有有效的音频段
    if (!segment) {
      return false;
    }

    // 检查段的基本质量
    if (segment.confidence < this.config.segmentationConfig!.confidenceThreshold) {
      return false;
    }

    // 检查段的持续时间
    if (segment.duration < this.config.segmentationConfig!.minSegmentDuration) {
      return false;
    }

    // 优先发送语音段
    if (segment.segmentType === 'speech') {
      return true;
    }

    // 检查是否是语音结束
    if (vadResult.isEndOfSpeech && speechInfo.isActive) {
      console.log('🔚 Sending audio due to end of speech detection');
      return true;
    }

    // 检查是否强制结束（时长限制）
    if (speechInfo.shouldForceEnd) {
      console.log('⏰ Sending audio due to duration limit');
      return true;
    }

    // 检查混合段的质量
    if (segment.segmentType === 'mixed' && 
        segment.confidence > 0.5 && 
        vadResult.segmentQuality && vadResult.segmentQuality > 0.6) {
      return true;
    }

    return false;
  }

  /**
   * 🔥 更新性能指标
   */
  private updatePerformanceMetrics(processingTime: number, vadResult: VADResult): void {
    const metrics = this.performanceMetrics;
    
    // 更新平均处理时间
    metrics.averageProcessingTime = (
      (metrics.averageProcessingTime * (metrics.totalChunks - 1) + processingTime) / 
      metrics.totalChunks
    );
    
    // 更新质量和连续性
    if (vadResult.segmentQuality !== undefined) {
      metrics.lastQuality = vadResult.segmentQuality;
    }
    
    if (vadResult.speechContinuity !== undefined) {
      metrics.lastContinuity = vadResult.speechContinuity;
    }
    
    this.lastProcessingTime = processingTime;
  }

  /**
   * 🔥 获取处理器状态
   */
  getProcessorState(): {
    isProcessing: boolean;
    segmentCount: number;
    performance: typeof this.performanceMetrics;
    vadState: any;
    currentSegment: any;
  } {
    const self = this;
    return {
      isProcessing: self.isProcessing,
      segmentCount: self.segmentCount,
      performance: { ...self.performanceMetrics },
      vadState: self.vadDetector.getSpeechSegmentInfo(),
      currentSegment: self.dynamicSegmenter.getCurrentSegmentInfo()
    };
  }

  /**
   * 🔥 强制完成当前段
   */
  forceFinalize(): AudioSegment | null {
    console.log('🔄 Forcing finalization of current segment');
    return this.dynamicSegmenter.forceFinalize();
  }

  /**
   * 🔥 重置处理器
   */
  reset(): void {
    console.log('🔄 Resetting Enhanced Audio Processor');
    
    this.vadDetector.reset();
    this.dynamicSegmenter.reset();
    this.bufferManager.reset();
    
    this.segmentCount = 0;
    this.isProcessing = false;
    this.performanceMetrics = {
      totalChunks: 0,
      processedSegments: 0,
      averageProcessingTime: 0,
      lastQuality: 0,
      lastContinuity: 0
    };
  }

  /**
   * 🔥 更新配置
   */
  updateConfig(newConfig: Partial<EnhancedAudioConfig>): void {
    this.config = { ...this.config, ...newConfig };
    
    if (newConfig.vadConfig) {
      // VAD配置更新需要重新初始化
      this.vadDetector = new VADDetector(this.audioContext, {
        ...this.config.vadConfig,
        ...newConfig.vadConfig
      });
    }
    
    if (newConfig.segmentationConfig) {
      this.dynamicSegmenter.updateConfig(newConfig.segmentationConfig);
    }
    
    console.log('🔧 Enhanced Audio Processor config updated:', this.config);
  }

  /**
   * 🔥 获取统计信息
   */
  getStatistics(): {
    processing: typeof this.performanceMetrics;
    segmentation: any;
    vad: any;
  } {
    const self = this;
    return {
      processing: { ...self.performanceMetrics },
      segmentation: self.dynamicSegmenter.getSegmentationStats(),
      vad: self.vadDetector.getSpeechSegmentInfo()
    };
  }

  /**
   * 🔥 清理资源
   */
  destroy(): void {
    console.log('🗑️ Destroying Enhanced Audio Processor');
    this.reset();
    // 清理其他资源
  }
} 