import React from 'react';

interface TextPasteProps {
  text: string;
  onChange: (text: string) => void;
}

const TextPaste: React.FC<TextPasteProps> = ({ text, onChange }) => {
  return (
    <div>
      <textarea
        className="w-full h-64 p-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 outline-none resize-none transition-all"
        placeholder="请粘贴您的简历内容..."
        value={text}
        onChange={(e) => onChange(e.target.value)}
      ></textarea>
      <p className="text-sm text-gray-500 mt-2">
        请确保文本格式整洁，以便AI更好地分析您的技能和经验
      </p>
    </div>
  );
};

export default TextPaste;
