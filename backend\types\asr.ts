// ASR (Automatic Speech Recognition) 相关类型定义

export enum ASRProvider {
  DASHSCOPE = 'dashscope',  // 🔥 主要识别引擎
  ALIBABA = 'alibaba',      // 旧版NLS API
  BAIDU = 'baidu',
  WHISPER = 'whisper'
}

export interface ASRConfig {
  provider: ASRProvider;
  priority: number;
  timeout: number;
  retryCount: number;
  weight: number;
  enabled: boolean;
}



export interface AlibabaConfig {
  APPKEY: string;
  URL: string;
  ACCESS_KEY_ID: string;
  ACCESS_KEY_SECRET: string;
  ENDPOINT: string;
  API_VERSION: string;
}

export interface BaiduConfig {
  APP_ID: string;
  CLIENT_ID: string;
  CLIENT_SECRET: string;
}

export interface WhisperConfig {
  apiKey: string;
  model: string;
  language: string;
}

export interface DashScopeConfig {
  apiKey: string;
  model: string;
  endpoint: string;
  timeout: number;
}

export interface ASRResult {
  text: string;
  confidence: number;
  provider: ASRProvider;
  processingTime: number;
  timestamp: number;
  isPartial?: boolean;
  segmentId?: string;
  language?: string;
}

export interface ASRError extends Error {
  provider: ASRProvider;
  code: string;
  retryable: boolean;
  originalError?: Error;
  timestamp?: number; // 🔥 添加时间戳支持
}

export interface ASRServiceInterface {
  getName(): string;
  recognize(audioBuffer: Buffer, options?: ASROptions): Promise<ASRResult>;
  isAvailable(): boolean;
  getConfig(): ASRConfig;
  updateConfig(config: Partial<ASRConfig>): void;
  destroy(): Promise<void>;
}

export interface ASROptions {
  language?: string;
  sampleRate?: number;
  channels?: number;
  format?: string;
  enablePartialResults?: boolean;
  vadEos?: number;
  timeout?: number;
}

export interface ASRServiceManager {
  addProvider(provider: ASRServiceInterface): void;
  removeProvider(providerName: string): void;
  recognize(audioBuffer: Buffer, options?: ASROptions): Promise<ASRResult>;
  getAvailableProviders(): ASRServiceInterface[];
  setProviderPriority(providerName: string, priority: number): void;
}

export interface TextProcessingOptions {
  removeRepeats: boolean;
  cleanNonsense: boolean;
  normalizeSpaces: boolean;
  minLength: number;
}

export interface ProcessedText {
  original: string;
  cleaned: string;
  confidence: number;
  processingSteps: string[];
}
