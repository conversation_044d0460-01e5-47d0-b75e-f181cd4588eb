import { useCallback, useRef, useState, useEffect } from 'react';
import { useEnhancedAudioSession, AudioSessionConfig } from './useEnhancedAudioSession';

export interface EnhancedInterviewSessionConfig {
  websocketUrl?: string;
  audioConfig?: AudioSessionConfig;
  enableRealTimeTranscription?: boolean;
  enableVADLogging?: boolean;
  qualityThreshold?: number;
}

export interface TranscriptionEvent {
  type: 'partial' | 'final';
  text: string;
  confidence: number;
  timestamp: number;
  segmentId?: string;
}

/**
 * 🔥 增强面试会话Hook
 * 集成新的VAD和音频分段功能的面试会话管理器
 */
export function useInterviewSessionEnhanced(config: EnhancedInterviewSessionConfig = {}) {
  // WebSocket相关
  const wsRef = useRef<WebSocket | null>(null);
  const [wsConnected, setWsConnected] = useState(false);
  const [transcriptionEvents, setTranscriptionEvents] = useState<TranscriptionEvent[]>([]);
  
  // 面试状态
  const [isRecording, setIsRecording] = useState(false);
  const [currentTranscription, setCurrentTranscription] = useState('');
  const [segmentCount, setSegmentCount] = useState(0);
  
  // 音频处理统计
  const [audioStats, setAudioStats] = useState({
    totalSegments: 0,
    averageQuality: 0,
    speechDetectionRate: 0,
    processingLatency: 0
  });

  // 配置增强音频会话
  const audioSessionConfig: AudioSessionConfig = {
    sampleRate: 16000,
    bufferSize: 4096,
    enableVAD: true,
    enableSegmentation: true,
    onSegmentReady: handleAudioSegment,
    onVADResult: handleVADResult,
    onError: handleAudioError,
    ...config.audioConfig
  };

  // 使用增强音频会话
  const {
    sessionState,
    startAudioSession,
    stopAudioSession,
    forceFinalize,
    getProcessorState,
    getStatistics,
    isReady: audioReady,
    isActive: audioActive
  } = useEnhancedAudioSession(audioSessionConfig);

  /**
   * 🔥 处理音频段数据
   */
  async function handleAudioSegment(audioData: Float32Array, metadata: any) {
    try {
      if (!wsRef.current || wsRef.current.readyState !== WebSocket.OPEN) {
        console.warn('⚠️ WebSocket not connected, skipping audio segment');
        return;
      }

      // 更新段计数
      setSegmentCount(prev => prev + 1);

      // 构造发送数据
      const audioMessage = {
        type: 'audio_chunk',
        data: Array.from(audioData),
        metadata: {
          sampleRate: 16000,
          channels: 1,
          segmentId: `segment_${Date.now()}`,
          quality: metadata.segment?.quality || 0,
          continuity: metadata.vadResult?.speechContinuity || 0,
          isEndOfSpeech: metadata.vadResult?.isEndOfSpeech || false,
          forced: metadata.forced || false,
          timestamp: Date.now()
        }
      };

      // 发送音频数据
      wsRef.current.send(JSON.stringify(audioMessage));

      // 更新统计信息
      updateAudioStats(metadata);

      // 日志记录
      if (config.enableVADLogging) {
        console.log('🎤 Audio segment sent:', {
          length: audioData.length,
          quality: metadata.segment?.quality,
          continuity: metadata.vadResult?.speechContinuity,
          isEndOfSpeech: metadata.vadResult?.isEndOfSpeech
        });
      }

    } catch (error) {
      console.error('❌ Error sending audio segment:', error);
    }
  }

  /**
   * 🔥 处理VAD结果
   */
  function handleVADResult(vadResult: any) {
    if (config.enableVADLogging) {
      console.log('🔍 VAD Result:', {
        isSpeech: vadResult.isSpeech,
        energy: vadResult.energy,
        isEndOfSpeech: vadResult.isEndOfSpeech,
        speechContinuity: vadResult.speechContinuity,
        segmentQuality: vadResult.segmentQuality
      });
    }

    // 可以在这里添加实时VAD状态更新逻辑
    // 例如更新UI指示器显示当前是否检测到语音
  }

  /**
   * 🔥 处理音频错误
   */
  function handleAudioError(error: Error) {
    console.error('❌ Audio processing error:', error);
    // 可以在这里添加错误处理逻辑
    // 例如显示错误提示或尝试重新初始化
  }

  /**
   * 🔥 更新音频统计信息
   */
  function updateAudioStats(metadata: any) {
    setAudioStats(prev => {
      const stats = getStatistics();
      if (!stats) return prev;

      return {
        totalSegments: prev.totalSegments + 1,
        averageQuality: stats.averageQuality || 0,
        speechDetectionRate: stats.speechDetectionRate || 0,
        processingLatency: metadata.processingTime || 0
      };
    });
  }

  /**
   * 🔥 连接WebSocket
   */
  const connectWebSocket = useCallback(async () => {
    try {
      const wsUrl = config.websocketUrl || 'ws://localhost:8000/ws/transcribe';
      const ws = new WebSocket(wsUrl);

      ws.onopen = () => {
        console.log('🔗 WebSocket connected');
        setWsConnected(true);
        
        // 发送初始化消息
        ws.send(JSON.stringify({
          type: 'initialize',
          config: {
            sampleRate: 16000,
            channels: 1,
            enableVAD: true,
            enableSegmentation: true
          }
        }));
      };

      ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          handleWebSocketMessage(data);
        } catch (error) {
          console.error('❌ Error parsing WebSocket message:', error);
        }
      };

      ws.onclose = () => {
        console.log('🔌 WebSocket disconnected');
        setWsConnected(false);
      };

      ws.onerror = (error) => {
        console.error('❌ WebSocket error:', error);
        setWsConnected(false);
      };

      wsRef.current = ws;
      return true;

    } catch (error) {
      console.error('❌ Failed to connect WebSocket:', error);
      return false;
    }
  }, [config.websocketUrl]);

  /**
   * 🔥 处理WebSocket消息
   */
  function handleWebSocketMessage(data: any) {
    switch (data.type) {
      case 'transcription_partial':
        setCurrentTranscription(data.text);
        if (config.enableRealTimeTranscription) {
          setTranscriptionEvents(prev => [...prev, {
            type: 'partial',
            text: data.text,
            confidence: data.confidence || 0,
            timestamp: Date.now(),
            segmentId: data.segmentId
          }]);
        }
        break;

      case 'transcription_final':
        setCurrentTranscription('');
        setTranscriptionEvents(prev => [...prev, {
          type: 'final',
          text: data.text,
          confidence: data.confidence || 0,
          timestamp: Date.now(),
          segmentId: data.segmentId
        }]);
        break;

      case 'error':
        console.error('❌ Server error:', data.message);
        break;

      default:
        console.log('📨 Unknown message type:', data.type);
    }
  }

  /**
   * 🔥 开始录音
   */
  const startRecording = useCallback(async () => {
    try {
      // 连接WebSocket
      if (!wsConnected) {
        const connected = await connectWebSocket();
        if (!connected) {
          throw new Error('Failed to connect WebSocket');
        }
        // 等待连接建立
        await new Promise(resolve => setTimeout(resolve, 1000));
      }

      // 获取麦克风权限
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          sampleRate: 16000,
          channelCount: 1,
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true
        }
      });

      // 开始音频会话
      const started = await startAudioSession(stream);
      if (!started) {
        throw new Error('Failed to start audio session');
      }

      setIsRecording(true);
      console.log('🎤 Enhanced recording started');
      return true;

    } catch (error) {
      console.error('❌ Failed to start recording:', error);
      return false;
    }
  }, [wsConnected, connectWebSocket, startAudioSession]);

  /**
   * 🔥 停止录音
   */
  const stopRecording = useCallback(async () => {
    try {
      // 强制完成当前音频段
      const finalSegment = forceFinalize();
      if (finalSegment) {
        console.log('🔚 Final segment processed');
      }

      // 停止音频会话
      stopAudioSession();

      // 发送结束信号
      if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
        wsRef.current.send(JSON.stringify({
          type: 'end_session',
          timestamp: Date.now()
        }));
      }

      setIsRecording(false);
      console.log('🔇 Enhanced recording stopped');

    } catch (error) {
      console.error('❌ Error stopping recording:', error);
    }
  }, [forceFinalize, stopAudioSession]);

  /**
   * 🔥 断开连接
   */
  const disconnect = useCallback(() => {
    if (isRecording) {
      stopRecording();
    }

    if (wsRef.current) {
      wsRef.current.close();
      wsRef.current = null;
    }

    setWsConnected(false);
    setTranscriptionEvents([]);
    setCurrentTranscription('');
    setSegmentCount(0);
  }, [isRecording, stopRecording]);

  // 清理资源
  useEffect(() => {
    return () => {
      disconnect();
    };
  }, [disconnect]);

  return {
    // 连接状态
    wsConnected,
    audioReady,
    audioActive,
    isRecording,

    // 数据
    transcriptionEvents,
    currentTranscription,
    segmentCount,
    audioStats,
    sessionState,

    // 方法
    startRecording,
    stopRecording,
    connectWebSocket,
    disconnect,

    // 高级功能
    forceFinalize,
    getProcessorState,
    getStatistics,

    // 实用工具
    isReady: audioReady && wsConnected,
    canStart: audioReady && wsConnected && !isRecording,
    canStop: isRecording
  };
} 