// LLM服务 - 使用DeepSeek API进行动态问题生成
import OpenAI from 'openai';
import { logger } from '../utils/logger.js';

interface LLMConfig {
  provider: 'deepseek';
  apiKey?: string;
  baseURL: string;
  model: string;
  timeout: number;
}

interface InterviewContext {
  companyName: string;
  positionName: string;
  candidateBackground?: string;
  interviewLanguage: 'chinese' | 'english';
  answerStyle: 'keywords_conversational' | 'conversational';
  previousQuestions: string[];
  previousAnswers: string[];
  currentQuestionIndex: number;
  totalQuestions: number;
}

interface GeneratedQuestion {
  questionText: string;
  questionType: 'behavioral' | 'technical' | 'situational' | 'company_specific';
  difficulty: 'easy' | 'medium' | 'hard';
  expectedDuration: number;
  context: string;
  keywords: string[];
  followUpQuestions?: string[];
}

interface AnswerFeedback {
  score: number;
  strengths: string[];
  improvements: string[];
  keywordsCovered: string[];
  missingKeywords: string[];
  overallAssessment: string;
  detailedAnalysis: string;
}

export class LLMService {
  private deepseekClient: OpenAI | null = null;
  private config: LLMConfig;

  constructor() {
    // 配置DeepSeek提供商
    this.config = {
      provider: 'deepseek',
      apiKey: process.env.DEEPSEEK_API_KEY || '***********************************',
      model: 'deepseek-chat', // DeepSeek的主要模型
      baseURL: 'https://api.deepseek.com',
      timeout: 30000 // 30秒超时
    };

    this.initializeClients();
    logger.info('✅ LLM Service initialized with DeepSeek');
  }

  /**
   * 初始化LLM客户端
   */
  private initializeClients(): void {
    try {
      if (this.config.provider === 'deepseek' && this.config.apiKey) {
        this.deepseekClient = new OpenAI({
          apiKey: this.config.apiKey,
          baseURL: this.config.baseURL,
          timeout: this.config.timeout
        });
        logger.info('🤖 DeepSeek client initialized for dynamic question generation');
      } else {
        logger.warn('⚠️ No DeepSeek API key configured, falling back to template questions');
      }
    } catch (error) {
      logger.error('❌ Failed to initialize DeepSeek client:', error);
    }
  }

  /**
   * 生成动态面试问题
   */
  async generateInterviewQuestion(context: InterviewContext): Promise<GeneratedQuestion> {
    try {
      if (!this.deepseekClient) {
        logger.warn('⚠️ DeepSeek client not available, using fallback question');
        return this.generateFallbackQuestion(context);
      }

      const prompt = this.buildQuestionPrompt(context);

      logger.info('🤖 Calling DeepSeek API for question generation...');
      const response = await this.deepseekClient.chat.completions.create({
        model: this.config.model,
        messages: [
          {
            role: 'system',
            content: this.getSystemPrompt(context.interviewLanguage)
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.7,
        max_tokens: 1000,
        response_format: { type: 'json_object' }
      });

      logger.info('✅ DeepSeek API call successful');
      const result = JSON.parse(response.choices[0].message.content || '{}');

      return {
        questionText: result.questionText || '请介绍一下你自己。',
        questionType: result.questionType || 'behavioral',
        difficulty: result.difficulty || 'medium',
        expectedDuration: result.expectedDuration || 120,
        context: result.context || '',
        keywords: result.keywords || [],
        followUpQuestions: result.followUpQuestions || []
      };

    } catch (error) {
      logger.error('❌ Failed to generate question with DeepSeek:', error);
      return this.generateFallbackQuestion(context);
    }
  }

  /**
   * 分析用户回答并生成反馈
   */
  async analyzeAnswer(
    question: string,
    answer: string,
    context: InterviewContext,
    expectedKeywords: string[]
  ): Promise<AnswerFeedback> {
    try {
      if (!this.deepseekClient) {
        logger.warn('⚠️ DeepSeek client not available, using fallback feedback');
        return this.generateFallbackFeedback(answer, expectedKeywords);
      }

      const prompt = this.buildFeedbackPrompt(question, answer, context, expectedKeywords);

      logger.info('🤖 Calling DeepSeek API for answer analysis...');
      const response = await this.deepseekClient.chat.completions.create({
        model: this.config.model,
        messages: [
          {
            role: 'system',
            content: this.getFeedbackSystemPrompt(context.interviewLanguage)
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.3,
        max_tokens: 1500,
        response_format: { type: 'json_object' }
      });

      logger.info('✅ DeepSeek answer analysis successful');
      const result = JSON.parse(response.choices[0].message.content || '{}');

      return {
        score: Math.min(Math.max(result.score || 60, 0), 100),
        strengths: result.strengths || ['回答态度积极'],
        improvements: result.improvements || ['可以提供更多具体例子'],
        keywordsCovered: result.keywordsCovered || [],
        missingKeywords: result.missingKeywords || [],
        overallAssessment: result.overallAssessment || '整体表现良好',
        detailedAnalysis: result.detailedAnalysis || '需要更详细的分析'
      };

    } catch (error) {
      logger.error('❌ Failed to analyze answer with DeepSeek:', error);
      return this.generateFallbackFeedback(answer, expectedKeywords);
    }
  }

  /**
   * 构建问题生成提示
   */
  private buildQuestionPrompt(context: InterviewContext): string {
    const language = context.interviewLanguage === 'chinese' ? '中文' : 'English';
    const previousQuestionsText = context.previousQuestions.length > 0 
      ? `已问过的问题：\n${context.previousQuestions.map((q, i) => `${i + 1}. ${q}`).join('\n')}\n\n`
      : '';

    return `
请为以下面试场景生成一个合适的面试问题：

公司：${context.companyName}
岗位：${context.positionName}
面试语言：${language}
当前是第 ${context.currentQuestionIndex + 1} 个问题，总共 ${context.totalQuestions} 个问题
回答风格偏好：${context.answerStyle === 'keywords_conversational' ? '关键词+对话式' : '纯对话式'}

${previousQuestionsText}

要求：
1. 问题应该适合当前面试进度（开始/中期/结尾）
2. 避免重复已问过的问题
3. 问题难度应该递进
4. 符合岗位特点和公司背景
5. 用${language}提问

请返回JSON格式：
{
  "questionText": "具体问题内容",
  "questionType": "behavioral|technical|situational|company_specific",
  "difficulty": "easy|medium|hard",
  "expectedDuration": 120,
  "context": "问题背景说明",
  "keywords": ["期望关键词1", "期望关键词2"],
  "followUpQuestions": ["可能的追问1", "可能的追问2"]
}
`;
  }

  /**
   * 构建反馈分析提示
   */
  private buildFeedbackPrompt(
    question: string,
    answer: string,
    context: InterviewContext,
    expectedKeywords: string[]
  ): string {
    const language = context.interviewLanguage === 'chinese' ? '中文' : 'English';
    
    return `
请分析以下面试回答并提供详细反馈：

面试问题：${question}
候选人回答：${answer}
期望关键词：${expectedKeywords.join(', ')}
岗位：${context.positionName}
公司：${context.companyName}

请从以下维度分析：
1. 内容完整性和相关性
2. 关键词覆盖情况
3. 回答结构和逻辑
4. 具体例子和细节
5. 专业性和深度

请用${language}返回JSON格式：
{
  "score": 85,
  "strengths": ["优点1", "优点2"],
  "improvements": ["改进建议1", "改进建议2"],
  "keywordsCovered": ["覆盖的关键词"],
  "missingKeywords": ["缺失的关键词"],
  "overallAssessment": "总体评价",
  "detailedAnalysis": "详细分析说明"
}
`;
  }

  /**
   * 获取系统提示（问题生成）
   */
  private getSystemPrompt(language: 'chinese' | 'english'): string {
    if (language === 'chinese') {
      return `你是一位专业的面试官，擅长根据不同岗位和公司背景设计合适的面试问题。你的问题应该：
1. 具有针对性和专业性
2. 能够有效评估候选人的能力
3. 符合现代面试最佳实践
4. 考虑面试的整体流程和节奏
5. 用中文提问，表达自然流畅

请始终返回有效的JSON格式。`;
    } else {
      return `You are a professional interviewer skilled at designing appropriate interview questions based on different positions and company backgrounds. Your questions should:
1. Be targeted and professional
2. Effectively assess candidate capabilities
3. Follow modern interview best practices
4. Consider the overall interview flow and pace
5. Be asked in English with natural and fluent expression

Always return valid JSON format.`;
    }
  }

  /**
   * 获取反馈系统提示
   */
  private getFeedbackSystemPrompt(language: 'chinese' | 'english'): string {
    if (language === 'chinese') {
      return `你是一位经验丰富的面试官和HR专家，擅长分析候选人的面试回答并提供建设性反馈。你的分析应该：
1. 客观公正，基于事实
2. 具体明确，避免空泛
3. 既指出优点也提出改进建议
4. 考虑岗位要求和行业标准
5. 用中文提供反馈，语言专业友善

请始终返回有效的JSON格式。`;
    } else {
      return `You are an experienced interviewer and HR expert skilled at analyzing candidate interview responses and providing constructive feedback. Your analysis should:
1. Be objective and fact-based
2. Be specific and avoid vague statements
3. Highlight both strengths and areas for improvement
4. Consider job requirements and industry standards
5. Provide feedback in English with professional and friendly language

Always return valid JSON format.`;
    }
  }

  /**
   * 生成备用问题（当LLM不可用时）
   */
  private generateFallbackQuestion(context: InterviewContext): GeneratedQuestion {
    const fallbackQuestions = [
      {
        questionText: context.interviewLanguage === 'chinese' 
          ? '请简单介绍一下你自己，包括你的背景和为什么对这个岗位感兴趣。'
          : 'Please briefly introduce yourself, including your background and why you are interested in this position.',
        questionType: 'behavioral' as const,
        difficulty: 'easy' as const,
        expectedDuration: 120,
        context: 'Opening question to understand candidate background',
        keywords: context.interviewLanguage === 'chinese' 
          ? ['背景', '经验', '兴趣', '动机', '技能']
          : ['background', 'experience', 'interest', 'motivation', 'skills']
      },
      {
        questionText: context.interviewLanguage === 'chinese'
          ? '描述一个你在工作中遇到的挑战，以及你是如何解决的。'
          : 'Describe a challenge you faced at work and how you solved it.',
        questionType: 'behavioral' as const,
        difficulty: 'medium' as const,
        expectedDuration: 180,
        context: 'Problem-solving and resilience assessment',
        keywords: context.interviewLanguage === 'chinese'
          ? ['挑战', '解决方案', '方法', '结果', '学习']
          : ['challenge', 'solution', 'approach', 'result', 'learning']
      }
    ];

    const questionIndex = Math.min(context.currentQuestionIndex, fallbackQuestions.length - 1);
    return fallbackQuestions[questionIndex];
  }

  /**
   * 生成备用反馈（当LLM不可用时）
   */
  private generateFallbackFeedback(answer: string, expectedKeywords: string[]): AnswerFeedback {
    const answerLength = answer.length;
    const keywordsCovered = expectedKeywords.filter(keyword => 
      answer.toLowerCase().includes(keyword.toLowerCase())
    );
    const missingKeywords = expectedKeywords.filter(keyword => 
      !answer.toLowerCase().includes(keyword.toLowerCase())
    );

    const keywordScore = expectedKeywords.length > 0 
      ? (keywordsCovered.length / expectedKeywords.length) * 60 
      : 50;
    const lengthScore = Math.min(answerLength / 100, 1) * 20;
    const structureScore = answer.includes('首先') || answer.includes('其次') || answer.includes('最后') ? 20 : 10;
    
    const totalScore = Math.min(keywordScore + lengthScore + structureScore, 100);

    return {
      score: Math.round(totalScore),
      strengths: [
        keywordsCovered.length > 0 ? `涵盖了关键要点：${keywordsCovered.join('、')}` : '回答态度积极',
        answerLength > 200 ? '回答内容详细' : '表达简洁明了'
      ],
      improvements: [
        missingKeywords.length > 0 ? `可以进一步阐述：${missingKeywords.join('、')}` : '整体表现良好',
        answerLength < 100 ? '可以提供更多具体细节' : '继续保持'
      ],
      keywordsCovered,
      missingKeywords,
      overallAssessment: totalScore >= 80 ? '表现优秀，回答全面' : totalScore >= 60 ? '表现良好，有改进空间' : '需要加强，建议多练习',
      detailedAnalysis: `回答长度：${answerLength}字符，关键词覆盖率：${Math.round((keywordsCovered.length / Math.max(expectedKeywords.length, 1)) * 100)}%`
    };
  }

  /**
   * 检查LLM服务是否可用
   */
  isAvailable(): boolean {
    return this.openaiClient !== null;
  }

  /**
   * 获取服务状态
   */
  getStatus(): { provider: string; model: string; available: boolean } {
    return {
      provider: this.config.provider,
      model: this.config.model,
      available: this.isAvailable()
    };
  }
}
