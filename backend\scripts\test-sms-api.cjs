#!/usr/bin/env node

/**
 * 阿里云短信服务API测试脚本
 * 用于测试通过API发送短信验证码
 */

const http = require('http');

function makeRequest(options, postData) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: jsonData
          });
        } catch (error) {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: data
          });
        }
      });
    });
    
    req.on('error', (error) => {
      reject(error);
    });
    
    if (postData) {
      req.write(postData);
    }
    
    req.end();
  });
}

async function testSmsAPI() {
  console.log('🚀 开始测试短信API...\n');

  // 测试用的手机号（请替换为真实手机号进行测试）
  const testPhoneNumber = '13800138000'; // 请替换为真实手机号
  
  console.log('⚠️  注意：这是一个演示测试，使用的是示例手机号');
  console.log('⚠️  如需测试实际发送，请将 testPhoneNumber 替换为真实手机号\n');

  try {
    // 1. 测试发送短信验证码
    console.log('📤 测试发送短信验证码...');
    
    const sendCodeOptions = {
      hostname: 'localhost',
      port: 3000,
      path: '/api/auth/send-verification-code',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      }
    };

    const sendCodeData = JSON.stringify({
      identifier: testPhoneNumber,
      type: 'SMS',
      purpose: 'LOGIN'
    });

    const sendResponse = await makeRequest(sendCodeOptions, sendCodeData);
    
    console.log(`状态码: ${sendResponse.statusCode}`);
    console.log('响应数据:', JSON.stringify(sendResponse.data, null, 2));

    if (sendResponse.statusCode === 200 && sendResponse.data.success) {
      console.log('✅ 短信验证码发送成功！');
      
      // 2. 模拟验证码登录测试（使用示例验证码）
      console.log('\n🔐 测试验证码登录...');
      console.log('⚠️  注意：实际测试需要使用收到的真实验证码');
      
      const loginOptions = {
        hostname: 'localhost',
        port: 3000,
        path: '/api/auth/login-with-code',
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      };

      const loginData = JSON.stringify({
        identifier: testPhoneNumber,
        code: '123456', // 示例验证码，实际测试需要使用真实验证码
        type: 'SMS'
      });

      const loginResponse = await makeRequest(loginOptions, loginData);
      
      console.log(`状态码: ${loginResponse.statusCode}`);
      console.log('响应数据:', JSON.stringify(loginResponse.data, null, 2));
      
    } else {
      console.log('❌ 短信验证码发送失败');
      if (sendResponse.data.error) {
        console.log('错误信息:', sendResponse.data.error);
      }
    }

  } catch (error) {
    console.error('💥 测试过程中发生错误:', error.message);
  }
}

// 运行测试
console.log('📋 短信API测试说明:');
console.log('1. 确保后端服务已启动 (npm run dev)');
console.log('2. 确保阿里云短信服务已正确配置');
console.log('3. 如需测试实际发送，请修改 testPhoneNumber 为真实手机号');
console.log('4. 确保已在阿里云控制台完成签名和模板审核\n');

testSmsAPI().then(() => {
  console.log('\n🎉 API测试完成');
}).catch((error) => {
  console.error('💥 API测试失败:', error);
});
