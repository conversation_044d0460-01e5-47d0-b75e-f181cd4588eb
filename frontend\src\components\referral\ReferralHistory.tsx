import React from 'react';
import { Calendar, Mail, Gift, Clock, CheckCircle, UserPlus } from 'lucide-react';

// 邀请记录接口
interface ReferralRecord {
  id: string;
  invitedUserEmail: string;
  registeredAt: string;
  firstPaymentAt?: string;
  rewardAmount?: number;
  status: 'registered' | 'paid' | 'rewarded';
}

// 组件属性接口
interface ReferralHistoryProps {
  records: ReferralRecord[];
  onLoadMore: () => void;
  hasMore: boolean;
  isLoading?: boolean;
  isLoadingMore?: boolean;
}

/**
 * 邀请历史记录组件
 */
const ReferralHistory: React.FC<ReferralHistoryProps> = ({
  records,
  onLoadMore,
  hasMore,
  isLoading = false,
  isLoadingMore = false
}) => {



  // 获取状态显示信息
  const getStatusInfo = (status: string) => {
    switch (status) {
      case 'registered':
        return {
          label: '已注册',
          color: 'blue',
          icon: UserPlus,
          description: '等待首次充值'
        };
      case 'paid':
        return {
          label: '已充值',
          color: 'orange',
          icon: Clock,
          description: '奖励处理中'
        };
      case 'rewarded':
        return {
          label: '已奖励',
          color: 'green',
          icon: CheckCircle,
          description: '奖励已发放'
        };
      default:
        return {
          label: '未知',
          color: 'gray',
          icon: Clock,
          description: ''
        };
    }
  };

  // 获取状态颜色类名
  const getStatusColorClasses = (color: string) => {
    const colorMap = {
      blue: 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300',
      orange: 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300',
      green: 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300',
      gray: 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300'
    };
    return colorMap[color as keyof typeof colorMap] || colorMap.gray;
  };

  if (isLoading) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 h-full flex flex-col">
        <div className="animate-pulse flex-1">
          <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded mb-4"></div>
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="h-20 bg-gray-200 dark:bg-gray-700 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 h-full flex flex-col">
      <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">
        邀请记录
      </h2>

      <div className="flex-1 flex flex-col">
        <div className="max-h-[300px] overflow-y-auto custom-scrollbar pr-2">
          {records.length === 0 ? (
            <div className="text-center py-12 h-full flex flex-col items-center justify-center">
              <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
                <UserPlus className="w-8 h-8 text-gray-400" />
              </div>
              <p className="text-gray-500 dark:text-gray-400 mb-2">暂无邀请记录</p>
              <p className="text-sm text-gray-400 dark:text-gray-500">
                分享您的邀请码，开始邀请好友吧！
              </p>
            </div>
          ) : (
            <div className="space-y-4">
          {records.map((record) => {
            const statusInfo = getStatusInfo(record.status);
            const StatusIcon = statusInfo.icon;
            
            return (
              <div
                key={record.id}
                className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:shadow-sm transition-shadow duration-200"
              >
                <div className="flex items-start justify-between">
                  {/* 左侧信息 */}
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <Mail className="w-4 h-4 text-gray-400" />
                      <span className="text-sm font-medium text-gray-900 dark:text-white">
                        {record.invitedUserEmail}
                      </span>
                    </div>
                    
                    <div className="flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400">
                      <div className="flex items-center space-x-1">
                        <Calendar className="w-3 h-3" />
                        <span>注册: {new Date(record.registeredAt).toLocaleDateString()}</span>
                      </div>
                      
                      {record.firstPaymentAt && (
                        <div className="flex items-center space-x-1">
                          <Gift className="w-3 h-3" />
                          <span>充值: {new Date(record.firstPaymentAt).toLocaleDateString()}</span>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* 右侧状态和奖励 */}
                  <div className="flex flex-col items-end space-y-2">
                    <div className={`px-2 py-1 rounded-full text-xs font-medium flex items-center space-x-1 ${getStatusColorClasses(statusInfo.color)}`}>
                      <StatusIcon className="w-3 h-3" />
                      <span>{statusInfo.label}</span>
                    </div>
                    
                    {record.rewardAmount && (
                      <div className="text-right">
                        <p className="text-sm font-bold text-green-600 dark:text-green-400">
                          +{record.rewardAmount} 面巾值
                        </p>
                      </div>
                    )}
                    
                    {record.status === 'registered' && (
                      <p className="text-xs text-gray-400 text-right">
                        {statusInfo.description}
                      </p>
                    )}
                  </div>
                </div>

                {/* 进度指示器 */}
                <div className="mt-3 flex items-center space-x-2">
                  <div className="flex-1 bg-gray-200 dark:bg-gray-700 rounded-full h-1">
                    <div 
                      className={`h-1 rounded-full transition-all duration-500 ${
                        record.status === 'registered' ? 'bg-blue-500 w-1/3' :
                        record.status === 'paid' ? 'bg-orange-500 w-2/3' :
                        'bg-green-500 w-full'
                      }`}
                    ></div>
                  </div>
                  <span className="text-xs text-gray-400">
                    {record.status === 'registered' ? '1/3' :
                     record.status === 'paid' ? '2/3' : '3/3'}
                  </span>
                </div>
              </div>
            );
          })}

            {/* 加载更多按钮 */}
            {hasMore && (
              <div className="text-center pt-4">
                <button
                  onClick={onLoadMore}
                  disabled={isLoadingMore}
                  className="px-6 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white rounded-lg transition-colors duration-200 flex items-center space-x-2 mx-auto"
                >
                  {isLoadingMore ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                      <span>加载中...</span>
                    </>
                  ) : (
                    <span>加载更多</span>
                  )}
                </button>
              </div>
            )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ReferralHistory;
