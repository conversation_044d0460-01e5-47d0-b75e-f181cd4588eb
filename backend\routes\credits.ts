import express, { Request, Response } from 'express';
import { authenticateToken, AuthenticatedRequest } from '../middleware/auth';
import prisma from '../lib/prisma';

const router = express.Router();

// 导入UsageType枚举
enum UsageType {
  MOCK_INTERVIEW = 'MOCK_INTERVIEW',
  FORMAL_INTERVIEW = 'FORMAL_INTERVIEW',
  CREDIT_RECHARGE = 'CREDIT_RECHARGE'
}

/**
 * 扣费API
 */
router.post('/deduct', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user!.userId;
    const { type, reason } = req.body;

    // 验证必填字段
    if (!type || !reason) {
      return res.status(400).json({ 
        success: false,
        message: '缺少必要的扣费信息' 
      });
    }

    // 验证扣费类型
    if (!['mock', 'formal'].includes(type)) {
      return res.status(400).json({ 
        success: false,
        message: '无效的扣费类型' 
      });
    }

    // 获取用户当前余额
    let userBalance = await prisma.userBalance.findUnique({
      where: { userId: userId },
    });

    // 如果用户余额记录不存在，创建一个默认记录
    if (!userBalance) {
      userBalance = await prisma.userBalance.create({
        data: {
          userId: userId,
          mockInterviewCredits: 2, // 新用户默认2次模拟面试
          formalInterviewCredits: 0, // 新用户默认0次正式面试
          mianshijunBalance: 0,
        },
      });
    }

    // 检查余额是否足够
    const fieldName = type === 'mock' ? 'mockInterviewCredits' : 'formalInterviewCredits';
    const currentCredits = userBalance[fieldName];

    if (currentCredits < 1) {
      return res.status(400).json({
        success: false,
        message: '您的面试机会已用完，可以考虑充值获取更多次数哦',
        currentCredits: currentCredits
      });
    }

    // 使用事务进行扣费和记录创建
    const result = await prisma.$transaction(async (tx) => {
      // 在事务中再次验证余额，防止并发问题
      const currentBalance = await tx.userBalance.findUnique({
        where: { userId: userId },
        select: { [fieldName]: true, updatedAt: true }
      });

      if (!currentBalance || (currentBalance as any)[fieldName] < 1) {
        throw new Error(`余额不足，无法进行${type === 'mock' ? '模拟' : '正式'}面试`);
      }

      // 扣费
      const updatedBalance = await tx.userBalance.update({
        where: {
          userId: userId,
          updatedAt: (currentBalance as any).updatedAt // 乐观锁
        },
        data: {
          [fieldName]: {
            decrement: 1
          }
        }
      });

      // 创建消费记录
      await tx.usageRecord.create({
        data: {
          userId: userId,
          type: type === 'mock' ? UsageType.MOCK_INTERVIEW : UsageType.FORMAL_INTERVIEW,
          amount: -1, // 负数表示消耗
          reason: reason,
        }
      });

      return updatedBalance;
    });

    return res.status(200).json({
      success: true,
      message: '准备就绪！即将开始面试...',
      newBalance: {
        mockInterviewCredits: result.mockInterviewCredits,
        formalInterviewCredits: result.formalInterviewCredits,
        mianshijunBalance: result.mianshijunBalance
      }
    });

  } catch (error: any) {
    console.error('验证失败:', error);
    return res.status(500).json({
      success: false,
      message: '验证失败，请稍后再试'
    });
  }
});

export default router;
