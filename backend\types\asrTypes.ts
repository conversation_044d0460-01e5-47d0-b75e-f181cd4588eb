// ASR (Automatic Speech Recognition) 相关类型定义

export enum ASRState {
  IDLE = 'idle',
  LISTENING = 'listening',
  PROCESSING = 'processing',
  MERGING = 'merging',
  OUTPUTTING = 'outputting'
}

export interface VADInfo {
  isSpeech: boolean;
  energy: number;
  confidence: number;
  timestamp: number;
  isEndOfSpeech?: boolean;
  spectralFeatures?: {
    spectralCentroid: number;
    zeroCrossingRate: number;
    spectralRolloff: number;
  };
}

export interface AudioSegmentInfo {
  id: string;
  duration: number;
  confidence: number;
  segmentType: 'speech' | 'silence' | 'mixed';
  triggerReason: string;
  timestamp: number;
}

export interface RecognitionResult {
  text: string;
  confidence: number;
  timestamp: number;
  service: string;
  isPartial: boolean;
  segmentId: string;
  processingTime?: number;
  metadata?: {
    audioLength: number;
    sampleRate: number;
    channels: number;
  };
}

export interface RecognitionSession {
  sessionId: string;
  state: ASRState;
  audioBuffer: Buffer[];
  startTime: number;
  lastActivityTime: number;
  vadInfo: VADInfo[];
  partialResults: RecognitionResult[];
  contextHistory: string[];
  currentSegment?: AudioSegmentInfo;
  statistics: SessionStatistics;
}

export interface SessionStatistics {
  totalSegments: number;
  totalAudioDuration: number;
  averageConfidence: number;
  serviceUsage: Record<string, number>;
  errorCount: number;
  lastUpdated: number;
}

export interface ASRServiceConfig {
  name: string;
  priority: number;
  timeout: number;
  retryCount: number;
  enabled: boolean;
  weight: number;
}

export interface ASRServiceResult {
  success: boolean;
  text?: string;
  confidence?: number;
  error?: string;
  processingTime: number;
  service: string;
}

export interface MergedRecognitionResult {
  text: string;
  confidence: number;
  segments: RecognitionResult[];
  mergeStrategy: string;
  finalConfidence: number;
  processingTime: number;
}

export interface StreamingConfig {
  maxSessionDuration: number;      // 最大会话时长(ms)
  maxSilenceDuration: number;      // 最大静音时长(ms)
  maxBufferSize: number;           // 最大缓冲区大小(bytes)
  recognitionTimeout: number;      // 识别超时时间(ms)
  mergeThreshold: number;          // 合并阈值
  contextWindowSize: number;       // 上下文窗口大小
}

export const DEFAULT_STREAMING_CONFIG: StreamingConfig = {
  maxSessionDuration: 300000,      // 5分钟
  maxSilenceDuration: 5000,        // 5秒
  maxBufferSize: 1024 * 1024,      // 1MB
  recognitionTimeout: 10000,       // 10秒
  mergeThreshold: 0.7,             // 70%置信度
  contextWindowSize: 10            // 10个历史结果
};

export interface ASRServiceInterface {
  getName(): string;
  recognize(audioBuffer: Buffer, metadata?: any): Promise<RecognitionResult>;
  isAvailable(): boolean;
  getConfig(): ASRServiceConfig;
  updateConfig(config: Partial<ASRServiceConfig>): void;
}

export interface ContextInfo {
  previousResults: string[];
  currentTopic?: string;
  speakerInfo?: {
    language: string;
    accent?: string;
    speakingRate?: number;
  };
  environmentInfo?: {
    noiseLevel: number;
    audioQuality: number;
  };
}

export interface IncrementalUpdate {
  type: 'partial' | 'final' | 'correction';
  text: string;
  confidence: number;
  timestamp: number;
  segmentId: string;
  position: {
    start: number;
    end: number;
  };
}

export interface StreamingResponse {
  type: 'transcription' | 'partial_result' | 'final_result' | 'error' | 'status';
  data: any;
  timestamp: number;
  sessionId: string;
}

// 错误类型定义
export class ASRError extends Error {
  constructor(
    message: string,
    public code: string,
    public service?: string,
    public retryable: boolean = false
  ) {
    super(message);
    this.name = 'ASRError';
  }
}

export enum ASRErrorCode {
  TIMEOUT = 'TIMEOUT',
  NETWORK_ERROR = 'NETWORK_ERROR',
  INVALID_AUDIO = 'INVALID_AUDIO',
  SERVICE_UNAVAILABLE = 'SERVICE_UNAVAILABLE',
  QUOTA_EXCEEDED = 'QUOTA_EXCEEDED',
  AUTHENTICATION_FAILED = 'AUTHENTICATION_FAILED',
  PROCESSING_ERROR = 'PROCESSING_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

// 事件类型定义
export interface ASREvent {
  type: 'state_change' | 'result_ready' | 'error' | 'session_start' | 'session_end';
  sessionId: string;
  timestamp: number;
  data: any;
}

export interface StateChangeEvent extends ASREvent {
  type: 'state_change';
  data: {
    fromState: ASRState;
    toState: ASRState;
    reason: string;
  };
}

export interface ResultReadyEvent extends ASREvent {
  type: 'result_ready';
  data: {
    result: RecognitionResult;
    isPartial: boolean;
  };
}

export interface ErrorEvent extends ASREvent {
  type: 'error';
  data: {
    error: ASRError;
    context: any;
  };
}
