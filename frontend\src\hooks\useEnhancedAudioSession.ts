import { useCallback, useRef, useEffect, useState } from 'react';
import { EnhancedAudioProcessor, AudioProcessingResult } from '../utils/enhancedAudioProcessor';

export interface AudioSessionConfig {
  sampleRate?: number;
  bufferSize?: number;
  enableVAD?: boolean;
  enableSegmentation?: boolean;
  onSegmentReady?: (audioData: Float32Array, metadata: any) => void;
  onVADResult?: (vadResult: any) => void;
  onError?: (error: Error) => void;
}

export interface AudioSessionState {
  isActive: boolean;
  isProcessing: boolean;
  segmentCount: number;
  currentSpeechDuration: number;
  lastProcessingTime: number;
  processingStats: {
    totalChunks: number;
    processedSegments: number;
    averageQuality: number;
    averageContinuity: number;
  };
}

/**
 * 🔥 增强音频会话Hook
 * 集成VAD检测和智能音频分段的统一音频处理解决方案
 */
export function useEnhancedAudioSession(config: AudioSessionConfig = {}) {
  // 状态管理
  const [sessionState, setSessionState] = useState<AudioSessionState>({
    isActive: false,
    isProcessing: false,
    segmentCount: 0,
    currentSpeechDuration: 0,
    lastProcessingTime: 0,
    processingStats: {
      totalChunks: 0,
      processedSegments: 0,
      averageQuality: 0,
      averageContinuity: 0
    }
  });

  // 引用
  const audioProcessorRef = useRef<EnhancedAudioProcessor | null>(null);
  const audioContextRef = useRef<AudioContext | null>(null);
  const processorNodeRef = useRef<ScriptProcessorNode | null>(null);
  const sourceNodeRef = useRef<MediaStreamAudioSourceNode | null>(null);
  const streamRef = useRef<MediaStream | null>(null);

  // 配置
  const sessionConfig = {
    sampleRate: 16000,
    bufferSize: 4096,
    enableVAD: true,
    enableSegmentation: true,
    ...config
  };

  /**
   * 🔥 初始化音频处理器
   */
  const initializeAudioProcessor = useCallback(async () => {
    try {
      // 创建AudioContext
      const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)({
        sampleRate: sessionConfig.sampleRate
      });

      audioContextRef.current = audioContext;

      // 创建增强音频处理器
      const processor = new EnhancedAudioProcessor(audioContext, {
        vadConfig: {
          energyThreshold: 0.01,
          silenceThreshold: 0.003,
          minSpeechDuration: 300,
          minSilenceDuration: 800,
          endOfSpeechTimeout: 1500,
          maxSpeechDuration: 15000,
          qualityThreshold: 0.4,
          continuityThreshold: 0.3
        },
        segmentationConfig: {
          minSegmentDuration: 500,
          maxSegmentDuration: 15000,
          confidenceThreshold: 0.3
        },
        bufferConfig: {
          maxBufferSize: 20,
          sendInterval: 1000,
          qualityThreshold: 0.4
        }
      });

      audioProcessorRef.current = processor;

      console.log('🔥 Enhanced Audio Processor initialized successfully');
      return true;

    } catch (error) {
      console.error('❌ Failed to initialize audio processor:', error);
      config.onError?.(error as Error);
      return false;
    }
  }, [sessionConfig.sampleRate, config]);

  /**
   * 🔥 开始音频会话
   */
  const startAudioSession = useCallback(async (stream: MediaStream) => {
    try {
      if (!audioContextRef.current || !audioProcessorRef.current) {
        const initialized = await initializeAudioProcessor();
        if (!initialized) {
          throw new Error('Failed to initialize audio processor');
        }
      }

      const audioContext = audioContextRef.current!;
      const processor = audioProcessorRef.current!;

      // 确保AudioContext处于运行状态
      if (audioContext.state === 'suspended') {
        await audioContext.resume();
      }

      // 创建音频源
      const source = audioContext.createMediaStreamSource(stream);
      sourceNodeRef.current = source;
      streamRef.current = stream;

      // 创建处理节点
      const processorNode = audioContext.createScriptProcessor(
        sessionConfig.bufferSize, 
        1, 
        1
      );
      processorNodeRef.current = processorNode;

      // 连接音频节点
      source.connect(processorNode);
      processorNode.connect(audioContext.destination);

      // 设置音频处理回调
      processorNode.onaudioprocess = (event) => {
        if (!sessionState.isActive) return;

        const inputBuffer = event.inputBuffer;
        const inputData = inputBuffer.getChannelData(0);
        
        // 复制音频数据
        const audioData = new Float32Array(inputData.length);
        audioData.set(inputData);

        // 处理音频数据
        const result = processor.processAudioChunk(audioData);
        handleAudioProcessingResult(result);
      };

      // 更新状态
      setSessionState(prev => ({
        ...prev,
        isActive: true,
        isProcessing: true
      }));

      console.log('🎤 Enhanced audio session started');
      return true;

    } catch (error) {
      console.error('❌ Failed to start audio session:', error);
      config.onError?.(error as Error);
      return false;
    }
  }, [sessionState.isActive, sessionConfig.bufferSize, initializeAudioProcessor]);

  /**
   * 🔥 处理音频处理结果
   */
  const handleAudioProcessingResult = useCallback((result: AudioProcessingResult) => {
    // 更新统计信息
    setSessionState(prev => {
      const newStats = {
        ...prev.processingStats,
        totalChunks: result.metadata.segmentCount,
        lastProcessingTime: result.metadata.processingTime
      };

      if (result.segment) {
        newStats.processedSegments++;
      }

      return {
        ...prev,
        segmentCount: result.metadata.segmentCount,
        lastProcessingTime: result.metadata.processingTime,
        processingStats: newStats
      };
    });

    // 触发VAD结果回调
    if (config.onVADResult) {
      config.onVADResult(result.vadResult);
    }

    // 如果需要发送音频段
    if (result.shouldSend && result.audioData && config.onSegmentReady) {
      config.onSegmentReady(result.audioData, {
        segment: result.segment,
        vadResult: result.vadResult,
        metadata: result.metadata
      });
    }
  }, [config]);

  /**
   * 🔥 停止音频会话
   */
  const stopAudioSession = useCallback(() => {
    try {
      // 清理音频节点
      if (processorNodeRef.current) {
        processorNodeRef.current.onaudioprocess = null;
        processorNodeRef.current.disconnect();
        processorNodeRef.current = null;
      }

      if (sourceNodeRef.current) {
        sourceNodeRef.current.disconnect();
        sourceNodeRef.current = null;
      }

      // 停止媒体流
      if (streamRef.current) {
        streamRef.current.getTracks().forEach(track => track.stop());
        streamRef.current = null;
      }

      // 关闭AudioContext
      if (audioContextRef.current && audioContextRef.current.state !== 'closed') {
        audioContextRef.current.close();
        audioContextRef.current = null;
      }

      // 重置音频处理器
      if (audioProcessorRef.current) {
        audioProcessorRef.current.reset();
      }

      // 更新状态
      setSessionState(prev => ({
        ...prev,
        isActive: false,
        isProcessing: false
      }));

      console.log('🔇 Enhanced audio session stopped');

    } catch (error) {
      console.error('❌ Error stopping audio session:', error);
      config.onError?.(error as Error);
    }
  }, [config]);

  /**
   * 🔥 强制完成当前段
   */
  const forceFinalize = useCallback(() => {
    if (audioProcessorRef.current) {
      const segment = audioProcessorRef.current.forceFinalize();
      if (segment && config.onSegmentReady) {
        config.onSegmentReady(segment.audioData, {
          segment,
          forced: true
        });
      }
      return segment;
    }
    return null;
  }, [config]);

  /**
   * 🔥 获取处理器状态
   */
  const getProcessorState = useCallback(() => {
    if (audioProcessorRef.current) {
      return audioProcessorRef.current.getProcessorState();
    }
    return null;
  }, []);

  /**
   * 🔥 获取统计信息
   */
  const getStatistics = useCallback(() => {
    if (audioProcessorRef.current) {
      return audioProcessorRef.current.getStatistics();
    }
    return null;
  }, []);

  /**
   * 🔥 更新配置
   */
  const updateConfig = useCallback((newConfig: any) => {
    if (audioProcessorRef.current) {
      audioProcessorRef.current.updateConfig(newConfig);
    }
  }, []);

  // 清理资源
  useEffect(() => {
    return () => {
      stopAudioSession();
      if (audioProcessorRef.current) {
        audioProcessorRef.current.destroy();
      }
    };
  }, [stopAudioSession]);

  return {
    // 状态
    sessionState,
    
    // 方法
    startAudioSession,
    stopAudioSession,
    forceFinalize,
    getProcessorState,
    getStatistics,
    updateConfig,
    
    // 工具方法
    isReady: !!audioProcessorRef.current,
    isActive: sessionState.isActive,
    isProcessing: sessionState.isProcessing
  };
} 