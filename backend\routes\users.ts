// 用户相关API路由
import express, { Request, Response } from 'express';
import bcrypt from 'bcryptjs';
import { z } from 'zod';
import prisma from '../lib/prisma';
import { authenticateToken, AuthenticatedRequest } from '../middleware/auth';

const router = express.Router();

// 请求验证模式
const updateUserSchema = z.object({
  name: z.string().optional(),
  email: z.string().email().optional(),
  currentPassword: z.string().optional(),
  newPassword: z.string().min(6).optional()
});

/**
 * 获取当前用户信息
 */
router.get('/me', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    console.log('👤 Users - Get user info request for:', req.user?.userId);

    const user = await prisma.user.findUnique({
      where: { id: req.user!.userId },
      select: {
        id: true,
        email: true,
        name: true,
        phoneNumber: true,
        role: true,
        createdAt: true,
        updatedAt: true,
        balance: true
      }
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    console.log('✅ Users - User info retrieved successfully');

    res.json({
      success: true,
      data: user
    });
  } catch (error: any) {
    console.error('❌ Users - Get user info failed:', error);
    res.status(500).json({
      success: false,
      message: '获取用户信息失败'
    });
  }
});

/**
 * 检查用户余额状态（预检查，不扣费）
 */
router.get('/balance-check', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    console.log('🔍 Users - Balance check request for:', req.user?.userId);

    // 获取用户余额信息
    let userBalance = await prisma.userBalance.findUnique({
      where: { userId: req.user!.userId }
    });

    // 如果用户没有余额记录，创建默认记录
    if (!userBalance) {
      userBalance = await prisma.userBalance.create({
        data: {
          userId: req.user!.userId,
          mockInterviewCredits: 0,
          formalInterviewCredits: 0,
          mianshijunBalance: 0
        }
      });
    }

    console.log('✅ Users - Balance check completed successfully');

    res.json({
      success: true,
      data: {
        mockInterviewCredits: userBalance.mockInterviewCredits,
        formalInterviewCredits: userBalance.formalInterviewCredits,
        mianshijunBalance: userBalance.mianshijunBalance,
        hasMockCredits: userBalance.mockInterviewCredits > 0,
        hasFormalCredits: userBalance.formalInterviewCredits > 0,
        updatedAt: userBalance.updatedAt
      }
    });
  } catch (error) {
    console.error('❌ Users - Balance check failed:', error);
    res.status(500).json({
      success: false,
      message: '获取余额状态失败'
    });
  }
});

/**
 * 获取用户余额信息
 */
router.get('/me/credits', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    console.log('💰 Users - Get user credits request for:', req.user?.userId);

    // 获取用户余额信息
    let userBalance = await prisma.userBalance.findUnique({
      where: { userId: req.user!.userId },
      select: {
        mockInterviewCredits: true,
        formalInterviewCredits: true,
        mianshijunBalance: true,
        updatedAt: true,
      },
    });

    // 如果用户余额记录不存在，创建一个默认记录
    if (!userBalance) {
      console.log('💰 Users - Creating default balance for user:', req.user!.userId);
      userBalance = await prisma.userBalance.create({
        data: {
          userId: req.user!.userId,
          mockInterviewCredits: 2, // 新用户默认2次模拟面试
          formalInterviewCredits: 0, // 新用户默认0次正式面试
          mianshijunBalance: 0,
        },
        select: {
          mockInterviewCredits: true,
          formalInterviewCredits: true,
          mianshijunBalance: true,
          updatedAt: true,
        },
      });
    }

    console.log('✅ Users - User credits retrieved successfully:', userBalance);

    res.json({
      success: true,
      credits: userBalance
    });
  } catch (error: any) {
    console.error('❌ Users - Get user credits failed:', error);
    res.status(500).json({
      success: false,
      message: '获取用户余额失败'
    });
  }
});

/**
 * 更新用户信息
 */
router.put('/me', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    console.log('👤 Users - Update user info request for:', req.user?.userId);
    const { name, email, currentPassword, newPassword } = updateUserSchema.parse(req.body);

    const userId = req.user!.userId;

    // 获取当前用户信息
    const currentUser = await prisma.user.findUnique({
      where: { id: userId }
    });

    if (!currentUser) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    // 准备更新数据
    const updateData: any = {};

    if (name !== undefined) {
      updateData.name = name;
    }

    if (email !== undefined) {
      // 检查邮箱是否已被其他用户使用
      const existingUser = await prisma.user.findFirst({
        where: {
          email,
          id: { not: userId }
        }
      });

      if (existingUser) {
        return res.status(400).json({
          success: false,
          message: '该邮箱已被其他用户使用'
        });
      }

      updateData.email = email;
    }

    // 如果要更新密码
    if (newPassword && currentPassword) {
      // 验证当前密码
      const isCurrentPasswordValid = await bcrypt.compare(currentPassword, currentUser.password);
      if (!isCurrentPasswordValid) {
        return res.status(400).json({
          success: false,
          message: '当前密码不正确'
        });
      }

      // 加密新密码
      updateData.password = await bcrypt.hash(newPassword, 12);
    }

    // 更新用户信息
    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: updateData,
      select: {
        id: true,
        email: true,
        name: true,
        phoneNumber: true,
        role: true,
        createdAt: true,
        updatedAt: true
      }
    });

    console.log('✅ Users - User info updated successfully');

    res.json({
      success: true,
      message: '用户信息更新成功',
      data: updatedUser
    });
  } catch (error: any) {
    console.error('❌ Users - Update user info failed:', error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数错误',
        errors: error.errors
      });
    }

    res.status(500).json({
      success: false,
      message: '更新用户信息失败'
    });
  }
});

/**
 * 删除用户账户
 */
router.delete('/me', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    console.log('👤 Users - Delete user request for:', req.user?.userId);

    const userId = req.user!.userId;

    // 删除用户相关数据（级联删除）
    await prisma.user.delete({
      where: { id: userId }
    });

    console.log('✅ Users - User deleted successfully');

    res.json({
      success: true,
      message: '账户删除成功'
    });
  } catch (error: any) {
    console.error('❌ Users - Delete user failed:', error);
    res.status(500).json({
      success: false,
      message: '删除账户失败'
    });
  }
});

/**
 * 获取用户统计信息
 */
router.get('/me/stats', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    console.log('📊 Users - Get user stats request for:', req.user?.userId);

    const userId = req.user!.userId;

    // 获取用户统计信息
    const [
      resumeCount,
      positionCount,
      interviewCount,
      feedbackCount
    ] = await Promise.all([
      prisma.resume.count({ where: { userId } }),
      prisma.targetPosition.count({ where: { userId } }),
      prisma.interviewSession.count({ where: { userId } }),
      prisma.feedback.count({ where: { userId } })
    ]);

    const stats = {
      resumeCount,
      positionCount,
      interviewCount,
      feedbackCount
    };

    console.log('✅ Users - User stats retrieved successfully:', stats);

    res.json({
      success: true,
      data: stats
    });
  } catch (error: any) {
    console.error('❌ Users - Get user stats failed:', error);
    res.status(500).json({
      success: false,
      message: '获取用户统计信息失败'
    });
  }
});

export default router;
