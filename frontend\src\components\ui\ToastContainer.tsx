import React from 'react';
import { createPortal } from 'react-dom';
import Toast from './Toast';
import { useToastContext } from '../../contexts/ToastContext';
import { Toast as ToastType } from '../../types/toast';

interface ToastContainerProps {
  toasts?: ToastType[];
  onRemoveToast?: (id: string) => void;
}

const ToastContainer: React.FC<ToastContainerProps> = ({ toasts: propToasts, onRemoveToast: propRemoveToast }) => {
  // 如果传入了props，使用props；否则使用context
  const context = propToasts ? null : useToastContext();
  const toasts = propToasts || context?.toasts || [];
  const removeToast = propRemoveToast || context?.removeToast || (() => {});

  // 如果没有Toast，不渲染任何内容
  if (toasts.length === 0) {
    return null;
  }

  // 使用Portal渲染到body，确保在最顶层，保持居中定位
  return createPortal(
    <div className="fixed inset-0 z-[9999] pointer-events-none">
      {toasts.map((toast, index) => (
        <div
          key={toast.id}
          className="pointer-events-auto"
          style={{
            position: 'absolute',
            top: `${20 + index * 80}px`,
            left: '50%',
            transform: `translateX(-50%)`,
            zIndex: 10000 + index
          }}
        >
          <Toast toast={toast} onRemove={removeToast} />
        </div>
      ))}
    </div>,
    document.body
  );
};

export default ToastContainer;
