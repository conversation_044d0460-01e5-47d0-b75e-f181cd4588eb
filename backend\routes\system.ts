import express, { Request, Response } from 'express';
import MonitoringService from '../services/monitoringService';
import SecurityService from '../services/securityService';
import fs from 'fs';
import path from 'path';

const router = express.Router();

/**
 * 计算系统健康分数
 */
function calculateHealthScore(health: any): number {
  let score = 0;
  let totalChecks = 0;

  // 检查各个服务状态
  const services = ['redis', 'database', 'email', 'sms'];
  
  services.forEach(service => {
    totalChecks++;
    if (health[service]) {
      score += 25; // 每个服务25分
    }
  });

  return Math.round(score);
}

/**
 * 系统健康检查
 */
router.get('/health', async (req: Request, res: Response) => {
  try {
    const monitoringService = new MonitoringService();
    const securityService = new SecurityService();

    // 获取系统健康状态
    const healthStatus = await monitoringService.healthCheck();
    
    // 获取系统统计信息
    const systemStats = await monitoringService.getSystemStats();
    
    // 获取安全统计信息
    const securityStats = await securityService.getSecurityStats();

    // 计算整体健康分数
    const healthScore = calculateHealthScore(healthStatus);

    const response = {
      success: true,
      data: {
        health: {
          ...healthStatus,
          score: healthScore,
          status: healthScore >= 80 ? 'healthy' : healthScore >= 60 ? 'warning' : 'critical'
        },
        statistics: systemStats,
        security: securityStats,
        timestamp: Date.now()
      }
    };

    // 根据健康状态设置HTTP状态码
    const httpStatus = healthScore >= 80 ? 200 : healthScore >= 60 ? 200 : 503;
    
    return res.status(httpStatus).json(response);

  } catch (error: any) {
    console.error('Health check error:', error);
    return res.status(500).json({
      success: false,
      message: '健康检查失败',
      error: { code: 'HEALTH_CHECK_ERROR' }
    });
  }
});

// 日志目录（与后端logger.ts保持一致）
const LOG_DIR = 'E:/Data/Own/Entrepreneurship/local-mianshijun/docs/development-plan/log';

// 确保日志目录存在
if (!fs.existsSync(LOG_DIR)) {
  fs.mkdirSync(LOG_DIR, { recursive: true });
}

// 生成前端日志文件名
const generateLogFileName = (): string => {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const hour = String(now.getHours()).padStart(2, '0');
  const minute = String(now.getMinutes()).padStart(2, '0');
  const second = String(now.getSeconds()).padStart(2, '0');

  return `frontend-${year}-${month}-${day}-${hour}-${minute}-${second}.txt`;
};

/**
 * 前端日志保存接口
 */
router.post('/frontend-logs', async (req: Request, res: Response) => {
  try {
    // 处理sendBeacon发送的数据（可能是字符串格式）
    let requestData;
    if (typeof req.body === 'string') {
      requestData = JSON.parse(req.body);
    } else {
      requestData = req.body;
    }

    const { logs, fileName } = requestData;

    if (!logs || !Array.isArray(logs)) {
      return res.status(400).json({ message: '日志数据格式错误' });
    }

    // 使用提供的文件名或生成新的文件名
    const logFileName = fileName || generateLogFileName();
    const logFilePath = path.join(LOG_DIR, logFileName);

    // 生成日志内容
    const logContent = [
      `Frontend Console Log`,
      `Generated: ${new Date().toISOString()}`,
      `Total entries: ${logs.length}`,
      '',
      '=== CONSOLE OUTPUT ===',
      '',
      ...logs
    ].join('\n');

    // 写入文件
    fs.writeFileSync(logFilePath, logContent, 'utf8');

    console.log(`前端日志已保存: ${logFilePath}`);

    res.json({
      success: true,
      message: '日志保存成功',
      fileName: logFileName,
      filePath: logFilePath
    });

  } catch (error) {
    console.error('保存前端日志失败:', error);
    res.status(500).json({
      success: false,
      message: '保存日志失败',
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
});

export default router;
