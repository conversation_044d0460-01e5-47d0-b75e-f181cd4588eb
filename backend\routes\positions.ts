import express, { Request, Response } from 'express';
import { authenticateToken, AuthenticatedRequest } from '../middleware/auth';
import prisma from '../lib/prisma';

const router = express.Router();

/**
 * 创建意向岗位
 */
router.post('/', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user!.userId;
    const { title, company, description, requirements, salary, location } = req.body;

    if (!title || !company) {
      return res.status(400).json({
        success: false,
        message: 'Title and company are required'
      });
    }

    const position = await prisma.targetPosition.create({
      data: {
        userId: userId,
        positionName: title,
        companyName: company,
        companyProfile: description || '',
        positionRequirements: requirements || '',
        status: 'active',
      },
    });

    // 转换字段名以保持API兼容性
    const formattedPosition = {
      id: position.id,
      title: position.positionName,
      company: position.companyName,
      description: position.companyProfile,
      requirements: position.positionRequirements,
      status: position.status,
      createdAt: position.createdAt,
      updatedAt: position.updatedAt,
    };

    return res.status(201).json({
      success: true,
      message: 'Position created successfully',
      data: { position: formattedPosition }
    });

  } catch (error: any) {
    console.error('Create position error:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to create position'
    });
  }
});

/**
 * 获取用户意向岗位列表
 */
router.get('/', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user!.userId;
    console.log('🔍 Positions - Starting query for userId:', userId);

    const positions = await prisma.targetPosition.findMany({
      where: { userId: userId },
      orderBy: { createdAt: 'desc' },
      select: {
        id: true,
        positionName: true,
        companyName: true,
        companyProfile: true,
        positionRequirements: true,
        status: true,
        createdAt: true,
        updatedAt: true,
      }
    });

    console.log('✅ Positions - Query successful, found', positions.length, 'positions');

    // 转换字段名以保持API兼容性
    const formattedPositions = positions.map(pos => ({
      id: pos.id,
      title: pos.positionName,
      company: pos.companyName,
      description: pos.companyProfile,
      requirements: pos.positionRequirements,
      status: pos.status,
      createdAt: pos.createdAt,
      updatedAt: pos.updatedAt,
    }));

    return res.json({
      success: true,
      data: { positions: formattedPositions }
    });

  } catch (error: any) {
    console.error('Get positions error:', error);
    console.error('Error details:', {
      name: error?.name,
      message: error?.message,
      stack: error?.stack
    });
    return res.status(500).json({
      success: false,
      message: 'Failed to retrieve positions'
    });
  }
});

/**
 * 获取单个意向岗位详情
 */
router.get('/:id', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user!.userId;
    const { id } = req.params;

    const position = await prisma.targetPosition.findFirst({
      where: {
        id: id,
        userId: userId, // 确保用户只能访问自己的岗位
      },
    });

    if (!position) {
      return res.status(404).json({
        success: false,
        message: 'Position not found'
      });
    }

    // 转换字段名以保持API兼容性
    const formattedPosition = {
      id: position.id,
      title: position.positionName,
      company: position.companyName,
      description: position.companyProfile,
      requirements: position.positionRequirements,
      status: position.status,
      createdAt: position.createdAt,
      updatedAt: position.updatedAt,
    };

    return res.json({
      success: true,
      data: { position: formattedPosition }
    });

  } catch (error: any) {
    console.error('Get position error:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to retrieve position'
    });
  }
});

/**
 * 更新意向岗位
 */
router.put('/:id', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user!.userId;
    const { id } = req.params;
    const { title, company, description, requirements, salary, location } = req.body;

    // 验证岗位是否属于当前用户
    const existingPosition = await prisma.targetPosition.findFirst({
      where: {
        id: id,
        userId: userId,
      },
    });

    if (!existingPosition) {
      return res.status(404).json({
        success: false,
        message: 'Position not found'
      });
    }

    // 更新岗位信息
    const updatedPosition = await prisma.targetPosition.update({
      where: { id: id },
      data: {
        ...(title !== undefined && { positionName: title }),
        ...(company !== undefined && { companyName: company }),
        ...(description !== undefined && { companyProfile: description }),
        ...(requirements !== undefined && { positionRequirements: requirements }),
      },
    });

    // 转换字段名以保持API兼容性
    const formattedPosition = {
      id: updatedPosition.id,
      title: updatedPosition.positionName,
      company: updatedPosition.companyName,
      description: updatedPosition.companyProfile,
      requirements: updatedPosition.positionRequirements,
      status: updatedPosition.status,
      createdAt: updatedPosition.createdAt,
      updatedAt: updatedPosition.updatedAt,
    };

    return res.json({
      success: true,
      message: 'Position updated successfully',
      data: { position: formattedPosition }
    });

  } catch (error: any) {
    console.error('Update position error:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to update position'
    });
  }
});

/**
 * 删除意向岗位
 */
router.delete('/:id', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user!.userId;
    const { id } = req.params;

    // 验证岗位是否属于当前用户
    const existingPosition = await prisma.targetPosition.findFirst({
      where: {
        id: id,
        userId: userId,
      },
    });

    if (!existingPosition) {
      return res.status(404).json({
        success: false,
        message: 'Position not found'
      });
    }

    // 删除岗位
    await prisma.targetPosition.delete({
      where: { id: id },
    });

    return res.json({
      success: true,
      message: 'Position deleted successfully'
    });

  } catch (error: any) {
    console.error('Delete position error:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to delete position'
    });
  }
});

export default router;
