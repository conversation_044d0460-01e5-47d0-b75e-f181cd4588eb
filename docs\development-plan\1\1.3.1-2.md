好的，我们来一步步完成 **任务 1.3: 用户登录功能**。这个任务的目标是让已注册的用户能够使用他们的邮箱和密码成功登录。

请严格按照下面的清单步骤操作。这些步骤会指导您修改和创建文件，安装必要的软件，并最终实现登录功能。

**项目根目录**: 除非特别说明，所有 `cd` (切换目录) 命令都是相对于您的项目总文件夹 `new-mianshijun` 来说的。

------

## 任务 1.3: 用户登录功能 - 操作清单

## Task 1.3: User Login Functionality - Checklist

------

### Part 0: 准备工作 (Preparations)

在开始编码之前，我们需要安装一些新的软件包，并设置一个重要的安全密钥。

1. **安装后端依赖 (Install Backend Dependencies)**:

   - 中文：打开 Cursor 的终端，确保您在 `api` 文件夹内，然后运行以下命令来安装用于生成和验证 JSON Web Tokens (JWT) 的包以及相关的类型定义。

   - English: Open the terminal in Cursor, make sure you are inside the `api` folder, then run the following commands to install packages for generating and verifying JSON Web Tokens (JWT) and their type definitions.

   - 操作 (Action):

     Bash

     ```
     cd api
     npm install jsonwebtoken bcryptjs
     npm install --save-dev @types/jsonwebtoken @types/bcryptjs
     cd ..
     ```

     - *说明*: `bcryptjs` 和 `@types/bcryptjs` 可能已经在任务 1.2 中安装过了，如果提示已存在，可以忽略。JWT 用于在用户登录后保持其登录状态。
     - *Explanation*: `bcryptjs` and `@types/bcryptjs` might have been installed in Task 1.2; if it says they already exist, you can ignore it. JWT is used to maintain user login status after they log in.

2. **安装前端依赖 (Install Frontend Dependencies)**:

   - 中文：打开 Cursor 的终端，确保您在 `frontend` 文件夹内，然后运行以下命令来安装用于解码 JWT 和管理应用状态的包 (`zustand`)。

   - English: Open the terminal in Cursor, make sure you are inside the `frontend` folder, then run the following command to install a package for decoding JWT and a package for state management (`zustand`).

   - 操作 (Action):

     Bash

     ```
     cd frontend
     npm install zustand jwt-decode
     cd ..
     ```

     - *说明*: `zustand` 是一个轻量级的状态管理库，我们将用它来管理用户的登录状态和 token。`jwt-decode` 用于方便地从 token 中解析出用户信息。
     - *Explanation*: `zustand` is a lightweight state management library we will use to manage the user's login status and token. `jwt-decode` is used to easily parse user information from the token.

3. **设置 JWT 密钥 (Set JWT Secret)**:

   - 中文：JWT 需要一个密钥来签名和验证 token。这是一个非常重要的安全设置。

   - English: JWT needs a secret key to sign and verify tokens. This is a very important security setting.

   - 操作 (Action):

     1. **创建/更新 `.env` 文件 (Create/Update `.env` file)**:

        - 中文：在 `new-mianshijun/api/` 文件夹中，找到或创建一个名为 `.env` 的文件。

        - English: In the `new-mianshijun/api/` folder, find or create a file named `.env`.

        - 中文：打开 `.env` 文件，添加以下内容 (如果已有 `DATABASE_URL`，请确保不要删除它):

        - English: Open the 

          ```
          .env
          ```

           file and add the following line (if you already have 

          ```
          DATABASE_URL
          ```

          , make sure not to delete it):

          代码段

          ```
          DATABASE_URL="your_neon_database_connection_string" # 这个应该已经存在 This should already exist
          JWT_SECRET="YOUR_VERY_STRONG_AND_SECRET_KEY_HERE" # 替换成一个复杂且随机的字符串 Replace this with a complex and random string
          ```

        - **重要提示**: 将 `"YOUR_VERY_STRONG_AND_SECRET_KEY_HERE"` 替换为您自己生成的一个长而随机的字符串。您可以使用密码生成器生成一个例如至少32个字符的随机字符串。**不要使用示例中的字符串！**

        - **IMPORTANT**: Replace `"YOUR_VERY_STRONG_AND_SECRET_KEY_HERE"` with your own long, random string. You can use a password generator to create a random string of at least 32 characters. **Do not use the example string!**

     2. **添加到 Vercel 环境变量 (Add to Vercel Environment Variables)**:

        - 中文：登录到您的 Vercel 账户。
        - English: Log in to your Vercel account.
        - 中文：选择您的 `new-mianshijun` 项目。
        - English: Select your `new-mianshijun` project.
        - 中文：进入 "Settings" -> "Environment Variables"。
        - English: Go to "Settings" -> "Environment Variables".
        - 中文：添加一个新的环境变量：
          - Key: `JWT_SECRET`
          - Value: (粘贴您在 `.env` 文件中使用的那个**相同的**复杂随机字符串) (Paste the **same** complex random string you used in your `.env` file)
          - 确保所有环境 (Production, Preview, Development) 都勾选了。
          - Ensure all environments (Production, Preview, Development) are checked.
        - 中文：点击 "Save"。
        - English: Click "Save".

------

### Part A: 后端实现 - 登录 API (Backend Implementation - Login API)

现在我们来创建处理用户登录请求的后端接口。

1. **创建登录 API 文件 (Create Login API File)**:

   - 中文：在 `new-mianshijun/api/auth/` 文件夹下，创建一个新文件，命名为 `login.ts`。
   - English: In the `new-mianshijun/api/auth/` folder, create a new file named `login.ts`.
   - 操作 (Action):
     - 右键点击 `api/auth` 文件夹 -> New File -> `login.ts`
     - Right-click on the `api/auth` folder -> New File -> `login.ts`

2. **编写登录 API 代码 (Write Login API Code)**:

   - 中文：打开 `new-mianshijun/api/auth/login.ts` 文件，然后粘贴以下全部代码：

   - English: Open the `new-mianshijun/api/auth/login.ts` file and paste the following entire code:

   - 操作 (Action):

     TypeScript

     ```
     import type { VercelRequest, VercelResponse } from '@vercel/node';
     import { PrismaClient } from '@prisma/client';
     import bcrypt from 'bcryptjs';
     import jwt from 'jsonwebtoken';
     import { z } from 'zod';
     
     const prisma = new PrismaClient();
     
     // 定义输入数据的校验模式
     // Define validation schema for input data
     const LoginSchema = z.object({
       email: z.string().email({ message: "请输入有效的邮箱地址 (Invalid email address)" }),
       password: z.string().min(1, { message: "密码不能为空 (Password cannot be empty)" }),
     });
     
     export default async function handler(req: VercelRequest, res: VercelResponse) {
       if (req.method !== 'POST') {
         res.setHeader('Allow', ['POST']);
         return res.status(405).json({ message: `Method ${req.method} Not Allowed` });
       }
     
       try {
         // 1. 校验输入数据
         // 1. Validate input data
         const validatedData = LoginSchema.safeParse(req.body);
         if (!validatedData.success) {
           return res.status(400).json({
             message: "输入数据校验失败 (Invalid input data)",
             errors: validatedData.error.flatten().fieldErrors,
           });
         }
     
         const { email, password }_ = validatedData.data;
     
         // 2. 检查用户是否存在
         // 2. Check if user exists
         const user = await prisma.user.findUnique({
           where: { email },
         });
     
         if (!user) {
           return res.status(401).json({ message: "邮箱或密码错误 (Invalid email or password)" });
         }
     
         // 3. 校验密码
         // 3. Validate password
         const isPasswordValid = await bcrypt.compare(password, user.passwordHash);
         if (!isPasswordValid) {
           return res.status(401).json({ message: "邮箱或密码错误 (Invalid email or password)" }); // 通用错误信息以避免用户枚举 General error message to prevent user enumeration
         }
     
         // 4. 生成 JWT
         // 4. Generate JWT
         const jwtSecret = process.env.JWT_SECRET;
         if (!jwtSecret) {
           console.error("JWT_SECRET is not defined in environment variables.");
           return res.status(500).json({ message: "服务器内部错误：JWT 密钥未配置 (Internal server error: JWT secret not configured)" });
         }
     
         const token = jwt.sign(
           { userId: user.id, email: user.email, name: user.name },
           jwtSecret,
           { expiresIn: '1d' } // Token 有效期1天 Token expires in 1 day
         );
     
         // 5. 返回 Token 和用户信息 (不包括密码哈希)
         // 5. Return Token and user information (excluding password hash)
         return res.status(200).json({
           message: "登录成功 (Login successful)",
           token,
           user: {
             id: user.id,
             email: user.email,
             name: user.name,
             // 可以添加其他需要返回给前端的用户信息
             // You can add other user information needed by the frontend
           },
         });
     
       } catch (error) {
         console.error('Login error:', error);
         // 记录更详细的错误日志，但返回通用错误信息给客户端
         // Log more detailed error, but return a generic error message to the client
         if (error instanceof z.ZodError) { // 如果是Zod校验错误
           return res.status(400).json({
             message: "请求数据格式错误 (Invalid request data format)",
             errors: error.flatten().fieldErrors,
           });
         }
         return res.status(500).json({ message: "服务器内部错误 (Internal server error)" });
       } finally {
         await prisma.$disconnect();
       }
     }
     ```

------

### Part B: 前端实现 - 登录页面与状态管理 (Frontend Implementation - Login Page & State Management)

现在我们来创建前端登录页面，并设置状态管理来保存用户的登录状态。

1. **更新 Zod 验证 Schema (Update Zod Validation Schema)**:

   - 中文：我们需要为登录表单添加验证规则。

   - English: We need to add validation rules for the login form.

   - 操作 (Action):

     - 打开 `new-mianshijun/frontend/src/lib/validations/auth.ts` 文件。

     - Open the `new-mianshijun/frontend/src/lib/validations/auth.ts` file.

     - 在 `registerSchema` 下方，添加以下 `loginSchema` 代码：

     - Below 

       ```
       registerSchema
       ```

       , add the following 

       ```
       loginSchema
       ```

        code:

       TypeScript

       ```
       // ... (原有的 registerSchema 代码保持不变) ...
       // ... (existing registerSchema code remains unchanged) ...
       
       export const loginSchema = z.object({
         email: z.string().email({ message: "请输入有效的邮箱地址。" }),
         password: z.string().min(1, { message: "密码不能为空。" }),
       });
       
       export type LoginFormData = z.infer<typeof loginSchema>;
       ```

2. **更新 API 调用函数 (Update API Call Functions)**:

   - 中文：我们需要添加一个函数来调用新的登录 API。

   - English: We need to add a function to call the new login API.

   - 操作 (Action):

     - 打开 `new-mianshijun/frontend/src/lib/api/auth.ts` 文件。

     - Open the `new-mianshijun/frontend/src/lib/api/auth.ts` file.

     - 在 `registerUser` 函数下方，添加以下 `loginUser` 函数代码：

     - Below the 

       ```
       registerUser
       ```

        function, add the following 

       ```
       loginUser
       ```

        function code:

       TypeScript

       ```
       // ... (原有的 registerUser 函数代码保持不变) ...
       // ... (existing registerUser function code remains unchanged) ...
       
       import type { LoginFormData } from '../validations/auth'; // 确保从正确路径导入 Ensure import from correct path
       
       // 定义登录成功后API返回的用户信息和token的类型
       // Define the type for user info and token returned by the API upon successful login
       export interface LoginResponse {
         message: string;
         token: string;
         user: {
           id: string;
           email: string;
           name: string | null;
         };
       }
       
       export async function loginUser(data: LoginFormData): Promise<LoginResponse> {
         const response = await fetch('/api/auth/login', { // 注意这里的路径是 /api/auth/login
           method: 'POST',
           headers: {
             'Content-Type': 'application/json',
           },
           body: JSON.stringify(data),
         });
       
         const result = await response.json();
       
         if (!response.ok) {
           // 后端应该返回一个包含 message 属性的 JSON 对象
           // Backend should return a JSON object with a message property
           throw new Error(result.message || '登录失败，请稍后再试。 (Login failed, please try again later.)');
         }
         return result as LoginResponse;
       }
       ```

3. **创建认证状态存储 (Create Authentication Store using Zustand)**:

   - 中文：我们将使用 Zustand 来管理用户的认证状态 (如 token 和用户信息)。

   - English: We will use Zustand to manage the user's authentication state (like token and user information).

   - 操作 (Action):

     1. 创建 `stores` 文件夹 (Create `stores` folder)

        :

        - 中文：在 `new-mianshijun/frontend/src/` 文件夹下，创建一个名为 `stores` 的新文件夹。
        - English: In the `new-mianshijun/frontend/src/` folder, create a new folder named `stores`.

     2. 创建 `authStore.ts` 文件 (Create `authStore.ts` file)

        :

        - 中文：在 `new-mianshijun/frontend/src/stores/` 文件夹下，创建一个新文件，命名为 `authStore.ts`。

        - English: In the `new-mianshijun/frontend/src/stores/` folder, create a new file named `authStore.ts`.

        - 粘贴以下全部代码到 `authStore.ts`：

        - Paste the following entire code into 

          ```
          authStore.ts
          ```

          :

          TypeScript

          ```
          import { create } from 'zustand';
          import { persist, createJSONStorage } from 'zustand/middleware';
          import { jwtDecode } from 'jwt-decode'; // 注意这里导入的是 jwt-decode
          
          interface User {
            id: string;
            email: string;
            name: string | null;
            // 可以根据JWT中实际包含的用户信息扩展
            // Can be extended based on actual user info in JWT
          }
          
          interface AuthState {
            token: string | null;
            user: User | null;
            isAuthenticated: boolean;
            login: (token: string) => void;
            logout: () => void;
            initializeAuth: () => void; // 新增：用于应用加载时检查localStorage
          }
          
          // 定义从JWT解码后的载荷类型
          // Define the type for the payload decoded from JWT
          interface DecodedToken {
            userId: string;
            email: string;
            name: string | null;
            iat: number;
            exp: number;
          }
          
          const useAuthStore = create<AuthState>()(
            persist(
              (set, get) => ({
                token: null,
                user: null,
                isAuthenticated: false,
                login: (token) => {
                  try {
                    const decoded = jwtDecode<DecodedToken>(token);
                    set({
                      token,
                      user: { id: decoded.userId, email: decoded.email, name: decoded.name },
                      isAuthenticated: true,
                    });
                  } catch (error) {
                    console.error("Failed to decode token:", error);
                    // 如果token无效，则执行登出逻辑
                    // If token is invalid, execute logout logic
                    get().logout();
                  }
                },
                logout: () => {
                  set({ token: null, user: null, isAuthenticated: false });
                },
                // 新增初始化函数
                // Added initialize function
                initializeAuth: () => {
                  const token = get().token; // 从持久化存储中获取token
                  if (token) {
                    try {
                      const decoded = jwtDecode<DecodedToken>(token);
                      // 检查token是否过期
                      // Check if token is expired
                      if (decoded.exp * 1000 > Date.now()) {
                        set({
                          token,
                          user: { id: decoded.userId, email: decoded.email, name: decoded.name },
                          isAuthenticated: true,
                        });
                      } else {
                        // Token 已过期，执行登出
                        // Token expired, execute logout
                        console.log("Token expired, logging out.");
                        get().logout();
                      }
                    } catch (error) {
                      console.error("Failed to initialize auth state from persisted token:", error);
                      get().logout(); // 如果解码失败也登出
                    }
                  }
                }
              }),
              {
                name: 'auth-storage', // localStorage 中的 key 名称 The key name in localStorage
                storage: createJSONStorage(() => localStorage), // 使用 localStorage Use localStorage
                partialize: (state) => ({ token: state.token }), // 只持久化 token Only persist the token
              }
            )
          );
          
          // 在应用加载时调用一次初始化函数，以恢复登录状态
          // Call initializeAuth once when the app loads to restore login state
          // 注意：这个立即执行的调用可能在某些SSR场景下有问题，但在纯CSR应用中通常可行
          // Note: This immediate call might be problematic in some SSR scenarios, but usually fine in pure CSR apps.
          // 更好的做法是在 App.tsx 或 main.tsx 中调用
          // Better practice is to call it in App.tsx or main.tsx
          // useAuthStore.getState().initializeAuth(); // 我们将在 App.tsx 中调用 We will call this in App.tsx
          
          export default useAuthStore;
          ```

4. **创建登录页面组件 (Create Login Page Component)**:

   - 中文：这个文件 `frontend/src/pages/LoginPage.tsx` 您之前可能已经创建过了。如果已存在，请用下面的代码**完全替换**其内容。如果不存在，请创建它。

   - English: You might have already created the file `frontend/src/pages/LoginPage.tsx`. If it exists, please **completely replace** its content with the code below. If it doesn't exist, create it.

   - 操作 (Action):

     - 确保文件路径为 `new-mianshijun/frontend/src/pages/LoginPage.tsx`。

     - Ensure the file path is `new-mianshijun/frontend/src/pages/LoginPage.tsx`.

     - 粘贴以下全部代码：

     - Paste the following entire code:

       TypeScript

       ```
       import React, { useState } from 'react';
       import { useForm } from 'react-hook-form';
       import { zodResolver } from '@hookform/resolvers/zod';
       import { useNavigate, Link } from 'react-router-dom';
       import { loginSchema, LoginFormData } from '../lib/validations/auth';
       import { loginUser } from '../lib/api/auth';
       import useAuthStore from '../stores/authStore';
       import InputField from '../components/ui/InputField'; // 确保路径正确 Ensure path is correct
       import Button from '../components/ui/Button'; // 确保路径正确 Ensure path is correct
       
       const LoginPage: React.FC = () => {
         const navigate = useNavigate();
         const { login: storeLogin } = useAuthStore();
         const [errorMessage, setErrorMessage] = useState<string | null>(null);
         const [isLoading, setIsLoading] = useState(false);
       
         const {
           register,
           handleSubmit,
           formState: { errors },
         } = useForm<LoginFormData>({
           resolver: zodResolver(loginSchema),
         });
       
         const onSubmit = async (data: LoginFormData) => {
           setIsLoading(true);
           setErrorMessage(null);
           try {
             const response = await loginUser(data);
             storeLogin(response.token); // 使用Zustand的login方法存储token和用户信息 Use Zustand's login method to store token and user info
             navigate('/dashboard'); // 登录成功后跳转到面试准备页 (骨架) Redirect to interview prep page (skeleton) after successful login
           } catch (error: any) {
             console.error('Login failed:', error);
             setErrorMessage(error.message || '登录失败，请检查您的邮箱和密码。 (Login failed, please check your email and password.)');
           } finally {
             setIsLoading(false);
           }
         };
       
         return (
           <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
             <div className="sm:mx-auto sm:w-full sm:max-w-md">
               <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
                 登录您的账户 (Sign in to your account)
               </h2>
               <p className="mt-2 text-center text-sm text-gray-600">
                 或 (Or){' '}
                 <Link to="/register" className="font-medium text-indigo-600 hover:text-indigo-500">
                   创建一个新账户 (create a new account)
                 </Link>
               </p>
             </div>
       
             <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
               <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
                 <form className="space-y-6" onSubmit={handleSubmit(onSubmit)}>
                   {errorMessage && (
                     <div className="p-3 bg-red-100 border border-red-400 text-red-700 rounded">
                       {errorMessage}
                     </div>
                   )}
       
                   <InputField
                     id="email"
                     type="email"
                     label="邮箱地址 (Email address)"
                     register={register('email')}
                     error={errors.email?.message}
                     autoComplete="email"
                   />
       
                   <InputField
                     id="password"
                     type="password"
                     label="密码 (Password)"
                     register={register('password')}
                     error={errors.password?.message}
                     autoComplete="current-password"
                   />
       
                   <div>
                     <Button type="submit" className="w-full" disabled={isLoading}>
                       {isLoading ? '登录中... (Signing in...)' : '登录 (Sign in)'}
                     </Button>
                   </div>
                 </form>
               </div>
             </div>
           </div>
         );
       };
       
       export default LoginPage;
       ```

5. **创建占位符仪表盘页面 (Create Placeholder Dashboard Page)**:

   - 中文：用户登录后需要跳转到一个页面。我们先创建一个简单的占位符页面。

   - English: After a user logs in, they need to be redirected to a page. Let's create a simple placeholder page first.

   - 操作 (Action):

     1. 在 `new-mianshijun/frontend/src/pages/` 文件夹下，创建一个新文件，命名为 `DashboardPage.tsx`。

     2. In the `new-mianshijun/frontend/src/pages/` folder, create a new file named `DashboardPage.tsx`.

     3. 粘贴以下全部代码到 `DashboardPage.tsx`：

     4. Paste the following entire code into 

        ```
        DashboardPage.tsx
        ```

        :

        TypeScript

        ```
        import React from 'react';
        import useAuthStore from '../stores/authStore'; // 确保路径正确
        import { Navigate, Link } from 'react-router-dom';
        
        const DashboardPage: React.FC = () => {
          const { isAuthenticated, user, logout } = useAuthStore();
        
          if (!isAuthenticated) {
            // 如果未认证，可以重定向到登录页
            // If not authenticated, can redirect to login page
            return <Navigate to="/login" replace />;
          }
        
          return (
            <div className="min-h-screen bg-gray-100 flex flex-col items-center justify-center p-4">
              <div className="bg-white shadow-md rounded-lg p-6 md:p-8 max-w-2xl w-full text-center">
                <h1 className="text-2xl sm:text-3xl font-bold text-gray-800 mb-4">
                  欢迎回来, {user?.name || user?.email}! (Welcome back, {user?.name || user?.email}!)
                </h1>
                <p className="text-gray-600 mb-6">
                  这里是您的面试准备仪表盘。(This is your interview preparation dashboard.)
                </p>
                <p className="text-gray-600 mb-2">您的邮箱: {user?.email} (Your email: {user?.email})</p>
                {user?.name && <p className="text-gray-600 mb-6">您的称呼: {user?.name} (Your name: {user?.name})</p>}
        
                {/* 这里可以放置仪表盘的其他内容 */}
                {/* Other dashboard content can be placed here */}
        
                <div className="mt-8">
                   <Link
                    to="/" // 或者其他您希望用户登出后去往的页面
                    onClick={logout}
                    className="w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 md:py-4 md:text-lg md:px-10"
                  >
                    登出 (Logout)
                  </Link>
                </div>
                <div className="mt-4">
                    <Link to="/" className="text-sm text-indigo-600 hover:text-indigo-500">
                        返回首页 (Go back to Home)
                    </Link>
                </div>
              </div>
            </div>
          );
        };
        
        export default DashboardPage;
        ```

6. **更新应用路由和初始化认证状态 (Update App Routing and Initialize Auth State)**:

   - 中文：我们需要在应用中添加新页面的路由，并在应用加载时检查用户是否已登录。

   - English: We need to add routes for the new pages in our application and check if the user is already logged in when the app loads.

   - 操作 (Action):

     - 打开 `new-mianshijun/frontend/src/App.tsx` 文件。

     - Open the `new-mianshijun/frontend/src/App.tsx` file.

     - **用以下完整代码替换 `App.tsx` 的全部内容**:

     - Replace the entire content of `App.tsx` with the following complete code

       :

       TypeScript

       ```
       import React, { useEffect } from 'react';
       import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
       import HomePage from './pages/HomePage'; // 假设您有这个页面 Assuming you have this page
       import RegisterPage from './pages/RegisterPage';
       import LoginPage from './pages/LoginPage';
       import DashboardPage from './pages/DashboardPage'; // 新的仪表盘页面 New Dashboard page
       import useAuthStore from './stores/authStore'; // 导入Zustand store Import Zustand store
       import Layout from './components/Layout'; // 假设您的Layout组件路径 Assuming your Layout component path
       
       // 一个简单的受保护路由组件
       // A simple protected route component
       const ProtectedRoute: React.FC<{ children: JSX.Element }> = ({ children }) => {
         const { isAuthenticated, isLoading } = useAuthStore(state => ({
           isAuthenticated: state.isAuthenticated,
           isLoading: state.token === null && typeof window !== 'undefined' // 初始加载时，如果token还没从localStorage恢复，则认为是加载中
         }));
       
         // 在Zustand从localStorage恢复状态之前，可以显示加载中或不渲染任何东西
         // Before Zustand restores state from localStorage, you can show loading or render nothing
         if (isLoading && useAuthStore.persist.hasHydrated() === false) {
            // hasHydrated() 可以用来判断是否已经从localStorage恢复完毕
            // 首次加载时，如果还没有水合（从localStorage恢复），则可能需要等待
            // 可以考虑显示一个全局的加载指示器
           return <div>Loading authentication state...</div>; // 或者 null
         }
       
         if (!isAuthenticated) {
           return <Navigate to="/login" replace />;
         }
         return children;
       };
       
       
       function App() {
         const { initializeAuth, isAuthenticated } = useAuthStore();
       
         useEffect(() => {
           // 应用加载时，尝试从 localStorage 恢复认证状态
           // When the app loads, try to restore authentication state from localStorage
           initializeAuth();
         }, [initializeAuth]);
       
         // 确保在 initializeAuth 完成之前，可以有一个加载状态
         // It's good practice to have a loading state until initializeAuth completes
         // For simplicity here, we directly use routes.
       
         return (
           <Router>
             <Layout> {/* 使用您的 Layout 组件包裹 Routes */}
               <Routes>
                 <Route path="/" element={<HomePage />} />
                 <Route path="/register" element={<RegisterPage />} />
                 <Route path="/login" element={isAuthenticated ? <Navigate to="/dashboard" replace /> : <LoginPage />} />
                 {/* DashboardPage 现在是受保护的路由。
                   只有认证过的用户才能访问。
                   DashboardPage is now a protected route.
                   Only authenticated users can access it.
                 */}
                 <Route
                   path="/dashboard"
                   element={
                     <ProtectedRoute>
                       <DashboardPage />
                     </ProtectedRoute>
                   }
                 />
                 {/* 您可以在这里添加更多路由 */}
                 {/* You can add more routes here */}
       
                 {/* 可以添加一个404页面或重定向到首页 */}
                 {/* You can add a 404 page or redirect to home for unmatched routes */}
                 <Route path="*" element={<Navigate to="/" replace />} />
               </Routes>
             </Layout>
           </Router>
         );
       }
       
       export default App;
       ```

     - 注意 (Note)

       :

       - 我们添加了 `/login` 和 `/dashboard` 路由。
       - `DashboardPage` 现在被一个 `ProtectedRoute` 组件包裹，这意味着只有登录的用户才能访问它。未登录用户访问 `/dashboard` 会被重定向到 `/login`。
       - 如果用户已经登录，访问 `/login` 页面会重定向到 `/dashboard`。
       - 我们调用了 `initializeAuth()` 来在应用启动时从 `localStorage` 加载认证状态。
       - 您可能需要根据您项目中 `HomePage.tsx` 和 `Layout.tsx` 的实际路径调整 `import` 语句。

7. **（可选）更新导航栏 (Optional - Update Navigation Bar)**:

   - 中文：如果您的 `Layout.tsx` 或 `Header.tsx` 中有导航链接，您可能希望根据用户的登录状态显示不同的链接 (例如，“登录/注册” 或 “仪表盘/登出”)。

   - English: If you have navigation links in your `Layout.tsx` or `Header.tsx`, you might want to display different links based on the user's login status (e.g., "Login/Register" or "Dashboard/Logout").

   - 操作 (Action):

     - 打开您的导航栏组件文件 (例如 `new-mianshijun/frontend/src/components/Header.tsx` 或 `Layout.tsx`)。

     - Open your navigation bar component file (e.g., `new-mianshijun/frontend/src/components/Header.tsx` or `Layout.tsx`).

     - 导入 `useAuthStore` 并使用 `isAuthenticated`, `user`, 和 `logout`。

     - Import `useAuthStore` and use `isAuthenticated`, `user`, and `logout`.

     - 这是一个如何在 `Header.tsx` 中修改的简单示例：

     - Here's a simple example of how you might modify it in 

       ```
       Header.tsx
       ```

       :

       TypeScript

       ```
       // 在 Header.tsx 或类似组件中
       // In Header.tsx or a similar component
       import React from 'react';
       import { Link } from 'react-router-dom';
       import useAuthStore from '../stores/authStore'; // 调整路径 Adjust path if necessary
       
       const Header: React.FC = () => {
         const { isAuthenticated, user, logout } = useAuthStore();
       
         return (
           <header className="bg-white shadow-sm">
             <nav className="container mx-auto px-4 py-4 flex justify-between items-center">
               <Link to="/" className="text-xl font-bold text-indigo-600">
                 面试君 (MianshiJun)
               </Link>
               <div>
                 {isAuthenticated ? (
                   <>
                     <span className="mr-4 text-gray-700">你好, {user?.name || user?.email}! (Hi, {user?.name || user?.email}!)</span>
                     <Link to="/dashboard" className="mr-4 text-gray-700 hover:text-indigo-600">
                       仪表盘 (Dashboard)
                     </Link>
                     <button
                       onClick={() => {
                         logout();
                         // 登出后可以导航到首页或登录页
                         // After logout, can navigate to home or login page
                         // navigate('/'); // 如果您在这里使用 navigate，确保从 react-router-dom 导入
                       }}
                       className="text-gray-700 hover:text-indigo-600"
                     >
                       登出 (Logout)
                     </button>
                   </>
                 ) : (
                   <>
                     <Link to="/login" className="mr-4 text-gray-700 hover:text-indigo-600">
                       登录 (Login)
                     </Link>
                     <Link to="/register" className="text-gray-700 hover:text-indigo-600">
                       注册 (Register)
                     </Link>
                   </>
                 )}
               </div>
             </nav>
           </header>
         );
       };
       
       export default Header;
       ```

     - 确保 `Header` 组件被包含在 `Layout` 中，并且 `Layout` 包裹了 `App.tsx` 中的 `Routes`。

------

### Part C: 测试 (Testing)

1. **启动开发服务器 (Start Development Servers)**:

   - 中文：您需要同时启动前端和后端的开发服务器。

   - English: You need to start both the frontend and backend development servers.

   - 操作 (Action):

     1. 启动后端 API (Start Backend API)

        :

        - 在 Cursor 的一个终端中，导航到项目根目录 `new-mianshijun`。
        - In one terminal in Cursor, navigate to the project root `new-mianshijun`.
        - 运行 `vercel dev` (或 `npm run dev:api` 如果您在根 `package.json` 中配置了这样的脚本)。
        - Run `vercel dev` (or `npm run dev:api` if you have such a script configured in your root `package.json`).
        - *它应该会显示 API 在本地运行的端口，例如 `http://localhost:3000`。*
        - *It should show the port where the API is running locally, e.g., `http://localhost:3000`.*

     2. 启动前端应用 (Start Frontend App)

        :

        - 在 Cursor 的另一个终端中，导航到 `new-mianshijun/frontend` 目录。
        - In another terminal in Cursor, navigate to the `new-mianshijun/frontend` directory.
        - 运行 `npm run dev`。
        - Run `npm run dev`.
        - *它通常会在 `http://localhost:5173` (或 Vite 默认的其他端口) 上运行。*
        - *It will usually run on `http://localhost:5173` (or another default Vite port).*

2. **测试注册新用户 (Test Registering a New User)**:

   - 中文：在浏览器中打开前端应用的地址 (例如 `http://localhost:5173`)。
   - English: Open the frontend application address in your browser (e.g., `http://localhost:5173`).
   - 中文：导航到 `/register` 页面。
   - English: Navigate to the `/register` page.
   - 中文：注册一个新用户。确保注册成功。
   - English: Register a new user. Ensure registration is successful.

3. **测试登录功能 (Test Login Functionality)**:

   - 中文：注册成功后，您可能会被重定向，或者手动导航到 `/login` 页面。

   - English: After successful registration, you might be redirected, or manually navigate to the `/login` page.

   - 中文：使用您刚刚注册的用户的邮箱和密码尝试登录。

   - English: Try to log in using the email and password of the user you just registered.

   - 预期行为 (Expected Behavior)

     :

     - 登录成功。
     - Successfully logged in.
     - 页面应跳转到 `/dashboard`。
     - The page should redirect to `/dashboard`.
     - Dashboard 页面应显示欢迎信息和用户信息。
     - The Dashboard page should display a welcome message and user information.
     - (高级) 打开浏览器开发者工具 (通常是 F12)，在 "Application" (或 "Storage") 标签页下查看 `localStorage`，您应该能看到 `auth-storage` 项，并且其中包含一个 `token`。
     - (Advanced) Open browser developer tools (usually F12), look under the "Application" (or "Storage") tab for `localStorage`. You should see an `auth-storage` item containing a `token`.

4. **测试登出功能 (Test Logout Functionality)**:

   - 中文：在 Dashboard 页面，点击“登出”按钮。

   - English: On the Dashboard page, click the "Logout" button.

   - 预期行为 (Expected Behavior)

     :

     - 您应该被重定向到登录页面或首页 (取决于您在 `DashboardPage.tsx` 和 `Header.tsx` 中的 `logout` 逻辑)。
     - You should be redirected to the login page or homepage (depending on your `logout` logic in `DashboardPage.tsx` and `Header.tsx`).
     - `localStorage` 中的 `token` 应该被清除。
     - The `token` in `localStorage` should be cleared.
     - 如果您尝试再次访问 `/dashboard`，应该被重定向回 `/login`。
     - If you try to visit `/dashboard` again, you should be redirected back to `/login`.

5. **测试错误处理 (Test Error Handling)**:

   - 中文：尝试使用错误的密码或不存在的邮箱登录。

   - English: Try logging in with an incorrect password or a non-existent email.

   - 预期行为 (Expected Behavior)

     :

     - 登录表单下方应显示相应的错误提示信息。
     - The login form should display an appropriate error message.

------

### Part D: 提交与部署 (Commit and Deploy)

一旦您在本地测试通过，就可以将代码提交到 GitHub 并部署到 Vercel。

1. **检查状态并添加更改 (Check status and add changes)**:

   - 中文：在 Cursor 的终端中，确保您位于**项目根目录 `new-mianshijun`**。

   - English: In the terminal in Cursor, make sure you are in the **root of your project directory `new-mianshijun`**.

   - 操作 (Action):

     Bash

     ```
     git status
     git add .
     ```

2. **提交更改 (Commit changes)**:

   - 操作 (Action):

     Bash

     ```
     git commit -m "Feat(auth): Implement Task 1.3 - User Login Functionality (frontend & backend)"
     ```

     - 中文：(提交信息意思是：“功能(认证)：实现任务1.3 - 用户登录功能 (前端和后端)”)
     - English: (This commit message means: "Feature(auth): Implement Task 1.3 - User Login Functionality (frontend & backend)")

3. **推送到 GitHub (Push to GitHub)**:

   - 操作 (Action):

     Bash

     ```
     git push origin master # 或者您的主分支名称，例如 main (or your main branch name, e.g., main)
     ```

4. **在 Vercel 上测试 (Test on Vercel)**:

   - 中文：推送到 GitHub 后，Vercel 应该会自动开始一个新的部署。

   - English: After pushing to GitHub, Vercel should automatically start a new deployment.

   - 中文：访问您的 Vercel 部署链接。

   - English: Visit your Vercel deployment link.

   - 中文：重复 **Part C: 测试 (Testing)** 中的所有测试步骤，确保在线上环境一切正常。

   - English: Repeat all testing steps from 

     Part C: Testing

      to ensure everything works correctly in the live environment.

     - 特别注意检查 `JWT_SECRET` 环境变量是否在 Vercel 上正确配置，否则登录会失败。
     - Pay special attention to ensure the `JWT_SECRET` environment variable is correctly configured on Vercel, otherwise login will fail.

------

请一步一步仔细操作。如果您在任何步骤遇到问题或不理解的地方，请随时提出。祝您编码顺利！