# 讯飞ASR连接管理修复进度

## 问题描述
当前系统出现 `iFlyTek RTASR error (10800): over max connect limit` 错误，原因是程序错误地将"一次说话结束"等同于"一次ASR连接结束"，导致频繁创建/断开连接，超出了讯飞服务的并发连接限制。

## 解决方案
将当前的全局单例连接管理改为会话级连接管理，实现真正的长连接模式。

## 已完成的修改

### 1. 重构IflytekConnectionManager类 ✅
- **文件**: `backend/websocket/interviewWs.ts` (行358-612)
- **修改内容**:
  - 将单例模式改为会话实例模式
  - 添加sessionId绑定到构造函数
  - 移除CONNECTION_TIMEOUT自动断开机制
  - 添加isDestroyed状态管理
  - 添加destroy()方法用于清理连接

### 2. 添加会话级ASR连接管理 ✅
- **文件**: `backend/websocket/interviewWs.ts` (行614-646)
- **修改内容**:
  - 创建sessionASRConnections Map存储每个会话的ASR连接
  - 添加getOrCreateASRManager()函数
  - 添加cleanupSessionASRConnection()函数

### 3. 修改callIflytekASR函数签名 ✅
- **文件**: `backend/websocket/interviewWs.ts` (行643)
- **修改内容**:
  - 改为接收sessionId参数: `callIflytekASR(audioBuffer: Buffer, sessionId: string)`
  - 使用会话级连接管理器

### 4. 更新所有ASR函数调用点 ✅
- **文件**: `backend/websocket/interviewWs.ts`
- **修改内容**:
  - transcribeAudio函数 (行974): 添加sessionId参数
  - transcribePCMAudio函数 (行1528): 添加sessionId参数
  - 所有callIflytekASR调用点都传入sessionId参数

### 5. 更新WebSocket连接生命周期管理 ✅
- **文件**: `backend/websocket/interviewWs.ts`
- **修改内容**:
  - 连接建立时 (行2143): 调用getOrCreateASRManager()初始化ASR连接
  - 连接断开时 (行2340): 调用cleanupSessionASRConnection()清理ASR连接
  - cleanupSessionAudioBuffer函数 (行1717): 确保ASR连接也被清理

### 6. 更新ASR服务基类 ✅
- **文件**: `backend/services/asrServiceBase.ts`
- **修改内容**:
  - RealASRService类 (行342): 修改asrFunction类型定义，添加sessionId参数
  - recognize方法 (行376): 从metadata中提取sessionId并传递给ASR函数

### 7. 更新流式ASR管理器 ✅
- **文件**: `backend/services/streamingASRManager.ts`
- **修改内容**:
  - performRecognition方法 (行364): 传递sessionId到metadata
  - performIncrementalRecognition方法 (行424): 传递sessionId到metadata
  - finalizeRecognition方法 (行467): 传递sessionId到metadata

## 核心修改逻辑

### 连接管理流程
1. **WebSocket连接建立** → 创建会话级ASR连接管理器
2. **音频处理** → 使用现有的会话ASR连接
3. **VAD静音检测** → 只处理文本，不影响连接状态
4. **WebSocket连接断开** → 销毁会话ASR连接

### 关键改进
- ✅ 每个WebSocket会话有独立的讯飞ASR连接
- ✅ 连接在会话开始时创建，会话结束时销毁
- ✅ VAD静音检测不会影响连接状态
- ✅ 彻底解决10800连接数超限错误

## 测试验证
- [ ] 启动后端服务，检查是否有编译错误
- [ ] 建立WebSocket连接，验证ASR连接是否正确创建
- [ ] 进行语音识别测试，确认不再出现10800错误
- [ ] 断开WebSocket连接，验证ASR连接是否正确清理
- [ ] 多个并发会话测试，确认连接隔离正确

## 预期效果
修改完成后，系统应该：
1. 不再出现 `iFlyTek RTASR error (10800): over max connect limit` 错误
2. 每个面试会话维护独立的ASR连接
3. 连接生命周期与WebSocket会话完全同步
4. VAD静音检测只影响文本处理，不影响连接状态
5. 系统整体稳定性和性能得到提升

## 状态
🟢 **修改完成** - 所有代码修改已完成，等待测试验证