import express, { Request, Response } from 'express';
import { authenticateToken, AuthenticatedRequest } from '../middleware/auth';
import prisma from '../lib/prisma';

const router = express.Router();

/**
 * 提交反馈
 */
router.post('/', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user!.userId;
    const { type, content, contact } = req.body;

    // 验证必填字段
    if (!content || typeof content !== 'string') {
      return res.status(400).json({
        success: false,
        message: 'Feedback content is required'
      });
    }

    // 验证反馈类型
    const validTypes = ['bug', 'feature', 'improvement', 'other'];
    if (type && !validTypes.includes(type)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid feedback type'
      });
    }

    // 创建反馈记录
    const feedback = await prisma.feedback.create({
      data: {
        userId: userId,
        type: type || 'other',
        content: content.trim(),
        contact: contact || '',
        status: 'pending',
        createdAt: new Date(),
      },
    });

    return res.status(201).json({
      success: true,
      message: 'Feedback submitted successfully',
      data: { feedback }
    });

  } catch (error: any) {
    console.error('Submit feedback error:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to submit feedback'
    });
  }
});

/**
 * 获取用户反馈列表
 */
router.get('/', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user!.userId;
    const { page = 1, limit = 10, status } = req.query;

    const skip = (Number(page) - 1) * Number(limit);
    
    // 构建查询条件
    const where: any = { userId: userId };
    if (status && status !== 'all') {
      where.status = status;
    }

    // 获取总数和数据
    const [total, feedbacks] = await Promise.all([
      prisma.feedback.count({ where }),
      prisma.feedback.findMany({
        where,
        skip,
        take: Number(limit),
        orderBy: { createdAt: 'desc' },
        select: {
          id: true,
          type: true,
          content: true,
          contact: true,
          status: true,
          response: true,
          createdAt: true,
          updatedAt: true,
        }
      })
    ]);

    return res.json({
      success: true,
      data: {
        feedbacks,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total,
          totalPages: Math.ceil(total / Number(limit))
        }
      }
    });

  } catch (error: any) {
    console.error('Get feedbacks error:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to retrieve feedbacks'
    });
  }
});

/**
 * 获取单个反馈详情
 */
router.get('/:id', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user!.userId;
    const { id } = req.params;

    const feedback = await prisma.feedback.findFirst({
      where: {
        id: id,
        userId: userId, // 确保用户只能访问自己的反馈
      },
    });

    if (!feedback) {
      return res.status(404).json({
        success: false,
        message: 'Feedback not found'
      });
    }

    return res.json({
      success: true,
      data: { feedback }
    });

  } catch (error: any) {
    console.error('Get feedback error:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to retrieve feedback'
    });
  }
});

/**
 * 更新反馈（用户只能更新内容和联系方式）
 */
router.put('/:id', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user!.userId;
    const { id } = req.params;
    const { content, contact } = req.body;

    // 验证反馈是否属于当前用户
    const existingFeedback = await prisma.feedback.findFirst({
      where: {
        id: id,
        userId: userId,
      },
    });

    if (!existingFeedback) {
      return res.status(404).json({
        success: false,
        message: 'Feedback not found'
      });
    }

    // 只允许更新pending状态的反馈
    if (existingFeedback.status !== 'pending') {
      return res.status(400).json({
        success: false,
        message: 'Cannot update feedback that has been processed'
      });
    }

    // 更新反馈
    const updatedFeedback = await prisma.feedback.update({
      where: { id: id },
      data: {
        ...(content !== undefined && { content: content.trim() }),
        ...(contact !== undefined && { contact }),
        updatedAt: new Date(),
      },
    });

    return res.json({
      success: true,
      message: 'Feedback updated successfully',
      data: { feedback: updatedFeedback }
    });

  } catch (error: any) {
    console.error('Update feedback error:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to update feedback'
    });
  }
});

/**
 * 删除反馈（只能删除pending状态的反馈）
 */
router.delete('/:id', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user!.userId;
    const { id } = req.params;

    // 验证反馈是否属于当前用户
    const existingFeedback = await prisma.feedback.findFirst({
      where: {
        id: id,
        userId: userId,
      },
    });

    if (!existingFeedback) {
      return res.status(404).json({
        success: false,
        message: 'Feedback not found'
      });
    }

    // 只允许删除pending状态的反馈
    if (existingFeedback.status !== 'pending') {
      return res.status(400).json({
        success: false,
        message: 'Cannot delete feedback that has been processed'
      });
    }

    // 删除反馈
    await prisma.feedback.delete({
      where: { id: id },
    });

    return res.json({
      success: true,
      message: 'Feedback deleted successfully'
    });

  } catch (error: any) {
    console.error('Delete feedback error:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to delete feedback'
    });
  }
});

export default router;
