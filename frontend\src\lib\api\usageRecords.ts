import { fetchWithAuth } from './apiService';

// 消费类型
export type UsageType = 'mock_interview' | 'formal_interview' | 'credit_recharge';

// 消耗记录接口
export interface UsageRecord {
  id: string;
  date: string;
  type: UsageType;
  amount: number; // 正数表示增加，负数表示消耗
  reason: string;
}

// 消耗记录列表响应接口
export interface UsageRecordsResponse {
  success: boolean;
  records: UsageRecord[];
  total: number;
}

// 获取消耗记录列表
export const getUsageRecords = async (params?: {
  page?: number;
  pageSize?: number;
  type?: UsageType | 'all';
}): Promise<UsageRecordsResponse> => {
  const searchParams = new URLSearchParams();
  
  if (params?.page) {
    searchParams.append('page', params.page.toString());
  }
  if (params?.pageSize) {
    searchParams.append('pageSize', params.pageSize.toString());
  }
  if (params?.type && params.type !== 'all') {
    searchParams.append('type', params.type);
  }

  const url = `/api/usage-records${searchParams.toString() ? `?${searchParams.toString()}` : ''}`;

  // fetchWithAuth 直接返回解析后的JSON数据
  return await fetchWithAuth<UsageRecordsResponse>(url, {
    method: 'GET'
  });
};
