import { Request, Response } from 'express';
import jwt from 'jsonwebtoken';
import prisma from '../lib/prisma';

const JWT_SECRET = process.env.JWT_SECRET || 'your_default_secret';

// 验证JWT并获取用户ID
const verifyToken = (req: Request): string | null => {
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }

  const token = authHeader.substring(7);
  try {
    const decoded = jwt.verify(token, JWT_SECRET) as any;
    return decoded.userId || decoded.id;
  } catch (error) {
    console.error('JWT verification failed:', error);
    return null;
  }
};

// 获取面试回顾数据
export const getInterviewReviewData = async (req: Request, res: Response) => {
  const { sessionId } = req.params;

  if (!sessionId) {
    return res.status(400).json({ error: 'Session ID is required' });
  }

  // 验证JWT认证
  const userId = verifyToken(req);
  if (!userId) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  try {
    // 检查面试会话是否存在且属于当前用户
    const interviewSession = await prisma.interviewSession.findUnique({
      where: { id: sessionId },
    });

    if (!interviewSession) {
      return res.status(404).json({ error: 'Interview session not found' });
    }

    if (interviewSession.userId !== userId) {
      return res.status(403).json({ error: 'Access denied' });
    }

    // 获取面试文字记录
    const transcripts = await prisma.interviewTranscript.findMany({
      where: { sessionId: sessionId },
      orderBy: { timestamp: 'asc' },
      select: {
        speaker: true,
        content: true,
        timestamp: true,
      },
    });

    // TODO: 获取 AI 建议 - 暂时注释，等待AISuggestion模型实现
    // const aiSuggestions = await prisma.aISuggestion.findMany({
    //   where: { sessionId: sessionId },
    //   orderBy: { timestamp: 'asc' },
    //   select: {
    //     question_text: true,
    //     suggestion_text: true,
    //     timestamp: true,
    //   },
    // });

    // 优先使用新字段，回退到titleJobInfo解析
    let position: string;
    let company: string;

    if (interviewSession.positionName && interviewSession.companyName) {
      // 使用新的独立字段
      position = interviewSession.positionName;
      company = interviewSession.companyName;
    } else {
      // 回退到解析titleJobInfo（向后兼容）
      const jobInfo = interviewSession.titleJobInfo || '未知岗位';
      [position, company] = jobInfo.includes(' - ')
        ? jobInfo.split(' - ')
        : [jobInfo, '未知公司'];
    }

    return res.status(200).json({
      sessionId: interviewSession.id,
      startedAt: interviewSession.startedAt,
      endedAt: interviewSession.endedAt,
      status: interviewSession.status,
      position: position, // 添加岗位信息
      company: company,   // 添加公司信息
      transcripts: transcripts.map(t => ({
        speaker: t.speaker,
        content: t.content,
        timestamp: t.timestamp.toISOString(),
      })),
      // aiSuggestions: aiSuggestions || [], // 暂时返回空数组
      aiSuggestions: [], // 暂时返回空数组
    });
  } catch (error) {
    console.error('Error fetching interview review data:', error);
    if (error instanceof Error) {
      return res.status(500).json({ error: `Failed to fetch interview review data: ${error.message}` });
    }
    return res.status(500).json({ error: 'Failed to fetch interview review data due to an unknown error' });
  }
};

// 获取用户面试记录列表
export const getInterviewRecords = async (req: Request, res: Response) => {
  // 验证JWT认证
  const userId = verifyToken(req);
  if (!userId) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  try {
    // 获取用户的所有面试会话
    const sessions = await prisma.interviewSession.findMany({
      where: { userId: userId },
      orderBy: { createdAt: 'desc' },
      select: {
        id: true,
        titleJobInfo: true,
        status: true,
        createdAt: true,
        startedAt: true,
        endedAt: true,
      },
    });

    // 转换数据格式以匹配前端期望的结构
    const records = sessions.map(session => {
      // 计算面试时长
      let duration = '未知';
      if (session.startedAt && session.endedAt) {
        const durationMs = session.endedAt.getTime() - session.startedAt.getTime();
        const minutes = Math.floor(durationMs / (1000 * 60));
        duration = `${minutes}分钟`;
      }

      // 优先使用新字段，回退到titleJobInfo解析
      let position: string;
      let company: string;

      if (session.positionName && session.companyName) {
        // 使用新的独立字段
        position = session.positionName;
        company = session.companyName;
      } else {
        // 回退到解析titleJobInfo（向后兼容）
        const jobInfo = session.titleJobInfo || '未知岗位';
        [position, company] = jobInfo.includes(' - ')
          ? jobInfo.split(' - ')
          : [jobInfo, '未知公司'];
      }

      return {
        id: session.id,
        position: position,
        company: company,
        date: session.createdAt.toLocaleDateString('zh-CN', {
          timeZone: 'Asia/Shanghai',
          year: 'numeric',
          month: '2-digit',
          day: '2-digit'
        }).replace(/\//g, '-'), // 转换为 YYYY-MM-DD 格式
        duration: duration,
        score: 85, // 默认评分，因为数据库中暂无评分字段
        status: session.status as 'completed' | 'in-progress' | 'cancelled',
      };
    });

    return res.status(200).json(records);
  } catch (error) {
    console.error('Error fetching interview records:', error);
    if (error instanceof Error) {
      return res.status(500).json({ error: `Failed to fetch interview records: ${error.message}` });
    }
    return res.status(500).json({ error: 'Failed to fetch interview records due to an unknown error' });
  }
};
