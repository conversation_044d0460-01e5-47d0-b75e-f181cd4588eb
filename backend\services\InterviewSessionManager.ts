import WebSocket from 'ws';
import { SessionIATManager } from './sessionIATManager';
import axios from 'axios';

/**
 * 面试会话管理器 - 核心智能逻辑处理器
 * 负责实现3秒静音检测、60秒自动分段、智能文本拼接
 */
export class InterviewSessionManager {
  private sessionId: string;
  private clientWs: WebSocket;
  private iatManager!: SessionIATManager;
  private accumulatedText: string = '';
  private currentASRSessionId: string = ''; // 🔥 修复：存储当前ASR会话ID
  private maxDurationTimeout: NodeJS.Timeout | null = null;
  private isDestroyed: boolean = false;

  // 配置常量
  private readonly MAX_SPEECH_DURATION = 60 * 1000; // 60秒软切分
  private readonly SILENCE_DETECTION_TIME = 3000;   // 3秒静音检测
  private readonly MAX_RETRIES = 2;                 // 最大重试次数

  // 🔥 新增：音频缓冲机制
  private audioBuffer: Buffer[] = [];               // 音频缓冲区
  private readonly MAX_BUFFER_SIZE = 30;            // 最大缓冲30个音频块
  private readonly BUFFER_DURATION_MS = 3000;      // 缓冲3秒音频数据

  constructor(sessionId: string, clientWs: WebSocket) {
    this.sessionId = sessionId;
    this.clientWs = clientWs;
    
    console.log(`[${this.sessionId}] 🎯 InterviewSessionManager created`);
    
    // 初始化IAT管理器
    this.initializeIATManager();
  }

  /**
   * 初始化IAT管理器
   */
  private initializeIATManager(): void {
    this.iatManager = new SessionIATManager({
      APPID: process.env.IFLYTEK_APPID || '67c6992f',
      API_KEY: process.env.IFLYTEK_API_KEY || 'b7c48cb2f02e4c13cb747a2f4f75e1e9',
      API_SECRET: process.env.IFLYTEK_API_SECRET || 'NWYxMWY0MmRkYjA5NGM0ZDVjNzk3N2U2',
      HOST_URL: 'wss://iat-api.xfyun.cn/v2/iat'
    });

    this.createNewASRSession();
  }

  /**
   * 创建新的ASR会话（改进的串行执行版本）
   */
  private async createNewASRSession(): Promise<void> {
    if (this.isDestroyed) return;

    try {
      // 🔥 修复：确保旧会话完全关闭后再创建新会话
      if (this.currentASRSessionId) {
        console.log(`[${this.sessionId}] 🧹 Closing existing ASR session: ${this.currentASRSessionId}`);
        await this.iatManager.closeSession(this.currentASRSessionId);
        // 等待一小段时间确保会话完全关闭
        await new Promise(resolve => setTimeout(resolve, 500));
      }

      // 🔥 修复：生成并存储当前ASR会话ID
      this.currentASRSessionId = `${this.sessionId}_${Date.now()}`;

      await this.iatManager.createSession(
        this.currentASRSessionId,
        // onTranscription: 处理中间识别结果
        (text: string, isFinal: boolean) => {
          this.handleTranscriptionResult(text, isFinal);
        },
        // onLLMTrigger: 处理最终结果（3秒静音后触发）
        (text: string) => {
          this.handleFinalResult(text);
        },
        // onError: 处理错误
        (error: Error) => {
          console.error(`[${this.sessionId}] ❌ ASR error:`, error);
          // 🔥 修复：对invalid handle错误进行特殊处理
          if (error.message.includes('invalid handle')) {
            console.log(`[${this.sessionId}] 🔄 Detected invalid handle, recreating session...`);
            setTimeout(() => this.createNewASRSession(), 2000);
          }
        }
      );

      // 启动60秒计时器（已禁用）
      this.startMaxDurationTimer();

      console.log(`[${this.sessionId}] ✅ New ASR session created: ${this.currentASRSessionId}`);
    } catch (error) {
      console.error(`[${this.sessionId}] ❌ Failed to create ASR session:`, error);
    }
  }

  /**
   * 处理音频数据 - 增强版本（带音频缓冲）
   */
  public async handleAudioChunk(audioBuffer: Buffer): Promise<void> {
    if (this.isDestroyed) return;

    // 🔥 新增：将音频数据加入缓冲区
    this.audioBuffer.push(audioBuffer);

    // 限制缓冲区大小，保持最近的音频数据
    if (this.audioBuffer.length > this.MAX_BUFFER_SIZE) {
      this.audioBuffer.shift(); // 移除最旧的音频数据
    }

    try {
      // 🔥 修复：使用正确的当前ASR会话ID
      await this.iatManager.sendAudio(this.currentASRSessionId, audioBuffer);
    } catch (error) {
      console.error(`[${this.sessionId}] ❌ Error sending audio:`, error);
    }
  }

  /**
   * 🔥 新增：重新发送缓冲区中的音频数据
   */
  private async resendBufferedAudio(): Promise<void> {
    if (this.audioBuffer.length === 0) {
      console.log(`[${this.sessionId}] 📦 No buffered audio to resend`);
      return;
    }

    console.log(`[${this.sessionId}] 🔄 Resending ${this.audioBuffer.length} buffered audio chunks`);

    // 复制缓冲区数据，避免在发送过程中被修改
    const bufferedAudio = [...this.audioBuffer];

    // 逐个发送缓冲的音频数据，添加适当的延迟
    for (let i = 0; i < bufferedAudio.length; i++) {
      try {
        await this.iatManager.sendAudio(this.currentASRSessionId, bufferedAudio[i]);

        // 添加小延迟，避免发送过快
        if (i < bufferedAudio.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 50));
        }
      } catch (error) {
        console.error(`[${this.sessionId}] ❌ Error resending buffered audio chunk ${i}:`, error);
        break; // 如果发送失败，停止发送剩余数据
      }
    }

    console.log(`[${this.sessionId}] ✅ Finished resending buffered audio`);
  }

  /**
   * 处理转录结果（中间结果和最终结果）
   */
  private handleTranscriptionResult(text: string, isFinal: boolean): void {
    console.log(`[${this.sessionId}] 🎯 handleTranscriptionResult called:`, {
      text: text,
      isFinal: isFinal,
      isDestroyed: this.isDestroyed,
      timestamp: new Date().toISOString()
    });

    if (this.isDestroyed) {
      console.log(`[${this.sessionId}] ⚠️ handleTranscriptionResult: Session is destroyed, skipping`);
      return;
    }

    // 构建要发送的消息
    const message = {
      type: 'transcription',
      text: text,
      isFinal: isFinal,
      timestamp: Date.now(),
      service: 'iflytek_iat'
    };

    console.log(`[${this.sessionId}] 📤 Preparing to send transcription message:`, {
      messageType: message.type,
      textLength: message.text.length,
      textPreview: message.text.substring(0, 50) + (message.text.length > 50 ? '...' : ''),
      isFinal: message.isFinal,
      service: message.service,
      resultType: isFinal ? 'FINAL' : 'INTERMEDIATE'
    });

    // 发送结果到前端（蓝色气泡）
    this.sendToClient(message);

    // 🔥 Phase 1修复：如果是最终结果，触发LLM处理并准备创建新的ASR会话
    if (isFinal) {
      console.log(`[${this.sessionId}] 🎯 Final result processed, triggering LLM and preparing for next sentence`);

      // 🔥 关键修复：立即触发LLM处理
      setTimeout(async () => {
        if (!this.isDestroyed) {
          try {
            console.log(`[${this.sessionId}] 🤖 Triggering LLM with final text: "${text}"`);
            await this.sendToLLM(text.trim());
            console.log(`[${this.sessionId}] ✅ LLM processing completed for: "${text}"`);
          } catch (error) {
            console.error(`[${this.sessionId}] ❌ LLM processing failed:`, error);
          }
        }
      }, 100); // 100ms延迟确保前端接收到最终结果

      // 延迟创建新会话，确保当前结果完全处理完毕
      setTimeout(() => {
        if (!this.isDestroyed) {
          console.log(`[${this.sessionId}] 🔄 Creating new ASR session for next sentence`);
          this.createNewASRSession();
        }
      }, 500); // 500ms延迟确保LLM处理和前端处理都完成
    }
  }

  /**
   * 处理最终结果（3秒静音后触发）- 已弃用，现在通过handleTranscriptionResult处理
   */
  private async handleFinalResult(finalText: string): Promise<void> {
    if (this.isDestroyed) return;

    console.log(`[${this.sessionId}] 🎯 Legacy final result received (deprecated): "${finalText}"`);
    console.log(`[${this.sessionId}] ⚠️ This method is deprecated, final results now handled via handleTranscriptionResult with isFinal=true`);

    // 🔥 修改：现在最终结果通过handleTranscriptionResult处理，这里只做兜底处理
    // 如果由于某种原因，最终结果没有通过正常渠道处理，这里作为备用
    if (finalText && finalText.trim().length > 0) {
      console.log(`[${this.sessionId}] 🔄 Processing legacy final result as backup`);

      // 发送给LLM
      await this.sendToLLM(finalText.trim());
    }
  }

  /**
   * 启动60秒计时器（已禁用 - 使用单一长连接会话）
   */
  private startMaxDurationTimer(): void {
    // 🔥 修复：禁用60秒自动切换，使用单一长连接会话
    console.log(`[${this.sessionId}] ⏰ Using single long-lived session (60s timer disabled)`);
  }

  /**
   * 停止60秒计时器
   */
  private stopMaxDurationTimer(): void {
    if (this.maxDurationTimeout) {
      clearTimeout(this.maxDurationTimeout);
      this.maxDurationTimeout = null;
      console.log(`[${this.sessionId}] ⏰ 60-second timer stopped`);
    }
  }

  /**
   * 处理60秒超时（已禁用 - 使用单一长连接会话）
   */
  private async handleMaxDuration(): Promise<void> {
    // 🔥 修复：禁用60秒软切分，使用单一长连接会话
    console.log(`[${this.sessionId}] ⏱️ 60-second timeout disabled (using single session)`);
  }

  /**
   * 发送消息到客户端
   */
  private sendToClient(message: any): void {
    console.log(`[${this.sessionId}] 🔍 sendToClient called:`, {
      messageType: message.type,
      isDestroyed: this.isDestroyed,
      wsReadyState: this.clientWs.readyState,
      wsReadyStateText: this.getWebSocketStateText(this.clientWs.readyState),
      timestamp: new Date().toISOString()
    });

    if (this.isDestroyed) {
      console.log(`[${this.sessionId}] ⚠️ sendToClient: Session is destroyed, cannot send message`);
      return;
    }

    if (this.clientWs.readyState !== WebSocket.OPEN) {
      console.log(`[${this.sessionId}] ⚠️ sendToClient: WebSocket not open, state: ${this.getWebSocketStateText(this.clientWs.readyState)}`);
      return;
    }

    try {
      const messageString = JSON.stringify(message);
      console.log(`[${this.sessionId}] 📤 Sending message to client:`, {
        messageType: message.type,
        messageSize: messageString.length,
        messagePreview: messageString.substring(0, 200) + (messageString.length > 200 ? '...' : '')
      });

      this.clientWs.send(messageString);

      console.log(`[${this.sessionId}] ✅ Message sent successfully to client:`, {
        messageType: message.type,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error(`[${this.sessionId}] ❌ Error sending to client:`, {
        error: error,
        messageType: message.type,
        wsReadyState: this.clientWs.readyState,
        wsReadyStateText: this.getWebSocketStateText(this.clientWs.readyState)
      });
    }
  }

  /**
   * 获取WebSocket状态文本描述
   */
  private getWebSocketStateText(readyState: number): string {
    switch (readyState) {
      case WebSocket.CONNECTING: return 'CONNECTING(0)';
      case WebSocket.OPEN: return 'OPEN(1)';
      case WebSocket.CLOSING: return 'CLOSING(2)';
      case WebSocket.CLOSED: return 'CLOSED(3)';
      default: return `UNKNOWN(${readyState})`;
    }
  }

  /**
   * 发送完整文本给LLM
   */
  private async sendToLLM(text: string): Promise<void> {
    if (!text.trim()) return;

    console.log(`[${this.sessionId}] 🤖 Sending to LLM: "${text}"`);

    // 发送最终转录结果给前端
    this.sendToClient({
      type: 'final_transcription',
      data: text,
      timestamp: Date.now()
    });

    // 🔥 修复：直接调用LLM服务
    await this.callDeepSeekLLM(text);
  }

  /**
   * 检查API密钥是否存在
   * @returns API密钥或抛出错误
   */
  private checkApiKey(): string {
    const apiKey = process.env.DEEPSEEK_API_KEY;
    if (!apiKey) {
      const error = new Error('DeepSeek API key is missing. Please check your environment variables.');
      console.error(`[${this.sessionId}] ❌ ${error.message}`);
      this.sendToClient({ 
        type: 'ai_suggestion_error', 
        message: '系统配置错误：LLM API密钥未设置。' 
      });
      throw error;
    }
    return apiKey;
  }

  /**
   * 使用重试逻辑调用函数
   * @param fn 需要执行的异步函数
   * @returns 函数执行结果
   */
  private async callWithRetry<T>(fn: () => Promise<T>): Promise<T> {
    let retryCount = 0;
    let lastError: Error | null = null;

    while (retryCount <= this.MAX_RETRIES) {
      try {
        if (retryCount > 0) {
          console.log(`[${this.sessionId}] 🔄 Retry attempt ${retryCount}/${this.MAX_RETRIES}`);
          // 指数退避策略
          const retryDelay = Math.pow(2, retryCount) * 1000;
          await new Promise(resolve => setTimeout(resolve, retryDelay));
        }
        return await fn();
      } catch (error: any) {
        lastError = error;
        retryCount++;
        
        // 详细记录错误信息
        const errorResponse = error.response ? error.response.data : null;
        const errorStatus = error.response ? error.response.status : null;
        const errorMessage = error.message || "Unknown error";
        
        console.error(`[${this.sessionId}] ❌ API Error (attempt ${retryCount}/${this.MAX_RETRIES + 1}):`);
        console.error(`  Status: ${errorStatus || 'N/A'}`);
        console.error(`  Message: ${errorMessage}`);
        
        if (errorResponse) {
          try {
            // 尝试读取流式响应中的错误
            if (errorResponse.readable) {
              const chunks = [];
              for await (const chunk of errorResponse) {
                chunks.push(chunk);
              }
              const responseText = Buffer.concat(chunks).toString('utf8');
              console.error(`  Response: ${responseText}`);
            } else if (typeof errorResponse === 'object') {
              console.error(`  Response: ${JSON.stringify(errorResponse)}`);
            }
          } catch (e) {
            console.error(`  Error parsing response: ${e}`);
          }
        }
      }
    }

    // 所有重试都失败了
    console.error(`[${this.sessionId}] ❌ All API attempts failed after ${this.MAX_RETRIES + 1} tries`);
    if (lastError) throw lastError;
    throw new Error('All API attempts failed');
  }

  /**
   * 调用DeepSeek LLM服务
   */
  private async callDeepSeekLLM(userText: string): Promise<void> {
    if (!userText.trim() || userText.length <= 2 || !/[\u4e00-\u9fa5a-zA-Z0-9]/.test(userText)) {
      console.log(`[${this.sessionId}] ⚠️ Skipping LLM call - text too short or meaningless: "${userText}"`);
      return;
    }

    console.log(`[${this.sessionId}] 🤖 Generating AI suggestion: "${userText}"`);

    const messagesForLLM = [
      {
        role: 'system',
        content: `你是一位参加面试的应聘者。请以自然、口语化的方式回答面试官的问题。

        回答要求：
        1. 回答要简洁明了，控制在2-3句话内
        2. 语言要自然流畅，像真实对话一样
        3. 根据问题类型给出针对性回答
        4. 避免过于正式或模板化的表达
        5. 可以适当表达个人观点和经验`
      },
      {
        role: 'user',
        content: `面试官说："${userText}"。请给出合适的回答建议。`
      }
    ];

    try {
      // 验证API密钥是否存在
      const apiKey = this.checkApiKey();

      // 使用重试逻辑调用API
      await this.callWithRetry(async () => {
        console.log(`[${this.sessionId}] 🔄 Calling DeepSeek API`);
        
        const response = await axios.post(
          'https://api.deepseek.com/v1/chat/completions',
          {
            model: "deepseek-chat",
            messages: messagesForLLM,
            stream: true,
          },
          {
            headers: {
              'Authorization': `Bearer ${apiKey}`,
              'Content-Type': 'application/json',
              'Accept': 'text/event-stream'
            },
            responseType: 'stream',
            timeout: 30000 // 30秒超时
          }
        );

        let accumulatedSuggestion = "";
        let hasReceivedContent = false;

        for await (const chunk of response.data) {
          const lines = chunk.toString('utf8').split('\n\n');
          for (const line of lines) {
            if (line.startsWith('data: ')) {
              const jsonData = line.substring(6);
              if (jsonData.trim() === '[DONE]') {
                this.sendToClient({ type: 'ai_suggestion_end' });
                console.log(`[${this.sessionId}] ✅ LLM suggestion completed: "${accumulatedSuggestion}"`);
                return;
              }
              try {
                const parsed = JSON.parse(jsonData);
                if (parsed.choices && parsed.choices[0].delta && parsed.choices[0].delta.content) {
                  const contentChunk = parsed.choices[0].delta.content;
                  accumulatedSuggestion += contentChunk;
                  this.sendToClient({ type: 'ai_suggestion_chunk', text: contentChunk });
                  hasReceivedContent = true;
                }
              } catch (e) {
                console.error(`[${this.sessionId}] Error parsing DeepSeek stream chunk:`, e);
              }
            }
          }
        }

        // 检查是否收到了任何内容
        if (!hasReceivedContent) {
          throw new Error("API返回了空响应");
        }
      });
    } catch (error) {
      console.error(`[${this.sessionId}] ❌ Failed to get LLM response:`, error);
      
      // 发送错误消息到客户端
      this.sendToClient({ 
        type: 'ai_suggestion_error', 
        message: '无法获取AI建议，请稍后再试。' 
      });
      
      // 使用备用响应
      this.sendFallbackResponse(userText);
    }
  }

  /**
   * 发送备用响应
   */
  private sendFallbackResponse(userText: string): void {
    try {
      // 简单的备用响应
      const fallbackResponse = "抱歉，我现在无法给出建议。请继续您的面试，我会尽快恢复服务。";
      
      // 逐字符发送备用响应，模拟流式响应
      const chars = fallbackResponse.split('');
      let index = 0;
      
      const sendNextChar = () => {
        if (index < chars.length) {
          this.sendToClient({ 
            type: 'ai_suggestion_chunk', 
            text: chars[index],
            isFallback: true
          });
          index++;
          setTimeout(sendNextChar, 50); // 每50ms发送一个字符
        } else {
          this.sendToClient({ type: 'ai_suggestion_end', isFallback: true });
        }
      };
      
      // 开始发送
      sendNextChar();
      
    } catch (error) {
      console.error(`[${this.sessionId}] ❌ Error sending fallback response:`, error);
    }
  }

  /**
   * 销毁会话管理器
   */
  public async destroy(): Promise<void> {
    if (this.isDestroyed) return;

    console.log(`[${this.sessionId}] 🧹 Destroying InterviewSessionManager`);
    
    this.isDestroyed = true;
    this.stopMaxDurationTimer();

    try {
      // 处理任何剩余的累积文本
      if (this.accumulatedText.trim()) {
        await this.sendToLLM(this.accumulatedText.trim());
      }

      // 销毁IAT管理器
      await this.iatManager.destroy();
    } catch (error) {
      console.error(`[${this.sessionId}] ❌ Error during destruction:`, error);
    }
  }
}
