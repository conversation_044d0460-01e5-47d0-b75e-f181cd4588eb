好的！我们一步一步来完成开发计划中的 **任务 1.1: 项目初始化与基础配置**。这个任务的目标是把项目的基础架子搭起来。

我会提供中文和英文的对照，方便您在Cursor中操作。您提到您的 GitHub 账号是 `<EMAIL>`，项目仓库名称暂定为 `new-mianshijun`。太棒了，这些信息很有用！

**重要前提 (Important Prerequisites):**

- **Node.js 和 npm (或 yarn)**: 请确保您的电脑上安装了它们。Node.js 通常会自带 npm。您可以在终端输入 `node -v` 和 `npm -v` 来检查。 (Make sure you have Node.js and npm (or yarn) installed. You can check by typing `node -v` and `npm -v` in your terminal.)
- **Git**: 请确保安装了 Git。您可以在终端输入 `git --version` 来检查。 (Make sure you have Git installed. You can check by typing `git --version` in your terminal.)
- **Cursor (或 VS Code)**: 您选择的开发工具。 (Your chosen development tool, Cursor or VS Code.)
- **浏览器 (Browser)**: 如 Chrome, Firefox, Edge 等。 (A web browser like Chrome, Firefox, Edge, etc.)

------

## 任务 1.1: 项目初始化与基础配置 - 操作清单

## Task 1.1: Project Initialization & Basic Configuration - Checklist

------

### 零、准备工作区 (Workspace Preparation)

1. **创建主项目文件夹 (Create a main project directory):**

   - 在你电脑上找一个合适的位置，创建一个总的文件夹来存放你的前端和后端项目。比如，可以命名为 `MianshiJun_AI_Project`。 (On your computer, find a suitable location and create a main directory to hold both your frontend and backend projects. For example, name it `MianshiJun_AI_Project`.)
   - **操作 (Action):** 使用文件管理器手动创建这个文件夹。 (Manually create this folder using your file explorer.)

2. **将现有的 `project` 文件夹移动或复制到主项目文件夹内 (Move or copy your existing `project` folder into this main directory):**

   - 你之前上传的 `project` 文件夹就是你的前端项目。我们把它重命名为 `frontend` 以明确区分。 (Your previously uploaded `project` folder will be your frontend. Let's rename it to `frontend` for clarity.)

   - 操作 (Action):

     1. 找到你现有的 `project` 文件夹。 (Locate your existing `project` folder.)
     2. 将其**复制**或**移动**到刚刚创建的 `MianshiJun_AI_Project` 文件夹内。 (Copy or move it into the `MianshiJun_AI_Project` folder you just created.)
     3. 将其重命名为 `frontend`。 (Rename it to `frontend`.)

     - 现在的结构应该是 (Your structure should now be):

       ```
       MianshiJun_AI_Project/
       └── frontend/         <-- 这是你之前的 "project" 文件夹 (This is your previous "project" folder)
           ├── src/
           ├── package.json
           └── ... 其他文件 (other files) ...
       ```

------

### Part A: GitHub 设置 (GitHub Setup)

1. **登录 GitHub (Log in to GitHub):**

   - **操作 (Action):** 打开浏览器，访问 https://github.com/ 并用您的账号 `<EMAIL>` 登录。 (Open your browser, go to https://github.com/ and log in with your account `<EMAIL>`.)

2. **创建新的 GitHub 仓库 (Create a New GitHub Repository):**

   - 操作 (Action):
     1. 在 GitHub 页面右上角，点击 `+` 号，然后选择 "New repository"。 (On the top right of the GitHub page, click the `+` sign, then select "New repository".)
     2. **Repository name (仓库名称):** 输入 `new-mianshijun`。 (Enter `new-mianshijun`.)
     3. **Description (描述):** (可选/Optional) 输入 "AI 面试助手项目" 或 "AI Interview Assistant Project"。
     4. **Public/Private (公开/私有):** 选择 `Public` (公开) 或 `Private` (私有)。对于学习项目，`Public` 也是可以的。 (Choose `Public` or `Private`. `Public` is fine for learning projects.)
     5. **不要勾选** "Add a README file", "Add .gitignore", 或 "Choose a license"。 (DO NOT check "Add a README file", "Add .gitignore", or "Choose a license".)
     6. 点击 "Create repository" (创建仓库) 按钮。 (Click the "Create repository" button.)

3. **将你的本地项目连接到 GitHub (Connect your local project to GitHub):**

   - 操作 (Action):

     1. 打开你的 

        Cursor 编辑器

        ，并打开它的

        终端 (Terminal)

        。 (Open your 

        Cursor editor

         and then open its 

        Terminal

        .)

        - 通常可以在菜单栏找到 "Terminal" > "New Terminal"。 (Usually found under "Terminal" > "New Terminal" in the menu.)

     2. 在终端中，

        导航到你的总项目文件夹 `MianshiJun_AI_Project`

        。例如： (In the terminal, 

        navigate to your main project directory `MianshiJun_AI_Project`

        . For example:)

        Bash

        ```
        cd path/to/your/MianshiJun_AI_Project
        ```

        (用你实际的路径替换 

        ```
        path/to/your/
        ```

         (Replace 

        ```
        path/to/your/
        ```

         with the actual path.))

     3. 初始化 Git 仓库 (Initialize Git repository):

        Bash

        ```
        git init
        ```

     4. (非常重要/Very Important) 创建 `.gitignore` 文件 (Create a `.gitignore` file):

        - 在 `MianshiJun_AI_Project` 文件夹的根目录下，**手动创建一个名为 `.gitignore` 的文件**。 (In the root of your `MianshiJun_AI_Project` folder, **manually create a file named `.gitignore`**.)

        - 打开这个 

          ```
          .gitignore
          ```

           文件，并粘贴以下内容 (这是为了避免将不必要的文件上传到GitHub)： (Open this 

          ```
          .gitignore
          ```

           file and paste the following content (this prevents uploading unnecessary files to GitHub):)

          ```
          # 依赖文件夹 (Dependency directories)
          node_modules/
          frontend/node_modules/
          api/node_modules/
          
          # 构建产物 (Build artifacts)
          frontend/dist/
          api/dist/
          coverage/
          
          # 本地配置文件 (Local configuration files)
          .env
          .env.*.local
          frontend/.env
          frontend/.env.*.local
          api/.env
          api/.env.*.local
          
          # IDE 和编辑器文件 (IDE and editor files)
          .vscode/
          .idea/
          *.suo
          *.ntvs*
          *.njsproj
          *.sln
          *.sw?
          
          # 系统文件 (System files)
          .DS_Store
          Thumbs.db
          
          # 日志文件 (Log files)
          npm-debug.log*
          yarn-debug.log*
          yarn-error.log*
          ```

        - 保存并关闭 `.gitignore` 文件。 (Save and close the `.gitignore` file.)

     5. 将所有文件添加到 Git (Add all files to Git):

        Bash

        ```
        git add .
        ```

     6. 提交你的第一个版本 (Commit your first version):

        Bash

        ```
        git commit -m "Initial project structure with frontend and .gitignore"
        ```

     7. 连接到 GitHub 远程仓库 (Connect to the GitHub remote repository):

        - 在你的 GitHub 仓库页面 (你刚刚创建 

          ```
          new-mianshijun
          ```

           的页面)，找到标题为 "...or push an existing repository from the command line" 下面的命令。它看起来会像这样，但 

          ```
          YOUR_GITHUB_USERNAME
          ```

           会是你的 GitHub 用户名 (你的是 

          ```
          486044028lin
          ```

          ，因为你的邮箱是 

          ```
          <EMAIL>
          ```

          ，但请再次确认你的确切GitHub用户名，它可能不同于邮箱前缀): (On your GitHub repository page (the one you just created for 

          ```
          new-mianshijun
          ```

          ), find the commands under "...or push an existing repository from the command line". It will look something like this, but 

          ```
          YOUR_GITHUB_USERNAME
          ```

           will be your GitHub username (yours might be 

          ```
          486044028lin
          ```

           based on your email, but please double-check your exact GitHub username as it can be different from the email prefix):)

          Bash

          ```
          git remote add origin https://github.com/YOUR_GITHUB_USERNAME/new-mianshijun.git
          git branch -M main
          git push -u origin main
          ```

        - 复制这些命令，并粘贴到你的终端中，然后按 Enter 执行。

          (Copy these commands, paste them into your terminal, and press Enter to execute them.)

          - **注意:** 如果你的 GitHub 用户名不是 `486044028lin`，请手动替换 `YOUR_GITHUB_USERNAME`。 (Note: If your GitHub username is not `486044028lin`, please replace `YOUR_GITHUB_USERNAME` manually.)

------

### Part B: 前端工作 (React) - (Frontend Work (React))

你之前提供的 `project` 文件夹现在是 `MianshiJun_AI_Project/frontend/`。我们将在这个 `frontend` 文件夹里操作。

**B1. 验证/确保 Vite React TypeScript 项目和 Tailwind CSS 集成 (Verify/Ensure Vite React TypeScript Project & Tailwind CSS Integration):**

这部分主要是检查。从你之前上传的文件看，这些应该都配置好了。

1. 打开前端项目文件夹 (Open frontend project folder):

   - **操作 (Action):** 在 Cursor (或VS Code) 中，确保你打开的是 `MianshiJun_AI_Project/frontend/` 这个文件夹。 (In Cursor (or VS Code), make sure you have the `MianshiJun_AI_Project/frontend/` folder open.)

2. 检查 `package.json` (Check `package.json`):

   - 操作 (Action):

      打开 

     ```
     frontend/package.json
     ```

      文件。确认 

     ```
     "react"
     ```

     , 

     ```
     "vite"
     ```

     , 

     ```
     "typescript"
     ```

     , 

     ```
     "tailwindcss"
     ```

      都在 

     ```
     dependencies
     ```

      或 

     ```
     devDependencies
     ```

      里。 (Open the 

     ```
     frontend/package.json
     ```

      file. Confirm 

     ```
     "react"
     ```

     , 

     ```
     "vite"
     ```

     , 

     ```
     "typescript"
     ```

     , 

     ```
     "tailwindcss"
     ```

      are in 

     ```
     dependencies
     ```

      or 

     ```
     devDependencies
     ```

     .)

     - *你上传的文件显示这些已存在，很好！ (Your uploaded files show these exist, good!)*

3. 检查 Tailwind CSS 配置 (Check Tailwind CSS Configuration):

   - 操作 (Action):

      确认 

     ```
     frontend/tailwind.config.js
     ```

      和 

     ```
     frontend/postcss.config.js
     ```

      文件存在。确认 

     ```
     frontend/src/index.css
     ```

      文件顶部有以下内容： (Confirm 

     ```
     frontend/tailwind.config.js
     ```

      and 

     ```
     frontend/postcss.config.js
     ```

      exist. Confirm 

     ```
     frontend/src/index.css
     ```

      has the following at the top:)

     CSS

     ```
     @tailwind base;
     @tailwind components;
     @tailwind utilities;
     ```

     - *你上传的文件显示这些已存在，很好！ (Your uploaded files show these exist, good!)*

4. 安装依赖 (Install Dependencies):

   - 操作 (Action):

      在 Cursor 的终端中，确保你

     当前目录是 `frontend` 文件夹

      (If you are in 

     ```
     MianshiJun_AI_Project
     ```

     , type 

     ```
     cd frontend
     ```

     ). 然后运行： (In Cursor's terminal, make sure you are 

     inside the `frontend` folder

     . Then run:)

     Bash

     ```
     npm install
     ```

     - (或者 `yarn install` 如果你用 yarn) (or `yarn install` if you use yarn)
     - 这会下载项目需要的所有库。 (This downloads all necessary libraries for the project.)

**B2. 配置 React Router v6+ 基础路由 (Configure React Router v6+ Basic Routes):**

1. 安装 React Router (Install React Router):

   - 操作 (Action):

      在终端中 (确保仍在 

     ```
     frontend
     ```

      目录下)，运行： (In the terminal (still in the 

     ```
     frontend
     ```

      directory), run:)

     Bash

     ```
     npm install react-router-dom
     ```

2. 创建页面组件文件夹和文件 (Create page component folder and files):

   - 操作 (Action):

     1. 在 `frontend/src/` 目录下，创建一个新文件夹，命名为 `pages`。 (Inside the `frontend/src/` directory, create a new folder named `pages`.)

     2. 在 `frontend/src/pages/` 文件夹内，创建以下文件并粘贴内容： (Inside the `frontend/src/pages/` folder, create the following files and paste the content:)

        - `HomePage.tsx`:

          TypeScript

          ```
          import React from 'react';
          
          const HomePage: React.FC = () => {
            return (
              <div className="p-4"> {/* Added some padding for better look */}
                <h1 className="text-2xl font-bold text-gray-800">
                  欢迎来到 面试君! (MianshiJun Homepage)
                </h1>
                <p className="mt-2 text-gray-600">
                  在这里，您将获得AI的实时面试辅助，提升您的面试表现！
                </p>
                {/* 将来这里可以放 Logo 或其他引导内容 (Logo or other introductory content can go here in the future) */}
              </div>
            );
          };
          
          export default HomePage;
          ```

        - `LoginPage.tsx`:

          TypeScript

          ```
          import React from 'react';
          
          const LoginPage: React.FC = () => {
            return (
              <div className="p-4">
                <h2 className="text-xl font-semibold text-gray-700">登录页面 (Login Page)</h2>
                {/* 登录表单将在此处 (Login form will go here) */}
                <p className="mt-2 text-gray-500">登录表单占位符 (Placeholder for login form)</p>
              </div>
            );
          };
          
          export default LoginPage;
          ```

        - `RegisterPage.tsx`:

          TypeScript

          ```
          import React from 'react';
          
          const RegisterPage: React.FC = () => {
            return (
              <div className="p-4">
                <h2 className="text-xl font-semibold text-gray-700">注册页面 (Register Page)</h2>
                {/* 注册表单将在此处 (Registration form will go here) */}
                <p className="mt-2 text-gray-500">注册表单占位符 (Placeholder for registration form)</p>
              </div>
            );
          };
          
          export default RegisterPage;
          ```

**B3. 创建基础布局组件 (Create Basic Layout Components):**

你已经有了 `Sidebar.tsx` 和 `Header.tsx`。我们将创建一个 `Footer.tsx` 和一个 `Layout.tsx` 来整合它们。

1. 创建 `frontend/src/components/Footer.tsx`:

   - 操作 (Action):

      在 

     ```
     frontend/src/components/
     ```

      目录下创建 

     ```
     Footer.tsx
     ```

      文件，并粘贴以下内容： (In the 

     ```
     frontend/src/components/
     ```

      directory, create a 

     ```
     Footer.tsx
     ```

      file and paste the following:)

     TypeScript

     ```
     import React from 'react';
     
     const Footer: React.FC = () => {
       return (
         <footer className="bg-gray-100 text-gray-600 p-4 text-center border-t border-gray-200">
           {/* 根据 design_system.md，页脚应该是极简的 (As per design_system.md, footer should be minimal) */}
           <p className="text-sm">&copy; {new Date().getFullYear()} 面试君 (MianshiJun). AI助力，面试无忧.</p>
         </footer>
       );
     };
     
     export default Footer;
     ```

2. 创建/修改 `frontend/src/components/Layout.tsx`:

   - 操作 (Action):

      在 

     ```
     frontend/src/components/
     ```

      目录下创建或修改 

     ```
     Layout.tsx
     ```

      文件。这个组件会包含你的 

     ```
     Sidebar
     ```

      (侧边栏), 

     ```
     Header
     ```

      (头部导航栏), 和 

     ```
     Footer
     ```

      (页脚)，并使用 

     ```
     Outlet
     ```

      来显示具体的页面内容。 (In the 

     ```
     frontend/src/components/
     ```

      directory, create or modify 

     ```
     Layout.tsx
     ```

     . This component will include your 

     ```
     Sidebar
     ```

     , 

     ```
     Header
     ```

     , 

     ```
     Footer
     ```

     , and use 

     ```
     Outlet
     ```

      to display specific page content.)

     TypeScript

     ```
     import React from 'react';
     import { Outlet } from 'react-router-dom'; // 从 react-router-dom 导入 Outlet (Import Outlet from react-router-dom)
     import Sidebar from './Sidebar';
     import Header from './Header';
     import Footer from './Footer';
     
     const Layout: React.FC = () => {
       const greetingMessage = "下午好"; // 你可以根据时间动态修改 (You can change this dynamically based on time)
     
       return (
         <div className="flex h-screen overflow-hidden bg-slate-50"> {/* Changed background to a lighter gray */}
           <Sidebar />
           <div className="flex-1 flex flex-col overflow-hidden">
             <Header greeting={greetingMessage} />
             {/* 主内容区域，允许滚动 (Main content area, allows scrolling) */}
             <main className="flex-1 overflow-x-hidden overflow-y-auto p-6"> {/* Added more padding */}
               <Outlet /> {/* 页面组件会在这里渲染 (Page components will render here) */}
             </main>
             <Footer />
           </div>
         </div>
       );
     };
     
     export default Layout;
     ```

3. 修改 `frontend/src/App.tsx` 以使用路由和布局 (Modify `frontend/src/App.tsx` to use Router and Layout):

   - 操作 (Action):

      打开 

     ```
     frontend/src/App.tsx
     ```

      文件，将其内容替换为以下代码来设置路由： (Open 

     ```
     frontend/src/App.tsx
     ```

      and replace its content with the following to set up routing:)

     TypeScript

     ```
     import React from 'react';
     import { BrowserRouter, Routes, Route, Link } from 'react-router-dom';
     import Layout from './components/Layout'; // 我们刚刚创建的布局 (The layout we just created)
     import HomePage from './pages/HomePage';
     import LoginPage from './pages/LoginPage';
     import RegisterPage from './pages/RegisterPage';
     // 如果你有 Dashboard 组件并且希望它作为首页的一部分，可以在 HomePage 里处理
     // import Dashboard from './components/Dashboard'; // 你的 Dashboard 组件 (Your Dashboard component)
     
     function App() {
       return (
         <BrowserRouter>
           <Routes>
             {/* 所有使用 Layout 的路由都嵌套在下面 (All routes using Layout are nested below) */}
             <Route path="/" element={<Layout />}>
               {/* 'index' 表示这是访问 "/" 时的默认子路由 (index means this is the default child route for "/") */}
               <Route index element={<HomePage />} />
               <Route path="login" element={<LoginPage />} />
               <Route path="register" element={<RegisterPage />} />
               {/* 你可以将 Dashboard 作为首页内容，或者创建一个专门的 /dashboard 路由 */}
               {/* For example, if Dashboard is your main logged-in view for "/" */}
               {/* <Route index element={<Dashboard />} /> */}
     
               {/* 如果你想有一个专门的 /dashboard 路径 (If you want a dedicated /dashboard path) */}
               {/* <Route path="dashboard" element={<Dashboard />} /> */}
     
               {/* 可以在这里添加一个404页面 (You can add a 404 page here later) */}
               <Route path="*" element={
                 <div className="p-4">
                   <h1 className="text-2xl font-bold">404 - 页面未找到 (Page Not Found)</h1>
                   <Link to="/" className="text-blue-500 hover:underline">返回首页 (Go back to Home)</Link>
                 </div>
               } />
             </Route>
           </Routes>
         </BrowserRouter>
       );
     }
     
     export default App;
     ```

4. 在 `frontend/src/main.tsx` 中确保 App 被渲染 (Ensure App is rendered in `frontend/src/main.tsx`):

   - 操作 (Action):

      打开 

     ```
     frontend/src/main.tsx
     ```

     。它的内容应该大致如下，确保它导入并渲染了你的 

     ```
     App
     ```

      组件： (Open 

     ```
     frontend/src/main.tsx
     ```

     . Its content should be something like this, ensuring it imports and renders your 

     ```
     App
     ```

      component:)

     TypeScript

     ```
     import React from 'react'; // 确保导入了 React (Make sure React is imported)
     import ReactDOM from 'react-dom/client'; // 使用新的 client API (Use the new client API)
     import App from './App.tsx';
     import './index.css';
     
     ReactDOM.createRoot(document.getElementById('root')!).render(
       <React.StrictMode>
         <App />
       </React.StrictMode>
     );
     ```

     - *你上传的文件显示这个已正确设置，很好！ (Your uploaded files show this is correctly set up, good!)*

5. （可选）在 `Header.tsx` 中添加导航链接 (Optional: Add navigation links in `Header.tsx`):

   - 为了方便测试路由，可以临时在 `Header.tsx` 中加入一些链接。 (For easy route testing, you can temporarily add some links to `Header.tsx`.)

   - 操作 (Action):

      打开 

     ```
     frontend/src/components/Header.tsx
     ```

     ，在返回的 

     ```
     header
     ```

      标签内部，可以添加如下内容： (Open 

     ```
     frontend/src/components/Header.tsx
     ```

     , and inside the returned 

     ```
     header
     ```

      tag, you can add something like:)

     TypeScript

     ```
     // ... (原有的 Header 内容)
     <nav className="flex gap-4">
       <Link to="/" className="text-gray-600 hover:text-sky-500">首页 (Home)</Link>
       <Link to="/login" className="text-gray-600 hover:text-sky-500">登录 (Login)</Link>
       <Link to="/register" className="text-gray-600 hover:text-sky-500">注册 (Register)</Link>
     </nav>
     // ... (原有的 Header 内容)
     ```

     - **别忘了在文件顶部导入 `Link`**: `import { Link } from 'react-router-dom';` (Don't forget to import `Link` at the top of the file: `import { Link } from 'react-router-dom';`)

------

### Part C: 后端工作 (Node.js 与 Vercel Functions) - (Backend Work (Node.js & Vercel Functions))

1. **创建 `api` 文件夹用于后端代码 (Create an `api` folder for backend code):**

   - 操作 (Action):

      在你的

     总项目文件夹 `MianshiJun_AI_Project` 的根目录

     下 (与 

     ```
     frontend
     ```

      文件夹同级)，创建一个名为 

     ```
     api
     ```

      的新文件夹。 (In the 

     root of your main project directory `MianshiJun_AI_Project`

      (at the same level as the 

     ```
     frontend
     ```

      folder), create a new folder named 

     ```
     api
     ```

     .)

     - 现在的结构 (Your structure should now be):

       ```
       MianshiJun_AI_Project/
       ├── frontend/
       └── api/       <-- 新的后端文件夹 (New backend folder)
       ```

2. **初始化 Node.js 项目 (Initialize Node.js project):**

   - 操作 (Action):

     1. 在终端中，

        导航到 `api` 文件夹

        ： (In the terminal, 

        navigate into the `api` folder

        :)

        Bash

        ```
        cd api
        ```

        (如果你当前在 

        ```
        MianshiJun_AI_Project
        ```

         目录，就输入 

        ```
        cd api
        ```

        。如果你在 

        ```
        frontend
        ```

         目录，先输入 

        ```
        cd ..
        ```

         返回上一级，再 

        ```
        cd api
        ```

        。) (If you are in 

        ```
        MianshiJun_AI_Project
        ```

         directory, type 

        ```
        cd api
        ```

        . If you are in 

        ```
        frontend
        ```

         directory, first type 

        ```
        cd ..
        ```

         to go up one level, then 

        ```
        cd api
        ```

        .)

     2. 运行以下命令来创建 

        ```
        package.json
        ```

         文件： (Run the following command to create a 

        ```
        package.json
        ```

         file:)

        Bash

        ```
        npm init -y
        ```

3. **安装 Express.js (Install Express.js):**

   - 操作 (Action):

      在终端中 (确保仍在 

     ```
     api
     ```

      目录下)，运行： (In the terminal (still in the 

     ```
     api
     ```

      directory), run:)

     Bash

     ```
     npm install express
     ```

4. **创建简单的健康检查 API (Create a simple health check API):**

   - 操作 (Action):

      在 

     ```
     api
     ```

      文件夹内，创建一个名为 

     ```
     health.ts
     ```

      (或 

     ```
     health.js
     ```

      如果你暂时不用TypeScript写后端) 的文件，并粘贴以下内容： (Inside the 

     ```
     api
     ```

      folder, create a file named 

     ```
     health.ts
     ```

      (or 

     ```
     health.js
     ```

      if you are not using TypeScript for backend yet) and paste the following content:)

     - ```
       api/health.ts
       ```

       :

       TypeScript

       ```
       import type { VercelRequest, VercelResponse } from '@vercel/node';
       
       export default function handler(
         request: VercelRequest,
         response: VercelResponse,
       ) {
         response.status(200).json({
           status: 'ok',
           message: 'Backend API is healthy!',
           timestamp: new Date().toISOString(),
         });
       }
       ```

     - 注意 (Note):

        为了让 Vercel 正确识别 TypeScript Serverless Functions，你可能需要在 

       ```
       api
       ```

        文件夹中也包含一个 

       ```
       tsconfig.json
       ```

       。一个简单的 

       ```
       tsconfig.json
       ```

        可以是： (For Vercel to correctly recognize TypeScript Serverless Functions, you might also need a 

       ```
       tsconfig.json
       ```

        in your 

       ```
       api
       ```

        folder. A simple 

       ```
       tsconfig.json
       ```

        could be:)

       - ```
         api/tsconfig.json
         ```

         :

         JSON

         ```
         {
           "compilerOptions": {
             "target": "es2020",
             "module": "commonjs",
             "esModuleInterop": true,
             "strict": true,
             "skipLibCheck": true,
             "outDir": ".vercel/output/functions" // Vercel 通常会处理构建，这个 outDir 可能不直接使用但有益于本地理解
           },
           "include": ["**/*.ts"],
           "exclude": ["node_modules"]
         }
         ```

       - 你还需要安装 

         ```
         @vercel/node
         ```

          作为 dev dependency: (You'll also need 

         ```
         @vercel/node
         ```

          as a dev dependency:)

         Bash

         ```
         npm install --save-dev @vercel/node
         ```

5. **配置 Vercel 环境变量 (占位符) (Configure Vercel Environment Variables (Placeholders)):**

   - 这部分主要在 Vercel 平台上操作，但你可以在本地 `api` 文件夹下创建一个 `.env` 文件来模拟。 (This part is mostly done on the Vercel platform, but you can create a `.env` file in your local `api` folder to simulate it.)

   - 操作 (Action - 本地模拟/Local Simulation):

     1. 在 `api` 文件夹内，创建一个名为 `.env` 的文件。 (Inside the `api` folder, create a file named `.env`.)

     2. 添加占位符数据库连接字符串 (我们稍后会从 Neon 获取真实的值): (Add a placeholder database connection string (we will get the real one from Neon later):)

        ```
        DATABASE_URL="postgresql://user:password@host:port/database?sslmode=require"
        ```

     - **确保 `.env` 文件已被添加到 `.gitignore` 中！** (之前步骤已包含)。 (Make sure the `.env` file is added to your `.gitignore`! (Covered in a previous step).)

   - Vercel 平台配置 (Vercel Platform Configuration):

     - 当你之后将项目部署到 Vercel 时，你需要在 Vercel 项目的设置中添加这些环境变量。 (When you deploy your project to Vercel later, you'll need to add these environment variables in your Vercel project's settings.)

------

### Part D: 数据库相关 (Neon) - (Database Related (Neon))

1. 注册/登录 Neon (Sign up/Log in to Neon):

   - 操作 (Action):
     1. 打开浏览器，访问 https://neon.tech/。 (Open your browser and go to https://neon.tech/.)
     2. 点击 "Sign up" (注册) 或 "Login" (登录)。你可以使用 GitHub 账号快速注册/登录。 (Click "Sign up" or "Login". You can use your GitHub account for quick registration/login.)

2. 创建 Neon 项目和数据库 (Create a Neon Project and Database):

   - 操作 (Action):
     1. 登录后，你会进入 Neon 控制台 (Dashboard)。 (After logging in, you'll be on the Neon console/dashboard.)
     2. 点击 "Create a project" (创建项目) 或类似按钮。 (Click "Create a project" or a similar button.)
     3. 给你的项目命名，例如 `mianshijun-db`。 (Name your project, e.g., `mianshijun-db`.)
     4. 选择一个区域 (Region)，离你的用户或 Vercel 服务器近的通常更好 (比如美国东部 `US East (N. Virginia)` 或西部 `US West (Oregon)`)。(Choose a region, one close to your users or Vercel servers is usually better, e.g., `US East (N. Virginia)` or `US West (Oregon)`.)
     5. 数据库名称可以默认 (通常是 `neondb`)，或者你也可以修改它。 (The database name can be default (usually `neondb`), or you can change it.)
     6. PostgreSQL 版本选择最新的稳定版即可。 (Choose the latest stable PostgreSQL version.)
     7. 点击 "Create project" (创建项目)。Neon 会为你创建一个数据库实例。 (Click "Create project". Neon will create a database instance for you.)

3. 获取数据库连接字符串 (Get the Database Connection String):

   - 操作 (Action):
     1. 项目创建成功后，在你的项目仪表盘中，找到 "Connection Details" (连接详情) 或类似的部分。 (After the project is created, on your project dashboard, find "Connection Details" or a similar section.)
     2. 通常会提供一个 **连接字符串 (Connection String)**，它看起来像这样： `postgresql://<user>:<password>@<host>:<port>/<dbname>?sslmode=require`。 (It will usually provide a **Connection String** that looks like: `postgresql://<user>:<password>@<host>:<port>/<dbname>?sslmode=require`.)
     3. **复制这个完整的连接字符串。** 这是非常敏感的信息，不要分享给别人。 (Copy this entire connection string. This is very sensitive information, do not share it.)

4. 配置到 Vercel 环境变量 (后续部署时) (Configure to Vercel Environment Variables (during later deployment)):

   - 操作 (Action):

     1. (本地模拟/Local Simulation) 将你从 Neon 复制的真实连接字符串更新到你之前创建的 

        ```
        MianshiJun_AI_Project/api/.env
        ```

         文件中的 

        ```
        DATABASE_URL
        ```

        。 (Update the 

        ```
        DATABASE_URL
        ```

         in your previously created 

        ```
        MianshiJun_AI_Project/api/.env
        ```

         file with the real connection string you copied from Neon.)

        ```
        DATABASE_URL="YOUR_NEON_CONNECTION_STRING_HERE"
        ```

     2. (Vercel平台/Vercel Platform) 当你把项目连接到 Vercel 并准备部署时，你需要： (When you connect your project to Vercel and are ready to deploy, you will need to:)

        - 进入你的 Vercel 项目设置 (Project Settings)。
        - 找到 "Environment Variables" (环境变量) 部分。
        - 添加一个名为 `DATABASE_URL` 的新环境变量，并将你从 Neon 复制的连接字符串粘贴到值 (Value) 中。
        - 确保它可用于所有环境 (Production, Preview, Development)。

------

### Part E: 提交代码到 GitHub (Commit Code to GitHub)

1. 检查状态并添加更改 (Check status and add changes):

   - 操作 (Action):

     1. 在终端中，确保你**位于总项目文件夹 `MianshiJun_AI_Project` 的根目录**。 (In the terminal, make sure you are in the **root of your main project directory `MianshiJun_AI_Project`**.)

     2. 输入 `git status` 查看哪些文件被修改或添加了。 (Type `git status` to see which files have been modified or added.)

     3. 将所有更改添加到暂存区： (Add all changes to the staging area:)

        Bash

        ```
        git add .
        ```

2. 提交更改 (Commit changes):

   - 操作 (Action):

     Bash

     ```
     git commit -m "Feat: Task 1.1 - Initial frontend routing, layout, and backend API folder setup"
     ```

     (这个提交信息的意思是：“功能：任务1.1 - 初始化了前端路由、布局和后端API文件夹结构”) (This commit message means: "Feature: Task 1.1 - Initialized frontend routing, layout, and backend API folder structure")

3. 推送到 GitHub (Push to GitHub):

   - 操作 (Action):

     Bash

     ```
     git push origin main
     ```

------

### Part F: 在本地运行和测试 (Running and Testing Locally) - 初步

1. **运行前端应用 (Run Frontend Application):**

   - 操作 (Action):

     1. 在终端中，**导航到 `frontend` 文件夹** (`cd frontend`)。 (In the terminal, **navigate into the `frontend` folder** (`cd frontend`).)

     2. 运行开发服务器： (Run the development server:)

        Bash

        ```
        npm run dev
        ```

     3. 终端会显示一个本地地址，通常是 `http://localhost:5173/`。在浏览器中打开它。 (The terminal will show a local address, usually `http://localhost:5173/`. Open it in your browser.)

     4. 测试 (Test):

        - 你应该能看到带有 `Sidebar`, `Header`, `Footer` 的基础布局。 (You should see the basic layout with `Sidebar`, `Header`, `Footer`.)
        - `HomePage` 的内容应该显示在布局中间。 (The content of `HomePage` should be displayed in the middle.)
        - 尝试点击你在 `Header` 中添加的 "登录" 和 "注册" 链接，看看页面内容是否切换到 `LoginPage` 和 `RegisterPage` 的占位符。 (Try clicking the "Login" and "Register" links you added in the `Header` to see if the page content switches to the placeholders for `LoginPage` and `RegisterPage`.)

2. **测试后端 API (本地模拟 - Vercel CLI) (Test Backend API (Local Simulation - Vercel CLI)):**

   - Vercel Serverless Functions 在本地测试通常需要 Vercel CLI。 (Testing Vercel Serverless Functions locally usually requires the Vercel CLI.)

   - 操作 (Action):

     1. 安装 Vercel CLI (Install Vercel CLI):

         如果你还没有安装，打开一个新的终端窗口运行： (If you haven't installed it, open a new terminal window and run:)

        Bash

        ```
        npm install -g vercel
        ```

     2. 登录 Vercel (Login to Vercel):

        Bash

        ```
        vercel login
        ```

        (按照提示操作，它可能会让你在浏览器中授权。) (Follow the prompts, it might ask you to authorize in your browser.)

     3. 在总项目文件夹 `MianshiJun_AI_Project` 的根目录下运行开发服务器

         (它会自动找到 

        ```
        api
        ```

         文件夹中的函数)： (In the terminal, 

        at the root of your main project directory `MianshiJun_AI_Project`

        , run the development server (it will automatically find functions in the 

        ```
        api
        ```

         folder):)

        Bash

        ```
        vercel dev
        ```

     4. Vercel CLI 会启动一个本地服务器，通常在 `http://localhost:3000`。 (Vercel CLI will start a local server, usually on `http://localhost:3000`.)

     5. 测试 (Test):

         在浏览器中打开 

        ```
        http://localhost:3000/api/health
        ```

        。你应该能看到类似这样的 JSON 响应： (Open 

        ```
        http://localhost:3000/api/health
        ```

         in your browser. You should see a JSON response like:)

        JSON

        ```
        {"status":"ok","message":"Backend API is healthy!","timestamp":"..."}
        ```

------

### Part G: Vercel 部署与测试 (Vercel Deployment & Testing)

1. 注册/登录 Vercel (Sign up/Log in to Vercel):

   - 操作 (Action):
     1. 打开浏览器，访问 https://vercel.com/。 (Open your browser and go to https://vercel.com/.)
     2. 使用你的 GitHub 账号注册或登录。 (Sign up or log in using your GitHub account.)

2. 在 Vercel 上创建新项目并连接 GitHub 仓库 (Create a new project on Vercel and connect your GitHub repository):

   - 操作 (Action):

     1. 在 Vercel 仪表盘，点击 "Add New..." > "Project"。 (On the Vercel dashboard, click "Add New..." > "Project".)

     2. 在 "Import Git Repository" 部分，找到并选择你刚刚创建的 `new-mianshijun` GitHub 仓库。你可能需要授权 Vercel 访问你的 GitHub 仓库。 (In the "Import Git Repository" section, find and select your newly created `new-mianshijun` GitHub repository. You might need to authorize Vercel to access your GitHub repositories.)

     3. 项目设置 (Project Settings):

        - **Project Name (项目名称):** Vercel 可能会根据你的仓库名自动填充，你可以修改。 (Vercel might auto-fill based on your repository name; you can change it.)

        - **Framework Preset (框架预设):** Vercel 通常能自动检测到 Vite。如果它选择了 "Vite"，那很好。 (Vercel can usually auto-detect Vite. If it selects "Vite", that's good.)

        - Root Directory (根目录):

          - 由于你的前端在 `frontend` 子文件夹，后端在 `api` 子文件夹，你需要告诉 Vercel。 (Since your frontend is in the `frontend` subfolder and backend in the `api` subfolder, you need to tell Vercel.)

          - 重要 (Important):

             Vercel 的 monorepo 支持允许你从一个仓库部署多个项目或指定一个根目录。

            - **对于纯前端部署 (For frontend-only deployment first):** 如果你只想先部署前端，你可以将 Root Directory 设置为 `frontend`。Vercel 会在该目录下寻找 `package.json` 和 Vite 配置。 (If you only want to deploy the frontend first, you can set the Root Directory to `frontend`. Vercel will look for `package.json` and Vite config in that directory.)

            - 对于同时部署前后端 (For deploying both frontend and backend together from the root):

               Vercel 可以自动检测到 

              ```
              frontend
              ```

               (作为 Vite 应用) 和 

              ```
              api
              ```

               文件夹 (作为 Serverless Functions) 如果它们都在仓库的根目录。你的 

              ```
              MianshiJun_AI_Project
              ```

               就是仓库的根。 (Vercel can auto-detect 

              ```
              frontend
              ```

               (as a Vite app) and the 

              ```
              api
              ```

               folder (as Serverless Functions) if they are at the root of the repository. Your 

              ```
              MianshiJun_AI_Project
              ```

               is the root.)

              - **所以，保持 "Root Directory" 为仓库根目录 (通常是默认设置，不选择子文件夹)。** (So, keep "Root Directory" as the repository root (usually the default, don't select a subfolder).)

        - Build and Output Settings (构建和输出设置):

          - Vite 项目的构建命令通常是 `npm run build` (或 `yarn build`)，输出目录是 `dist`。Vercel 通常能自动识别。 (Vite's build command is usually `npm run build` (or `yarn build`), and the output directory is `dist`. Vercel usually auto-detects this.)

          - 如果你的前端 Vite 项目的 `package.json` 在 `frontend` 文件夹内，Vercel 的构建系统需要知道这一点。

             当 Root Directory 是仓库根时，Vercel 会寻找根目录的 

            ```
            package.json
            ```

            。 (If your frontend Vite project's 

            ```
            package.json
            ```

             is inside the 

            ```
            frontend
            ```

             folder, Vercel's build system needs to know this. When the Root Directory is the repo root, Vercel looks for 

            ```
            package.json
            ```

             at the root.)

            - 解决方案 (Solution):

              1. 你可以在 Vercel 的 "Build & Development Settings" 中覆盖构建命令和输出目录，并指定工作目录。

                 (You can override the build command and output directory in Vercel's "Build & Development Settings" and specify the working directory.)

                 - **Build Command (构建命令):** `cd frontend && npm run build`
                 - **Output Directory (输出目录):** `frontend/dist`
                 - **Install Command (安装命令):** `cd frontend && npm install` (或者如果根目录也有 `package.json` 用于管理 workspace，则可能是 `npm install -w frontend` 或 `yarn workspace frontend install`，但我们目前结构简单，前者适用)

              2. **或者，在仓库根目录创建一个 `package.json` 来管理整个 monorepo (更高级)。** 对于小白，方案1更直接。 (Or, create a `package.json` at the repository root to manage the entire monorepo (more advanced). For novices, option 1 is more direct.)

        - Environment Variables (环境变量):

          - 点击 "Environment Variables"。
          - 添加一个，名称为 `DATABASE_URL`，值为你从 Neon 复制的**真实连接字符串**。 (Add one, Name: `DATABASE_URL`, Value: your **real connection string** from Neon.)
          - 确保它对所有环境 (Production, Preview, Development) 都可用。 (Ensure it's available for all environments.)
          - (以后你还会有 `DEEPSEEK_API_KEY`, `BAIDU_ASR_API_KEY` 等也在这里添加。) (Later you'll add `DEEPSEEK_API_KEY`, `BAIDU_ASR_API_KEY`, etc., here as well.)

     4. 点击 "Deploy" (部署) 按钮。 (Click the "Deploy" button.)

3. 测试部署 (Test Deployment):

   - 操作 (Action):
     1. Vercel 会开始构建和部署你的项目。这可能需要几分钟。 (Vercel will start building and deploying your project. This might take a few minutes.)
     2. 部署成功后，Vercel 会给你一个或多个 URL (通常是 `xxx.vercel.app`)。 (Once successful, Vercel will give you one or more URLs (usually `xxx.vercel.app`).)
     3. 前端测试 (Frontend Test):
        - 打开主 URL。你应该能看到你的 React 前端骨架页面 (带 Logo 的首页)。 (Open the main URL. You should see your React frontend skeleton page (homepage with logo).)
        - 测试你在 `Header` 中添加的导航链接，确保可以访问 `/login` 和 `/register` 页面。 (Test the navigation links you added in the `Header` to ensure `/login` and `/register` pages are accessible.)
     4. 后端 API 测试 (Backend API Test):
        - 在 Vercel 提供的 URL 后面加上 `/api/health` (例如 `https://new-mianshijun-xxxx.vercel.app/api/health`)。 (Append `/api/health` to the URL Vercel provided (e.g., `https://new-mianshijun-xxxx.vercel.app/api/health`).)
        - 你应该能看到 `{"status": "ok", ...}` 的 JSON 响应。 (You should see the `{"status": "ok", ...}` JSON response.)

------

**恭喜！(Congratulations!)**

如果以上步骤都顺利完成，你就成功搭建了项目的基础架构，配置了开发环境，并完成了初步的部署流程！你的前后端骨架代码也已经提交到了 GitHub。

**接下来做什么 (What's next?):**

- 根据开发计划，下一步是 **任务 1.2: 用户注册功能**。 (According to the development plan, the next step is **Task 1.2: User Registration Functionality**.)

遇到任何问题，随时告诉我具体是哪一步以及遇到的提示或现象，我会尽力帮助！

(If you encounter any issues, let me know which step and any messages or observations, and I'll do my best to help!)