import express, { Request, Response } from 'express';
import jwt from 'jsonwebtoken';
import { z } from 'zod';
import prisma from '../../lib/prisma';

const router = express.Router();
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

// 扩展Request接口以包含管理员用户信息
interface AdminRequest extends Request {
  adminUser?: {
    userId: string;
  };
}

// JWT 验证中间件 - 管理员权限
const verifyAdminToken = async (req: AdminRequest, res: Response, next: any) => {
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({ 
      success: false,
      message: '未授权访问' 
    });
  }

  const token = authHeader.substring(7);
  try {
    const decoded = jwt.verify(token, JWT_SECRET) as { userId: string };
    
    // 验证用户是否为管理员
    const user = await prisma.user.findUnique({
      where: { id: decoded.userId },
      select: { id: true, role: true }
    });

    if (!user || user.role !== 'ADMIN') {
      return res.status(403).json({ 
        success: false,
        message: '需要管理员权限' 
      });
    }

    req.adminUser = { userId: user.id };
    next();
  } catch (error) {
    return res.status(401).json({ 
      success: false,
      message: '无效的认证令牌' 
    });
  }
};

// 请求验证模式
const createNotificationSchema = z.object({
  title: z.string().min(1, '标题不能为空').max(200, '标题不能超过200字符'),
  content: z.string().min(1, '内容不能为空').max(2000, '内容不能超过2000字符'),
  type: z.enum(['ANNOUNCEMENT', 'SYSTEM_UPDATE', 'MAINTENANCE', 'PROMOTION']).default('ANNOUNCEMENT'),
  priority: z.enum(['LOW', 'NORMAL', 'HIGH', 'URGENT']).default('NORMAL'),
  expiresAt: z.string().datetime().optional(),
  targetType: z.enum(['ALL_USERS', 'NEW_USERS', 'DATE_RANGE', 'ROLE_BASED']).default('ALL_USERS'),
  targetValue: z.string().optional() // JSON字符串，存储筛选条件
});

const updateNotificationSchema = z.object({
  title: z.string().min(1).max(200).optional(),
  content: z.string().min(1).max(2000).optional(),
  type: z.enum(['ANNOUNCEMENT', 'SYSTEM_UPDATE', 'MAINTENANCE', 'PROMOTION']).optional(),
  priority: z.enum(['LOW', 'NORMAL', 'HIGH', 'URGENT']).optional(),
  expiresAt: z.string().datetime().optional(),
  status: z.enum(['DRAFT', 'PUBLISHED', 'EXPIRED', 'ARCHIVED']).optional()
});

/**
 * 获取通知列表
 */
router.get('/', verifyAdminToken, async (req: AdminRequest, res: Response) => {
  try {
    const { page = '1', limit = '10', status, type } = req.query;
    const pageNum = parseInt(page as string);
    const limitNum = parseInt(limit as string);
    const skip = (pageNum - 1) * limitNum;

    const where: any = {};
    if (status) where.status = status;
    if (type) where.type = type;

    const [notifications, total] = await Promise.all([
      prisma.notification.findMany({
        where,
        include: {
          creator: {
            select: { id: true, name: true, email: true }
          },
          targets: true,
          _count: {
            select: { userNotifications: true }
          }
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take: limitNum
      }),
      prisma.notification.count({ where })
    ]);

    return res.json({
      success: true,
      data: {
        notifications,
        pagination: {
          page: pageNum,
          limit: limitNum,
          total,
          pages: Math.ceil(total / limitNum)
        }
      }
    });

  } catch (error: any) {
    console.error('获取通知列表失败:', error);
    return res.status(500).json({
      success: false,
      message: '获取通知列表失败'
    });
  }
});

/**
 * 创建通知
 */
router.post('/', verifyAdminToken, async (req: AdminRequest, res: Response) => {
  try {
    const validatedData = createNotificationSchema.parse(req.body);
    const { targetType, targetValue, ...notificationData } = validatedData;

    // 创建通知并直接发布
    const notification = await prisma.notification.create({
      data: {
        ...notificationData,
        expiresAt: validatedData.expiresAt ? new Date(validatedData.expiresAt) : null,
        status: 'PUBLISHED', // 直接发布
        publishedAt: new Date(), // 设置发布时间
        createdBy: req.adminUser!.userId,
        targets: {
          create: {
            targetType,
            targetValue
          }
        }
      },
      include: {
        creator: {
          select: { id: true, name: true, email: true }
        },
        targets: true
      }
    });

    // 创建用户通知关联
    await createUserNotifications(notification);

    return res.status(201).json({
      success: true,
      message: '通知创建成功',
      data: { notification }
    });

  } catch (error: any) {
    console.error('创建通知失败:', error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数错误',
        errors: error.errors
      });
    }

    return res.status(500).json({
      success: false,
      message: '创建通知失败'
    });
  }
});

/**
 * 获取单个通知详情
 */
router.get('/:id', verifyAdminToken, async (req: AdminRequest, res: Response) => {
  try {
    const { id } = req.params;

    const notification = await prisma.notification.findUnique({
      where: { id },
      include: {
        creator: {
          select: { id: true, name: true, email: true }
        },
        targets: true,
        _count: {
          select: { 
            userNotifications: true,
            userNotifications: { where: { isRead: true } }
          }
        }
      }
    });

    if (!notification) {
      return res.status(404).json({
        success: false,
        message: '通知不存在'
      });
    }

    return res.json({
      success: true,
      data: { notification }
    });

  } catch (error: any) {
    console.error('获取通知详情失败:', error);
    return res.status(500).json({
      success: false,
      message: '获取通知详情失败'
    });
  }
});

/**
 * 更新通知
 */
router.put('/:id', verifyAdminToken, async (req: AdminRequest, res: Response) => {
  try {
    const { id } = req.params;
    const validatedData = updateNotificationSchema.parse(req.body);

    const notification = await prisma.notification.update({
      where: { id },
      data: {
        ...validatedData,
        expiresAt: validatedData.expiresAt ? new Date(validatedData.expiresAt) : undefined
      },
      include: {
        creator: {
          select: { id: true, name: true, email: true }
        },
        targets: true
      }
    });

    return res.json({
      success: true,
      message: '通知更新成功',
      data: { notification }
    });

  } catch (error: any) {
    console.error('更新通知失败:', error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数错误',
        errors: error.errors
      });
    }

    return res.status(500).json({
      success: false,
      message: '更新通知失败'
    });
  }
});

/**
 * 发布通知
 */
router.post('/:id/publish', verifyAdminToken, async (req: AdminRequest, res: Response) => {
  try {
    const { id } = req.params;

    // 更新通知状态为已发布
    const notification = await prisma.notification.update({
      where: { id },
      data: {
        status: 'PUBLISHED',
        publishedAt: new Date()
      },
      include: {
        targets: true
      }
    });

    // 根据目标类型创建用户通知关联
    await createUserNotifications(notification);

    return res.json({
      success: true,
      message: '通知发布成功',
      data: { notification }
    });

  } catch (error: any) {
    console.error('发布通知失败:', error);
    return res.status(500).json({
      success: false,
      message: '发布通知失败'
    });
  }
});

/**
 * 删除通知
 */
router.delete('/:id', verifyAdminToken, async (req: AdminRequest, res: Response) => {
  try {
    const { id } = req.params;

    await prisma.notification.delete({
      where: { id }
    });

    return res.json({
      success: true,
      message: '通知删除成功'
    });

  } catch (error: any) {
    console.error('删除通知失败:', error);
    return res.status(500).json({
      success: false,
      message: '删除通知失败'
    });
  }
});

/**
 * 根据目标类型创建用户通知关联
 */
async function createUserNotifications(notification: any) {
  const target = notification.targets[0]; // 假设每个通知只有一个目标
  if (!target) return;

  let userIds: string[] = [];

  switch (target.targetType) {
    case 'ALL_USERS':
      // 获取所有用户
      const allUsers = await prisma.user.findMany({
        select: { id: true }
      });
      userIds = allUsers.map(user => user.id);
      break;

    case 'NEW_USERS':
      // 获取最近7天注册的用户
      const sevenDaysAgo = new Date();
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

      const newUsers = await prisma.user.findMany({
        where: {
          createdAt: {
            gte: sevenDaysAgo
          }
        },
        select: { id: true }
      });
      userIds = newUsers.map(user => user.id);
      break;

    case 'DATE_RANGE':
      // 根据注册日期范围筛选用户
      if (target.targetValue) {
        try {
          const dateRange = JSON.parse(target.targetValue);
          const users = await prisma.user.findMany({
            where: {
              createdAt: {
                gte: new Date(dateRange.startDate),
                lte: new Date(dateRange.endDate)
              }
            },
            select: { id: true }
          });
          userIds = users.map(user => user.id);
        } catch (error) {
          console.error('解析日期范围失败:', error);
        }
      }
      break;

    case 'ROLE_BASED':
      // 根据用户角色筛选
      if (target.targetValue) {
        try {
          const roleFilter = JSON.parse(target.targetValue);
          const users = await prisma.user.findMany({
            where: {
              role: roleFilter.role
            },
            select: { id: true }
          });
          userIds = users.map(user => user.id);
        } catch (error) {
          console.error('解析角色筛选失败:', error);
        }
      }
      break;
  }

  // 批量创建用户通知关联
  if (userIds.length > 0) {
    const userNotifications = userIds.map(userId => ({
      userId,
      notificationId: notification.id
    }));

    await prisma.userNotification.createMany({
      data: userNotifications,
      skipDuplicates: true
    });
  }
}

export default router;
