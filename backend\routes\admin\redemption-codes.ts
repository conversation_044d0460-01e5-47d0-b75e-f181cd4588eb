import express, { Request, Response } from 'express';
import jwt from 'jsonwebtoken';
import prisma from '../../lib/prisma';

const router = express.Router();
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

// 扩展Request接口以包含管理员用户信息
interface AdminRequest extends Request {
  adminUser?: {
    userId: string;
  };
}

// JWT 验证中间件 - 管理员权限
const verifyAdminToken = async (req: AdminRequest, res: Response, next: any) => {
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({ 
      success: false,
      message: '未授权访问' 
    });
  }

  const token = authHeader.substring(7);
  try {
    const decoded = jwt.verify(token, JWT_SECRET) as { userId: string };
    
    // 验证用户是否为管理员
    const user = await prisma.user.findUnique({
      where: { id: decoded.userId },
      select: { id: true, role: true }
    });

    if (!user || user.role !== 'ADMIN') {
      return res.status(403).json({ 
        success: false,
        message: '需要管理员权限' 
      });
    }

    req.adminUser = { userId: user.id };
    next();
  } catch (error) {
    return res.status(401).json({ 
      success: false,
      message: '无效的认证令牌' 
    });
  }
};

// 生成随机兑换码
const generateRedemptionCode = (prefix?: string): string => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = '';
  
  if (prefix) {
    result = prefix.toUpperCase();
  }
  
  // 生成8位随机字符
  for (let i = 0; i < 8; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  
  return result;
};

/**
 * 获取兑换码列表
 */
router.get('/', verifyAdminToken, async (req: AdminRequest, res: Response) => {
  try {
    const { 
      page = 1, 
      limit = 10, 
      search = '', 
      status = 'all',
      benefitType = 'all'
    } = req.query;

    const skip = (Number(page) - 1) * Number(limit);
    
    // 构建查询条件
    const where: any = {};
    
    if (search) {
      where.code = {
        contains: String(search),
        mode: 'insensitive'
      };
    }
    
    if (status !== 'all') {
      if (status === 'active') {
        where.isActive = true;
      } else if (status === 'inactive') {
        where.isActive = false;
      } else if (status === 'used') {
        where.isUsed = true;
      } else if (status === 'unused') {
        where.isUsed = false;
      }
    }
    
    if (benefitType !== 'all') {
      where.benefitType = benefitType;
    }

    // 获取总数和数据
    const [total, codes] = await Promise.all([
      prisma.redemptionCode.count({ where }),
      prisma.redemptionCode.findMany({
        where,
        skip,
        take: Number(limit),
        orderBy: { createdAt: 'desc' },
        select: {
          id: true,
          code: true,
          benefitType: true,
          benefitValue: true,
          usageLimit: true,
          usageCount: true,
          isActive: true,
          isUsed: true,
          expiresAt: true,
          createdAt: true,
          updatedAt: true,
          description: true
        }
      })
    ]);

    return res.json({
      success: true,
      data: {
        codes,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total,
          totalPages: Math.ceil(total / Number(limit))
        }
      }
    });

  } catch (error: any) {
    console.error('获取兑换码列表失败:', error);
    return res.status(500).json({ 
      success: false,
      message: '获取兑换码列表失败，请稍后再试' 
    });
  }
});

/**
 * 创建兑换码
 */
router.post('/', verifyAdminToken, async (req: AdminRequest, res: Response) => {
  try {
    const {
      benefitType,
      benefitAmount,
      generateCount = 1,
      usageLimit = 1,
      expiresAt,
      expiresInDays,
      description = '',
      codePrefix = ''
    } = req.body;

    // 验证必填字段
    if (!benefitType || !benefitAmount) {
      return res.status(400).json({
        success: false,
        message: '权益类型和权益值不能为空'
      });
    }

    // 验证权益类型
    const validBenefitTypes = ['POINTS', 'MOCK_INTERVIEW', 'FORMAL_INTERVIEW'];
    if (!validBenefitTypes.includes(benefitType)) {
      return res.status(400).json({
        success: false,
        message: '无效的权益类型'
      });
    }

    // 处理过期时间
    let finalExpiresAt = null;
    if (expiresAt) {
      finalExpiresAt = new Date(expiresAt);
    } else if (expiresInDays) {
      finalExpiresAt = new Date();
      finalExpiresAt.setDate(finalExpiresAt.getDate() + Number(expiresInDays));
    }

    // 批量创建兑换码
    const codes = [];
    for (let i = 0; i < Number(generateCount); i++) {
      let code;
      let isUnique = false;

      // 确保生成的兑换码是唯一的
      while (!isUnique) {
        code = generateRedemptionCode(codePrefix);
        const existing = await prisma.redemptionCode.findUnique({
          where: { code }
        });
        if (!existing) {
          isUnique = true;
        }
      }

      const redemptionCode = await prisma.redemptionCode.create({
        data: {
          code: code!,
          benefitType,
          benefitValue: Number(benefitAmount),
          usageLimit: Number(usageLimit),
          expiresAt: finalExpiresAt,
          description,
          isActive: true,
          isUsed: false,
          usageCount: 0
        }
      });

      codes.push(redemptionCode);
    }

    return res.status(201).json({
      success: true,
      message: `成功创建 ${codes.length} 个兑换码`,
      data: { codes }
    });

  } catch (error: any) {
    console.error('创建兑换码失败:', error);
    return res.status(500).json({
      success: false,
      message: '创建兑换码失败，请稍后再试'
    });
  }
});

/**
 * 更新兑换码
 */
router.put('/:id', verifyAdminToken, async (req: AdminRequest, res: Response) => {
  try {
    const { id } = req.params;
    const { isActive, description, expiresAt } = req.body;

    const updatedCode = await prisma.redemptionCode.update({
      where: { id },
      data: {
        ...(isActive !== undefined && { isActive }),
        ...(description !== undefined && { description }),
        ...(expiresAt !== undefined && { expiresAt: expiresAt ? new Date(expiresAt) : null })
      }
    });

    return res.json({
      success: true,
      message: '兑换码更新成功',
      data: { code: updatedCode }
    });

  } catch (error: any) {
    console.error('更新兑换码失败:', error);
    return res.status(500).json({
      success: false,
      message: '更新兑换码失败，请稍后再试'
    });
  }
});

/**
 * 删除兑换码
 */
router.delete('/:id', verifyAdminToken, async (req: AdminRequest, res: Response) => {
  try {
    const { id } = req.params;

    await prisma.redemptionCode.delete({
      where: { id }
    });

    return res.json({
      success: true,
      message: '兑换码删除成功'
    });

  } catch (error: any) {
    console.error('删除兑换码失败:', error);
    return res.status(500).json({
      success: false,
      message: '删除兑换码失败，请稍后再试'
    });
  }
});

/**
 * 启用兑换码
 */
router.patch('/:id/enable', verifyAdminToken, async (req: AdminRequest, res: Response) => {
  try {
    const { id } = req.params;

    const updatedCode = await prisma.redemptionCode.update({
      where: { id },
      data: { isActive: true }
    });

    return res.json({
      success: true,
      message: '兑换码已启用',
      data: { code: updatedCode }
    });

  } catch (error: any) {
    console.error('启用兑换码失败:', error);
    return res.status(500).json({
      success: false,
      message: '启用兑换码失败，请稍后再试'
    });
  }
});

/**
 * 禁用兑换码
 */
router.patch('/:id/disable', verifyAdminToken, async (req: AdminRequest, res: Response) => {
  try {
    const { id } = req.params;

    const updatedCode = await prisma.redemptionCode.update({
      where: { id },
      data: { isActive: false }
    });

    return res.json({
      success: true,
      message: '兑换码已禁用',
      data: { code: updatedCode }
    });

  } catch (error: any) {
    console.error('禁用兑换码失败:', error);
    return res.status(500).json({
      success: false,
      message: '禁用兑换码失败，请稍后再试'
    });
  }
});

/**
 * 批量删除兑换码
 */
router.delete('/batch-delete', verifyAdminToken, async (req: AdminRequest, res: Response) => {
  try {
    const { ids } = req.body;

    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({
        success: false,
        message: '请提供要删除的兑换码ID列表'
      });
    }

    const result = await prisma.redemptionCode.deleteMany({
      where: {
        id: { in: ids }
      }
    });

    return res.json({
      success: true,
      message: `成功删除 ${result.count} 个兑换码`
    });

  } catch (error: any) {
    console.error('批量删除兑换码失败:', error);
    return res.status(500).json({
      success: false,
      message: '批量删除兑换码失败，请稍后再试'
    });
  }
});

export default router;
