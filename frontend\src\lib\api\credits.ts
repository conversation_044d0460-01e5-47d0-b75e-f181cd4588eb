import { fetchWithAuth } from './apiService';

// 扣费类型
export type DeductType = 'mock' | 'formal';

// 扣费请求接口
export interface DeductCreditsRequest {
  type: DeductType;
  reason: string;
}

// 扣费响应接口
export interface DeductCreditsResponse {
  success: boolean;
  message: string;
  newBalance?: {
    mockInterviewCredits: number;
    formalInterviewCredits: number;
    mianshijunBalance: number;
  };
  currentCredits?: number;
}

// 扣费API调用
export const deductCredits = async (data: DeductCreditsRequest): Promise<DeductCreditsResponse> => {
  // fetchWithAuth 直接返回解析后的JSON数据
  return await fetchWithAuth<DeductCreditsResponse>('/credits/deduct', {
    method: 'POST',
    body: data,
  });
};

// 检查并扣费的便捷函数
export const checkAndDeductCredits = async (
  type: DeductType,
  reason: string
): Promise<{ success: boolean; message: string; newBalance?: any }> => {
  try {
    const result = await deductCredits({ type, reason });
    return result;
  } catch (error: any) {
    return {
      success: false,
      message: error.message || '验证失败，请稍后再试'
    };
  }
};
