import React, { useEffect } from 'react';
import { Routes, Route, Link, Navigate } from 'react-router-dom';
import Layout from './components/Layout'; // 我们刚刚创建的布局 (The layout we just created)
import HomePage from './pages/HomePage';
import ApiTest from './components/ApiTest'; // 导入 API 测试组件
import Dashboard from './components/Dashboard'; // 仪表盘组件

// 导入日志保存功能
import './utils/logSaver';

// 开发环境连接监控
import ConnectionMonitor from './components/debug/ConnectionMonitor';
import { useConnectionMonitor } from './hooks/useConnectionMonitor';

// 统一的认证页面
import StandaloneLoginPage from './pages/StandaloneLoginPage';
import InterviewSetupPage from './pages/InterviewSetupPage'; // 导入面试准备页面
import AIInterviewPage from './pages/AIInterviewPage'; // 导入AI正式面试页面
import LiveInterviewPage from './pages/LiveInterviewPage'; // 导入实时面试页面
import MockInterviewConfigPage from './pages/MockInterviewConfigPage'; // 导入AI模拟面试配置页面
import MockInterviewSessionPage from './pages/MockInterviewSessionPage'; // 导入AI模拟面试实时页面

// 个人中心和设置页面
import Profile from './pages/Profile'; // 导入个人中心页面
import Settings from './pages/Settings'; // 导入设置页面
import PricingPage from './pages/PricingPage'; // 导入充值中心页面
import PaymentQRPage from './pages/PaymentQRPage'; // 导入支付二维码页面
import PaymentSuccessPage from './pages/PaymentSuccessPage'; // 导入支付成功页面
import OrdersPage from './pages/OrdersPage'; // 导入我的订单页面
import UsageRecordsPage from './pages/UsageRecordsPage'; // 导入消耗记录页面
import ReferralRewardsPage from './pages/ReferralRewardsPage'; // 导入分享有礼页面
import NotFoundPage from './pages/NotFoundPage'; // 导入404页面
import UnifiedWebSocketTest from './components/test/UnifiedWebSocketTest'; // 导入WebSocket测试组件

// 认证状态管理
import useAuthStore from './stores/authStore';
import ErrorBoundary from './components/ErrorBoundary';
import { ToastProvider } from './contexts/ToastContext';
import { initializeTheme, setupSystemThemeListener } from './stores/userStore';
import { WebSocketProvider } from './providers/WebSocketProvider';
import { FeatureFlagProvider } from './providers/FeatureFlagProvider';

// 扩展useAuthStore类型以包含persist属性
declare module './stores/authStore' {
  interface AuthStore {
    persist?: {
      hasHydrated?: () => boolean;
    };
  }
}

// 受保护的路由组件，用于需要登录才能访问的页面
const ProtectedRoute: React.FC<{ children: JSX.Element }> = ({ children }) => {
  const { isAuthenticated } = useAuthStore(); // 使用 hook 形式以响应状态变化

  // 检查 Zustand persist 是否已经水合完成
  const hasHydrated = (useAuthStore as any).persist?.hasHydrated?.() ?? true;

  console.log('ProtectedRoute: hasHydrated =', hasHydrated, 'isAuthenticated =', isAuthenticated);

  // 如果还没有水合完成，显示加载状态
  if (!hasHydrated) {
    console.log('ProtectedRoute: Waiting for hydration...');
    return <div className="flex items-center justify-center min-h-screen">
      <div className="text-lg">正在加载认证状态...</div>
    </div>;
  }

  if (!isAuthenticated) {
    console.log('ProtectedRoute: User not authenticated, redirecting to login');
    return <Navigate to="/login" replace />; // 指向统一的登录页
  }

  console.log('ProtectedRoute: User authenticated, rendering protected content');
  return children;
};

function App() {
  const { initializeAuth, isAuthenticated } = useAuthStore();

  // 🔍 连接监控（开发环境）
  const { isMonitorVisible, setIsMonitorVisible } = useConnectionMonitor();

  // 检查 Zustand persist 是否已经水合完成
  const hasHydrated = (useAuthStore as any).persist?.hasHydrated?.() ?? true;

  useEffect(() => {
    initializeAuth();

    // 初始化主题
    initializeTheme();

    // 设置系统主题监听器
    const cleanupThemeListener = setupSystemThemeListener();

    // 添加全局错误处理，捕获浏览器扩展相关错误
    const handleGlobalError = (event: ErrorEvent) => {
      // 过滤掉浏览器扩展相关的错误
      if (event.error && event.error.message &&
          (event.error.message.includes('message channel closed') ||
           event.error.message.includes('Extension context invalidated') ||
           event.error.message.includes('listener indicated an asynchronous response'))) {
        console.warn('浏览器扩展相关错误，已忽略:', event.error.message);
        event.preventDefault();
        return;
      }
    };

    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      // 过滤掉浏览器扩展相关的Promise拒绝
      if (event.reason && typeof event.reason === 'string' &&
          (event.reason.includes('message channel closed') ||
           event.reason.includes('Extension context invalidated') ||
           event.reason.includes('listener indicated an asynchronous response'))) {
        console.warn('浏览器扩展相关Promise拒绝，已忽略:', event.reason);
        event.preventDefault();
        return;
      }
    };

    window.addEventListener('error', handleGlobalError);
    window.addEventListener('unhandledrejection', handleUnhandledRejection);

    return () => {
      window.removeEventListener('error', handleGlobalError);
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
      cleanupThemeListener(); // 清理主题监听器
    };
  }, [initializeAuth]);

  // 等待认证状态初始化和水合完成
  if (!hasHydrated) {
    return <div className="flex items-center justify-center min-h-screen">
      <div className="text-lg">正在初始化应用...</div>
    </div>;
  }

  return (
    <ErrorBoundary>
      <FeatureFlagProvider>
        <WebSocketProvider>
          <ToastProvider>
        <Routes>
        {/* 统一的登录和注册页面 - 不使用 Layout */}
        <Route path="/login" element={isAuthenticated ? <Navigate to="/dashboard" replace /> : <StandaloneLoginPage initialMode="login" />} />
        <Route path="/register" element={isAuthenticated ? <Navigate to="/dashboard" replace /> : <StandaloneLoginPage initialMode="register" />} />

        {/* 实时面试页面 - 需要登录才能访问，但不使用Layout */}
        <Route path="/interview/live" element={
          <ProtectedRoute>
            <LiveInterviewPage />
          </ProtectedRoute>
        } />

        {/* AI模拟面试配置页面 - 需要登录才能访问，但不使用Layout顶栏 */}
        <Route path="/interview/mock/config" element={
          <ProtectedRoute>
            <MockInterviewConfigPage />
          </ProtectedRoute>
        } />

        {/* AI模拟面试实时页面 - 需要登录才能访问，但不使用Layout */}
        <Route path="/interview/mock/session" element={
          <ProtectedRoute>
            <MockInterviewSessionPage />
          </ProtectedRoute>
        } />

        {/* 支付二维码页面 - 需要登录才能访问，但不使用Layout */}
        <Route path="/payment-qr" element={
          <ProtectedRoute>
            <PaymentQRPage />
          </ProtectedRoute>
        } />

        {/* 支付成功页面 - 需要登录才能访问，但不使用Layout */}
        <Route path="/payment-success" element={
          <ProtectedRoute>
            <PaymentSuccessPage />
          </ProtectedRoute>
        } />

        {/* 旧的 /new-login, /new-register, /ai-login 可以移除或重定向到新的 /login 或 /register */}
        <Route path="/new-login" element={<Navigate to="/login" replace />} />
        <Route path="/new-register" element={<Navigate to="/register" replace />} />
        <Route path="/ai-login" element={<Navigate to="/login" replace />} /> {/* 假设 AI 登录也统一到标准登录 */}

        {/* 其他使用主 Layout 的页面 */}
        <Route path="/" element={<Layout />}>
          {/* 'index' 表示这是访问 "/" 时的默认子路由 */}
          <Route index element={<HomePage />} />

          {/* 仪表盘页面 - 需要登录才能访问 */}
          <Route path="dashboard" element={
            <ProtectedRoute>
              <Dashboard />
            </ProtectedRoute>
          } />

          {/* 面试准备页面 - 需要登录才能访问 */}
          <Route path="interview-setup" element={
            <ProtectedRoute>
              <InterviewSetupPage />
            </ProtectedRoute>
          } />

          {/* AI正式面试页面 - 需要登录才能访问 */}
          <Route path="ai-interview" element={
            <ProtectedRoute>
              <AIInterviewPage />
            </ProtectedRoute>
          } />



          {/* WebSocket测试页面 - 需要登录才能访问 */}
          <Route path="websocket-test" element={
            <ProtectedRoute>
              <UnifiedWebSocketTest />
            </ProtectedRoute>
          } />

          {/* 实时面试页面已移至Layout外，见下方 */}

          {/* 上传简历页面 - 需要登录才能访问 */}
          <Route path="resume-upload" element={
            <ProtectedRoute>
              <InterviewSetupPage />
            </ProtectedRoute>
          } />

          {/* 个人中心页面 - 需要登录才能访问 */}
          <Route path="profile" element={
            <ProtectedRoute>
              <Profile />
            </ProtectedRoute>
          } />

          {/* 设置页面 - 需要登录才能访问 */}
          <Route path="settings" element={
            <ProtectedRoute>
              <Settings />
            </ProtectedRoute>
          } />

          {/* 充值中心页面 - 需要登录才能访问 */}
          <Route path="pricing" element={
            <ProtectedRoute>
              <PricingPage />
            </ProtectedRoute>
          } />

          {/* 我的订单页面 - 需要登录才能访问 */}
          <Route path="orders" element={
            <ProtectedRoute>
              <OrdersPage />
            </ProtectedRoute>
          } />

          {/* 消耗记录页面 - 需要登录才能访问 */}
          <Route path="usage-records" element={
            <ProtectedRoute>
              <UsageRecordsPage />
            </ProtectedRoute>
          } />

          {/* 分享有礼页面 - 需要登录才能访问 */}
          <Route path="referral-rewards" element={
            <ProtectedRoute>
              <ReferralRewardsPage />
            </ProtectedRoute>
          } />

          {/* 重定向旧的登录和注册路由到新页面 */}
          <Route path="login" element={<Navigate to="/login" replace />} />
          <Route path="register" element={<Navigate to="/register" replace />} />

          {/* API测试页面 */}
          <Route path="api-test" element={<ApiTest />} />

          {/* 404页面 */}
          <Route path="*" element={<NotFoundPage />} />
        </Route>
      </Routes>
          </ToastProvider>
        </WebSocketProvider>
      </FeatureFlagProvider>

      {/* 🔍 开发环境连接监控面板 */}
      {process.env.NODE_ENV === 'development' && (
        <ConnectionMonitor
          isVisible={isMonitorVisible}
          onClose={() => setIsMonitorVisible(false)}
        />
      )}
    </ErrorBoundary>
  );
}

export default App;