# 验证码登录系统使用说明

## 📋 系统概述

面试君验证码登录系统已成功配置并部署，支持邮箱验证码登录和自动用户注册功能。系统包含完整的安全增强、监控告警和用户体验优化功能。

## 🎯 主要功能

### ✅ 已实现功能
- **邮箱验证码登录**: 支持通过邮箱验证码进行登录
- **自动用户注册**: 新用户首次登录时自动创建账户
- **阿里云邮件推送**: 使用阿里云邮件推送服务发送验证码
- **频率限制**: 防止验证码滥发，60秒内只能发送一次
- **Redis缓存**: 验证码存储在Redis中，有效期5分钟
- **现代化邮件模板**: 美观的HTML邮件模板，匹配项目UI风格
- **系统健康检查**: 实时监控各服务状态
- **安全增强**: IP频率限制、设备指纹、异常检测
- **监控告警**: 邮件发送失败监控和告警
- **前端优化**: 改进的Toast通知和错误处理

### 🔄 待实现功能
- **短信验证码**: 阿里云短信服务集成（代码已完成，需配置密钥）
- **密码登录禁用**: 临时禁用密码登录功能

## 🛠️ 技术配置

### 邮件服务配置
```env
# 阿里云邮件推送服务
SMTP_HOST=smtpdm.aliyun.com
SMTP_PORT=25
SMTP_USER=<EMAIL>
SMTP_PASS=20030826MSJmsj
SMTP_FROM=面试君 <<EMAIL>>
```

### Redis配置
```env
# Redis缓存服务
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=
```

### DNS配置
已在域名 `mianshijun.xyz` 配置以下记录：
- **MX记录**: `mxdm.aliyun.com` (优先级 5)
- **TXT记录**: `v=spf1 include:spfdm.aliyun.com -all`
- **CNAME记录**: `dm._domainkey.mianshijun.xyz` → `dm._domainkey.mianshijun.xyz.aliyunemail.net`

## 🔗 API 端点

### 系统健康检查
```http
GET /api/system/health
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "health": {
      "redis": true,
      "database": true,
      "email": true,
      "sms": false,
      "score": 75,
      "status": "healthy"
    },
    "statistics": {
      "last24Hours": {
        "email": {
          "sent": 10,
          "failed": 1,
          "successRate": "90.91"
        }
      }
    }
  }
}
```

### 发送验证码
```http
POST /api/auth/send-verification-code
Content-Type: application/json

{
  "identifier": "<EMAIL>",
  "type": "EMAIL",
  "purpose": "LOGIN"
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "验证码已发送",
  "data": {
    "expiresIn": 300,
    "canResendAfter": 60
  }
}
```

### 验证码登录
```http
POST /api/auth/login-with-code
Content-Type: application/json

{
  "identifier": "<EMAIL>",
  "code": "123456",
  "type": "EMAIL"
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "登录成功",
  "data": {
    "id": "user_id",
    "email": "<EMAIL>",
    "name": "用户名",
    "token": "jwt_token"
  }
}
```

## 🎨 前端组件

### VerificationCodeInput
6位数字验证码输入组件，支持：
- 自动聚焦下一个输入框
- 粘贴验证码自动分配
- 键盘导航（方向键、退格键）
- 错误状态显示

### SendCodeButton
发送验证码按钮组件，支持：
- 发送中加载状态
- 60秒倒计时防重复发送
- 自定义样式和文本

## 🧪 测试方法

### 1. 邮件服务测试
```bash
# 在项目根目录运行
node -e "
const nodemailer = require('nodemailer');
require('dotenv').config({ path: './backend/.env' });
const transporter = nodemailer.createTransport({
  host: process.env.SMTP_HOST,
  port: parseInt(process.env.SMTP_PORT),
  secure: false,
  auth: { user: process.env.SMTP_USER, pass: process.env.SMTP_PASS }
});
transporter.verify().then(() => console.log('✅ 邮件服务连接成功')).catch(err => console.error('❌ 邮件服务连接失败:', err));
"
```

### 2. 验证码流程测试
```bash
# 发送验证码
curl -X POST http://localhost:3000/api/auth/send-verification-code \
  -H "Content-Type: application/json" \
  -d '{"identifier":"<EMAIL>","type":"EMAIL","purpose":"LOGIN"}'

# 验证码登录（替换为实际验证码）
curl -X POST http://localhost:3000/api/auth/login-with-code \
  -H "Content-Type: application/json" \
  -d '{"identifier":"<EMAIL>","code":"123456","type":"EMAIL"}'
```

## 🔒 安全特性

### 验证码安全
- **有效期限制**: 验证码5分钟后自动失效
- **频率限制**: 60秒内只能发送一次验证码
- **一次性使用**: 验证码使用后立即失效
- **数字随机**: 6位随机数字验证码

### IP安全防护
- **频率限制**: 每个IP每10分钟最多10次请求
- **自动封禁**: 超过限制自动封禁30分钟
- **异常检测**: 检测可疑活动模式
- **设备指纹**: 限制每用户最多5个设备

### 邮件安全
- **SPF记录**: 防止邮件伪造
- **DKIM签名**: 邮件完整性验证
- **专用域名**: 使用专用发信域名

### 监控告警
- **失败监控**: 自动监控发送失败率
- **告警通知**: 超过阈值自动发送告警邮件
- **健康检查**: 实时监控系统各组件状态
- **统计报表**: 24小时发送统计和成功率

## 📱 用户体验

### 邮件模板特色
- **现代化设计**: 匹配项目蓝色渐变主题
- **响应式布局**: 支持移动端和桌面端
- **动画效果**: 浮动装饰元素
- **清晰提示**: 安全提醒和使用说明

### 前端交互
- **实时验证**: 输入格式实时检查
- **智能聚焦**: 自动聚焦到下一个输入框
- **错误提示**: 友好的错误信息显示
- **加载状态**: 发送和验证过程的加载提示

## 🚀 部署说明

### 环境要求
- Node.js 18+
- Redis 6+
- PostgreSQL 13+
- 阿里云邮件推送服务

### 启动服务
```bash
# 启动Redis
redis-server

# 启动后端服务
cd backend && npm run dev

# 启动前端服务
cd frontend && npm run dev
```

## 📞 技术支持

如遇到问题，请检查：
1. Redis服务是否正常运行
2. 环境变量配置是否正确
3. 阿里云邮件推送服务状态
4. 域名DNS记录配置

---

**© 2025 面试君 - 让每一次面试都成为成功的开始**
