# 自研商家接入

**商家自研接入流程**

![img](https://yuque.antfin.com/images/lark/0/2022/jpeg/210044/1669341763359-3a3b95dc-6088-4b6b-9128-ed7679b7a37c.jpeg)

## 创建应用

登录支付宝开放平台创建 [网页/移动应用](https://open.alipay.com/develop/pm/create?templateId=6-bcb9-7250e6fd2c431487669730456&from=payappsite)。

**说明：**生成的应用唯一标识 APPID 可用于调用开放产品接口。

## 配置应用

在 **开发** > **开发设置** 中配置应用信息。

![img](http://mdn.alipayobjects.com/afts/img/A*aQGqRLRTh3oAAAAAAAAAAAAAAa8wAA/original?bz=openpt_doc&t=xHf3eJsM7ltT37DB6D43YxWQo_SFiEhCjukDjMwAa40DAAAAZAAAMK8AAAAA)

### 接口加签方式

**必填。**用于防止数据篡改，保障应用和支付宝交互的安全性，可查看 [接口加签方式](https://opendocs.alipay.com/common/02mriz)。

**说明**：若产品涉及 [资金支出的接口](https://opendocs.alipay.com/common/02kg66) 必须 [设置证书加签方式](https://opendocs.alipay.com/common/056zub?pathHash=91c49771)。

![img](https://cdn.nlark.com/yuque/0/2024/png/179989/1709794229988-b9e9660d-3ab4-4c62-bc86-5b690bcd8226.png)

### 服务器 IP 白名单

**选填。**用于提高应用访问开放平台的安全性，避免因应用私钥泄漏等原因导致业务受损，保障用户资金安全，可查看 [服务器 IP 白名单](https://opendocs.alipay.com/common/02kg65)。

![img](https://cdn.nlark.com/yuque/0/2022/png/179989/1658400829561-01f1d120-1c55-4445-9ac2-bc8830ce8518.png)

### 应用网关

**必填。**用于接收支付宝异步通知消息，需要传入 http(s) 公网可访问网页地址，可查看 [应用网关](https://opendocs.alipay.com/common/02qibh)。

![img](https://cdn.nlark.com/yuque/0/2024/png/179989/1709794346222-3f2ab68c-8ca8-4600-af9d-ec6499592680.png)

### 接口内容加密方式

**选填。**用于加 / 解密 OpenAPI bizContent 报文内容，可大幅提升接口内容传输的安全性。可查看 [接口内容加密方式](https://opendocs.alipay.com/common/02mse3)。

![img](https://cdn.nlark.com/yuque/0/2024/png/179989/1709794380678-c1161394-855c-4102-9d04-35ef3f6e4b2e.png)

### 授权回调地址

**选填**。**网页/移动应用** 指定的回调页面 URL，[用户信息授权](https://opendocs.alipay.com/open/02xtl7#关于 redirect_uri 的说明) 成功后，将在该 URL 后携带授权码等信息并跳转至该页面。

**说明：**授权链接中配置的 redirect_uri 的值必须与此回调地址保持一致 (如：`https://www.alipay.com`) 。

![img](https://cdn.nlark.com/yuque/0/2022/png/179989/1665387736367-717a6dae-da9c-4a79-8a96-66fc344185c6.png)

### 订阅消息服务

**选填**。异步消息通知，用于提高应用 API 的调用效率。详情可查看 [主动推送服务（From 蚂蚁）](https://opendocs.alipay.com/common/02km9j)和 [消息回流服务（To 蚂蚁）](https://opendocs.alipay.com/common/02kiq0)。

![img](https://cdn.nlark.com/yuque/0/2024/png/179989/1709794514446-dc83e6b1-b8a1-450a-b598-b589dc104b30.png)

## 上线应用

**网页/移动应用：**需要手动上线。**提交审核后，预计 1 个工作日的审核时间**。详细步骤可点击查看 [上线应用](https://opendocs.alipay.com/open/200/golive/) 。应用上线后，还需要完成产品开通才能在线上环境（生产环境）使用产品。

## 绑定商家账号

可选操作。在支付宝开放平台创建的应用归属于对应的开放平台账号。如果要在应用中使用支付和资金等相关产品，需要将应用和支付宝商家平台账号绑定，应用才可调用需要商家开通的产品，具体步骤可查看 [绑定应用](https://opendocs.alipay.com/open/0128wr)。

## 开通产品

应用上线后，还需要完成开通才能使用 **电脑网站支付**。请在 商家平台 > [产品中心](https://b.alipay.com/page/product-mall/all-product) 的 [电脑网站支付产品详情页](https://b.alipay.com/page/product-mall/product-detail/I1080300001000041203) 点击 [立即开通](https://b.alipay.com/page/product-mall/product-detail/I1080300001000041016)，填写并提交相关信息完成产品开通。详情可点击查看 [开通产品](https://opendocs.alipay.com/open/200/105314/) 。 

如您已经开通当面付，则无需再次开通，开通状态可点击 [产品开通情况](https://mrchportalweb.alipay.com/dynlink/productSign/signManage.htm) 查询。

# 服务商代开发模式接入

**服务商代开发接入流程**

![img](https://yuque.antfin.com/images/lark/0/2022/jpeg/210044/1669341875794-86f4a410-ae36-4702-9f2e-552d5f1d1be3.jpeg)

## 创建应用

服务商登录 [支付宝开放平台](https://open.alipay.com/)，创建 **第三方应用** 并提交审核。详情可查看 [创建&配置第三方应用](https://opendocs.alipay.com/isv/03kvcj)。

**说明：**生成的应用唯一标识 APPID 可用于调用开放产品接口。

## 开通产品

服务商可以通过以下三种方式之一，协助商家开通 **电脑网站支付**：

- 请商家登录 商家平台 > 产品中心的 [电脑网站支付产品详情页](https://b.alipay.com/page/product-mall/product-detail/I1080300001000041203) 点击开通此产品。
- 收集商家资料，登录 [服务商平台](http://p.alipay.com) 协助商家开通此产品。
- 收集商家资料，通过 [接口](https://opendocs.alipay.com/isv/04f525) 协助商家开通此产品。

## 获取代开发授权

1. 服务商登录 [支付宝开放平台](https://open.alipay.com/) > **第三方应用** > **商家授权** > **授权范围选择** 页面，选择对应产品。
2. 服务商根据 [第三方应用授权](https://opendocs.alipay.com/isv/10467/xldcyq) 指引，获取商家代开发授权，以此获取 `app_auth_token`（商家授权令牌）用于调用产品相关接口。

**说明**：可根据产品实际情况，选择 [全权委托授权代开发模式](https://opendocs.alipay.com/isv/064ndg)，减少拓展业务的成本。

## 接口调用

服务商代商家调用服务端接口时：

1. 需传入第三方应用授权得到商家授权令牌（app_auth_token）作为请求参数传入。
2. 使用第三方应用的支付宝公钥、应用公钥、应用私钥、APPID（证书模式使用第三方应用证书）构造 AlipayClient，其余入参与接口文档相同。

更多详情可查看 [代商家调用接口说明](https://opendocs.alipay.com/isv/01jg7a)。

## 订阅消息服务

服务商可在第三方应用详情页选择 **开发设置**，在页面下方 **消息服务** 中完成服务商自订阅消息服务。消息服务详情可查看 [主动推送服务（From 蚂蚁）](https://opendocs.alipay.com/common/02km9j)和 [消息回流服务（To 蚂蚁）](https://opendocs.alipay.com/common/02kiq0)。

![img](https://cdn.nlark.com/yuque/0/2022/png/179989/1671433708670-f44f463e-5ed7-466a-8118-ced6026845f6.png)

# 集成并配置 SDK

服务端 SDK 需要商家或服务商集成在服务端系统中，用于后续的服务端接口调用。

## 下载服务端 SDK

为了帮助开发者调用开放接口，支付宝提供了开放平台服务端 SDK，包含 Java、PHP、NodeJS、Python 和 .NET 五种语言，封装了签名与验签、HTTP 接口请求等基础功能。

**说明**：请先下载对应语言版本的最新版 [服务端 SDK](https://opendocs.alipay.com/common/02n6z6) 并引入开发工程。

## 接口调用配置

在 SDK 调用具体的接口前需要进行 alipayClient 对象初始化。alipayClient 对象只需要初始化一次，后续调用不同的接口都可以使用同一个 alipayClient 对象。

### 公钥模式加签

接口加签方式为 **公钥** 模式加签时 alipayClient 对象初始化的 Java 语言示例代码：

```java
AlipayConfig alipayConfig = new AlipayConfig();
alipayConfig.setServerUrl(URL);
alipayConfig.setAppId(APP_ID);
alipayConfig.setPrivateKey(PRIVATE_KEY);
alipayConfig.setFormat("json");
alipayConfig.setCharset(CHARSET);
alipayConfig.setAlipayPublicKey(ALIPAY_PUBLIC_KEY);
alipayConfig.setSignType(SIGN_TYPE);
//构造client
AlipayClient alipayClient = new DefaultAlipayClient(alipayConfig);
```

#### 关键参数说明

| **配置参数**      | **示例值解释**                                           | **获取方式/示例值**                                          |
| ----------------- | -------------------------------------------------------- | ------------------------------------------------------------ |
| URL               | 支付宝网关（固定）。                                     | `https://openapi.alipay.com/gateway.do`                      |
| APPID             | APPID 即创建应用后生成。                                 | 获取可查看 [获取 APPID](https://opendocs.alipay.com/common/02nebp)。 |
| PRIVATE_KEY       | 开发者私钥，由开发者自己生成。                           | 获取可查看 [接口加签方式](https://opendocs.alipay.com/common/02mriz)。 |
| FORMAT            | 参数返回格式，只支持 JSON（固定）。                      | JSON                                                         |
| CHARSET           | 编码集，支持 GBK/UTF-8。                                 | 开发者根据实际工程编码配置。                                 |
| ALIPAY_PUBLIC_KEY | 支付宝公钥，由支付宝生成。                               | 获取详情可查看 [接口加签方式](https://opendocs.alipay.com/common/02mriz)。 |
| SIGN_TYPE         | 生成签名字符串所使用的签名算法类型，目前支持 RSA2 算法。 | RSA2                                                         |

### 公钥证书模式加签

接口加签方式为 **公钥证****书** 模式加签时，alipayClient 对象初始化的 Java 语言示例代码如下：

**注意：**若使用 **公钥证书** 模式进行加签，需额外引入如下 JAR 包：

- [bcprov-jdk15on](https://mvnrepository.com/artifact/org.bouncycastle/bcprov-jdk15on)
- [commons-logging](https://mvnrepository.com/artifact/commons-logging/commons-logging)
- [fastjson](https://mvnrepository.com/artifact/com.alibaba/fastjson)

```java
CertAlipayRequest certAlipayRequest = new CertAlipayRequest();  
certAlipayRequest.setServerUrl(URL);  
certAlipayRequest.setAppId(APPID);  
certAlipayRequest.setPrivateKey(PRIVATE_KEY);  
certAlipayRequest.setFormat("json");  
certAlipayRequest.setCharset(CHARSET);  
certAlipayRequest.setSignType(SIGN_TYPE);  
certAlipayRequest.setCertPath(app_cert_pathAPP_CERT_PATH);  
certAlipayRequest.setAlipayPublicCertPath(alipay_cert_path);  
certAlipayRequest.setRootCertPath(alipay_root_cert_path );  
DefaultAlipayClient alipayClient = new DefaultAlipayClient(certAlipayRequest);
// 提交数据至支付宝时请使用  
alipayClient.certificateExecute(request);
```

#### 关键参数说明

| **配置参数**          | **示例值解释**                                           | **获取方式/示例值**                                          |
| --------------------- | -------------------------------------------------------- | ------------------------------------------------------------ |
| URL                   | 支付宝网关（固定）。                                     | `https://openapi.alipay.com/gateway.do`                      |
| APPID                 | APPID 即创建应用后生成。                                 | 获取可查看 [获取 APPID](https://opendocs.alipay.com/common/02nebp)。 |
| PRIVATE_KEY           | 开发者私钥，由开发者自己生成。                           | 获取可查看 [接口加签方式](https://opendocs.alipay.com/common/02mriz)。 |
| FORMAT                | 参数返回格式，只支持 JSON（固定）。                      | JSON                                                         |
| CHARSET               | 编码集，支持 GBK/UTF-8。                                 | 开发者根据实际工程编码配置。                                 |
| SIGN_TYPE             | 生成签名字符串所使用的签名算法类型，目前支持 RSA2 算法。 | RSA2                                                         |
| app_cert_path         | 应用公钥证书文件本地路径。                               | 获取详情可查看 [接口加签方式](https://opendocs.alipay.com/common/02mriz)。 |
| alipay_cert_path      | 支付宝公钥证书文件本地路径。                             | 获取详情可查看 [接口加签方式](https://opendocs.alipay.com/common/02mriz)。 |
| alipay_root_cert_path | 支付宝根证书文件本地路径。                               | 获取详情可查看 [接口加签方式](https://opendocs.alipay.com/common/02mriz)。 |