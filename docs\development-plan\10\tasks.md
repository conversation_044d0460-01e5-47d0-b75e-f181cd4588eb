# Implementation Plan

## 状态机核心重构

- [x] 1. 更新状态机状态定义以匹配设计文档



  - 修改 `frontend/src/machines/mockInterviewMachine.ts` 中的状态定义
  - 将状态从当前的 `idle, initializing, requestingMicPermission, connecting, ready, active` 更新为设计文档中的 `INITIALIZING, WAITING_FOR_AI_QUESTION, LISTENING_FOR_ANSWER, PROCESSING_ANSWER, COMPLETED, ERROR`
  - 更新状态转换逻辑以符合设计文档中的状态机图
  - _Requirements: 6.1, 6.2, 6.3, 6.4, 6.5_

- [x] 2. 实现正确的面试流程状态转换



  - 修改状态机以确保 AI 先提问，然后用户回答的流程
  - 实现 `WAITING_FOR_AI_QUESTION -> LISTENING_FOR_ANSWER -> PROCESSING_ANSWER -> WAITING_FOR_AI_QUESTION` 循环
  - 移除页面加载时立即启动 ASR 的逻辑
  - _Requirements: 2.1, 2.2, 2.3, 2.4, 2.5_

## ASR 生命周期管理系统





- [x] 3. 创建 ASR 生命周期管理器
  - ✅ 创建了 `frontend/src/services/ASRLifecycleManager.ts` 文件
  - ✅ 创建了 `frontend/src/services/AudioRecordingManager.ts` 音频录制管理器
  - ✅ 创建了 `frontend/src/utils/EventEmitter.ts` 事件发射器工具类
  - ✅ 实现了 `ASRLifecycleManager` 类，包含完整的生命周期管理方法
  - ✅ 实现了静音检测、转录更新和错误处理的事件监听器
  - ✅ 添加了 ASR 服务失败时的备用提供商切换逻辑和指数退避重试
  - _Requirements: 4.1, 4.2, 4.3, 4.4, 4.6_

- [x] 4. 集成 ASR 管理器到状态机
  - ✅ 修改了 `mockInterviewMachine.ts` 以使用真实的 ASR 生命周期管理器
  - ✅ 替换了占位符 `audioRecordingService` 为实际的 `asrLifecycleService`
  - ✅ 创建了 `useMockInterviewStateMachine.ts` Hook 集成所有管理器
  - ✅ 实现了 AI 问题发送后自动启动 ASR 的逻辑
  - ✅ 实现了基于静音检测的自动 ASR 停止和状态同步
  - ✅ 集成了 WebSocket 连接管理器和错误恢复机制
  - _Requirements: 4.1, 4.2, 4.3, 4.4_

## 消息类型系统重构

- [x] 5. 验证和完善消息类型隔离
  - ✅ 检查并完善了 `backend/websocket/handlers/messageHandler.ts` 中的消息路由逻辑
  - ✅ 确保模拟面试和正式面试消息类型完全隔离，添加了严格的模式验证
  - ✅ 验证了 `mock_interview_question` 和 `mock_interview_answer` 消息类型的正确处理
  - ✅ 添加了消息类型验证函数，自动拒绝不匹配当前模式的消息
  - ✅ 实现了完整的消息类型分类：通用、正式面试专用、模拟面试专用
  - _Requirements: 5.1, 5.2, 5.3, 5.4, 5.5_

- [x] 6. 增强消息结构以支持个性化问题生成
  - ✅ 已实现 `MockInterviewQuestionMessage` 接口，包含 `questionType`, `difficulty`, `expectedDuration` 字段
  - ✅ 已实现 `MockInterviewAnswerMessage` 接口，包含 `duration`, `confidence` 字段
  - ✅ 后端消息生成逻辑已包含这些字段
  - _Requirements: 3.1, 3.2, 3.3, 3.4_

## 个性化问题生成系统

- [x] 7. 实现个性化问题生成逻辑
  - ✅ `MockInterviewService` 已实现基于公司名称和岗位名称的第一个问题生成
  - ✅ 已实现基于用户回答内容的后续问题动态生成（使用LLM）
  - ✅ 已实现问题去重逻辑，确保不重复已问的问题
  - _Requirements: 3.1, 3.2, 3.3, 3.4_

- [x] 8. 创建问题模板库和备用机制
  - ✅ `MockInterviewService` 中已有完整的 `questionTemplates` 数组
  - ✅ 已实现按问题类型（behavioral, technical, situational）分类的模板
  - ✅ 已实现问题生成失败时的备用问题选择逻辑
  - ✅ 已实现问题质量验证和LLM降级机制
  - _Requirements: 3.5, 9.2_

## 错误处理和恢复机制

- [x] 9. 实现全面的错误处理系统
  - ✅ 在状态机中添加了 `ERROR` 状态和错误恢复转换
  - ✅ 实现了 ASR 服务失败的自动恢复机制，包括指数退避重试和备用提供商切换
  - ✅ 实现了 AI 问题生成失败的备用处理，包括网络错误重试和模板问题降级
  - ✅ 实现了 WebSocket 连接断开的自动重连和状态恢复，包括心跳检测和消息队列
  - ✅ 创建了友好的错误提示界面组件，支持错误分类、重试操作和状态显示
  - _Requirements: 9.1, 9.2, 9.3, 9.4, 9.5_

- [x] 10. 创建错误日志和监控系统
  - ✅ 创建了 `backend/services/InterviewErrorLogger.ts` 服务，支持错误分类、日志记录和统计
  - ✅ 实现了前端 `ErrorRecoveryManager.ts` 错误恢复策略管理器
  - ✅ 创建了 `ErrorMonitoringDashboard.tsx` 监控仪表板组件
  - ✅ 集成了错误日志记录到 MockInterviewService 中
  - ✅ 实现了自动错误恢复策略选择和执行
  - _Requirements: 9.5, 9.6_

## 性能优化和用户体验

- [x] 11. 优化 AI 问题生成性能



  - 在 `MockInterviewService` 中实现问题生成缓存机制
  - 优化 LLM 调用以确保 3 秒内完成问题生成
  - 实现问题预生成策略以减少等待时间
  - 添加问题生成超时处理和备用机制



  - _Requirements: 8.1, 8.2, 8.3_

- [x] 12. 优化 WebSocket 消息传输性能


  - 检查和优化 WebSocket 消息大小和频率
  - 实现消息压缩和批处理机制
  - 添加网络延迟监控和自适应调整


  - 确保消息传输延迟低于 500ms
  - _Requirements: 8.4, 8.5_

## 系统兼容性和隔离性验证

### 📊 任务进度更新 (2024-12-19)
- **已完成任务**: 13, 14, 15, 16
- **当前进行**: 准备任务17 (创建端到端集成测试)
- **完成进度**: 16/20 (80%)
- **系统兼容性验证**: ✅ 完成
- **消息路由验证**: ✅ 完成
- **状态机单元测试**: ✅ 完成
- **ASR生命周期管理器测试**: ✅ 完成


- [x] 13. 验证正式面试功能完全不受影响
  - ✅ 创建了 `backend/tests/formal-interview-verification.test.ts` 正式面试功能完整性验证测试
  - ✅ 创建了 `backend/tests/interview-mode-isolation.test.ts` 面试模式隔离性验证测试
  - ✅ 创建了 `backend/tests/websocket-message-routing.test.ts` WebSocket消息路由验证测试
  - ✅ 创建了 `backend/tests/ai-suggestion-functionality.test.ts` AI建议功能专项验证测试
  - ✅ 创建了 `backend/tests/task13-verification-runner.ts` 综合验证测试运行器
  - ✅ 实现了完整的正式面试工作流程验证（配置→启动→进行→结束）
  - ✅ 验证了AI建议功能的DeepSeek API集成、流式输出、响应性能和多轮对话支持
  - ✅ 验证了模拟面试和正式面试的状态完全隔离，包括同时运行、消息路由和错误隔离
  - ✅ 验证了WebSocket消息类型验证逻辑、模式检测路由和不匹配消息拒绝机制
  - ✅ 建立了自动化验证框架，可生成详细的测试报告和状态汇总
  - _Requirements: 7.1, 7.2, 7.3, 7.4, 7.5_



- [x] 14. 实现模式检测和消息路由验证
  - ✅ 增强了 `messageHandler.ts` 中的面试模式检测逻辑，添加了详细的消息验证功能
  - ✅ 实现了基于模式的消息路由和验证，包含通用、正式面试专用、模拟面试专用消息类型分类
  - ✅ 添加了不匹配模式消息的拒绝和增强日志记录，包含错误分类、原因和推荐操作
  - ✅ 实现了错误隔离机制，确保一个模式的错误不影响另一个模式
  - ✅ 添加了统计和监控功能，包括拒绝统计、接受统计和安全阈值检查
  - ✅ 实现了会话级别的安全监控，检测潜在的恶意行为
  - ✅ 创建了 `backend/tests/task14-mode-detection-verification.test.ts` 专项验证测试
  - ✅ 提供了消息路由统计信息获取接口，支持实时监控和调试
  - _Requirements: 7.4, 7.5_

## 测试和质量保证

- [x] 15. 创建状态机单元测试
  - ✅ 更新了 `frontend/src/machines/__tests__/mockInterviewMachine.test.ts` 为增强版测试套件
  - ✅ 实现了完整的状态转换正确性测试，覆盖所有6个状态的转换路径
  - ✅ 测试了所有边界条件，包括空消息、重复事件、无效数据处理
  - ✅ 实现了全面的错误状态和恢复机制测试，包括连接错误、AI错误、ASR错误
  - ✅ 验证了状态机与设计文档的完全一致性，包括状态定义、转换路径、上下文结构
  - ✅ 添加了ASR状态管理验证，确保各状态下ASR状态正确设置
  - ✅ 实现了消息管理验证，测试面试官消息和用户回答消息的正确处理
  - ✅ 添加了守卫条件和验证测试，包括重试限制、最终回答验证、ASR状态更新
  - ✅ 使用现代XState v5 API (createActor) 替代旧版本API
  - ✅ 提供了完整的测试工具函数和模拟数据，便于后续测试扩展
  - _Requirements: 10.1, 10.4_

- [x] 16. 创建 ASR 生命周期管理器测试
  - ✅ 增强了 `frontend/src/services/__tests__/ASRLifecycleManager.test.ts` 测试套件
  - ✅ 实现了全面的ASR场景测试，包括启动、停止、暂停、恢复监听功能
  - ✅ 添加了静音检测和自动停止功能测试，验证静音阈值配置和最大持续时间限制
  - ✅ 实现了备用ASR提供商切换逻辑测试，包括主要提供商失败时的自动切换
  - ✅ 创建了错误处理和恢复机制测试，覆盖网络错误、权限错误、重试限制等场景
  - ✅ 添加了边界条件和性能测试，包括并发操作、快速启动停止循环、内存清理
  - ✅ 实现了语音活动检测测试，验证静音计时器重置功能
  - ✅ 测试了大量转录更新的性能处理能力
  - ✅ 提供了完整的模拟对象和测试数据，支持各种ASR场景模拟
  - ✅ 验证了ASR状态管理的正确性和一致性
  - _Requirements: 10.1, 10.4_

- [ ] 17. 创建端到端集成测试
  - 创建完整面试流程的自动化测试
  - 测试 AI 提问 -> ASR 启动 -> 用户回答 -> AI 分析 -> 生成新问题的完整循环
  - 验证个性化问题生成的正确性
  - 测试各种错误场景下的系统恢复能力
  - _Requirements: 10.2, 10.3, 10.4_

## 最终验证和优化

- [ ] 18. 性能基准测试和优化
  - 验证 AI 问题生成时间 < 3 秒
  - 验证 ASR 启动时间 < 1 秒
  - 验证用户回答处理时间 < 2 秒
  - 验证 WebSocket 消息延迟 < 500ms
  - 验证系统稳定运行时间 > 30 分钟
  - _Requirements: 8.1, 8.2, 8.3, 8.4, 8.5_

- [ ] 19. 用户体验验证和边界情况测试
  - 测试页面初始加载后 AI 主动提问的流程
  - 测试长时间面试会话的稳定性
  - 测试网络断开重连后的状态恢复
  - 验证所有错误情况下的友好提示
  - _Requirements: 2.6, 8.6, 9.6, 10.5_

- [ ] 20. 代码质量和文档完善
  - 添加关键函数和类的 TypeScript 类型注释
  - 完善错误处理的代码注释和文档
  - 优化代码结构和命名规范
  - 确保所有新增代码符合项目编码标准
  - _Requirements: 10.6_