// 功能开关React Context Provider
// 为整个应用提供功能开关访问能力，支持动态更新和实时响应

import React, { createContext, useContext, useEffect, useState, ReactNode, useCallback } from 'react';
import { featureFlagManager, FeatureFlagConfig, isFeatureEnabled } from '../services/featureFlags';
import useAuthStore from '../stores/authStore';

interface FeatureFlagContextValue {
  // 检查功能开关状态
  isEnabled: (flagKey: keyof FeatureFlagConfig) => boolean;
  
  // 获取所有开关状态概览
  getFlagsOverview: () => Record<string, any>;
  
  // 动态更新开关（仅用于开发和测试）
  updateFlag: (flagKey: keyof FeatureFlagConfig, enabled: boolean, rolloutPercentage?: number) => void;
  
  // 紧急熔断
  emergencyDisable: (flagKey: keyof FeatureFlagConfig, reason: string) => void;
  
  // 当前用户的功能开关状态
  userFlags: Record<string, boolean>;
  
  // 是否正在同步远程配置
  isSyncing: boolean;
}

const FeatureFlagContext = createContext<FeatureFlagContextValue | null>(null);

interface FeatureFlagProviderProps {
  children: ReactNode;
}

/**
 * 功能开关Provider
 * 实现绞杀榕模式的核心控制机制
 */
export const FeatureFlagProvider: React.FC<FeatureFlagProviderProps> = ({ children }) => {
  const { user } = useAuthStore();
  const [userFlags, setUserFlags] = useState<Record<string, boolean>>({});
  const [isSyncing, setIsSyncing] = useState(false);

  // 初始化用户ID和同步远程配置
  useEffect(() => {
    if (user?.id) {
      console.log('🎯 FeatureFlagProvider: Setting user ID for personalized flags:', user.id);
      featureFlagManager.setUserId(user.id);
      
      // 同步远程配置
      syncRemoteFlags();
    }
  }, [user?.id]);

  // 同步远程功能开关配置
  const syncRemoteFlags = useCallback(async () => {
    setIsSyncing(true);
    try {
      await featureFlagManager.syncFromRemote();
      updateUserFlags();
      console.log('✅ FeatureFlagProvider: Remote flags synced successfully');
    } catch (error) {
      console.error('❌ FeatureFlagProvider: Failed to sync remote flags:', error);
    } finally {
      setIsSyncing(false);
    }
  }, []);

  // 更新用户的功能开关状态
  const updateUserFlags = useCallback(() => {
    const flags: Record<string, boolean> = {
      'enable-unified-websocket': isFeatureEnabled('enable-unified-websocket'),
      'enable-new-chat-service': isFeatureEnabled('enable-new-chat-service'),
      'enable-new-media-signaling': isFeatureEnabled('enable-new-media-signaling'),
      'enable-session-orchestration': isFeatureEnabled('enable-session-orchestration'),
      'enable-ai-analysis-relay': isFeatureEnabled('enable-ai-analysis-relay'),
      'enable-websocket-debug-logs': isFeatureEnabled('enable-websocket-debug-logs'),
      'enable-connection-monitoring': isFeatureEnabled('enable-connection-monitoring'),
      'enable-performance-metrics': isFeatureEnabled('enable-performance-metrics'),
    };

    setUserFlags(flags);
    
    // 输出当前用户的功能开关状态（仅在开发环境）
    if (process.env.NODE_ENV === 'development') {
      console.log('🎛️ FeatureFlagProvider: User flags updated:', flags);
    }
  }, []);

  // 监听功能开关变化
  useEffect(() => {
    const unsubscribers: (() => void)[] = [];

    // 为每个关键功能开关设置监听器
    const flagKeys: (keyof FeatureFlagConfig)[] = [
      'enable-unified-websocket',
      'enable-new-chat-service',
      'enable-new-media-signaling',
      'enable-session-orchestration',
      'enable-ai-analysis-relay'
    ];

    flagKeys.forEach(flagKey => {
      const unsubscribe = featureFlagManager.onFlagChange(flagKey, (enabled) => {
        console.log(`🔄 FeatureFlagProvider: Flag '${flagKey}' changed to:`, enabled);
        updateUserFlags();
        
        // 如果是关键的WebSocket相关功能被紧急关闭，显示用户通知
        if (!enabled && flagKey.includes('websocket')) {
          console.warn(`⚠️ WebSocket功能 '${flagKey}' 已被关闭，可能影响实时功能`);
        }
      });
      
      unsubscribers.push(unsubscribe);
    });

    // 初始化用户功能开关状态
    updateUserFlags();

    // 清理监听器
    return () => {
      unsubscribers.forEach(unsubscribe => unsubscribe());
    };
  }, [updateUserFlags]);

  // 定期同步远程配置（每5分钟）
  useEffect(() => {
    const interval = setInterval(() => {
      if (!isSyncing) {
        syncRemoteFlags();
      }
    }, 5 * 60 * 1000); // 5分钟

    return () => clearInterval(interval);
  }, [syncRemoteFlags, isSyncing]);

  // Context值
  const contextValue: FeatureFlagContextValue = {
    isEnabled: useCallback((flagKey: keyof FeatureFlagConfig) => {
      return featureFlagManager.isEnabled(flagKey);
    }, []),

    getFlagsOverview: useCallback(() => {
      return featureFlagManager.getFlagsOverview();
    }, []),

    updateFlag: useCallback((flagKey: keyof FeatureFlagConfig, enabled: boolean, rolloutPercentage = 100) => {
      if (process.env.NODE_ENV !== 'development') {
        console.warn('⚠️ Flag updates are only allowed in development environment');
        return;
      }

      featureFlagManager.updateFlag(flagKey, {
        enabled,
        rolloutPercentage: enabled ? rolloutPercentage : 0
      });
      
      updateUserFlags();
    }, [updateUserFlags]),

    emergencyDisable: useCallback((flagKey: keyof FeatureFlagConfig, reason: string) => {
      console.error(`🚨 FeatureFlagProvider: Emergency disabling ${flagKey} - ${reason}`);
      featureFlagManager.emergencyDisable(flagKey, reason);
      updateUserFlags();
    }, [updateUserFlags]),

    userFlags,
    isSyncing
  };

  return (
    <FeatureFlagContext.Provider value={contextValue}>
      {children}
    </FeatureFlagContext.Provider>
  );
};

/**
 * 使用功能开关的Hook
 * 提供简洁的API来访问功能开关状态
 */
export const useFeatureFlags = (): FeatureFlagContextValue => {
  const context = useContext(FeatureFlagContext);
  
  if (!context) {
    throw new Error('useFeatureFlags must be used within a FeatureFlagProvider');
  }
  
  return context;
};

/**
 * 检查单个功能开关的Hook
 * 用于组件级别的条件渲染
 */
export const useFeatureFlag = (flagKey: keyof FeatureFlagConfig): boolean => {
  const { isEnabled } = useFeatureFlags();
  return isEnabled(flagKey);
};

/**
 * 功能开关高阶组件
 * 用于包装需要功能开关控制的组件
 */
export const withFeatureFlag = <P extends object>(
  flagKey: keyof FeatureFlagConfig,
  FallbackComponent?: React.ComponentType<P>
) => {
  return (WrappedComponent: React.ComponentType<P>) => {
    const WithFeatureFlagComponent: React.FC<P> = (props) => {
      const isEnabled = useFeatureFlag(flagKey);

      if (!isEnabled) {
        if (FallbackComponent) {
          return <FallbackComponent {...props} />;
        }
        return null;
      }

      return <WrappedComponent {...props} />;
    };

    WithFeatureFlagComponent.displayName = `withFeatureFlag(${flagKey})(${WrappedComponent.displayName || WrappedComponent.name})`;

    return WithFeatureFlagComponent;
  };
};

/**
 * 功能开关条件渲染组件
 * 用于JSX中的条件渲染
 */
interface FeatureFlagGateProps {
  flag: keyof FeatureFlagConfig;
  children: ReactNode;
  fallback?: ReactNode;
}

export const FeatureFlagGate: React.FC<FeatureFlagGateProps> = ({ flag, children, fallback = null }) => {
  const isEnabled = useFeatureFlag(flag);
  
  return isEnabled ? <>{children}</> : <>{fallback}</>;
};

// 导出类型
export type { FeatureFlagContextValue, FeatureFlagConfig };
