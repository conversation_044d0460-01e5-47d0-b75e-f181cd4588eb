import { useState, useEffect } from 'react';

/**
 * 连接监控Hook - 开发环境专用
 * 提供快捷键打开连接监控面板
 */
export function useConnectionMonitor() {
  const [isMonitorVisible, setIsMonitorVisible] = useState(false);

  useEffect(() => {
    // 只在开发环境启用
    if (process.env.NODE_ENV !== 'development') return;

    const handleKeyDown = (event: KeyboardEvent) => {
      // Ctrl + Shift + M 打开监控面板
      if (event.ctrlKey && event.shiftKey && event.key === 'M') {
        event.preventDefault();
        setIsMonitorVisible(prev => !prev);
        console.log('🔍 Connection Monitor toggled');
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    
    // 在控制台显示提示
    console.log('🔍 Connection Monitor: Press Ctrl+Shift+M to open monitor panel');

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, []);

  return {
    isMonitorVisible,
    setIsMonitorVisible,
    toggleMonitor: () => setIsMonitorVisible(prev => !prev)
  };
}
