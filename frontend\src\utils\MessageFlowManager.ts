// frontend/src/utils/MessageFlowManager.ts
import { Message } from '../hooks/useInterviewSession';
import { BubbleManager, UpdateResult } from './BubbleManager';
import { LLMResponseDeduplicator } from './LLMResponseDeduplicator';
import { MessageValidator, ValidationResult, RepairResult } from './MessageValidator';

export interface TranscriptionData {
  text: string;
  isFinal: boolean;
  timestamp?: number;
  service?: string;
}

export interface ProcessResult {
  messages: Message[];
  success: boolean;
  action: string;
  error?: string;
}

/**
 * 消息流管理器 - 统一管理所有消息流处理逻辑
 * 整合气泡管理、去重处理和消息验证
 */
export class MessageFlowManager {
  private bubbleManager: BubbleManager;
  private deduplicator: LLMResponseDeduplicator;
  private validator: MessageValidator;
  private isProcessing: boolean = false;

  constructor() {
    this.bubbleManager = new BubbleManager();
    this.deduplicator = new LLMResponseDeduplicator();
    this.validator = new MessageValidator();
    
    console.log('🔧 MessageFlowManager initialized with all components');
  }

  /**
   * 处理转录消息
   * @param data 转录数据
   * @param currentMessages 当前消息数组
   * @returns 处理结果
   */
  processTranscriptionMessage(data: TranscriptionData, currentMessages: Message[]): ProcessResult {
    if (this.isProcessing) {
      console.warn('⚠️ MessageFlowManager: Already processing, skipping');
      return {
        messages: currentMessages,
        success: false,
        action: 'skipped_busy',
        error: 'Manager is busy processing another message'
      };
    }

    this.isProcessing = true;

    try {
      // 🔍 诊断日志：处理开始 - 详细信息
      console.log('🔍 MessageFlowManager: Processing transcription START', {
        text: data.text.substring(0, 50) + '...',
        textLength: data.text.length,
        isFinal: data.isFinal,
        timestamp: data.timestamp,
        service: data.service,
        currentMessageCount: currentMessages.length,
        lastMessage: currentMessages.length > 0 ? {
          id: currentMessages[currentMessages.length - 1].id,
          type: currentMessages[currentMessages.length - 1].type,
          content: currentMessages[currentMessages.length - 1].content.substring(0, 30) + '...'
        } : null,
        bubbleManagerState: this.bubbleManager.getState(),
        isProcessing: this.isProcessing
      });

      // 步骤1：验证当前消息完整性
      const validationResult = this.validator.validate(currentMessages);
      let workingMessages = currentMessages;

      if (!validationResult.isValid) {
        console.warn('⚠️ MessageFlowManager: Message integrity issues detected, attempting repair');
        const repairResult = this.validator.repair(currentMessages);
        workingMessages = repairResult.repairedMessages;
        
        console.log('🔧 MessageFlowManager: Repair completed:', {
          originalCount: repairResult.originalCount,
          repairedCount: repairResult.repairedCount,
          actions: repairResult.repairActions
        });
      }

      // 步骤2：处理转录消息
      console.log('🔍 MessageFlowManager: Calling BubbleManager', {
        method: data.isFinal ? 'handleFinalResult' : 'handleIntermediateResult',
        text: data.text.substring(0, 30) + '...',
        workingMessageCount: workingMessages.length,
        bubbleStateBefore: this.bubbleManager.getState()
      });

      let updateResult: UpdateResult;
      if (data.isFinal) {
        updateResult = this.bubbleManager.handleFinalResult(data.text, workingMessages);
      } else {
        updateResult = this.bubbleManager.handleIntermediateResult(data.text, workingMessages);
      }

      // 🔥 修复：确保updateResult包含正确的text字段
      if (!updateResult.text && data.text) {
        updateResult.text = data.text;
        console.log('🔧 MessageFlowManager: Added missing text to updateResult');
      }

      // 🔍 诊断日志：BubbleManager处理结果
      console.log('🔍 MessageFlowManager: BubbleManager result', {
        action: updateResult.action,
        success: updateResult.success,
        index: updateResult.index,
        bubbleId: updateResult.bubble?.id,
        text: updateResult.text?.substring(0, 30) + '...',
        originalText: data.text.substring(0, 30) + '...',
        bubbleStateAfter: this.bubbleManager.getState(),
        error: updateResult.error
      });

      // 步骤3：更新消息数组
      console.log('🔍 MessageFlowManager: Updating messages', {
        workingMessageCount: workingMessages.length,
        updateAction: updateResult.action,
        updateIndex: updateResult.index,
        updateText: updateResult.text?.substring(0, 30) + '...'
      });

      const newMessages = this.updateMessages(workingMessages, updateResult);

      // 🔍 诊断日志：消息更新结果
      console.log('🔍 MessageFlowManager: Messages updated', {
        originalCount: workingMessages.length,
        newCount: newMessages.length,
        lastMessage: newMessages.length > 0 ? {
          id: newMessages[newMessages.length - 1].id,
          type: newMessages[newMessages.length - 1].type,
          content: newMessages[newMessages.length - 1].content.substring(0, 30) + '...'
        } : null
      });

      // 步骤4：验证更新后的消息
      const postValidation = this.validator.quickValidate(newMessages);
      if (!postValidation) {
        console.error('❌ MessageFlowManager: Post-update validation failed, rolling back');
        return {
          messages: currentMessages,
          success: false,
          action: 'rollback_validation_failed',
          error: 'Post-update validation failed'
        };
      }

      console.log('✅ MessageFlowManager: Transcription processing completed:', {
        action: updateResult.action,
        messageCount: newMessages.length,
        bubbleState: this.bubbleManager.getState()
      });

      return {
        messages: newMessages,
        success: true,
        action: `transcription_${updateResult.action}`
      };

    } catch (error) {
      console.error('❌ MessageFlowManager: Error processing transcription:', error);
      return {
        messages: currentMessages,
        success: false,
        action: 'error',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * 处理AI建议片段
   * @param chunk 文本片段
   * @returns 去重后的片段，如果应该过滤则返回null
   */
  processAISuggestionChunk(chunk: string): string | null {
    console.log('🎯 MessageFlowManager: Processing AI suggestion chunk:', {
      chunk: chunk.substring(0, 20) + '...',
      chunkLength: chunk.length
    });

    const result = this.deduplicator.processChunk(chunk);
    
    if (result === null) {
      console.log('🔧 MessageFlowManager: Chunk filtered by deduplicator');
    } else {
      console.log('✅ MessageFlowManager: Chunk accepted');
    }

    return result;
  }

  /**
   * 处理AI建议结束
   * @returns 统计信息
   */
  processAISuggestionEnd(): {
    accumulatedText: string;
    statistics: ReturnType<LLMResponseDeduplicator['getStatistics']>;
  } {
    const accumulatedText = this.deduplicator.getAccumulatedText();
    const statistics = this.deduplicator.getStatistics();
    
    console.log('🏁 MessageFlowManager: AI suggestion completed:', {
      totalLength: accumulatedText.length,
      filterRate: statistics.filterRate + '%',
      totalChunks: statistics.totalChunks,
      acceptedChunks: statistics.acceptedChunks
    });

    // 重置去重器为下一次使用
    this.deduplicator.reset();

    return {
      accumulatedText,
      statistics
    };
  }

  /**
   * 更新消息数组
   * @param messages 当前消息数组
   * @param updateResult 更新结果
   * @returns 新的消息数组
   */
  private updateMessages(messages: Message[], updateResult: UpdateResult): Message[] {
    const newMessages = [...messages];

    switch (updateResult.action) {
      case 'create':
        if (updateResult.bubble) {
          // 🔥 关键修复：新气泡总是添加到最后
          newMessages.push(updateResult.bubble);
          console.log('📝 MessageFlowManager: Added new bubble to end:', {
            bubbleId: updateResult.bubble.id,
            position: newMessages.length - 1,
            totalMessages: newMessages.length
          });
        }
        break;

      case 'update':
        if (updateResult.index !== undefined && updateResult.text !== undefined) {
          // 🔥 关键修复：更新指定索引的气泡
          if (updateResult.index >= 0 && updateResult.index < newMessages.length) {
            newMessages[updateResult.index] = {
              ...newMessages[updateResult.index],
              content: updateResult.text,
              timestamp: Date.now()
            };
            console.log('🔄 MessageFlowManager: Updated bubble at index:', {
              index: updateResult.index,
              bubbleId: newMessages[updateResult.index].id,
              newContent: updateResult.text.substring(0, 30) + '...'
            });
          } else {
            console.error('❌ MessageFlowManager: Invalid update index:', {
              index: updateResult.index,
              messagesLength: newMessages.length
            });
          }
        }
        break;

      case 'finalize':
        if (updateResult.index !== undefined && updateResult.text !== undefined) {
          // 将中间气泡转换为最终气泡
          if (updateResult.index >= 0 && updateResult.index < newMessages.length) {
            const originalId = newMessages[updateResult.index].id;
            newMessages[updateResult.index] = {
              ...newMessages[updateResult.index],
              id: originalId.replace('transcription-current', 'transcription-final'),
              content: updateResult.text,
              timestamp: Date.now()
            };
            console.log('🏁 MessageFlowManager: Finalized bubble at index:', {
              index: updateResult.index,
              oldId: originalId,
              newId: newMessages[updateResult.index].id
            });
          }
        }
        break;

      default:
        console.warn('⚠️ MessageFlowManager: Unknown update action:', updateResult.action);
    }

    // 🔥 关键修复：确保消息按时间戳排序
    const sortedMessages = newMessages.sort((a, b) => a.timestamp - b.timestamp);
    
    console.log('📊 MessageFlowManager: Messages updated and sorted:', {
      originalCount: messages.length,
      newCount: sortedMessages.length,
      action: updateResult.action
    });

    return sortedMessages;
  }

  /**
   * 获取管理器状态
   * @returns 状态信息
   */
  getState(): {
    bubbleState: ReturnType<BubbleManager['getState']>;
    deduplicatorStats: ReturnType<LLMResponseDeduplicator['getStatistics']>;
    isProcessing: boolean;
  } {
    return {
      bubbleState: this.bubbleManager.getState(),
      deduplicatorStats: this.deduplicator.getStatistics(),
      isProcessing: this.isProcessing
    };
  }

  /**
   * 重置所有组件状态
   */
  reset(): void {
    console.log('🔧 MessageFlowManager: Resetting all components');
    this.bubbleManager.reset();
    this.deduplicator.reset();
    this.isProcessing = false;
  }

  /**
   * 验证消息数组
   * @param messages 消息数组
   * @returns 验证结果
   */
  validateMessages(messages: Message[]): ValidationResult {
    return this.validator.validate(messages);
  }

  /**
   * 修复消息数组
   * @param messages 消息数组
   * @returns 修复结果
   */
  repairMessages(messages: Message[]): RepairResult {
    return this.validator.repair(messages);
  }

  /**
   * 获取调试信息
   * @returns 调试信息
   */
  getDebugInfo(): {
    bubbleManager: ReturnType<BubbleManager['getState']>;
    deduplicator: ReturnType<LLMResponseDeduplicator['getDebugInfo']>;
    validator: string;
    isProcessing: boolean;
  } {
    return {
      bubbleManager: this.bubbleManager.getState(),
      deduplicator: this.deduplicator.getDebugInfo(),
      validator: 'MessageValidator active',
      isProcessing: this.isProcessing
    };
  }
}
