import { Resume, ResumeCreateInput, ResumeUpdateInput, TargetPosition, TargetPositionCreateInput, TargetPositionUpdateInput } from './apiService';

// 模拟数据存储
let mockResumes: Resume[] = [];
let mockPositions: TargetPosition[] = [];

// 生成唯一ID
const generateId = () => `mock_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

// 获取当前时间的ISO字符串
const getCurrentTime = () => new Date().toISOString();

// 模拟延迟函数
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// 模拟简历服务
export const mockResumeService = {
  // 创建简历
  createResume: async (data: ResumeCreateInput): Promise<Resume> => {
    // 模拟网络延迟
    await delay(800);
    
    // 创建新简历
    const newResume: Resume = {
      id: generateId(),
      userId: 1, // 模拟用户ID
      fileName: data.fileName,
      filePath: data.filePath,
      fileType: data.fileType || null,
      fileSize: data.fileSize || null,
      jobTitle: data.jobTitle || null,
      uploadTimestamp: getCurrentTime(),
      createdAt: getCurrentTime(),
      updatedAt: getCurrentTime()
    };
    
    // 添加到模拟数据存储
    mockResumes.push(newResume);
    console.log('[模拟API] 创建简历成功:', newResume);
    
    return newResume;
  },
  
  // 获取所有简历
  getResumes: async (): Promise<Resume[]> => {
    // 模拟网络延迟
    await delay(500);
    
    console.log('[模拟API] 获取简历列表:', mockResumes);
    return [...mockResumes];
  },
  
  // 更新简历
  updateResume: async (id: string, data: ResumeUpdateInput): Promise<Resume> => {
    // 模拟网络延迟
    await delay(700);
    
    // 查找要更新的简历
    const resumeIndex = mockResumes.findIndex(resume => resume.id === id);
    if (resumeIndex === -1) {
      throw new Error('简历不存在');
    }
    
    // 更新简历
    const updatedResume = {
      ...mockResumes[resumeIndex],
      ...data,
      updatedAt: getCurrentTime()
    };
    
    mockResumes[resumeIndex] = updatedResume;
    console.log('[模拟API] 更新简历成功:', updatedResume);
    
    return updatedResume;
  },
  
  // 删除简历
  deleteResume: async (id: string): Promise<void> => {
    // 模拟网络延迟
    await delay(600);
    
    // 查找要删除的简历
    const resumeIndex = mockResumes.findIndex(resume => resume.id === id);
    if (resumeIndex === -1) {
      throw new Error('简历不存在');
    }
    
    // 删除简历
    mockResumes.splice(resumeIndex, 1);
    console.log('[模拟API] 删除简历成功，ID:', id);
  }
};

// 模拟岗位服务
export const mockTargetPositionService = {
  // 创建岗位
  createTargetPosition: async (data: TargetPositionCreateInput): Promise<TargetPosition> => {
    // 模拟网络延迟
    await delay(800);
    
    // 创建新岗位
    const newPosition: TargetPosition = {
      id: generateId(),
      userId: 1, // 模拟用户ID
      positionName: data.positionName,
      positionRequirements: data.positionRequirements || null,
      companyName: data.companyName || null,
      companyProfile: data.companyProfile || null,
      status: data.status || 'active',
      createdAt: getCurrentTime(),
      updatedAt: getCurrentTime()
    };
    
    // 添加到模拟数据存储
    mockPositions.push(newPosition);
    console.log('[模拟API] 创建岗位成功:', newPosition);
    
    return newPosition;
  },
  
  // 获取所有岗位
  getTargetPositions: async (): Promise<TargetPosition[]> => {
    // 模拟网络延迟
    await delay(500);
    
    console.log('[模拟API] 获取岗位列表:', mockPositions);
    return [...mockPositions];
  },
  
  // 更新岗位
  updateTargetPosition: async (id: string, data: TargetPositionUpdateInput): Promise<TargetPosition> => {
    // 模拟网络延迟
    await delay(700);
    
    // 查找要更新的岗位
    const positionIndex = mockPositions.findIndex(position => position.id === id);
    if (positionIndex === -1) {
      throw new Error('岗位不存在');
    }
    
    // 更新岗位
    const updatedPosition = {
      ...mockPositions[positionIndex],
      ...data,
      updatedAt: getCurrentTime()
    };
    
    mockPositions[positionIndex] = updatedPosition;
    console.log('[模拟API] 更新岗位成功:', updatedPosition);
    
    return updatedPosition;
  },
  
  // 删除岗位
  deleteTargetPosition: async (id: string): Promise<void> => {
    // 模拟网络延迟
    await delay(600);
    
    // 查找要删除的岗位
    const positionIndex = mockPositions.findIndex(position => position.id === id);
    if (positionIndex === -1) {
      throw new Error('岗位不存在');
    }
    
    // 删除岗位
    mockPositions.splice(positionIndex, 1);
    console.log('[模拟API] 删除岗位成功，ID:', id);
  }
};

// 添加一些初始的模拟数据
mockPositions.push({
  id: 'mock_initial_1',
  userId: 1,
  positionName: '前端开发工程师',
  positionRequirements: '熟悉React, TypeScript, 3年以上经验',
  companyName: '科技有限公司',
  companyProfile: '一家创新的科技公司',
  status: 'active',
  createdAt: getCurrentTime(),
  updatedAt: getCurrentTime()
});
