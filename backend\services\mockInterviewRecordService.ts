// AI模拟面试记录服务
import { PrismaClient } from '@prisma/client';
import { logger } from '../utils/logger.js';

const prisma = new PrismaClient();

interface CreateRecordData {
  userId: string;
  sessionId: string;
  companyName: string;
  positionName: string;
  interviewLanguage: 'chinese' | 'english';
  answerStyle: 'keywords_conversational' | 'conversational';
  totalQuestions: number;
}

interface QuestionRecordData {
  recordId: string;
  questionId: string;
  questionText: string;
  questionType: 'behavioral' | 'technical' | 'situational' | 'company_specific';
  difficulty: 'easy' | 'medium' | 'hard';
  expectedDuration: number;
  context?: string;
  keywords: string[];
  answerText?: string;
  answerDuration?: number;
  score?: number;
  strengths?: string[];
  improvements?: string[];
  keywordsCovered?: string[];
  missingKeywords?: string[];
  overallAssessment?: string;
  detailedAnalysis?: string;
}

interface InterviewAnalytics {
  totalInterviews: number;
  averageScore: number;
  improvementTrend: number; // 相比上次的改进百分比
  strongestArea: string;
  weakestArea: string;
  recentPerformance: Array<{
    date: string;
    score: number;
    position: string;
  }>;
}

export class MockInterviewRecordService {
  /**
   * 创建面试记录
   */
  async createRecord(data: CreateRecordData): Promise<string> {
    try {
      const record = await prisma.mockInterviewRecord.create({
        data: {
          userId: data.userId,
          sessionId: data.sessionId,
          companyName: data.companyName,
          positionName: data.positionName,
          interviewLanguage: data.interviewLanguage,
          answerStyle: data.answerStyle,
          status: 'in_progress',
          startTime: new Date(),
          totalQuestions: data.totalQuestions
        }
      });

      logger.info(`📝 Created mock interview record: ${record.id} for user ${data.userId}`);
      return record.id;
    } catch (error) {
      logger.error('❌ Failed to create mock interview record:', error);
      throw error;
    }
  }

  /**
   * 添加问题记录
   */
  async addQuestionRecord(data: QuestionRecordData): Promise<void> {
    try {
      await prisma.mockInterviewQuestionRecord.create({
        data: {
          recordId: data.recordId,
          questionId: data.questionId,
          questionText: data.questionText,
          questionType: data.questionType,
          difficulty: data.difficulty,
          expectedDuration: data.expectedDuration,
          context: data.context,
          keywords: data.keywords,
          answerText: data.answerText,
          answerDuration: data.answerDuration,
          score: data.score,
          strengths: data.strengths || [],
          improvements: data.improvements || [],
          keywordsCovered: data.keywordsCovered || [],
          missingKeywords: data.missingKeywords || [],
          overallAssessment: data.overallAssessment,
          detailedAnalysis: data.detailedAnalysis
        }
      });

      logger.info(`📝 Added question record: ${data.questionId} to record ${data.recordId}`);
    } catch (error) {
      logger.error('❌ Failed to add question record:', error);
      throw error;
    }
  }

  /**
   * 完成面试记录
   */
  async completeRecord(sessionId: string, overallFeedback?: string): Promise<void> {
    try {
      // 获取记录和所有问题
      const record = await prisma.mockInterviewRecord.findUnique({
        where: { sessionId },
        include: { questions: true }
      });

      if (!record) {
        throw new Error(`Record not found for session: ${sessionId}`);
      }

      // 计算平均分数
      const scores = record.questions
        .map(q => q.score)
        .filter((score): score is number => score !== null);
      
      const averageScore = scores.length > 0 
        ? scores.reduce((sum, score) => sum + score, 0) / scores.length 
        : null;

      // 计算总时长
      const totalDuration = record.questions
        .map(q => q.answerDuration || 0)
        .reduce((sum, duration) => sum + duration, 0);

      // 更新记录
      await prisma.mockInterviewRecord.update({
        where: { sessionId },
        data: {
          status: 'completed',
          endTime: new Date(),
          totalDuration,
          averageScore,
          overallFeedback
        }
      });

      // 更新用户统计
      await this.updateUserStatistics(record.userId, averageScore || 0);

      logger.info(`✅ Completed mock interview record for session: ${sessionId}, average score: ${averageScore}`);
    } catch (error) {
      logger.error('❌ Failed to complete mock interview record:', error);
      throw error;
    }
  }

  /**
   * 获取用户面试记录
   */
  async getUserRecords(userId: string, limit: number = 10, offset: number = 0) {
    try {
      const records = await prisma.mockInterviewRecord.findMany({
        where: { userId },
        include: {
          questions: {
            select: {
              questionType: true,
              difficulty: true,
              score: true,
              answerDuration: true
            }
          }
        },
        orderBy: { createdAt: 'desc' },
        take: limit,
        skip: offset
      });

      logger.info(`📊 Retrieved ${records.length} mock interview records for user ${userId}`);
      return records;
    } catch (error) {
      logger.error('❌ Failed to get user records:', error);
      throw error;
    }
  }

  /**
   * 获取面试分析数据
   */
  async getInterviewAnalytics(userId: string): Promise<InterviewAnalytics> {
    try {
      const records = await prisma.mockInterviewRecord.findMany({
        where: { 
          userId,
          status: 'completed'
        },
        include: { questions: true },
        orderBy: { createdAt: 'desc' },
        take: 20 // 最近20次面试
      });

      if (records.length === 0) {
        return {
          totalInterviews: 0,
          averageScore: 0,
          improvementTrend: 0,
          strongestArea: 'N/A',
          weakestArea: 'N/A',
          recentPerformance: []
        };
      }

      // 计算总体平均分
      const allScores = records
        .map(r => r.averageScore)
        .filter((score): score is number => score !== null);
      
      const averageScore = allScores.length > 0 
        ? allScores.reduce((sum, score) => sum + score, 0) / allScores.length 
        : 0;

      // 计算改进趋势（最近5次 vs 之前5次）
      const recentScores = allScores.slice(0, 5);
      const previousScores = allScores.slice(5, 10);
      
      const recentAvg = recentScores.length > 0 
        ? recentScores.reduce((sum, score) => sum + score, 0) / recentScores.length 
        : 0;
      const previousAvg = previousScores.length > 0 
        ? previousScores.reduce((sum, score) => sum + score, 0) / previousScores.length 
        : recentAvg;
      
      const improvementTrend = previousAvg > 0 
        ? ((recentAvg - previousAvg) / previousAvg) * 100 
        : 0;

      // 分析最强和最弱领域
      const questionTypeScores: Record<string, number[]> = {};
      records.forEach(record => {
        record.questions.forEach(question => {
          if (question.score !== null) {
            if (!questionTypeScores[question.questionType]) {
              questionTypeScores[question.questionType] = [];
            }
            questionTypeScores[question.questionType].push(question.score);
          }
        });
      });

      const typeAverages = Object.entries(questionTypeScores).map(([type, scores]) => ({
        type,
        average: scores.reduce((sum, score) => sum + score, 0) / scores.length
      }));

      const strongestArea = typeAverages.length > 0 
        ? typeAverages.reduce((max, current) => current.average > max.average ? current : max).type
        : 'N/A';
      
      const weakestArea = typeAverages.length > 0 
        ? typeAverages.reduce((min, current) => current.average < min.average ? current : min).type
        : 'N/A';

      // 最近表现
      const recentPerformance = records.slice(0, 10).map(record => ({
        date: record.createdAt.toISOString().split('T')[0],
        score: record.averageScore || 0,
        position: record.positionName
      }));

      return {
        totalInterviews: records.length,
        averageScore: Math.round(averageScore * 100) / 100,
        improvementTrend: Math.round(improvementTrend * 100) / 100,
        strongestArea,
        weakestArea,
        recentPerformance
      };
    } catch (error) {
      logger.error('❌ Failed to get interview analytics:', error);
      throw error;
    }
  }

  /**
   * 更新用户统计数据
   */
  private async updateUserStatistics(userId: string, latestScore: number): Promise<void> {
    try {
      const existingStats = await prisma.interviewStatistics.findUnique({
        where: { userId }
      });

      if (existingStats) {
        // 更新现有统计
        const newTotal = existingStats.totalMockInterviews + 1;
        const newAverage = existingStats.averageMockScore 
          ? ((existingStats.averageMockScore * existingStats.totalMockInterviews) + latestScore) / newTotal
          : latestScore;

        await prisma.interviewStatistics.update({
          where: { userId },
          data: {
            totalMockInterviews: newTotal,
            averageMockScore: newAverage,
            lastInterviewDate: new Date()
          }
        });
      } else {
        // 创建新统计
        await prisma.interviewStatistics.create({
          data: {
            userId,
            totalMockInterviews: 1,
            averageMockScore: latestScore,
            lastInterviewDate: new Date()
          }
        });
      }

      logger.info(`📊 Updated statistics for user ${userId}: latest score ${latestScore}`);
    } catch (error) {
      logger.error('❌ Failed to update user statistics:', error);
      // 不抛出错误，因为这不是关键功能
    }
  }

  /**
   * 获取用户统计数据
   */
  async getUserStatistics(userId: string) {
    try {
      const stats = await prisma.interviewStatistics.findUnique({
        where: { userId }
      });

      return stats || {
        totalMockInterviews: 0,
        totalFormalInterviews: 0,
        averageMockScore: null,
        averageFormalScore: null,
        totalInterviewTime: 0,
        lastInterviewDate: null
      };
    } catch (error) {
      logger.error('❌ Failed to get user statistics:', error);
      throw error;
    }
  }
}
