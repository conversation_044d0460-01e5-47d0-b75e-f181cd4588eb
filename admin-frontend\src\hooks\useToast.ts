import { useState, useCallback } from 'react';
import type { Toast, ToastOptions, UseToastReturn } from '../types/toast';

// 生成唯一ID
const generateId = (): string => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
};

export const useToast = (): UseToastReturn => {
  const [toasts, setToasts] = useState<Toast[]>([]);

  // 添加Toast
  const showToast = useCallback((message: string, options: ToastOptions = {}): string => {
    const id = generateId();
    const toast: Toast = {
      id,
      type: options.type || 'info',
      title: options.title,
      message,
      duration: options.duration || 3000,
      autoClose: options.autoClose !== false,
      createdAt: Date.now()
    };

    setToasts(prev => {
      const newToasts = [...prev, toast];
      // 限制最多显示5个Toast
      return newToasts.slice(-5);
    });

    // 自动移除
    if (toast.autoClose && toast.duration && toast.duration > 0) {
      setTimeout(() => {
        removeToast(id);
      }, toast.duration);
    }

    return id;
  }, []);

  // 移除Toast
  const removeToast = useCallback((id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id));
  }, []);

  // 清除所有Toast
  const clearAllToasts = useCallback(() => {
    setToasts([]);
  }, []);

  // 便捷方法
  const showSuccess = useCallback((message: string, duration = 3000): string => {
    return showToast(message, { type: 'success', duration });
  }, [showToast]);

  const showError = useCallback((message: string, duration = 5000): string => {
    return showToast(message, { type: 'error', duration });
  }, [showToast]);

  const showInfo = useCallback((message: string, duration = 3000): string => {
    return showToast(message, { type: 'info', duration });
  }, [showToast]);

  const showWarning = useCallback((message: string, duration = 4000): string => {
    return showToast(message, { type: 'warning', duration });
  }, [showToast]);

  return {
    toasts,
    showToast,
    showSuccess,
    showError,
    showInfo,
    showWarning,
    removeToast,
    clearAllToasts
  };
};
