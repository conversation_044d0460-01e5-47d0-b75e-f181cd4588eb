# 验证码登录系统实现细节补充文档

## 核心代码实现示例

### 1. 后端核心服务实现

#### 1.1 验证码服务 (backend/services/verificationService.ts)

```typescript
import crypto from 'crypto';
import Redis from 'ioredis';
import { PrismaClient } from '@prisma/client';

export class VerificationService {
  private redis: Redis;
  private prisma: PrismaClient;

  constructor() {
    this.redis = new Redis(process.env.REDIS_URL);
    this.prisma = new PrismaClient();
  }

  // 生成6位数字验证码
  generateCode(): string {
    return Math.floor(100000 + Math.random() * 900000).toString();
  }

  // 发送验证码
  async sendVerificationCode(
    identifier: string,
    type: 'EMAIL' | 'SMS',
    purpose: 'LOGIN' = 'LOGIN'
  ): Promise<{ success: boolean; message: string; expiresIn?: number }> {

    // 检查发送频率限制
    const rateLimitKey = `rate_limit:${identifier}:${type}`;
    const lastSent = await this.redis.get(rateLimitKey);

    if (lastSent) {
      const remainingTime = 60 - (Date.now() - parseInt(lastSent)) / 1000;
      if (remainingTime > 0) {
        return {
          success: false,
          message: `请等待 ${Math.ceil(remainingTime)} 秒后再试`
        };
      }
    }

    // 生成验证码
    const code = this.generateCode();
    const expiresAt = new Date(Date.now() + 5 * 60 * 1000); // 5分钟后过期

    try {
      // 存储验证码到数据库
      await this.prisma.verificationCode.create({
        data: {
          identifier,
          code: await this.hashCode(code), // 加密存储
          type,
          purpose,
          expiresAt
        }
      });

      // 存储到Redis缓存
      const cacheKey = `verification:${identifier}:${type}:${purpose}`;
      await this.redis.setex(cacheKey, 300, JSON.stringify({
        code: await this.hashCode(code),
        attempts: 0,
        createdAt: Date.now()
      }));

      // 设置发送频率限制
      await this.redis.setex(rateLimitKey, 60, Date.now().toString());

      // 发送验证码
      if (type === 'EMAIL') {
        await this.sendEmailCode(identifier, code);
      } else {
        await this.sendSMSCode(identifier, code);
      }

      // 记录发送日志
      await this.logSendAttempt(identifier, type, purpose, 'SUCCESS');

      return {
        success: true,
        message: '验证码已发送',
        expiresIn: 300
      };

    } catch (error) {
      await this.logSendAttempt(identifier, type, purpose, 'FAILED', error.message);
      return {
        success: false,
        message: '验证码发送失败，请稍后重试'
      };
    }
  }

  // 验证验证码
  async verifyCode(
    identifier: string,
    code: string,
    type: 'EMAIL' | 'SMS',
    purpose: 'LOGIN' = 'LOGIN'
  ): Promise<{ success: boolean; message: string; isLocked?: boolean }> {

    const cacheKey = `verification:${identifier}:${type}:${purpose}`;
    const lockKey = `verification_lock:${identifier}:${type}`;

    // 检查是否被锁定
    const isLocked = await this.redis.get(lockKey);
    if (isLocked) {
      const lockExpires = parseInt(isLocked);
      const remainingTime = Math.ceil((lockExpires - Date.now()) / 1000);
      return {
        success: false,
        message: `验证失败次数过多，请等待 ${remainingTime} 秒后再试`,
        isLocked: true
      };
    }

    // 获取验证码信息
    const cacheData = await this.redis.get(cacheKey);
    if (!cacheData) {
      return {
        success: false,
        message: '验证码已过期，请重新获取'
      };
    }

    const verificationData = JSON.parse(cacheData);
    const hashedInputCode = await this.hashCode(code);

    // 验证码错误
    if (verificationData.code !== hashedInputCode) {
      verificationData.attempts += 1;

      // 更新尝试次数
      await this.redis.setex(cacheKey, 300, JSON.stringify(verificationData));

      // 检查是否需要锁定
      if (verificationData.attempts >= 3) {
        const lockExpires = Date.now() + 10 * 60 * 1000; // 锁定10分钟
        await this.redis.setex(lockKey, 600, lockExpires.toString());
        await this.redis.del(cacheKey); // 删除验证码

        return {
          success: false,
          message: '验证失败次数过多，账户已被锁定10分钟',
          isLocked: true
        };
      }

      return {
        success: false,
        message: `验证码错误，还可尝试 ${3 - verificationData.attempts} 次`
      };
    }

    // 验证成功，清理缓存
    await this.redis.del(cacheKey);
    await this.redis.del(lockKey);

    // 标记验证码为已使用
    await this.prisma.verificationCode.updateMany({
      where: {
        identifier,
        type,
        purpose,
        isUsed: false
      },
      data: {
        isUsed: true
      }
    });

    return {
      success: true,
      message: '验证成功'
    };
  }

  // 加密验证码
  private async hashCode(code: string): Promise<string> {
    return crypto.createHash('sha256').update(code + process.env.JWT_SECRET).digest('hex');
  }

  // 发送邮件验证码
  private async sendEmailCode(email: string, code: string): Promise<void> {
    const emailService = new EmailService();
    await emailService.sendVerificationCode(email, code);
  }

  // 发送短信验证码
  private async sendSMSCode(phoneNumber: string, code: string): Promise<void> {
    const smsService = new SMSService();
    await smsService.sendVerificationCode(phoneNumber, code);
  }

  // 记录发送日志
  private async logSendAttempt(
    identifier: string,
    type: 'EMAIL' | 'SMS',
    purpose: string,
    status: 'SUCCESS' | 'FAILED',
    errorMessage?: string
  ): Promise<void> {
    await this.prisma.verificationLog.create({
      data: {
        identifier,
        type,
        purpose,
        status,
        errorMessage
      }
    });
  }
}
```

#### 1.2 邮件服务 (backend/services/emailService.ts)

```typescript
import nodemailer from 'nodemailer';
import { createTransport } from 'nodemailer';

export class EmailService {
  private transporter: nodemailer.Transporter;

  constructor() {
    this.transporter = createTransport({
      host: process.env.SMTP_HOST,
      port: parseInt(process.env.SMTP_PORT || '587'),
      secure: false,
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS
      }
    });
  }

  async sendVerificationCode(email: string, code: string): Promise<void> {
    const mailOptions = {
      from: process.env.SMTP_FROM || '面试君 <<EMAIL>>',
      to: email,
      subject: '【面试君】登录验证码',
      html: this.generateEmailTemplate(code)
    };

    await this.transporter.sendMail(mailOptions);
  }

  private generateEmailTemplate(code: string): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>面试君登录验证码</title>
        <style>
          .container { max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; }
          .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; text-align: center; }
          .content { padding: 30px; background: #f8f9fa; }
          .code-box { background: white; border: 2px dashed #667eea; padding: 20px; text-align: center; margin: 20px 0; }
          .code { font-size: 32px; font-weight: bold; color: #667eea; letter-spacing: 8px; }
          .footer { padding: 20px; text-align: center; color: #666; font-size: 12px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>面试君</h1>
            <p>您的专业面试助手</p>
          </div>
          <div class="content">
            <h2>登录验证码</h2>
            <p>您好！您正在使用邮箱验证码登录面试君，验证码如下：</p>
            <div class="code-box">
              <div class="code">${code}</div>
            </div>
            <p><strong>重要提醒：</strong></p>
            <ul>
              <li>验证码有效期为 <strong>5分钟</strong></li>
              <li>请勿将验证码告诉他人</li>
              <li>如非本人操作，请忽略此邮件</li>
            </ul>
          </div>
          <div class="footer">
            <p>此邮件由系统自动发送，请勿回复</p>
            <p>© 2025 面试君 - 让面试更简单</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }
}
```

#### 1.3 短信服务 (backend/services/smsService.ts)

```typescript
import Core from '@alicloud/pop-core';

export class SMSService {
  private client: Core;

  constructor() {
    this.client = new Core({
      accessKeyId: process.env.ALIBABA_ACCESS_KEY_ID,
      accessKeySecret: process.env.ALIBABA_ACCESS_KEY_SECRET,
      endpoint: 'https://dysmsapi.aliyuncs.com',
      apiVersion: '2017-05-25'
    });
  }

  async sendVerificationCode(phoneNumber: string, code: string): Promise<void> {
    const params = {
      PhoneNumbers: phoneNumber,
      SignName: process.env.ALIBABA_SMS_SIGN_NAME || '面试君',
      TemplateCode: process.env.ALIBABA_SMS_TEMPLATE_CODE,
      TemplateParam: JSON.stringify({
        code: code,
        product: '面试君'
      })
    };

    const requestOption = {
      method: 'POST'
    };

    try {
      const result = await this.client.request('SendSms', params, requestOption);

      if (result.Code !== 'OK') {
        throw new Error(`短信发送失败: ${result.Message}`);
      }
    } catch (error) {
      console.error('SMS sending error:', error);
      throw error;
    }
  }
}
```

### 2. API路由实现

#### 2.1 发送验证码路由 (backend/api/auth/send-verification-code.ts)

```typescript
import type { VercelRequest, VercelResponse } from '@vercel/node';
import { z } from 'zod';
import { VerificationService } from '../../services/verificationService';
import { rateLimit } from '../../middleware/rateLimit';

const sendCodeSchema = z.object({
  identifier: z.string().min(1, '邮箱或手机号不能为空'),
  type: z.enum(['EMAIL', 'SMS'], { message: '验证码类型无效' }),
  purpose: z.enum(['LOGIN']).default('LOGIN')
});

export default async function handler(req: VercelRequest, res: VercelResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      message: 'Method not allowed'
    });
  }

  try {
    // 应用频率限制中间件
    await rateLimit(req, res);

    // 验证请求参数
    const { identifier, type, purpose } = sendCodeSchema.parse(req.body);

    // 验证邮箱或手机号格式
    if (type === 'EMAIL') {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(identifier)) {
        return res.status(400).json({
          success: false,
          message: '请输入有效的邮箱地址',
          error: { code: 'INVALID_EMAIL' }
        });
      }
    } else if (type === 'SMS') {
      const phoneRegex = /^1[3-9]\d{9}$/;
      if (!phoneRegex.test(identifier)) {
        return res.status(400).json({
          success: false,
          message: '请输入有效的手机号',
          error: { code: 'INVALID_PHONE' }
        });
      }
    }

    // 检查用户是否存在（登录场景）
    if (purpose === 'LOGIN') {
      const prisma = new PrismaClient();
      const user = await prisma.user.findFirst({
        where: type === 'EMAIL'
          ? { email: identifier }
          : { phoneNumber: identifier }
      });

      if (!user) {
        return res.status(404).json({
          success: false,
          message: type === 'EMAIL' ? '该邮箱尚未注册' : '该手机号尚未注册',
          error: { code: 'USER_NOT_FOUND' }
        });
      }
    }

    // 发送验证码
    const verificationService = new VerificationService();
    const result = await verificationService.sendVerificationCode(identifier, type, purpose);

    if (result.success) {
      return res.status(200).json({
        success: true,
        message: result.message,
        data: {
          expiresIn: result.expiresIn,
          canResendAfter: 60
        }
      });
    } else {
      return res.status(429).json({
        success: false,
        message: result.message,
        error: { code: 'RATE_LIMITED' }
      });
    }

  } catch (error) {
    console.error('Send verification code error:', error);

    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: error.errors[0].message,
        error: { code: 'VALIDATION_ERROR', details: error.errors }
      });
    }

    return res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: { code: 'INTERNAL_ERROR' }
    });
  }
}
```

#### 2.2 验证码登录路由 (backend/api/auth/login-with-code.ts)

```typescript
import type { VercelRequest, VercelResponse } from '@vercel/node';
import { z } from 'zod';
import jwt from 'jsonwebtoken';
import { PrismaClient } from '@prisma/client';
import { VerificationService } from '../../services/verificationService';

const loginSchema = z.object({
  identifier: z.string().min(1, '邮箱或手机号不能为空'),
  code: z.string().regex(/^\d{6}$/, '验证码必须是6位数字'),
  type: z.enum(['EMAIL', 'SMS'], { message: '验证码类型无效' })
});

export default async function handler(req: VercelRequest, res: VercelResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      message: 'Method not allowed'
    });
  }

  try {
    // 验证请求参数
    const { identifier, code, type } = loginSchema.parse(req.body);

    // 验证验证码
    const verificationService = new VerificationService();
    const verifyResult = await verificationService.verifyCode(identifier, code, type, 'LOGIN');

    if (!verifyResult.success) {
      return res.status(400).json({
        success: false,
        message: verifyResult.message,
        error: {
          code: verifyResult.isLocked ? 'ACCOUNT_LOCKED' : 'INVALID_CODE'
        }
      });
    }

    // 查找用户
    const prisma = new PrismaClient();
    const user = await prisma.user.findFirst({
      where: type === 'EMAIL'
        ? { email: identifier }
        : { phoneNumber: identifier },
      include: { balance: true }
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在',
        error: { code: 'USER_NOT_FOUND' }
      });
    }

    // 生成JWT token
    const JWT_SECRET = process.env.JWT_SECRET || 'your_jwt_secret_key';
    const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '24h';

    const payload = {
      userId: user.id,
      email: user.email,
      name: user.name
    };

    const token = jwt.sign(payload, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN });

    // 返回登录成功响应
    const { password: _, ...userWithoutPassword } = user;

    return res.status(200).json({
      success: true,
      message: '登录成功',
      data: {
        token,
        user: userWithoutPassword,
        expiresIn: 24 * 60 * 60 // 24小时，单位秒
      }
    });

  } catch (error) {
    console.error('Login with code error:', error);

    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: error.errors[0].message,
        error: { code: 'VALIDATION_ERROR', details: error.errors }
      });
    }

    return res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: { code: 'INTERNAL_ERROR' }
    });
  }
}
```