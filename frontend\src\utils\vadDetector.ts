import { VADResult, SpectralFeatures, VADConfig, VADState, DEFAULT_VAD_CONFIG } from '../types/vadTypes';

/**
 * VAD (Voice Activity Detection) 检测器
 * 基于能量、频谱特征的智能语音活动检测
 * 🔥 增强版：支持语音结束检测和智能分段
 */
export class VADDetector {
  private config: VADConfig;
  private state: VADState;
  private audioContext: AudioContext;

  constructor(audioContext: AudioContext, config: Partial<VADConfig> = {}) {
    this.audioContext = audioContext;
    this.config = { ...DEFAULT_VAD_CONFIG, ...config };
    this.state = {
      isInSpeech: false,
      speechStartTime: 0,
      silenceStartTime: 0,
      recentEnergyHistory: [],
      adaptiveThreshold: this.config.energyThreshold,
      speechDuration: 0,
      lastSpeechQuality: 0,
      continuityScore: 1.0,
      pendingEndDetection: false
    };
    
    console.log('🎤 Enhanced VAD Detector initialized with config:', this.config);
  }

  /**
   * 检测语音活动
   * 🔥 增强版：支持语音结束检测和连续性分析
   */
  detectVoiceActivity(audioData: Float32Array): VADResult {
    const timestamp = Date.now();
    
    // 计算音频特征
    const energy = this.calculateEnergy(audioData);
    const spectralCentroid = this.calculateSpectralCentroid(audioData);
    const zeroCrossingRate = this.calculateZeroCrossingRate(audioData);
    const spectralRolloff = this.calculateSpectralRolloff(audioData);
    
    // 自适应阈值调整
    if (this.config.adaptiveThreshold) {
      this.updateAdaptiveThreshold(energy);
    }
    
    // 语音分类
    const isSpeech = this.classifyAsSpeech(energy, spectralCentroid, zeroCrossingRate);
    const confidence = this.calculateConfidence(energy, spectralCentroid, zeroCrossingRate);
    
    // 🔥 新增：计算语音质量和连续性
    const segmentQuality = this.calculateSegmentQuality(energy, spectralCentroid, zeroCrossingRate);
    const speechContinuity = this.calculateSpeechContinuity(isSpeech, energy);
    
    // 🔥 新增：检测语音结束
    const isEndOfSpeech = this.detectEndOfSpeech(isSpeech, timestamp);
    
    // 更新状态
    this.updateEnhancedState(isSpeech, timestamp, segmentQuality, speechContinuity);
    
    const result: VADResult = {
      isSpeech,
      energy,
      confidence,
      timestamp,
      spectralFeatures: {
        spectralCentroid,
        zeroCrossingRate,
        spectralRolloff
      },
      isEndOfSpeech,
      speechContinuity,
      segmentQuality
    };
    
    return result;
  }

  /**
   * 计算音频能量
   */
  private calculateEnergy(audioData: Float32Array): number {
    let sum = 0;
    for (let i = 0; i < audioData.length; i++) {
      sum += audioData[i] * audioData[i];
    }
    return Math.sqrt(sum / audioData.length);
  }

  /**
   * 计算频谱重心
   */
  private calculateSpectralCentroid(audioData: Float32Array): number {
    const fftSize = Math.min(1024, audioData.length);
    const fft = this.performFFT(audioData.slice(0, fftSize));
    
    let weightedSum = 0;
    let magnitudeSum = 0;
    
    for (let i = 1; i < fft.length / 2; i++) {
      const magnitude = Math.sqrt(fft[i * 2] ** 2 + fft[i * 2 + 1] ** 2);
      const frequency = (i * this.config.sampleRate) / fftSize;
      
      weightedSum += frequency * magnitude;
      magnitudeSum += magnitude;
    }
    
    return magnitudeSum > 0 ? weightedSum / magnitudeSum : 0;
  }

  /**
   * 计算过零率
   */
  private calculateZeroCrossingRate(audioData: Float32Array): number {
    let crossings = 0;
    for (let i = 1; i < audioData.length; i++) {
      if ((audioData[i] >= 0) !== (audioData[i - 1] >= 0)) {
        crossings++;
      }
    }
    return crossings / audioData.length;
  }

  /**
   * 计算频谱滚降点
   */
  private calculateSpectralRolloff(audioData: Float32Array): number {
    const fftSize = Math.min(1024, audioData.length);
    const fft = this.performFFT(audioData.slice(0, fftSize));
    
    let totalEnergy = 0;
    const magnitudes: number[] = [];
    
    for (let i = 1; i < fft.length / 2; i++) {
      const magnitude = Math.sqrt(fft[i * 2] ** 2 + fft[i * 2 + 1] ** 2);
      magnitudes.push(magnitude);
      totalEnergy += magnitude;
    }
    
    const threshold = 0.85 * totalEnergy;
    let cumulativeEnergy = 0;
    
    for (let i = 0; i < magnitudes.length; i++) {
      cumulativeEnergy += magnitudes[i];
      if (cumulativeEnergy >= threshold) {
        return ((i + 1) * this.config.sampleRate) / fftSize;
      }
    }
    
    return (magnitudes.length * this.config.sampleRate) / fftSize;
  }

  /**
   * 简化的FFT实现（用于频谱分析）
   */
  private performFFT(audioData: Float32Array): Float32Array {
    const N = audioData.length;
    const result = new Float32Array(N * 2);
    
    // 简化的DFT实现（实际项目中应使用优化的FFT库）
    for (let k = 0; k < N; k++) {
      let realSum = 0;
      let imagSum = 0;
      
      for (let n = 0; n < N; n++) {
        const angle = -2 * Math.PI * k * n / N;
        realSum += audioData[n] * Math.cos(angle);
        imagSum += audioData[n] * Math.sin(angle);
      }
      
      result[k * 2] = realSum;
      result[k * 2 + 1] = imagSum;
    }
    
    return result;
  }

  /**
   * 语音分类
   */
  private classifyAsSpeech(
    energy: number, 
    spectralCentroid: number, 
    zeroCrossingRate: number
  ): boolean {
    // 多特征融合的语音检测
    const energyScore = energy > this.state.adaptiveThreshold ? 1 : 0;
    const spectralScore = spectralCentroid > 1000 && spectralCentroid < 4000 ? 1 : 0;
    const zcrScore = zeroCrossingRate > 0.05 && zeroCrossingRate < 0.4 ? 1 : 0;
    
    const totalScore = energyScore + spectralScore + zcrScore;
    return totalScore >= 2; // 至少满足两个条件
  }

  /**
   * 计算置信度
   */
  private calculateConfidence(
    energy: number, 
    spectralCentroid: number, 
    zeroCrossingRate: number
  ): number {
    // 基于多特征的置信度计算
    const energyConf = Math.min(energy / this.state.adaptiveThreshold, 1);
    const spectralConf = spectralCentroid > 1000 && spectralCentroid < 4000 ? 1 : 0.5;
    const zcrConf = zeroCrossingRate > 0.05 && zeroCrossingRate < 0.4 ? 1 : 0.5;
    
    return (energyConf + spectralConf + zcrConf) / 3;
  }

  /**
   * 更新自适应阈值
   */
  private updateAdaptiveThreshold(energy: number): void {
    this.state.recentEnergyHistory.push(energy);
    if (this.state.recentEnergyHistory.length > 100) {
      this.state.recentEnergyHistory.shift();
    }
    
    if (this.state.recentEnergyHistory.length >= 10) {
      const avgEnergy = this.state.recentEnergyHistory.reduce((a, b) => a + b, 0) / this.state.recentEnergyHistory.length;
      const variance = this.state.recentEnergyHistory.reduce((sum, val) => sum + Math.pow(val - avgEnergy, 2), 0) / this.state.recentEnergyHistory.length;
      const stdEnergy = Math.sqrt(variance);
      
      // 动态调整阈值
      this.state.adaptiveThreshold = Math.max(
        this.config.silenceThreshold,
        avgEnergy + 1.5 * stdEnergy
      );
    }
  }

  /**
   * 🔥 新增：计算段质量
   */
  private calculateSegmentQuality(
    energy: number, 
    spectralCentroid: number, 
    zeroCrossingRate: number
  ): number {
    // 基于多个特征计算音频段质量
    const energyScore = Math.min(energy / this.state.adaptiveThreshold, 1);
    const spectralScore = (spectralCentroid > 1000 && spectralCentroid < 4000) ? 1 : 0.5;
    const zcrScore = (zeroCrossingRate > 0.05 && zeroCrossingRate < 0.4) ? 1 : 0.5;
    const noiseScore = energy > this.config.silenceThreshold ? 1 : 0.3;
    
    return (energyScore + spectralScore + zcrScore + noiseScore) / 4;
  }

  /**
   * 🔥 新增：计算语音连续性
   */
  private calculateSpeechContinuity(isSpeech: boolean, energy: number): number {
    if (!this.state.isInSpeech) {
      return isSpeech ? 1.0 : 0.0;
    }
    
    // 基于能量变化和语音状态计算连续性
    const energyStability = this.calculateEnergyStability();
    const speechConsistency = isSpeech ? 1.0 : 0.5;
    
    return (energyStability + speechConsistency) / 2;
  }

  /**
   * 🔥 新增：计算能量稳定性
   */
  private calculateEnergyStability(): number {
    if (this.state.recentEnergyHistory.length < 5) {
      return 1.0;
    }
    
    const recent = this.state.recentEnergyHistory.slice(-5);
    const avg = recent.reduce((a, b) => a + b, 0) / recent.length;
    const variance = recent.reduce((sum, val) => sum + Math.pow(val - avg, 2), 0) / recent.length;
    const stability = Math.max(0, 1 - Math.sqrt(variance) / avg);
    
    return stability;
  }

  /**
   * 🔥 新增：检测语音结束
   */
  private detectEndOfSpeech(isSpeech: boolean, timestamp: number): boolean {
    if (!this.state.isInSpeech) {
      return false;
    }
    
    // 检查语音持续时间是否接近限制
    const currentSpeechDuration = timestamp - this.state.speechStartTime;
    if (currentSpeechDuration >= this.config.maxSpeechDuration) {
      console.log('🕐 Speech duration limit reached, forcing end detection');
      return true;
    }
    
    // 检查静音持续时间
    if (!isSpeech && this.state.silenceStartTime > 0) {
      const silenceDuration = timestamp - this.state.silenceStartTime;
      if (silenceDuration >= this.config.endOfSpeechTimeout) {
        console.log('🔇 End of speech detected after silence:', silenceDuration + 'ms');
        return true;
      }
    }
    
    // 检查语音质量下降
    if (this.state.lastSpeechQuality < this.config.qualityThreshold && 
        this.state.continuityScore < this.config.continuityThreshold) {
      console.log('📉 End of speech detected due to quality degradation');
      return true;
    }
    
    return false;
  }

  /**
   * 🔥 增强：更新VAD状态
   */
  private updateEnhancedState(
    isSpeech: boolean, 
    timestamp: number, 
    segmentQuality: number, 
    speechContinuity: number
  ): void {
    // 更新语音质量和连续性
    this.state.lastSpeechQuality = segmentQuality;
    this.state.continuityScore = speechContinuity;
    
    if (isSpeech && !this.state.isInSpeech) {
      // 语音开始
      this.state.isInSpeech = true;
      this.state.speechStartTime = timestamp;
      this.state.silenceStartTime = 0;
      this.state.speechDuration = 0;
      this.state.pendingEndDetection = false;
      console.log('🎤 Speech started at', new Date(timestamp).toLocaleTimeString());
    } else if (!isSpeech && this.state.isInSpeech) {
      // 可能的语音结束
      if (this.state.silenceStartTime === 0) {
        this.state.silenceStartTime = timestamp;
        this.state.pendingEndDetection = true;
      } else {
        const silenceDuration = timestamp - this.state.silenceStartTime;
        if (silenceDuration > this.config.minSilenceDuration) {
          this.state.isInSpeech = false;
          this.state.speechStartTime = 0;
          this.state.silenceStartTime = 0;
          this.state.speechDuration = 0;
          this.state.pendingEndDetection = false;
          console.log('🔇 Speech ended after', silenceDuration + 'ms silence');
        }
      }
    } else if (isSpeech && this.state.isInSpeech) {
      // 语音继续，重置静音计时
      this.state.silenceStartTime = 0;
      this.state.pendingEndDetection = false;
      this.state.speechDuration = timestamp - this.state.speechStartTime;
    }
  }

  /**
   * 🔥 新增：获取当前语音段信息
   */
  getSpeechSegmentInfo(): {
    isActive: boolean;
    duration: number;
    quality: number;
    continuity: number;
    shouldForceEnd: boolean;
  } {
    const now = Date.now();
    const duration = this.state.isInSpeech ? now - this.state.speechStartTime : 0;
    const shouldForceEnd = duration >= this.config.maxSpeechDuration;
    
    return {
      isActive: this.state.isInSpeech,
      duration,
      quality: this.state.lastSpeechQuality,
      continuity: this.state.continuityScore,
      shouldForceEnd
    };
  }

  /**
   * 获取当前VAD状态
   */
  getState(): VADState {
    return { ...this.state };
  }

  /**
   * 重置VAD状态
   */
  reset(): void {
    this.state = {
      isInSpeech: false,
      speechStartTime: 0,
      silenceStartTime: 0,
      recentEnergyHistory: [],
      adaptiveThreshold: this.config.energyThreshold,
      speechDuration: 0,
      lastSpeechQuality: 0,
      continuityScore: 1.0,
      pendingEndDetection: false
    };
    console.log('VAD Detector reset');
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<VADConfig>): void {
    this.config = { ...this.config, ...newConfig };
    console.log('VAD config updated:', this.config);
  }
}
