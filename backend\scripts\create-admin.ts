import prisma from '../lib/prisma';
import * as bcrypt from 'bcryptjs';

async function createAdminUser() {
  try {
    console.log('🔍 检查管理员账户...');

    // 检查管理员是否已存在
    const existingAdmin = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });

    if (existingAdmin) {
      console.log('📋 现有管理员账户信息:');
      console.log('- ID:', existingAdmin.id);
      console.log('- 邮箱:', existingAdmin.email);
      console.log('- 姓名:', existingAdmin.name);
      console.log('- 角色:', existingAdmin.role);
      console.log('- 创建时间:', existingAdmin.createdAt);

      // 检查角色是否为ADMIN
      if (existingAdmin.role !== 'ADMIN') {
        console.log('⚠️ 角色不是ADMIN，正在更新...');
        await prisma.user.update({
          where: { id: existingAdmin.id },
          data: { role: 'ADMIN' }
        });
        console.log('✅ 角色已更新为ADMIN');
      }

      // 更新密码为admin123
      console.log('🔐 正在更新密码...');
      const hashedPassword = await bcrypt.hash('admin123', 12);
      await prisma.user.update({
        where: { id: existingAdmin.id },
        data: { password: hashedPassword }
      });
      console.log('✅ 密码已更新为: admin123');

    } else {
      console.log('❌ 管理员账户不存在，正在创建...');

      // 创建管理员账户
      const hashedPassword = await bcrypt.hash('admin123', 12);

      const admin = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: hashedPassword,
          name: '系统管理员',
          role: 'ADMIN',
          balance: {
            create: {
              mockInterviewCredits: 999,
              formalInterviewCredits: 999,
              mianshijunBalance: 999999
            }
          }
        },
        include: { balance: true }
      });

      console.log('✅ 管理员账户创建成功:');
      console.log('- ID:', admin.id);
      console.log('- 邮箱:', admin.email);
      console.log('- 姓名:', admin.name);
      console.log('- 角色:', admin.role);
      console.log('- 密码: admin123');
    }

    // 验证登录
    console.log('\n🧪 测试登录验证...');
    const testUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });

    if (testUser) {
      const isPasswordValid = await bcrypt.compare('admin123', testUser.password);
      console.log('- 密码验证:', isPasswordValid ? '✅ 通过' : '❌ 失败');
      console.log('- 角色检查:', testUser.role === 'ADMIN' ? '✅ ADMIN' : `❌ ${testUser.role}`);
    }

    console.log('\n🎉 管理员账户配置完成！');
    console.log('📝 登录信息:');
    console.log('- 邮箱: <EMAIL>');
    console.log('- 密码: admin123');
    console.log('- 管理员面板: http://localhost:5174');

  } catch (error) {
    console.error('❌ 创建管理员账户失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// 运行脚本
createAdminUser();
