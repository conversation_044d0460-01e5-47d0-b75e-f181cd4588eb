{"name": "new-mians<PERSON><PERSON>-monorepo", "private": true, "version": "1.0.0", "description": "MianshiJun AI Project", "scripts": {"dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && npm run dev", "dev": "npm-run-all --parallel dev:frontend dev:backend", "build:frontend": "npm run build --workspace=frontend", "build:backend": "npm run build --workspace=backend", "build": "npm run build --workspace=packages/common && npm run build:frontend && npm run build:backend", "start": "echo \"Run dev or specific workspace start scripts\"", "lint": "echo \"Linting all packages...\"", "test": "echo \"Testing all packages...\""}, "workspaces": ["frontend", "backend", "packages/*"], "keywords": [], "author": "Your Name <<EMAIL>>", "license": "ISC", "devDependencies": {"@types/fast-levenshtein": "^0.0.4", "npm-run-all": "^4.1.5"}, "dependencies": {"@playwright/test": "^1.52.0", "@types/winston": "^2.4.4", "cosine-similarity": "^1.0.1", "fast-levenshtein": "^3.0.0", "lru-cache": "^11.1.0", "lucide-react": "^0.513.0", "winston": "^3.17.0"}}