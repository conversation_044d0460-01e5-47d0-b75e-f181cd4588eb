import * as levenshtein from 'fast-levenshtein';
import { createHash } from 'crypto';

export interface SimilarityResult {
  editDistance: number;
  editSimilarity: number;
  cosineSimilarity: number;
  weightedSimilarity: number;
  isDuplicate: boolean;
}

export interface SimilarityConfig {
  editDistanceWeight: number;
  cosineWeight: number;
  duplicateThreshold: number;
  minTextLength: number;
}

export class TextSimilarityDetector {
  private config: SimilarityConfig;

  constructor(config?: Partial<SimilarityConfig>) {
    this.config = {
      editDistanceWeight: 0.6,
      cosineWeight: 0.4,
      duplicateThreshold: 0.85,
      minTextLength: 3,
      ...config
    };
  }

  /**
   * 计算两个文本的综合相似度
   */
  public calculateSimilarity(text1: string, text2: string): SimilarityResult {
    const cleanText1 = this.cleanTextInternal(text1);
    const cleanText2 = this.cleanTextInternal(text2);

    // 如果文本太短，直接比较
    if (cleanText1.length < this.config.minTextLength || cleanText2.length < this.config.minTextLength) {
      const isExactMatch = cleanText1 === cleanText2;
      return {
        editDistance: isExactMatch ? 0 : Math.max(cleanText1.length, cleanText2.length),
        editSimilarity: isExactMatch ? 1.0 : 0.0,
        cosineSimilarity: isExactMatch ? 1.0 : 0.0,
        weightedSimilarity: isExactMatch ? 1.0 : 0.0,
        isDuplicate: isExactMatch
      };
    }

    // 计算编辑距离相似度
    const editDistance = levenshtein.get(cleanText1, cleanText2);
    const maxLength = Math.max(cleanText1.length, cleanText2.length);
    const editSimilarity = 1 - (editDistance / maxLength);

    // 计算余弦相似度
    const vector1 = this.textToVector(cleanText1);
    const vector2 = this.textToVector(cleanText2);
    const cosineSimilarity = this.calculateCosineSimilarity(vector1, vector2);

    // 计算加权综合相似度
    const weightedSimilarity = 
      (editSimilarity * this.config.editDistanceWeight) + 
      (cosineSimilarity * this.config.cosineWeight);

    const isDuplicate = weightedSimilarity >= this.config.duplicateThreshold;

    return {
      editDistance,
      editSimilarity,
      cosineSimilarity,
      weightedSimilarity,
      isDuplicate
    };
  }

  /**
   * 生成文本指纹
   */
  public generateTextFingerprint(text: string): string {
    const cleanText = this.cleanTextInternal(text);
    return createHash('sha256').update(cleanText).digest('hex').substring(0, 16);
  }

  /**
   * 清理文本，移除标点符号和多余空格（公共方法）
   */
  public cleanText(text: string): string {
    return this.cleanTextInternal(text);
  }

  /**
   * 清理文本，移除标点符号和多余空格（内部方法）
   */
  private cleanTextInternal(text: string): string {
    return text
      .toLowerCase()
      .replace(/[^\u4e00-\u9fa5a-zA-Z0-9\s]/g, '') // 保留中文、英文、数字和空格
      .replace(/\s+/g, ' ')
      .trim();
  }

  /**
   * 将文本转换为词频向量
   */
  private textToVector(text: string): number[] {
    const words = text.split(/\s+/);
    const wordFreq: { [key: string]: number } = {};
    
    // 统计词频
    words.forEach(word => {
      if (word.length > 0) {
        wordFreq[word] = (wordFreq[word] || 0) + 1;
      }
    });

    // 获取所有唯一词汇
    const allWords = Object.keys(wordFreq).sort();
    
    // 转换为向量
    return allWords.map(word => wordFreq[word] || 0);
  }

  /**
   * 计算两个向量的余弦相似度
   */
  private calculateCosineSimilarity(vector1: number[], vector2: number[]): number {
    if (vector1.length === 0 || vector2.length === 0) {
      return 0;
    }

    // 确保向量长度相同
    const maxLength = Math.max(vector1.length, vector2.length);
    const v1 = [...vector1, ...new Array(maxLength - vector1.length).fill(0)];
    const v2 = [...vector2, ...new Array(maxLength - vector2.length).fill(0)];

    // 计算点积
    const dotProduct = v1.reduce((sum, a, i) => sum + a * v2[i], 0);
    
    // 计算向量长度
    const magnitude1 = Math.sqrt(v1.reduce((sum, a) => sum + a * a, 0));
    const magnitude2 = Math.sqrt(v2.reduce((sum, a) => sum + a * a, 0));

    if (magnitude1 === 0 || magnitude2 === 0) {
      return 0;
    }

    return dotProduct / (magnitude1 * magnitude2);
  }

  /**
   * 动态调整阈值
   */
  public adjustThreshold(textLength: number): number {
    // 根据文本长度动态调整阈值
    if (textLength < 10) {
      return 0.95; // 短文本要求更高的相似度
    } else if (textLength < 50) {
      return 0.85; // 中等长度文本
    } else {
      return 0.75; // 长文本可以容忍更多差异
    }
  }
}

// 创建默认实例
export const defaultSimilarityDetector = new TextSimilarityDetector(); 