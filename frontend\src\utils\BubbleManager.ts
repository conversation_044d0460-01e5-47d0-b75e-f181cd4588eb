// frontend/src/utils/BubbleManager.ts
import { Message } from '../hooks/useInterviewSession';

export interface UpdateResult {
  action: 'create' | 'update' | 'finalize';
  bubble?: Message;
  index?: number;
  text?: string;
}

export interface BubbleState {
  currentBubbleId: string | null;
  bubbleHistory: string[];
  lastUpdateTime: number;
}

// 🔥 性能监控：BubbleManager统计
interface BubbleManagerStats {
  totalOperations: number;
  createOperations: number;
  updateOperations: number;
  finalizeOperations: number;
  debouncedUpdates: number;
  averageUpdateInterval: number;
  lastOperationTime: number;
}

/**
 * 气泡管理器 - 专门管理转录气泡的生命周期
 * 解决气泡位置错误和更新逻辑混乱的问题
 * 🔥 优化版本：添加防抖机制和性能监控
 */
export class BubbleManager {
  private currentBubbleId: string | null = null;
  private bubbleHistory: string[] = [];
  private lastUpdateTime: number = 0;
  private debounceTimer: NodeJS.Timeout | null = null;
  private pendingUpdate: { text: string; messages: Message[] } | null = null;

  // 🔥 性能监控
  private stats: BubbleManagerStats = {
    totalOperations: 0,
    createOperations: 0,
    updateOperations: 0,
    finalizeOperations: 0,
    debouncedUpdates: 0,
    averageUpdateInterval: 0,
    lastOperationTime: 0
  };

  constructor(private debounceDelay: number = 50) { // 50ms防抖延迟
    console.log('🔧 BubbleManager initialized with debounce delay:', debounceDelay);
  }

  /**
   * 🔥 防抖处理中间结果更新
   */
  private debouncedIntermediateUpdate(text: string, messages: Message[]): UpdateResult {
    // 清除之前的防抖定时器
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
      this.stats.debouncedUpdates++;
    }

    // 保存待处理的更新
    this.pendingUpdate = { text, messages };

    // 设置新的防抖定时器
    this.debounceTimer = setTimeout(() => {
      if (this.pendingUpdate) {
        this.performIntermediateUpdate(this.pendingUpdate.text, this.pendingUpdate.messages);
        this.pendingUpdate = null;
      }
    }, this.debounceDelay);

    // 立即返回当前状态，避免UI卡顿
    return this.getCurrentUpdateResult(text, messages);
  }

  /**
   * 🔥 获取当前更新结果（用于防抖期间的立即响应）
   */
  private getCurrentUpdateResult(text: string, messages: Message[]): UpdateResult {
    if (!this.currentBubbleId) {
      return { action: 'create', text, index: messages.length };
    } else {
      const currentIndex = messages.findIndex(m => m.id === this.currentBubbleId);
      return { action: 'update', index: currentIndex !== -1 ? currentIndex : messages.length, text };
    }
  }

  /**
   * 🔥 更新性能统计
   */
  private updateStats(operation: 'create' | 'update' | 'finalize'): void {
    const now = Date.now();
    this.stats.totalOperations++;

    switch (operation) {
      case 'create':
        this.stats.createOperations++;
        break;
      case 'update':
        this.stats.updateOperations++;
        break;
      case 'finalize':
        this.stats.finalizeOperations++;
        break;
    }

    // 计算平均更新间隔
    if (this.stats.lastOperationTime > 0) {
      const interval = now - this.stats.lastOperationTime;
      this.stats.averageUpdateInterval =
        (this.stats.averageUpdateInterval * (this.stats.totalOperations - 1) + interval) / this.stats.totalOperations;
    }

    this.stats.lastOperationTime = now;
  }

  /**
   * 🔥 执行实际的中间结果更新
   */
  private performIntermediateUpdate(text: string, _messages: Message[]): void {
    this.updateStats('update');
    // 这里可以触发实际的UI更新事件
    console.log('📊 Performance: Debounced intermediate update executed:', {
      text: text.substring(0, 20) + '...',
      debouncedCount: this.stats.debouncedUpdates,
      averageInterval: Math.round(this.stats.averageUpdateInterval)
    });
  }

  /**
   * 处理中间转录结果
   * @param text 转录文本
   * @param messages 当前消息数组
   * @returns 更新结果
   */
  handleIntermediateResult(text: string, messages: Message[]): UpdateResult {
    // 🔥 性能优化：减少日志输出频率
    const shouldLog = this.stats.totalOperations % 10 === 0; // 每10次操作输出一次日志

    if (shouldLog) {
      console.log('🎯 BubbleManager: Processing INTERMEDIATE result:', {
        text: text.substring(0, 20) + '...',
        currentBubbleId: this.currentBubbleId,
        messagesCount: messages.length,
        totalOps: this.stats.totalOperations
      });
    }

    if (!this.currentBubbleId) {
      // 创建新的中间气泡（总是添加到最后）
      this.updateStats('create');
      const newBubble = this.createNewBubble(text, 'intermediate');
      this.currentBubbleId = newBubble.id;
      this.bubbleHistory.push(newBubble.id);
      this.lastUpdateTime = Date.now();

      if (shouldLog) {
        console.log('✅ BubbleManager: Created new INTERMEDIATE bubble:', {
          bubbleId: newBubble.id,
          text: text.substring(0, 30) + '...',
          totalBubbles: this.bubbleHistory.length,
          createOps: this.stats.createOperations
        });
      }

      return {
        action: 'create',
        bubble: newBubble,
        text: text,
        index: messages.length
      };
    } else {
      // 🔥 使用防抖机制优化频繁更新
      if (this.debounceDelay > 0) {
        return this.debouncedIntermediateUpdate(text, messages);
      } else {
        // 直接更新（无防抖）
        this.updateStats('update');
        const updateResult = this.updateCurrentBubble(text, messages);
        this.lastUpdateTime = Date.now();

        if (shouldLog) {
          console.log('🔄 BubbleManager: Updated existing INTERMEDIATE bubble:', {
            bubbleId: this.currentBubbleId,
            text: text.substring(0, 30) + '...',
            updateOps: this.stats.updateOperations
          });
        }

        return updateResult;
      }
    }
  }

  /**
   * 处理最终转录结果
   * @param text 转录文本
   * @param messages 当前消息数组
   * @returns 更新结果
   */
  handleFinalResult(text: string, messages: Message[]): UpdateResult {
    console.log('🎯 BubbleManager: Processing FINAL result:', {
      text: text.substring(0, 20) + '...',
      currentBubbleId: this.currentBubbleId,
      messagesCount: messages.length
    });

    if (this.currentBubbleId) {
      // 将当前气泡转换为最终状态
      const result = this.finalizeCurrentBubble(text, messages);
      this.currentBubbleId = null; // 清空，准备下一个
      this.lastUpdateTime = Date.now();

      console.log('✅ BubbleManager: Finalized current bubble:', {
        finalizedBubbleId: this.currentBubbleId,
        text: text.substring(0, 30) + '...',
        action: result.action
      });

      return result;
    } else {
      // 直接创建最终气泡
      const newBubble = this.createNewBubble(text, 'final');
      this.bubbleHistory.push(newBubble.id);
      this.lastUpdateTime = Date.now();

      console.log('✅ BubbleManager: Created new FINAL bubble (no current):', {
        bubbleId: newBubble.id,
        text: text.substring(0, 30) + '...',
        totalBubbles: this.bubbleHistory.length
      });

      return {
        action: 'create',
        bubble: newBubble,
        text: text, // 🔥 修复：确保返回原始text
        index: messages.length
      };
    }
  }

  /**
   * 更新当前气泡
   * @param text 新文本
   * @param messages 当前消息数组
   * @returns 更新结果
   */
  private updateCurrentBubble(text: string, messages: Message[]): UpdateResult {
    // 🔥 关键修复：确保更新的是最后一个气泡
    const currentIndex = messages.findIndex(m => m.id === this.currentBubbleId);

    if (currentIndex !== -1) {
      console.log('🔍 BubbleManager: Found current bubble at index:', {
        index: currentIndex,
        bubbleId: this.currentBubbleId,
        totalMessages: messages.length
      });

      return { action: 'update', index: currentIndex, text };
    } else {
      // 如果找不到，说明状态不一致，直接创建新气泡而不递归调用
      console.warn('⚠️ BubbleManager: Current bubble not found, creating new one:', {
        expectedId: this.currentBubbleId,
        availableIds: messages.map(m => m.id)
      });

      // 重置状态并直接创建新气泡（避免递归调用）
      this.currentBubbleId = null;
      const newBubble = this.createNewBubble(text, 'intermediate');
      this.currentBubbleId = newBubble.id;
      this.bubbleHistory.push(newBubble.id);
      this.lastUpdateTime = Date.now();

      console.log('✅ BubbleManager: Created new INTERMEDIATE bubble (recovery):', {
        bubbleId: newBubble.id,
        text: text.substring(0, 30) + '...',
        totalBubbles: this.bubbleHistory.length
      });

      return {
        action: 'create',
        bubble: newBubble,
        text: text,
        index: messages.length
      };
    }
  }

  /**
   * 将当前气泡转换为最终状态
   * @param text 最终文本
   * @param messages 当前消息数组
   * @returns 更新结果
   */
  private finalizeCurrentBubble(text: string, messages: Message[]): UpdateResult {
    const currentIndex = messages.findIndex(m => m.id === this.currentBubbleId);
    
    if (currentIndex !== -1) {
      console.log('🔍 BubbleManager: Finalizing bubble at index:', {
        index: currentIndex,
        bubbleId: this.currentBubbleId,
        finalText: text.substring(0, 30) + '...'
      });
      
      return { action: 'finalize', index: currentIndex, text };
    } else {
      // 如果找不到当前气泡，直接创建新的最终气泡
      console.warn('⚠️ BubbleManager: Current bubble not found for finalization, creating new final bubble');
      const newBubble = this.createNewBubble(text, 'final');
      this.bubbleHistory.push(newBubble.id);
      return {
        action: 'create',
        bubble: newBubble,
        text: text, // 🔥 修复：确保返回原始text
        index: messages.length
      };
    }
  }

  /**
   * 创建新气泡
   * @param text 文本内容
   * @param type 气泡类型
   * @returns 新气泡消息
   */
  private createNewBubble(text: string, type: 'intermediate' | 'final'): Message {
    const timestamp = Date.now();
    const id = this.generateBubbleId(type, timestamp);
    
    return {
      id,
      content: text,
      type: 'interviewer',
      timestamp
    };
  }

  /**
   * 生成气泡ID
   * @param type 气泡类型
   * @param timestamp 时间戳
   * @returns 唯一ID
   */
  private generateBubbleId(type: 'intermediate' | 'final', timestamp: number): string {
    const prefix = type === 'intermediate' ? 'transcription-current' : 'transcription-final';
    const randomSuffix = Math.random().toString(36).substring(2, 15);
    return `${prefix}-${timestamp}-${randomSuffix}`;
  }

  /**
   * 获取当前状态
   * @returns 气泡状态
   */
  getState(): BubbleState {
    return {
      currentBubbleId: this.currentBubbleId,
      bubbleHistory: [...this.bubbleHistory],
      lastUpdateTime: this.lastUpdateTime
    };
  }

  /**
   * 重置管理器状态
   */
  reset(): void {
    console.log('🔧 BubbleManager: Resetting state');
    this.currentBubbleId = null;
    this.bubbleHistory = [];
    this.lastUpdateTime = 0;

    // 🔥 清理防抖定时器
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
      this.debounceTimer = null;
    }
    this.pendingUpdate = null;

    // 重置统计
    this.stats = {
      totalOperations: 0,
      createOperations: 0,
      updateOperations: 0,
      finalizeOperations: 0,
      debouncedUpdates: 0,
      averageUpdateInterval: 0,
      lastOperationTime: 0
    };
  }

  /**
   * 🔥 获取性能统计
   */
  getPerformanceStats(): BubbleManagerStats & { timestamp: number } {
    return {
      ...this.stats,
      timestamp: Date.now()
    };
  }

  /**
   * 🔥 设置防抖延迟
   */
  setDebounceDelay(delay: number): void {
    this.debounceDelay = delay;
    console.log('📊 Performance: BubbleManager debounce delay updated to:', delay);
  }

  /**
   * 验证消息数组的完整性
   * @param messages 消息数组
   * @returns 验证结果
   */
  validateMessages(messages: Message[]): { isValid: boolean; issues: string[] } {
    const issues: string[] = [];

    // 检查时间戳顺序
    for (let i = 1; i < messages.length; i++) {
      if (messages[i].timestamp < messages[i-1].timestamp) {
        issues.push(`Timestamp order violation at index ${i}`);
      }
    }

    // 检查重复ID
    const ids = messages.map(m => m.id);
    const uniqueIds = new Set(ids);
    if (ids.length !== uniqueIds.size) {
      issues.push('Duplicate message IDs detected');
    }

    // 检查当前气泡是否存在
    if (this.currentBubbleId && !messages.find(m => m.id === this.currentBubbleId)) {
      issues.push(`Current bubble ${this.currentBubbleId} not found in messages`);
    }

    return {
      isValid: issues.length === 0,
      issues
    };
  }
}

// 🔥 全局性能监控工具
export class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private startTime: number = Date.now();
  private renderCounts: Map<string, number> = new Map();
  private lastLogTime: number = 0;
  private logInterval: number = 5000; // 5秒输出一次统计

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  /**
   * 记录组件渲染
   */
  recordRender(componentName: string): void {
    const current = this.renderCounts.get(componentName) || 0;
    this.renderCounts.set(componentName, current + 1);

    // 定期输出统计
    const now = Date.now();
    if (now - this.lastLogTime > this.logInterval) {
      this.logPerformanceStats();
      this.lastLogTime = now;
    }
  }

  /**
   * 输出性能统计
   */
  private logPerformanceStats(): void {
    const uptime = Date.now() - this.startTime;
    const stats = Array.from(this.renderCounts.entries()).map(([component, count]) => ({
      component,
      count,
      avgPerSecond: (count / (uptime / 1000)).toFixed(2)
    }));

    console.log('📊 Performance Monitor Stats:', {
      uptime: `${(uptime / 1000).toFixed(1)}s`,
      components: stats,
      totalRenders: Array.from(this.renderCounts.values()).reduce((sum, count) => sum + count, 0)
    });
  }

  /**
   * 重置统计
   */
  reset(): void {
    this.renderCounts.clear();
    this.startTime = Date.now();
    this.lastLogTime = 0;
  }
}

