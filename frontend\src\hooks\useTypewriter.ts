import { useState, useEffect, useRef, useMemo, useCallback } from 'react';

interface UseTypewriterOptions {
  speed?: number; // 打字速度，毫秒每字符
  enabled?: boolean; // 是否启用打字机效果
}

// 🔥 性能监控：Hook使用统计
const hookUsageStats = {
  totalInstances: 0,
  activeAnimations: 0,
  completedAnimations: 0
};

export const useTypewriter = (
  text: string,
  options: UseTypewriterOptions = {}
) => {
  // 🔥 使用useMemo优化选项解构，避免每次渲染都重新计算
  const { speed, enabled } = useMemo(() => ({
    speed: options.speed ?? 50,
    enabled: options.enabled ?? true
  }), [options.speed, options.enabled]);

  const [displayedText, setDisplayedText] = useState('');
  const [isComplete, setIsComplete] = useState(false);
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const instanceIdRef = useRef<string>();

  // 🔥 性能监控：为每个实例分配唯一ID
  if (!instanceIdRef.current) {
    instanceIdRef.current = `typewriter-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    hookUsageStats.totalInstances++;
    console.log('📊 Performance: useTypewriter instance created:', {
      instanceId: instanceIdRef.current,
      totalInstances: hookUsageStats.totalInstances
    });
  }

  // 🔥 使用useCallback优化清理函数
  const cleanup = useCallback(() => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
      hookUsageStats.activeAnimations--;
      console.log('📊 Performance: Typewriter animation cleaned up:', {
        instanceId: instanceIdRef.current,
        activeAnimations: hookUsageStats.activeAnimations
      });
    }
  }, []);

  useEffect(() => {
    // 清理之前的定时器
    cleanup();

    // 如果未启用打字机效果，直接显示完整文本
    if (!enabled) {
      setDisplayedText(text);
      setIsComplete(true);
      console.log('📊 Performance: Typewriter disabled, showing full text immediately');
      return;
    }

    // 重置状态
    setDisplayedText('');
    setIsComplete(false);

    if (!text) {
      setIsComplete(true);
      return;
    }

    // 🔥 性能监控：开始动画
    hookUsageStats.activeAnimations++;
    const startTime = Date.now();
    console.log('📊 Performance: Typewriter animation started:', {
      instanceId: instanceIdRef.current,
      textLength: text.length,
      speed,
      activeAnimations: hookUsageStats.activeAnimations
    });

    let currentIndex = 0;
    timerRef.current = setInterval(() => {
      if (currentIndex < text.length) {
        setDisplayedText(text.slice(0, currentIndex + 1));
        currentIndex++;
      } else {
        setIsComplete(true);
        cleanup();

        // 🔥 性能监控：动画完成
        hookUsageStats.completedAnimations++;
        const duration = Date.now() - startTime;
        console.log('📊 Performance: Typewriter animation completed:', {
          instanceId: instanceIdRef.current,
          duration,
          textLength: text.length,
          completedAnimations: hookUsageStats.completedAnimations
        });
      }
    }, speed);

    return cleanup;
  }, [text, speed, enabled, cleanup]);

  // 🔥 使用useMemo优化返回值，避免每次渲染都创建新对象
  return useMemo(() => ({
    displayedText,
    isComplete
  }), [displayedText, isComplete]);
};

// 🔥 导出性能统计函数，用于调试
export const getTypewriterStats = () => ({
  ...hookUsageStats,
  timestamp: Date.now()
});
