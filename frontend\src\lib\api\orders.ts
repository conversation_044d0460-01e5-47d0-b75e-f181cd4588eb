import { fetchWithAuth } from './apiService';

// 订单状态类型
export type OrderStatus = 'paid' | 'unpaid' | 'cancelled';

// 订单接口
export interface Order {
  id: string;
  orderNumber: string;
  createTime: string;
  product: string;
  price: string;
  status: OrderStatus;
}

// 订单列表响应接口
export interface OrdersResponse {
  success: boolean;
  orders: Order[];
  total: number;
}

// 获取订单列表
export const getOrders = async (params?: {
  page?: number;
  pageSize?: number;
  status?: OrderStatus | 'all';
}): Promise<OrdersResponse> => {
  const searchParams = new URLSearchParams();
  
  if (params?.page) {
    searchParams.append('page', params.page.toString());
  }
  if (params?.pageSize) {
    searchParams.append('pageSize', params.pageSize.toString());
  }
  if (params?.status && params.status !== 'all') {
    searchParams.append('status', params.status);
  }

  const url = `/api/orders${searchParams.toString() ? `?${searchParams.toString()}` : ''}`;

  // fetchWithAuth 直接返回解析后的JSON数据
  return await fetchWithAuth<OrdersResponse>(url, {
    method: 'GET'
  });
};
