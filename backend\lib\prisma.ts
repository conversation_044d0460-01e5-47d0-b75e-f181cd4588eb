// backend/lib/prisma.ts
import dotenv from 'dotenv';
dotenv.config();

import { PrismaClient } from '@prisma/client';

// 全局Prisma实例，避免多个连接池
declare global {
  var __prisma: PrismaClient | undefined;
}

// 创建单例Prisma客户端
const createPrismaClient = () => {
  return new PrismaClient({
    log: ['warn', 'error'], // 只记录警告和错误
    datasources: {
      db: {
        url: process.env.DATABASE_URL
      }
    },
    errorFormat: 'pretty',
    // 添加连接池配置以改善Serverless环境下的连接管理
    __internal: {
      engine: {
        connectTimeout: 60000, // 60秒连接超时
        pool_timeout: 60000,   // 60秒池超时
      }
    }
  });
};

// 在开发环境中使用全局变量避免热重载时创建多个实例
const prisma = globalThis.__prisma || createPrismaClient();

if (process.env.NODE_ENV === 'development') {
  globalThis.__prisma = prisma;
}

// 优雅关闭处理
process.on('beforeExit', async () => {
  await prisma.$disconnect();
});

process.on('SIGINT', async () => {
  await prisma.$disconnect();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  await prisma.$disconnect();
  process.exit(0);
});

export default prisma;
