// frontend/src/utils/LLMResponseDeduplicator.ts

/**
 * LLM响应去重器 - 智能过滤重复的文本片段
 * 解决LLM返回重复内容的问题
 */
export class LLMResponseDeduplicator {
  private seenChunks: Set<string> = new Set();
  private lastChunk: string = '';
  private accumulatedText: string = '';
  private consecutiveCount: Map<string, number> = new Map();
  private chunkHistory: string[] = [];

  constructor() {
    console.log('🔧 LLMResponseDeduplicator initialized');
  }

  /**
   * 处理单个文本片段
   * @param chunk 文本片段
   * @returns 去重后的片段，如果应该过滤则返回null
   */
  processChunk(chunk: string): string | null {
    // 记录原始chunk
    this.chunkHistory.push(chunk);

    // 🔥 去重策略1：过滤空白或无意义的chunk
    if (!chunk || chunk.trim().length === 0) {
      console.log('🔧 Filtered empty chunk');
      return null;
    }

    // 🔥 去重策略2：完全重复的chunk
    if (this.seenChunks.has(chunk)) {
      console.log(`🔧 Filtered duplicate chunk: "${chunk}"`);
      return null;
    }

    // 🔥 去重策略3：连续重复的chunk
    if (chunk === this.lastChunk) {
      const count = this.consecutiveCount.get(chunk) || 0;
      this.consecutiveCount.set(chunk, count + 1);
      
      if (count > 0) { // 第二次及以后出现
        console.log(`🔧 Filtered consecutive duplicate (${count + 1}x): "${chunk}"`);
        return null;
      }
    } else {
      // 重置连续计数
      this.consecutiveCount.clear();
    }

    // 🔥 去重策略4：已包含在累积文本中的chunk（避免重复显示）
    // 修复：只过滤完全重复的短语，不过滤包含相同词汇的不同句子
    if (chunk.length > 2) {
      // 检查是否是完全重复的短语（连续出现）
      const lastPartOfAccumulated = this.accumulatedText.slice(-chunk.length * 2);
      if (lastPartOfAccumulated.includes(chunk + chunk)) {
        console.log(`🔧 Filtered exact duplicate phrase: "${chunk}"`);
        return null;
      }
    }

    // 🔥 去重策略5：检测模式重复（如"您您是说是说"）
    if (this.detectPatternRepetition(chunk)) {
      console.log(`🔧 Filtered pattern repetition: "${chunk}"`);
      return null;
    }

    // 记录并返回有效chunk
    this.seenChunks.add(chunk);
    this.lastChunk = chunk;
    this.accumulatedText += chunk;

    console.log(`✅ Accepted chunk: "${chunk}" (total length: ${this.accumulatedText.length})`);
    return chunk;
  }

  /**
   * 检测模式重复（如"您您"、"是说是说"）
   * @param chunk 当前chunk
   * @returns 是否检测到模式重复
   */
  private detectPatternRepetition(chunk: string): boolean {
    // 检查当前chunk是否与最近的几个chunk形成重复模式
    const recentChunks = this.chunkHistory.slice(-5); // 检查最近5个chunk
    
    // 检查是否存在AB-AB模式
    if (recentChunks.length >= 3) {
      const prev2 = recentChunks[recentChunks.length - 3];
      const prev1 = recentChunks[recentChunks.length - 2];
      
      // 检查是否是AB-AB模式
      if (prev2 === this.lastChunk && prev1 === chunk) {
        return true;
      }
    }

    // 检查是否存在A-A模式（单字符重复）
    if (chunk.length === 1 && chunk === this.lastChunk) {
      return true;
    }

    // 检查是否存在词汇级别的重复
    if (chunk.length > 1 && this.lastChunk.length > 1) {
      const combinedText = this.lastChunk + chunk;
      const halfLength = Math.floor(combinedText.length / 2);
      const firstHalf = combinedText.substring(0, halfLength);
      const secondHalf = combinedText.substring(halfLength);
      
      if (firstHalf === secondHalf && firstHalf.length > 1) {
        return true;
      }
    }

    return false;
  }

  /**
   * 判断chunk是否有意义（不是标点符号等）
   * @param chunk 文本片段
   * @returns 是否有意义
   */
  private isSignificantChunk(chunk: string): boolean {
    // 标点符号和空白字符不算有意义的重复
    const insignificantPattern = /^[\s\p{P}\p{S}]*$/u;
    return !insignificantPattern.test(chunk);
  }

  /**
   * 获取当前累积的文本
   * @returns 累积文本
   */
  getAccumulatedText(): string {
    return this.accumulatedText;
  }

  /**
   * 获取处理统计信息
   * @returns 统计信息
   */
  getStatistics(): {
    totalChunks: number;
    acceptedChunks: number;
    filteredChunks: number;
    accumulatedLength: number;
    filterRate: number;
  } {
    const totalChunks = this.chunkHistory.length;
    const acceptedChunks = this.seenChunks.size;
    const filteredChunks = totalChunks - acceptedChunks;
    const filterRate = totalChunks > 0 ? (filteredChunks / totalChunks) * 100 : 0;

    return {
      totalChunks,
      acceptedChunks,
      filteredChunks,
      accumulatedLength: this.accumulatedText.length,
      filterRate: Math.round(filterRate * 100) / 100
    };
  }

  /**
   * 重置去重器状态（用于新的AI建议会话）
   */
  reset(): void {
    console.log('🔧 LLMResponseDeduplicator: Resetting state');
    this.seenChunks.clear();
    this.lastChunk = '';
    this.accumulatedText = '';
    this.consecutiveCount.clear();
    this.chunkHistory = [];
  }

  /**
   * 获取调试信息
   * @returns 调试信息
   */
  getDebugInfo(): {
    seenChunks: string[];
    lastChunk: string;
    accumulatedText: string;
    chunkHistory: string[];
    statistics: ReturnType<LLMResponseDeduplicator['getStatistics']>;
  } {
    return {
      seenChunks: Array.from(this.seenChunks),
      lastChunk: this.lastChunk,
      accumulatedText: this.accumulatedText,
      chunkHistory: [...this.chunkHistory],
      statistics: this.getStatistics()
    };
  }

  /**
   * 手动添加已知文本（用于初始化或恢复状态）
   * @param text 已知文本
   */
  addKnownText(text: string): void {
    this.accumulatedText += text;
    console.log(`🔧 Added known text: "${text.substring(0, 30)}..." (length: ${text.length})`);
  }

  /**
   * 验证当前状态的一致性
   * @returns 验证结果
   */
  validateState(): { isValid: boolean; issues: string[] } {
    const issues: string[] = [];

    // 检查累积文本长度是否合理
    if (this.accumulatedText.length > 10000) {
      issues.push('Accumulated text is too long (>10000 chars)');
    }

    // 检查seen chunks数量是否合理
    if (this.seenChunks.size > 1000) {
      issues.push('Too many seen chunks (>1000)');
    }

    // 检查chunk历史长度
    if (this.chunkHistory.length > 2000) {
      issues.push('Chunk history is too long (>2000)');
    }

    return {
      isValid: issues.length === 0,
      issues
    };
  }
}
