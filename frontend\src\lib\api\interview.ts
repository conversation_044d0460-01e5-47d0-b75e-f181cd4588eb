import { interviewService } from './apiService';

// 定义文字记录条目的类型
export interface TranscriptEntry {
  speaker: string; // 'interviewer' | 'candidate' 等
  content: string;
  timestamp: string; // ISO DateTime string
}

// TODO: 定义 AI 建议条目的类型 - 暂时注释，等待AISuggestion模型实现
// export interface AISuggestionEntry {
//   question_text: string;
//   suggestion_text: string;
//   timestamp: string; // 或者 Date 类型
// }

// 定义整个面试回顾数据的类型
export interface InterviewReviewData {
  sessionId: string;
  startedAt?: string; // ISO DateTime string
  endedAt?: string;   // ISO DateTime string
  status?: string;
  position?: string;  // 添加岗位信息
  company?: string;   // 添加公司信息
  transcripts: TranscriptEntry[];
  // aiSuggestions: AISuggestionEntry[]; // 暂时注释
  aiSuggestions: any[]; // 暂时使用any[]，等待AISuggestion实现
}

// 定义面试记录的类型（用于列表显示）
export interface InterviewRecord {
  id: string;
  position: string;
  company: string;
  date: string; // YYYY-MM-DD格式
  duration: string;
  score: number;
  status: 'completed' | 'in-progress' | 'cancelled';
}

// 定义获取面试回顾数据的异步函数
export const getInterviewReview = async (sessionId: string): Promise<InterviewReviewData> => {
  return await interviewService.getInterviewReview(sessionId);
};

// 定义获取面试记录列表的异步函数
export const getInterviewRecords = async (): Promise<InterviewRecord[]> => {
  return await interviewService.getInterviewRecords();
};
