import { RegisterFormValues } from '../validations/auth';

// API 基础URL (API base URL)
const API_URL = process.env.NODE_ENV === 'production'
  ? 'https://mianshijun.xyz' // 生产环境 API URL (使用完整域名)
  : '';     // 开发环境使用相对路径，通过Vite代理转发 (Use relative path for development, forwarded via Vite proxy)

// 注册 API (Register API)
export const registerUser = async (userData: RegisterFormValues) => {
  try {
    // 注意：我们需要移除confirmPassword，因为后端不需要这个字段
    // Note: We need to remove confirmPassword as backend doesn't need this field
    const { confirmPassword, ...registerData } = userData;

    console.log('Original userData:', userData);
    console.log('Processed registerData:', registerData);

    // 创建一个干净的对象，确保没有额外的属性
    const cleanData = {
      email: registerData.email,
      password: registerData.password,
      ...(registerData.name && registerData.name.trim() !== '' ? { name: registerData.name } : {})
    };

    console.log('Clean data to send:', cleanData);
    console.log('JSON string:', JSON.stringify(cleanData));

    const response = await fetch(`${API_URL}/api/auth/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(cleanData),
    });

    const data = await response.json();

    if (!response.ok) {
      // 如果响应不是2xx，抛出错误 (If response is not 2xx, throw error)
      throw new Error(data.message || '注册失败，请稍后再试');
    }

    return data;
  } catch (error) {
    console.error('Registration error:', error);
    throw error; // 重新抛出错误，让调用者处理 (Re-throw error for caller to handle)
  }
};

// 登录 API (Login API)
export const loginUser = async (email: string, password: string) => {
  try {
    console.log(`Attempting to login with email: ${email}`);
    console.log(`API URL: ${API_URL}/api/auth/login`);

    const response = await fetch(`${API_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email, password }),
    });

    console.log(`Login response status: ${response.status}`);

    let data;
    try {
      data = await response.json();
      console.log('Login response data:', data);
    } catch (jsonError) {
      console.error('Failed to parse JSON response:', jsonError);
      throw new Error('服务器响应格式错误');
    }

    if (!response.ok) {
      console.error('Login failed with status:', response.status);
      throw new Error(data.message || '登录失败，请检查您的凭据');
    }

    return data;
  } catch (error) {
    console.error('Login error:', error);
    throw error;
  }
};