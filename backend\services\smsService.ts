export class SmsService {
  private client: any;
  private initialized: boolean = false;

  constructor() {
    // 延迟初始化，在第一次使用时再创建客户端
  }

  private async initializeClient() {
    if (this.initialized) return;

    try {
      const { default: Dysmsapi20170525 } = await import('@alicloud/dysmsapi20170525');
      const { default: OpenApi } = await import('@alicloud/openapi-client');

      const config = new OpenApi.Config({
        accessKeyId: process.env.ALIBABA_ACCESS_KEY_ID,
        accessKeySecret: process.env.ALIBABA_ACCESS_KEY_SECRET,
      });

      // 访问的域名
      config.endpoint = 'dysmsapi.aliyuncs.com';
      this.client = new Dysmsapi20170525(config);
      this.initialized = true;
    } catch (error) {
      console.error('Failed to initialize SMS client:', error);
      throw new Error('短信服务初始化失败');
    }
  }

  /**
   * 发送验证码短信
   * @param phoneNumber 手机号码
   * @param code 验证码
   * @returns Promise<void> 发送成功或抛出异常
   */
  async sendVerificationCode(phoneNumber: string, code: string): Promise<void> {
    // 确保客户端已初始化
    await this.initializeClient();
    const signName = process.env.ALIBABA_SMS_SIGN_NAME || '面试君';
    const templateCode = process.env.ALIBABA_SMS_TEMPLATE_CODE;

    if (!templateCode) {
      console.error('SMS template code not configured');
      throw new Error('短信模板代码未配置，请联系管理员');
    }

    if (!process.env.ALIBABA_ACCESS_KEY_ID || !process.env.ALIBABA_ACCESS_KEY_SECRET) {
      console.error('Alibaba Cloud SMS credentials not configured');
      throw new Error('阿里云短信服务密钥未配置，请联系管理员');
    }

    // 验证手机号格式
    if (!SmsService.isValidPhoneNumber(phoneNumber)) {
      throw new Error('手机号格式不正确');
    }

    // 动态导入所需的类
    const { default: Dysmsapi20170525 } = await import('@alicloud/dysmsapi20170525');
    const { default: Util } = await import('@alicloud/tea-util');

    const sendSmsRequest = new Dysmsapi20170525.SendSmsRequest({
      phoneNumbers: phoneNumber,
      signName: signName,
      templateCode: templateCode,
      templateParam: JSON.stringify({ code: code })
    });

    const runtime = new Util.RuntimeOptions({});

    try {
      console.log(`Sending SMS to ${phoneNumber} with template ${templateCode}`);
      const response = await this.client.sendSmsWithOptions(sendSmsRequest, runtime);

      if (response.body.code === 'OK') {
        console.log('SMS sent successfully:', {
          requestId: response.body.requestId,
          bizId: response.body.bizId,
          phoneNumber: phoneNumber
        });
      } else {
        console.error('SMS sending failed:', {
          code: response.body.code,
          message: response.body.message,
          requestId: response.body.requestId,
          phoneNumber: phoneNumber
        });
        throw new Error(`短信发送失败: ${response.body.message || response.body.code}`);
      }
    } catch (error: any) {
      console.error('SMS service error:', {
        error: error.message,
        phoneNumber: phoneNumber,
        templateCode: templateCode
      });

      // 根据错误类型提供更友好的错误信息
      if (error.message?.includes('InvalidPhoneNumbers')) {
        throw new Error('手机号格式不正确');
      } else if (error.message?.includes('InvalidSignName')) {
        throw new Error('短信签名无效，请联系管理员');
      } else if (error.message?.includes('InvalidTemplateCode')) {
        throw new Error('短信模板无效，请联系管理员');
      } else if (error.message?.includes('Throttling')) {
        throw new Error('发送频率过快，请稍后再试');
      } else {
        throw new Error('短信发送失败，请稍后重试');
      }
    }
  }

  /**
   * 测试短信服务连接
   * @returns Promise<boolean> 连接是否成功
   */
  async testConnection(): Promise<boolean> {
    try {
      if (!process.env.ALIBABA_ACCESS_KEY_ID || !process.env.ALIBABA_ACCESS_KEY_SECRET) {
        console.log('SMS service credentials not configured');
        return false;
      }

      // 这里可以调用查询短信发送状态的API来测试连接
      // 暂时返回true，表示配置正确
      console.log('SMS service connection verified');
      return true;
    } catch (error) {
      console.error('SMS service connection failed:', error);
      return false;
    }
  }

  /**
   * 验证手机号格式
   * @param phoneNumber 手机号
   * @returns boolean 格式是否正确
   */
  static isValidPhoneNumber(phoneNumber: string): boolean {
    // 中国大陆手机号验证
    const phoneRegex = /^1[3-9]\d{9}$/;
    return phoneRegex.test(phoneNumber);
  }

  /**
   * 格式化手机号（添加+86前缀）
   * @param phoneNumber 手机号
   * @returns string 格式化后的手机号
   */
  static formatPhoneNumber(phoneNumber: string): string {
    // 移除所有非数字字符
    const cleanNumber = phoneNumber.replace(/\D/g, '');
    
    // 如果是11位数字且以1开头，添加+86前缀
    if (cleanNumber.length === 11 && cleanNumber.startsWith('1')) {
      return `+86${cleanNumber}`;
    }
    
    return cleanNumber;
  }


}

export default SmsService;
