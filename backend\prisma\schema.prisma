generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider     = "postgresql"
  url          = env("DATABASE_URL")
  relationMode = "prisma"
}

model User {
  id                String                @id @default(cuid())
  email             String                @unique
  password          String
  name              String?
  createdAt         DateTime              @default(now())
  updatedAt         DateTime              @updatedAt
  phoneNumber       String?
  role              UserRole              @default(USER)
  balance           UserBalance?
  resumes           Resume[]
  targetPositions   TargetPosition[]
  interviewSessions InterviewSession[]
  feedbacks         Feedback[]
  orders            Order[]
  redeemedCodes     RedemptionCode[]
  createdCodes      RedemptionCode[]      @relation("CreatedCodes")
  codeUsageRecords  CodeUsage[]
  usageRecords      UsageRecord[]
  mockInterviews    MockInterviewRecord[]
  statistics        InterviewStatistics?

  // 邀请相关关系
  referralCodes      ReferralCode[]
  inviterRelations   ReferralRelation[] @relation("InviterRelations")
  inviteeRelation    ReferralRelation?  @relation("InviteeRelation")
  inviterRewards     ReferralReward[]   @relation("InviterRewards")
  inviteeRewards     ReferralReward[]   @relation("InviteeRewards")

  // 通知关系
  createdNotifications Notification[]   // 创建的通知（管理员）
  userNotifications    UserNotification[] // 用户通知关联
}

model UserBalance {
  id                     Int      @id @default(autoincrement())
  userId                 String   @unique
  mockInterviewCredits   Int      @default(0)
  formalInterviewCredits Int      @default(0)
  createdAt              DateTime @default(now())
  updatedAt              DateTime @updatedAt
  mianshijunBalance      Int      @default(0)
  user                   User     @relation(fields: [userId], references: [id])
}

model Resume {
  id              String   @id @default(cuid())
  userId          String
  fileName        String
  filePath        String
  fileType        String?
  fileSize        Int?
  jobTitle        String?
  uploadTimestamp DateTime @default(now())
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  user            User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@map("resumes")
}

model TargetPosition {
  id                   String   @id @default(cuid())
  userId               String
  positionName         String
  positionRequirements String?
  companyName          String
  companyProfile       String?
  status               String?
  createdAt            DateTime @default(now())
  updatedAt            DateTime @updatedAt
  user                 User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@map("target_positions")
}

model InterviewSession {
  id           String                @id @default(cuid())
  userId       String
  titleJobInfo String?               // 保持向后兼容
  companyName  String?               // 新增：公司名称
  positionName String?               // 新增：岗位名称
  status       String                @default("pending")
  createdAt    DateTime              @default(now())
  startedAt    DateTime?
  endedAt      DateTime?
  user         User                  @relation(fields: [userId], references: [id], onDelete: Cascade)
  transcripts  InterviewTranscript[]

  @@map("interview_sessions")
}

model InterviewTranscript {
  id        String           @id @default(cuid())
  sessionId String
  speaker   String
  content   String
  timestamp DateTime         @default(now())
  session   InterviewSession @relation(fields: [sessionId], references: [id], onDelete: Cascade)

  @@map("interview_transcripts")
}

model Feedback {
  id        String   @id @default(cuid())
  userId    String
  type      String
  title     String
  content   String
  status    String   @default("pending")
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@map("feedbacks")
}

model Order {
  id              String      @id @default(cuid())
  userId          String
  amount          Decimal
  status          OrderStatus @default(PENDING)
  paymentMethod   String
  itemId          String?
  itemDescription String?
  transactionId   String?     @unique
  createdAt       DateTime    @default(now())
  updatedAt       DateTime    @updatedAt
  user            User        @relation(fields: [userId], references: [id])

  // 邀请奖励关系
  referralRewards ReferralReward[]

  @@index([userId])
  @@map("orders")
}

model RedemptionCode {
  id           String      @id @default(cuid())
  code         String      @unique
  isUsed       Boolean     @default(false)
  createdAt    DateTime    @default(now())
  updatedAt    DateTime    @updatedAt
  expiresAt    DateTime?
  benefitType  String      @default("POINTS")
  benefitValue Int         @default(0)
  userId       String?
  codePrefix   String?
  createdBy    String?
  description  String?
  isActive     Boolean     @default(true)
  usageCount   Int         @default(0)
  usageLimit   Int         @default(1)
  user         User?       @relation(fields: [userId], references: [id])
  creator      User?       @relation("CreatedCodes", fields: [createdBy], references: [id])
  usageRecords CodeUsage[]

  @@index([userId])
  @@index([createdBy])
  @@map("redemption_codes")
}

model CodeUsage {
  id            String         @id @default(cuid())
  codeId        String
  userId        String
  usedAt        DateTime       @default(now())
  benefitType   String
  benefitAmount Int
  code          RedemptionCode @relation(fields: [codeId], references: [id], onDelete: Cascade)
  user          User           @relation(fields: [userId], references: [id], onDelete: Cascade)

  // 邀请奖励关系
  referralRewards ReferralReward[]

  @@index([codeId])
  @@index([userId])
  @@map("code_usage_records")
}

model UsageRecord {
  id        String    @id @default(cuid())
  userId    String
  type      UsageType
  amount    Int
  reason    String
  createdAt DateTime  @default(now())
  user      User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([createdAt])
  @@map("usage_records")
}

model VerificationCode {
  id         String              @id @default(cuid())
  identifier String
  code       String
  type       VerificationType
  purpose    VerificationPurpose
  expiresAt  DateTime
  attempts   Int                 @default(0)
  isUsed     Boolean             @default(false)
  createdAt  DateTime            @default(now())
  updatedAt  DateTime            @updatedAt

  @@index([identifier, type, purpose])
  @@index([expiresAt])
  @@map("verification_codes")
}

model VerificationLog {
  id           String              @id @default(cuid())
  identifier   String
  type         VerificationType
  purpose      VerificationPurpose
  status       SendStatus
  errorMessage String?
  ipAddress    String?
  userAgent    String?
  createdAt    DateTime            @default(now())

  @@index([identifier, createdAt])
  @@index([createdAt])
  @@map("verification_logs")
}

model MockInterviewRecord {
  id                String                        @id @default(cuid())
  userId            String
  sessionId         String                        @unique
  companyName       String
  positionName      String
  interviewLanguage String
  answerStyle       String
  status            String
  startTime         DateTime
  endTime           DateTime?
  totalDuration     Int?
  totalQuestions    Int                           @default(0)
  averageScore      Float?
  overallFeedback   String?
  createdAt         DateTime                      @default(now())
  updatedAt         DateTime                      @updatedAt
  user              User                          @relation(fields: [userId], references: [id], onDelete: Cascade)
  questions         MockInterviewQuestionRecord[]

  @@index([userId])
  @@index([sessionId])
  @@map("mock_interview_records")
}

model MockInterviewQuestionRecord {
  id                String              @id @default(cuid())
  recordId          String
  questionId        String
  questionText      String
  questionType      String
  difficulty        String
  expectedDuration  Int
  context           String?
  keywords          Json
  answerText        String?
  answerDuration    Int?
  score             Int?
  strengths         Json?
  improvements      Json?
  keywordsCovered   Json?
  missingKeywords   Json?
  overallAssessment String?
  detailedAnalysis  String?
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt
  record            MockInterviewRecord @relation(fields: [recordId], references: [id], onDelete: Cascade)

  @@index([recordId])
  @@map("mock_interview_question_records")
}

model InterviewStatistics {
  id                    String    @id @default(cuid())
  userId                String    @unique
  totalMockInterviews   Int       @default(0)
  totalFormalInterviews Int       @default(0)
  averageMockScore      Float?
  averageFormalScore    Float?
  totalInterviewTime    Int       @default(0)
  favoriteQuestionType  String?
  weakestQuestionType   String?
  improvementTrend      Json?
  lastInterviewDate     DateTime?
  createdAt             DateTime  @default(now())
  updatedAt             DateTime  @updatedAt
  user                  User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@map("interview_statistics")
}

// 通知管理相关表
model Notification {
  id              String                @id @default(cuid())
  title           String                // 通知标题
  content         String                // 通知内容
  type            NotificationType      @default(ANNOUNCEMENT) // 通知类型
  priority        NotificationPriority  @default(NORMAL) // 优先级
  status          NotificationStatus    @default(DRAFT) // 发布状态
  publishedAt     DateTime?             // 发布时间
  expiresAt       DateTime?             // 过期时间
  createdBy       String                // 创建者ID
  createdAt       DateTime              @default(now())
  updatedAt       DateTime              @updatedAt

  creator         User                  @relation(fields: [createdBy], references: [id], onDelete: Cascade)
  targets         NotificationTarget[]  // 目标用户群体
  userNotifications UserNotification[]  // 用户通知关联

  @@index([status])
  @@index([publishedAt])
  @@index([createdBy])
  @@map("notifications")
}

model NotificationTarget {
  id             String           @id @default(cuid())
  notificationId String
  targetType     TargetType       // 目标类型：ALL_USERS, NEW_USERS, DATE_RANGE
  targetValue    String?          // 目标值（JSON格式存储筛选条件）
  createdAt      DateTime         @default(now())

  notification   Notification     @relation(fields: [notificationId], references: [id], onDelete: Cascade)

  @@index([notificationId])
  @@map("notification_targets")
}

model UserNotification {
  id             String       @id @default(cuid())
  userId         String
  notificationId String
  isRead         Boolean      @default(false)
  readAt         DateTime?
  createdAt      DateTime     @default(now())

  user           User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  notification   Notification @relation(fields: [notificationId], references: [id], onDelete: Cascade)

  @@unique([userId, notificationId])
  @@index([userId])
  @@index([notificationId])
  @@index([isRead])
  @@map("user_notifications")
}

enum UserRole {
  USER
  ADMIN
}

enum VerificationType {
  EMAIL
  SMS
}

enum VerificationPurpose {
  LOGIN
  REGISTER
  RESET_PASSWORD
}

enum SendStatus {
  SUCCESS
  FAILED
  PENDING
}

enum UsageType {
  MOCK_INTERVIEW
  FORMAL_INTERVIEW
  CREDIT_RECHARGE
}

enum NotificationType {
  ANNOUNCEMENT    // 公告
  SYSTEM_UPDATE   // 系统更新
  MAINTENANCE     // 维护通知
  PROMOTION       // 促销活动
}

enum NotificationPriority {
  LOW
  NORMAL
  HIGH
  URGENT
}

enum NotificationStatus {
  DRAFT       // 草稿
  PUBLISHED   // 已发布
  EXPIRED     // 已过期
  ARCHIVED    // 已归档
}

enum TargetType {
  ALL_USERS       // 所有用户
  NEW_USERS       // 新用户
  DATE_RANGE      // 按注册日期范围
  ROLE_BASED      // 按用户角色
}

enum OrderStatus {
  PENDING
  COMPLETED
  FAILED
  CANCELLED
}

// 邀请码表
model ReferralCode {
  id        String   @id @default(cuid())
  userId    String
  code      String   @unique
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  expiresAt DateTime?

  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([code])
  @@map("referral_codes")
}

// 邀请关系表
model ReferralRelation {
  id               String    @id @default(cuid())
  inviterId        String
  inviteeId        String    @unique
  referralCode     String
  createdAt        DateTime  @default(now())
  firstPaymentAt   DateTime?
  rewardGranted    Boolean   @default(false)
  rewardGrantedAt  DateTime?

  inviter          User      @relation("InviterRelations", fields: [inviterId], references: [id], onDelete: Cascade)
  invitee          User      @relation("InviteeRelation", fields: [inviteeId], references: [id], onDelete: Cascade)
  rewards          ReferralReward[]

  @@index([inviterId])
  @@index([referralCode])
  @@map("referral_relations")
}

// 邀请奖励表
model ReferralReward {
  id           String    @id @default(cuid())
  inviterId    String
  inviteeId    String
  relationId   String
  rewardAmount Int
  rewardType   String    @default("REFERRAL_BONUS")
  orderId      String?
  codeUsageId  String?
  status       String    @default("PENDING")
  createdAt    DateTime  @default(now())
  processedAt  DateTime?

  inviter      User             @relation("InviterRewards", fields: [inviterId], references: [id], onDelete: Cascade)
  invitee      User             @relation("InviteeRewards", fields: [inviteeId], references: [id], onDelete: Cascade)
  relation     ReferralRelation @relation(fields: [relationId], references: [id], onDelete: Cascade)
  order        Order?           @relation(fields: [orderId], references: [id], onDelete: SetNull)
  codeUsage    CodeUsage?       @relation(fields: [codeUsageId], references: [id], onDelete: SetNull)

  @@index([inviterId])
  @@index([inviteeId])
  @@index([relationId])
  @@index([orderId])
  @@index([codeUsageId])
  @@map("referral_rewards")
}
