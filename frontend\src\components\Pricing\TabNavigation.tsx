// 文件路径: frontend/src/components/Pricing/TabNavigation.tsx
// (File Path: frontend/src/components/Pricing/TabNavigation.tsx)
import React from 'react';
import { motion } from 'framer-motion';

interface TabNavigationProps {
  tabs: string[];
  activeTab: string;
  onTabChange: (tab: string) => void;
}

const TabNavigation: React.FC<TabNavigationProps> = ({
  tabs,
  activeTab,
  onTabChange,
}) => {
  return (
    <div className="border-b border-gray-200 mb-8">
      <div className="flex space-x-8 justify-center"> {/* Centered tabs */}
        {tabs.map((tab) => (
          <button
            key={tab}
            onClick={() => onTabChange(tab)}
            className={`relative py-4 px-2 text-lg font-medium transition-colors ${ // Increased font size and padding
              activeTab === tab
                ? 'text-blue-600'
                : 'text-gray-500 hover:text-gray-800'
            }`}
          >
            {tab}
            {activeTab === tab && (
              <motion.div
                layoutId="activePricingTab" // Unique layoutId
                className="absolute bottom-0 left-0 right-0 h-1 bg-blue-600 rounded-t-sm" // Thicker underline
                initial={false}
                transition={{ type: 'spring', stiffness: 400, damping: 30 }}
              />
            )}
          </button>
        ))}
      </div>
    </div>
  );
};

export default TabNavigation;
