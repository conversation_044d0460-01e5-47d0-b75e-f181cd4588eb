import React, { useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import AIInterviewHeader, { type TabType } from '../components/interview/AIInterviewHeader';
import VideoPlayerSection from '../components/interview/VideoPlayerSection';
import InterviewConfigForm from '../components/interview/InterviewConfigForm';
import InterviewRecordsPage from '../components/interview/InterviewRecordsPage';
import Sidebar from '../components/Sidebar';
import ToastContainer from '../components/ui/ToastContainer';
import useDocumentTitle from '../hooks/useDocumentTitle';
import { useBalance } from '../hooks/useBalance';
import { useMockInterviewStore } from '../stores/mockInterviewStore';

const MockInterviewConfigPage: React.FC = () => {
  // 设置页面标题
  useDocumentTitle('AI模拟面试');

  // 初始化余额管理
  useBalance();

  const [searchParams, setSearchParams] = useSearchParams();
  const [activeTab, setActiveTab] = useState<TabType>(
    (searchParams.get('tab') as TabType) || 'interview'
  );

  const handleTabChange = (tab: TabType) => {
    setActiveTab(tab);
    setSearchParams({ tab });
  };

  const renderContent = () => {
    if (activeTab === 'records') {
      return <InterviewRecordsPage mode="mock" />;
    }

    return (
      <div className="h-full flex p-4 gap-4">
        <VideoPlayerSection mode="mock" />
        <InterviewConfigForm mode="mock" />
      </div>
    );
  };

  return (
    <>
      {/* Toast容器 */}
      <ToastContainer />

      <div className="flex h-screen overflow-hidden bg-slate-50 dark:bg-gray-900 transition-colors">
        <Sidebar />
        <div className="flex-1 flex flex-col overflow-hidden">
          {/* 主内容区域 - 不包含Header */}
          <main className="flex-1 overflow-hidden">
            <div className="h-full flex flex-col">
              <AIInterviewHeader
                activeTab={activeTab}
                onTabChange={handleTabChange}
                mode="mock"
                title="AI模拟面试"
              />
              <div className="flex-1 overflow-hidden">
                {renderContent()}
              </div>
            </div>
          </main>
        </div>
      </div>
    </>
  );
};

export default MockInterviewConfigPage;
