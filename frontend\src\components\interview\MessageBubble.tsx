import React, { useMemo, useCallback, useRef } from 'react';
import { Message } from '../../hooks/useInterviewSession';
import { useTypewriter } from '../../hooks/useTypewriter';
import { PerformanceMonitor } from '../../utils/BubbleManager';

interface MessageBubbleProps {
  message: Message;
  mode?: 'live' | 'mock';
}

// 🔥 性能监控：渲染计数器
const renderCounters = new Map<string, number>();

const MessageBubbleComponent: React.FC<MessageBubbleProps> = ({ message, mode = 'live' }) => {
  const renderCountRef = useRef(0);
  renderCountRef.current += 1;

  // 🔥 性能监控：使用全局性能监控器
  const performanceMonitor = PerformanceMonitor.getInstance();
  performanceMonitor.recordRender('MessageBubble');

  // 🔥 性能监控：记录渲染次数
  const currentCount = renderCounters.get(message.id) || 0;
  renderCounters.set(message.id, currentCount + 1);

  // 🔧 优化后的调试日志：减少频繁输出
  if (renderCountRef.current % 10 === 1) { // 每10次渲染输出一次
    console.log('🎯 MessageBubble rendering:', {
      messageId: message.id,
      messageType: message.type,
      renderCount: renderCountRef.current,
      totalRenders: renderCounters.get(message.id),
      contentLength: message.content.length,
      content: message.content.substring(0, 30) + '...',
      timestamp: new Date(message.timestamp).toLocaleTimeString()
    });
  }

  // 🔥 智能打字机效果：只对面试官消息使用
  const shouldUseTypewriter = message.type === 'interviewer';
  // 🔥 使用useMemo优化typewriter配置
  const typewriterConfig = useMemo(() => ({
    enabled: shouldUseTypewriter,
    speed: 50 // 50ms每字符，类似ChatGPT的速度
  }), [shouldUseTypewriter]);

  const { displayedText, isComplete } = useTypewriter(message.content, typewriterConfig);

  // 🔥 使用useCallback优化样式计算函数
  const getBubbleStyle = useCallback(() => {
    const baseStyle = (() => {
      switch (message.type) {
        case 'interviewer':
          return 'bg-blue-50 border-blue-100 text-blue-800';
        case 'ai-suggestion':
          return 'bg-green-50 border-green-100 text-green-800';
        default:
          return 'bg-gray-50 border-gray-100 text-gray-800';
      }
    })();

    // 简化样式逻辑，移除不存在的isStreaming属性
    return baseStyle;
  }, [message.type]);

  // 🔥 使用useCallback优化标题计算函数，根据面试模式显示不同标题
  const getMessageTitle = useCallback(() => {
    switch (message.type) {
      case 'interviewer':
        // AI正式面试和AI模拟面试中，蓝色气泡都是面试官/AI提问
        return mode === 'live' ? '面试官' : 'AI面试官';
      case 'ai-suggestion':
        // AI正式面试：绿色气泡是AI建议；AI模拟面试：绿色气泡是用户回答
        return mode === 'live' ? '面试君建议' : '用户回答';
      default:
        return '';
    }
  }, [message.type, mode]);

  // 🔥 使用useCallback优化显示文本计算
  const getDisplayText = useCallback(() => {
    if (message.type === 'interviewer') {
      // 对面试官消息使用打字机效果
      return shouldUseTypewriter ? displayedText : message.content;
    }
    return message.content;
  }, [message.type, message.content, shouldUseTypewriter, displayedText]);

  // 🔥 使用useCallback优化状态指示器
  const getStatusIndicator = useCallback(() => {
    if (message.type === 'interviewer' && !isComplete && shouldUseTypewriter) {
      return <span className="animate-pulse">|</span>;
    }
    return null;
  }, [message.type, isComplete, shouldUseTypewriter]);

  return (
    <div className={`p-4 rounded-lg border ${getBubbleStyle()}`}>
      <div className="flex justify-between items-center mb-2">
        <div className="flex items-center gap-2">
          <span className="font-medium">{getMessageTitle()}</span>
          {getStatusIndicator()}
        </div>
        <span className="text-xs text-gray-500">
          {new Date(message.timestamp).toLocaleTimeString()}
        </span>
      </div>
      <p className="whitespace-pre-wrap">
        {getDisplayText()}
      </p>
    </div>
  );
};

// 🔥 增强版React.memo比较函数 - 解决频繁重渲染问题
const MessageBubble = React.memo(MessageBubbleComponent, (prevProps, nextProps) => {
  const prev = prevProps.message;
  const next = nextProps.message;

  // 🔥 第一层：快速比较基础属性
  if (prev.id !== next.id) {
    console.log('🔄 MessageBubble re-render: ID changed', { prev: prev.id, next: next.id });
    return false; // 需要重渲染
  }

  if (prev.type !== next.type) {
    console.log('🔄 MessageBubble re-render: Type changed', { prev: prev.type, next: next.type });
    return false; // 需要重渲染
  }

  // 🔥 第二层：内容比较（最常变化的属性）
  if (prev.content !== next.content) {
    console.log('🔄 MessageBubble re-render: Content changed', {
      messageId: next.id,
      prevLength: prev.content.length,
      nextLength: next.content.length,
      contentDiff: next.content.length - prev.content.length
    });
    return false; // 需要重渲染
  }

  // 🔥 第三层：时间戳比较（对于流式消息很重要）
  if (prev.timestamp !== next.timestamp) {
    const timeDiff = next.timestamp - prev.timestamp;
    // 只有时间差超过100ms才认为是有意义的更新
    if (timeDiff > 100) {
      console.log('🔄 MessageBubble re-render: Significant timestamp change', {
        messageId: next.id,
        timeDiff,
        prev: new Date(prev.timestamp).toLocaleTimeString(),
        next: new Date(next.timestamp).toLocaleTimeString()
      });
      return false; // 需要重渲染
    }
  }

  // 🔥 第四层：深度对象比较（防止引用变化导致的重渲染）
  try {
    const prevSerialized = JSON.stringify(prev);
    const nextSerialized = JSON.stringify(next);

    if (prevSerialized !== nextSerialized) {
      console.log('🔄 MessageBubble re-render: Deep object change detected', {
        messageId: next.id,
        prevSerialized: prevSerialized.substring(0, 100) + '...',
        nextSerialized: nextSerialized.substring(0, 100) + '...'
      });
      return false; // 需要重渲染
    }
  } catch (error) {
    console.warn('⚠️ MessageBubble memo: JSON serialization failed, allowing re-render', error);
    return false; // 安全起见，允许重渲染
  }

  // 🔥 所有比较都通过，阻止重渲染
  console.log('✅ MessageBubble memo: Prevented unnecessary re-render for:', next.id);
  return true; // 阻止重渲染
});

// 设置displayName便于调试
MessageBubble.displayName = 'MessageBubble';

export default MessageBubble;
