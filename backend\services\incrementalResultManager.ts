import {
  RecognitionResult,
  IncrementalUpdate,
  MergedRecognitionResult
} from '../types/asrTypes';

export interface ResultQueueItem {
  id: string;
  result: RecognitionResult;
  timestamp: number;
  processed: boolean;
  merged: boolean;
}

export interface MergeConfig {
  maxQueueSize: number;
  mergeTimeWindow: number;      // 合并时间窗口(ms)
  confidenceThreshold: number;  // 置信度阈值
  similarityThreshold: number;  // 相似度阈值
  maxMergeAttempts: number;     // 最大合并尝试次数
}

export const DEFAULT_MERGE_CONFIG: MergeConfig = {
  maxQueueSize: 50,
  mergeTimeWindow: 5000,        // 5秒
  confidenceThreshold: 0.6,     // 60%
  similarityThreshold: 0.8,     // 80%
  maxMergeAttempts: 3
};

/**
 * 增量识别结果管理器
 * 管理多个音频片段的识别结果，支持增量更新和智能合并
 */
export class IncrementalResultManager {
  private resultQueue: ResultQueueItem[] = [];
  private config: MergeConfig;
  private mergeHistory: Map<string, MergedRecognitionResult> = new Map();

  constructor(config: Partial<MergeConfig> = {}) {
    this.config = { ...DEFAULT_MERGE_CONFIG, ...config };
    console.log('IncrementalResultManager initialized with config:', this.config);
  }

  /**
   * 添加识别结果到队列
   */
  addResult(result: RecognitionResult): string {
    const queueItem: ResultQueueItem = {
      id: `queue-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      result,
      timestamp: Date.now(),
      processed: false,
      merged: false
    };

    this.resultQueue.push(queueItem);
    
    // 保持队列大小在限制内
    if (this.resultQueue.length > this.config.maxQueueSize) {
      const removed = this.resultQueue.shift();
      console.log(`Result queue full, removed oldest item: ${removed?.id}`);
    }

    console.log(`Added result to queue: ${queueItem.id}`, {
      text: result.text.substring(0, 50) + '...',
      confidence: result.confidence,
      service: result.service,
      isPartial: result.isPartial
    });

    return queueItem.id;
  }

  /**
   * 处理增量更新
   */
  processIncrementalUpdate(update: IncrementalUpdate): void {
    console.log(`Processing incremental update: ${update.type}`, {
      text: update.text.substring(0, 50) + '...',
      confidence: update.confidence,
      segmentId: update.segmentId
    });

    // 查找相关的结果项
    const relatedItems = this.resultQueue.filter(item => 
      item.result.segmentId === update.segmentId ||
      this.calculateTextSimilarity(item.result.text, update.text) > this.config.similarityThreshold
    );

    if (relatedItems.length > 0) {
      // 更新相关结果
      relatedItems.forEach(item => {
        this.updateResultItem(item, update);
      });
    } else {
      // 创建新的结果项
      const newResult: RecognitionResult = {
        text: update.text,
        confidence: update.confidence,
        timestamp: update.timestamp,
        service: 'incremental',
        isPartial: update.type === 'partial',
        segmentId: update.segmentId
      };
      this.addResult(newResult);
    }
  }

  /**
   * 更新结果项
   */
  private updateResultItem(item: ResultQueueItem, update: IncrementalUpdate): void {
    switch (update.type) {
      case 'partial':
        // 部分更新，如果置信度更高则替换
        if (update.confidence > item.result.confidence) {
          item.result.text = update.text;
          item.result.confidence = update.confidence;
          item.result.isPartial = true;
        }
        break;
        
      case 'final':
        // 最终更新，直接替换
        item.result.text = update.text;
        item.result.confidence = update.confidence;
        item.result.isPartial = false;
        item.processed = true;
        break;
        
      case 'correction':
        // 纠正更新，应用文本修正
        item.result.text = this.applyCorrection(item.result.text, update);
        item.result.confidence = Math.max(item.result.confidence, update.confidence);
        break;
    }
    
    item.timestamp = Date.now();
    console.log(`Updated result item: ${item.id}`, {
      updateType: update.type,
      newText: item.result.text.substring(0, 50) + '...',
      newConfidence: item.result.confidence
    });
  }

  /**
   * 应用文本纠正
   */
  private applyCorrection(originalText: string, correction: IncrementalUpdate): string {
    const { start, end } = correction.position;
    
    if (start >= 0 && end <= originalText.length && start <= end) {
      return originalText.substring(0, start) + correction.text + originalText.substring(end);
    }
    
    // 如果位置信息无效，返回原文本
    console.warn('Invalid correction position, keeping original text');
    return originalText;
  }

  /**
   * 获取可合并的结果
   */
  getMergeableResults(): ResultQueueItem[] {
    const now = Date.now();
    const timeThreshold = now - this.config.mergeTimeWindow;
    
    return this.resultQueue.filter(item => 
      !item.merged && 
      item.timestamp >= timeThreshold &&
      item.result.confidence >= this.config.confidenceThreshold
    );
  }

  /**
   * 合并结果
   */
  mergeResults(items: ResultQueueItem[]): MergedRecognitionResult | null {
    if (items.length === 0) {
      return null;
    }

    if (items.length === 1) {
      const item = items[0];
      item.merged = true;
      
      return {
        text: item.result.text,
        confidence: item.result.confidence,
        segments: [item.result],
        mergeStrategy: 'single',
        finalConfidence: item.result.confidence,
        processingTime: Date.now() - item.timestamp
      };
    }

    // 按时间戳排序
    const sortedItems = items.sort((a, b) => a.result.timestamp - b.result.timestamp);
    
    // 去重处理
    const deduplicatedItems = this.removeDuplicates(sortedItems);
    
    // 时间对齐
    const alignedItems = this.alignByTimestamp(deduplicatedItems);
    
    // 智能合并文本
    const mergedText = this.mergeTexts(alignedItems.map(item => item.result));
    
    // 计算综合置信度
    const finalConfidence = this.calculateCombinedConfidence(alignedItems.map(item => item.result));
    
    // 标记为已合并
    alignedItems.forEach(item => {
      item.merged = true;
    });

    const mergedResult: MergedRecognitionResult = {
      text: mergedText,
      confidence: finalConfidence,
      segments: alignedItems.map(item => item.result),
      mergeStrategy: this.determineMergeStrategy(alignedItems),
      finalConfidence,
      processingTime: Date.now() - sortedItems[0].timestamp
    };

    console.log(`Merged ${alignedItems.length} results:`, {
      text: mergedText.substring(0, 100) + '...',
      confidence: finalConfidence,
      strategy: mergedResult.mergeStrategy
    });

    return mergedResult;
  }

  /**
   * 去除重复结果
   */
  private removeDuplicates(items: ResultQueueItem[]): ResultQueueItem[] {
    const result: ResultQueueItem[] = [];
    
    for (const item of items) {
      const isDuplicate = result.some(existing => 
        this.calculateTextSimilarity(item.result.text, existing.result.text) > this.config.similarityThreshold
      );
      
      if (!isDuplicate) {
        result.push(item);
      } else {
        // 如果是重复，选择置信度更高的
        const existingIndex = result.findIndex(existing => 
          this.calculateTextSimilarity(item.result.text, existing.result.text) > this.config.similarityThreshold
        );
        
        if (existingIndex !== -1 && item.result.confidence > result[existingIndex].result.confidence) {
          result[existingIndex] = item;
        }
      }
    }
    
    console.log(`Deduplication: ${items.length} -> ${result.length} items`);
    return result;
  }

  /**
   * 时间戳对齐
   */
  private alignByTimestamp(items: ResultQueueItem[]): ResultQueueItem[] {
    // 简单的时间戳对齐，按时间顺序排序
    return items.sort((a, b) => a.result.timestamp - b.result.timestamp);
  }

  /**
   * 合并文本
   */
  private mergeTexts(results: RecognitionResult[]): string {
    if (results.length === 0) return '';
    if (results.length === 1) return results[0].text;

    let mergedText = '';
    
    for (let i = 0; i < results.length; i++) {
      const current = results[i];
      const previous = results[i - 1];
      
      if (i === 0) {
        mergedText = current.text;
        continue;
      }
      
      // 智能连接逻辑
      const connector = this.determineConnector(previous.text, current.text);
      mergedText += connector + current.text;
    }
    
    return this.postProcessText(mergedText);
  }

  /**
   * 确定连接符
   */
  private determineConnector(previousText: string, currentText: string): string {
    // 如果前一个文本以标点符号结尾，不需要额外连接符
    if (/[。！？，、；：]$/.test(previousText.trim())) {
      return '';
    }
    
    // 如果当前文本以标点符号开头，不需要额外连接符
    if (/^[。！？，、；：]/.test(currentText.trim())) {
      return '';
    }
    
    // 检查是否需要空格（英文）
    const isEnglish1 = /[a-zA-Z]$/.test(previousText.trim());
    const isEnglish2 = /^[a-zA-Z]/.test(currentText.trim());
    
    if (isEnglish1 && isEnglish2) {
      return ' ';
    }
    
    // 中文通常不需要连接符
    return '';
  }

  /**
   * 后处理文本
   */
  private postProcessText(text: string): string {
    let result = text;
    
    // 去除多余的空格
    result = result.replace(/\s+/g, ' ').trim();
    
    // 修正标点符号
    result = result.replace(/\s+([。！？，、；：])/g, '$1');
    
    // 首字母大写（英文）
    result = result.replace(/^[a-z]/, match => match.toUpperCase());
    
    // 去除重复的标点符号
    result = result.replace(/([。！？])\1+/g, '$1');
    
    return result;
  }

  /**
   * 计算综合置信度
   */
  private calculateCombinedConfidence(results: RecognitionResult[]): number {
    if (results.length === 0) return 0;
    
    // 加权平均置信度
    let totalWeight = 0;
    let weightedSum = 0;
    
    for (const result of results) {
      const weight = result.text.length; // 以文本长度作为权重
      weightedSum += result.confidence * weight;
      totalWeight += weight;
    }
    
    return totalWeight > 0 ? weightedSum / totalWeight : 0;
  }

  /**
   * 确定合并策略
   */
  private determineMergeStrategy(items: ResultQueueItem[]): string {
    if (items.length <= 1) return 'single';
    
    const services = new Set(items.map(item => item.result.service));
    const hasPartial = items.some(item => item.result.isPartial);
    
    if (services.size === 1) {
      return hasPartial ? 'single-service-incremental' : 'single-service-batch';
    } else {
      return hasPartial ? 'multi-service-incremental' : 'multi-service-batch';
    }
  }

  /**
   * 计算文本相似度
   */
  private calculateTextSimilarity(text1: string, text2: string): number {
    const distance = this.levenshteinDistance(text1, text2);
    const maxLength = Math.max(text1.length, text2.length);
    
    if (maxLength === 0) return 1;
    return 1 - (distance / maxLength);
  }

  /**
   * 计算编辑距离
   */
  private levenshteinDistance(str1: string, str2: string): number {
    const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));
    
    for (let i = 0; i <= str1.length; i++) matrix[0][i] = i;
    for (let j = 0; j <= str2.length; j++) matrix[j][0] = j;
    
    for (let j = 1; j <= str2.length; j++) {
      for (let i = 1; i <= str1.length; i++) {
        const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
        matrix[j][i] = Math.min(
          matrix[j][i - 1] + 1,     // deletion
          matrix[j - 1][i] + 1,     // insertion
          matrix[j - 1][i - 1] + indicator // substitution
        );
      }
    }
    
    return matrix[str2.length][str1.length];
  }

  /**
   * 获取队列统计信息
   */
  getQueueStats(): {
    totalItems: number;
    processedItems: number;
    mergedItems: number;
    averageConfidence: number;
    serviceDistribution: Record<string, number>;
  } {
    const totalItems = this.resultQueue.length;
    const processedItems = this.resultQueue.filter(item => item.processed).length;
    const mergedItems = this.resultQueue.filter(item => item.merged).length;
    
    const confidences = this.resultQueue.map(item => item.result.confidence);
    const averageConfidence = confidences.length > 0 ? 
      confidences.reduce((sum, conf) => sum + conf, 0) / confidences.length : 0;
    
    const serviceDistribution: Record<string, number> = {};
    this.resultQueue.forEach(item => {
      const service = item.result.service;
      serviceDistribution[service] = (serviceDistribution[service] || 0) + 1;
    });
    
    return {
      totalItems,
      processedItems,
      mergedItems,
      averageConfidence,
      serviceDistribution
    };
  }

  /**
   * 清理已处理的结果
   */
  cleanup(): void {
    const beforeCount = this.resultQueue.length;
    this.resultQueue = this.resultQueue.filter(item => !item.merged || !item.processed);
    const afterCount = this.resultQueue.length;
    
    if (beforeCount !== afterCount) {
      console.log(`Cleaned up ${beforeCount - afterCount} processed results`);
    }
  }

  /**
   * 重置管理器
   */
  reset(): void {
    this.resultQueue = [];
    this.mergeHistory.clear();
    console.log('IncrementalResultManager reset');
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<MergeConfig>): void {
    this.config = { ...this.config, ...newConfig };
    console.log('IncrementalResultManager config updated:', this.config);
  }
}
