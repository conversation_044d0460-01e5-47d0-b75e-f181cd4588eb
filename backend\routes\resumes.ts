import express, { Request, Response } from 'express';
import { authenticateToken, AuthenticatedRequest } from '../middleware/auth';
import prisma from '../lib/prisma';
import { validateFile, generateSafeFileName, fileExists } from '../utils/fileValidation';
import path from 'path';
import fs from 'fs';

const router = express.Router();

/**
 * 创建简历记录
 */
router.post('/', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user!.userId;
    const { fileName, filePath, fileType, fileSize, jobTitle } = req.body;

    if (!fileName || !filePath) {
      return res.status(400).json({ 
        success: false,
        message: 'fileName and filePath are required' 
      });
    }

    // 验证文件是否存在
    if (!fileExists(filePath)) {
      return res.status(400).json({ 
        success: false,
        message: 'File does not exist at the specified path' 
      });
    }

    // 创建简历记录
    const resume = await prisma.resume.create({
      data: {
        userId: userId,
        fileName: fileName,
        filePath: filePath,
        fileType: fileType || 'application/pdf',
        fileSize: fileSize || 0,
        jobTitle: jobTitle || '',
        uploadTimestamp: new Date(),
      },
    });

    return res.status(201).json({
      success: true,
      message: 'Resume created successfully',
      data: { resume }
    });

  } catch (error: any) {
    console.error('Create resume error:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to create resume record'
    });
  }
});

/**
 * 获取用户简历列表
 */
router.get('/', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user!.userId;

    const resumes = await prisma.resume.findMany({
      where: { userId: userId },
      orderBy: { uploadTimestamp: 'desc' },
      select: {
        id: true,
        fileName: true,
        filePath: true,
        fileType: true,
        fileSize: true,
        jobTitle: true,
        uploadTimestamp: true,
      }
    });

    // 转换字段名以保持API兼容性
    const formattedResumes = resumes.map(resume => ({
      id: resume.id,
      fileName: resume.fileName,
      filePath: resume.filePath,
      fileType: resume.fileType,
      fileSize: resume.fileSize,
      jobTitle: resume.jobTitle,
      uploadedAt: resume.uploadTimestamp, // 转换字段名
    }));

    return res.json({
      success: true,
      data: { resumes: formattedResumes }
    });

  } catch (error: any) {
    console.error('Get resumes error:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to retrieve resumes'
    });
  }
});

/**
 * 获取单个简历详情
 */
router.get('/:id', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user!.userId;
    const { id } = req.params;

    const resume = await prisma.resume.findFirst({
      where: {
        id: id,
        userId: userId, // 确保用户只能访问自己的简历
      },
    });

    if (!resume) {
      return res.status(404).json({
        success: false,
        message: 'Resume not found'
      });
    }

    return res.json({
      success: true,
      data: { resume }
    });

  } catch (error: any) {
    console.error('Get resume error:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to retrieve resume'
    });
  }
});

/**
 * 更新简历信息
 */
router.put('/:id', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user!.userId;
    const { id } = req.params;
    const { jobTitle, fileName } = req.body;

    // 验证简历是否属于当前用户
    const existingResume = await prisma.resume.findFirst({
      where: {
        id: id,
        userId: userId,
      },
    });

    if (!existingResume) {
      return res.status(404).json({
        success: false,
        message: 'Resume not found'
      });
    }

    // 更新简历信息
    const updatedResume = await prisma.resume.update({
      where: { id: id },
      data: {
        ...(jobTitle !== undefined && { jobTitle }),
        ...(fileName !== undefined && { fileName }),
      },
    });

    return res.json({
      success: true,
      message: 'Resume updated successfully',
      data: { resume: updatedResume }
    });

  } catch (error: any) {
    console.error('Update resume error:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to update resume'
    });
  }
});

/**
 * 删除简历
 */
router.delete('/:id', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user!.userId;
    const { id } = req.params;

    // 验证简历是否属于当前用户
    const existingResume = await prisma.resume.findFirst({
      where: {
        id: id,
        userId: userId,
      },
    });

    if (!existingResume) {
      return res.status(404).json({
        success: false,
        message: 'Resume not found'
      });
    }

    // 删除文件（如果存在）
    try {
      if (fs.existsSync(existingResume.filePath)) {
        fs.unlinkSync(existingResume.filePath);
      }
    } catch (fileError) {
      console.warn('Failed to delete resume file:', fileError);
      // 继续删除数据库记录，即使文件删除失败
    }

    // 删除数据库记录
    await prisma.resume.delete({
      where: { id: id },
    });

    return res.json({
      success: true,
      message: 'Resume deleted successfully'
    });

  } catch (error: any) {
    console.error('Delete resume error:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to delete resume'
    });
  }
});

export default router;
