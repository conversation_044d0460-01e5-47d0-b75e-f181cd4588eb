好的，我们来一步步完成这些功能。这个清单会指导你在 `local-mianshijun` 项目中进行操作。我会尽量说得简单直白。

请注意：这是一个比较复杂的任务，涉及到前端和后端以及数据库的修改。我会把步骤分解，但每一步的具体代码实现还是需要一些编程理解。如果你在某个具体代码文件修改上遇到困难，可以随时追问。

------

## 📝 操作清单 (Action Checklist)

### 第一部分：支付页面前端调整 (Part 1: Frontend Adjustments for Payment Page)

这部分我们会在现有的“定价页面”或创建一个新的支付流程页面，加入支付宝支付选项，并为微信支付预留位置。

1. **定位或创建支付页面/组件 (Locate or Create Payment Page/Component):**

   - **操作 (Action):** 我们需要在前端项目中找到显示价格套餐或者引导用户支付的地方。这可能是 `frontend/src/pages/PricingPage.tsx`。如果想创建一个专门的支付确认页面，可以在 `frontend/src/pages/`下新建一个文件，比如 `PaymentConfirmationPage.tsx`。
   - **英文 (English):** We need to find where pricing plans are displayed or where users are guided to payment in the frontend project. This might be `frontend/src/pages/PricingPage.tsx`. If you want a dedicated payment confirmation page, create a new file under `frontend/src/pages/`, for example, `PaymentConfirmationPage.tsx`.

2. **添加支付宝支付按钮 (Add Alipay Button):**

   - 操作 (Action):

      在你选定的支付页面或组件中，添加一个“支付宝支付”按钮。这个按钮点击后，理论上会开始支付流程。

     - 参考文件：可以查看 `frontend/src/components/ui/Button.tsx` 如何创建一个按钮。
     - 在 `frontend/src/pages/PricingPage.tsx` 或你选择的支付相关组件中，加入类似 `<Button>支付宝支付</Button>` 的代码。

   - 英文 (English):

      In your chosen payment page or component, add an "Alipay Pay" button. Clicking this button will theoretically start the payment process.

     - Reference file: You can check `frontend/src/components/ui/Button.tsx` for how to create a button.
     - In `frontend/src/pages/PricingPage.tsx` or your chosen payment-related component, add code similar to `<Button>Alipay Pay</Button>`.

3. **添加微信支付占位提示 (Add WeChat Pay Placeholder):**

   - 操作 (Action):

      在同样的位置，添加“微信支付”的文字提示，说明该功能暂未开放。

     - 例如，可以加上 `<p>微信支付 (维护中，敬请期待)</p>`。

   - 英文 (English):

      In the same location, add text for "WeChat Pay" indicating that this feature is not yet available.

     - For example, you can add `<p>WeChat Pay (Under Maintenance, Coming Soon)</p>`.

------

### 第二部分：用户余额/次数管理表结构与API基础 (Part 2: User Balance/Credits Management - Database Structure and Basic API)

这部分我们会在数据库中创建新的表来存储用户的余额或服务次数，并创建基础的后端API来查询这些信息。

1. **定义数据库表结构 (Define Database Table Structure):**

   - 操作 (Action):

      打开 

     ```
     backend/prisma/schema.prisma
     ```

      文件 (如果不存在，你可能需要根据项目结构找到类似的核心数据库结构定义文件，或者根据 

     ```
     backend/prisma/migrations/.../migration.sql
     ```

      的风格来理解如何定义)。

     - 在 `schema.prisma` 中，添加一个新的 `model` (表) 来存储用户余额/次数。例如，可以叫做 `UserCredit`。

     - 这个表至少需要包含：

       - `id`: 主键 (Primary Key)
       - `userId`: 用户ID，关联到 `User` 表 (User ID, linked to the `User` table)
       - `balance`: 余额 (DECIMAL or FLOAT type) 或 `credits`: 次数 (INTEGER type)
       - `createdAt`: 创建时间 (Creation timestamp)
       - `updatedAt`: 更新时间 (Update timestamp)

     - 示例结构 (Example structure for 

       ```
       UserCredit
       ```

        in 

       ```
       schema.prisma
       ```

       ):

       代码段

       ```
       model UserCredit {
         id        String   @id @default(cuid())
         userId    String   @unique // Or String if your User id is a String (like UUID)
         user      User     @relation(fields: [userId], references: [id])
         credits   Int      @default(0) // Number of available credits/interviews
         // or balance Decimal @default(0.00) // If you prefer a monetary balance
         createdAt DateTime @default(now())
         updatedAt DateTime @updatedAt
       }
       ```

     - 重要 (Important):

        确保 

       ```
       User
       ```

        model 中添加反向关系 (Ensure the 

       ```
       User
       ```

        model has a reverse relation):

       代码段

       ```
       model User {
         // ... other fields
         credit UserCredit? // Add this line
       }
       ```

   - 英文 (English):

      Open the 

     ```
     backend/prisma/schema.prisma
     ```

      file.

     - In `schema.prisma`, add a new `model` (table) to store user balance/credits. For example, call it `UserCredit`.
     - This table should at least include: `id` (Primary Key), `userId` (linked to `User` table), `balance` (DECIMAL or FLOAT) or `credits` (INTEGER), `createdAt`, `updatedAt`.
     - See the example structure above.
     - **Important:** Ensure you add a reverse relation in the `User` model.

2. **生成并应用数据库迁移 (Generate and Apply Database Migration):**

   - 操作 (Action):

      保存 

     ```
     schema.prisma
     ```

      文件后，在 

     ```
     backend
     ```

      文件夹的终端中运行以下命令：

     Bash

     ```
     npx prisma migrate dev --name addUserCreditTable
     ```

     这个命令会根据你的 

     ```
     schema.prisma
     ```

      修改，生成新的 SQL 迁移文件 (在 

     ```
     backend/prisma/migrations/
     ```

      下) 并应用到数据库。

   - 英文 (English):

      After saving the 

     ```
     schema.prisma
     ```

      file, run the following command in the terminal within the 

     ```
     backend
     ```

      folder:

     Bash

     ```
     npx prisma migrate dev --name addUserCreditTable
     ```

     This command will generate a new SQL migration file (under 

     ```
     backend/prisma/migrations/
     ```

     ) based on your 

     ```
     schema.prisma
     ```

      changes and apply it to the database.

3. **创建后端API - 获取用户余额/次数 (Create Backend API - Get User Balance/Credits):**

   - 操作 (Action):

     - 在 `backend/users/index.ts` (或者创建一个新的 `backend/credits/index.ts`) 文件中，添加一个新的路由来获取用户的余额/次数。

     - 这个路由应该需要用户认证。

     - 它会查询 `UserCredit` 表获取当前登录用户的数据。

     - 示例 (Conceptual code in 

       ```
       backend/users/index.ts
       ```

        or a new file):

       TypeScript

       ```
       // Presuming you have an Express router instance, e.g., 'router'
       // And a prisma client instance, e.g., 'prisma' from 'backend/lib/prisma.ts'
       // And an auth middleware, e.g., 'isAuthenticated'
       
       router.get('/me/credits', isAuthenticated, async (req, res) => {
         try {
           // Assuming req.user.id contains the authenticated user's ID
           const userId = req.user.id;
           let userCredit = await prisma.userCredit.findUnique({
             where: { userId: userId },
           });
       
           if (!userCredit) {
             // Optionally create a credit record if one doesn't exist
             userCredit = await prisma.userCredit.create({
               data: {
                 userId: userId,
                 credits: 0, // Default credits
               }
             });
           }
           res.json(userCredit);
         } catch (error) {
           console.error("Failed to get user credits:", error);
           res.status(500).json({ message: "Error fetching user credits" });
         }
       });
       ```

     - **注册路由 (Register the route):** 如果创建了新文件 (如 `credits/index.ts`), 确保在 `backend/server.ts` 中引入并使用它，就像其他路由 (如 `auth`, `resumes`) 一样。

   - 英文 (English):

     - In `backend/users/index.ts` (or create a new `backend/credits/index.ts`), add a new route to get the user's balance/credits.
     - This route should require user authentication.
     - It will query the `UserCredit` table for the current logged-in user's data.
     - See the conceptual code example above.
     - **Register the route:** If you created a new file (e.g., `credits/index.ts`), ensure it's imported and used in `backend/server.ts`, similar to other routes (like `auth`, `resumes`).

------

### 第三部分：支付宝支付流程前端引导与后端订单创建 (Part 3: Alipay Payment Flow - Frontend Guidance and Backend Order Creation)

这部分我们会在用户点击“支付宝支付”后，前端请求后端创建一个订单记录，后端记录这个订单，并返回信息给前端，前端再“引导”用户去支付（现阶段可能只是显示一个提示，真正的跳转到支付宝需要后续集成SDK）。

1. **定义订单数据库表结构 (Define Order Database Table Structure):**

   - 操作 (Action):

      再次打开 

     ```
     backend/prisma/schema.prisma
     ```

     。

     - 添加一个新的 `model` (表) 来存储订单信息，例如 `Order`。

     - 这个表至少需要包含：

       - `id`: 主键 (Primary Key)
       - `userId`: 用户ID (User ID)
       - `amount`: 订单金额 (Order amount - DECIMAL)
       - `status`: 订单状态 (e.g., PENDING, COMPLETED, FAILED - STRING or ENUM)
       - `paymentMethod`: 支付方式 (e.g., ALIPAY - STRING)
       - `itemId`: 购买的商品/套餐ID (可选，Optional - STRING or INT)
       - `itemDescription`: 商品描述 (Optional - STRING)
       - `createdAt`: 创建时间 (Creation timestamp)
       - `updatedAt`: 更新时间 (Update timestamp)

     - 示例结构 (Example structure for 

       ```
       Order
       ```

        in 

       ```
       schema.prisma
       ```

       ):

       代码段

       ```
       enum OrderStatus {
         PENDING
         COMPLETED
         FAILED
         CANCELLED
       }
       
       model Order {
         id              String      @id @default(cuid())
         userId          String
         user            User        @relation(fields: [userId], references: [id])
         amount          Decimal     // Or Float
         status          OrderStatus @default(PENDING)
         paymentMethod   String      // e.g., "ALIPAY", "WECHATPAY"
         itemId          String?     // ID of the package or item being purchased
         itemDescription String?     // Description of the item
         transactionId   String?     @unique // Store Alipay's transaction ID later
         createdAt       DateTime    @default(now())
         updatedAt       DateTime    @updatedAt
       
         @@index([userId])
       }
       ```

     - 重要 (Important):

        在 

       ```
       User
       ```

        model 中添加反向关系 (Ensure the 

       ```
       User
       ```

        model has a reverse relation):

       代码段

       ```
       model User {
         // ... other fields
         orders Order[] // Add this line
       }
       ```

   - 英文 (English):

      Open 

     ```
     backend/prisma/schema.prisma
     ```

      again.

     - Add a new `model` (table) to store order information, e.g., `Order`.
     - It should include: `id`, `userId`, `amount`, `status` (e.g., PENDING, COMPLETED), `paymentMethod`, `itemId` (optional), `itemDescription` (optional), `createdAt`, `updatedAt`.
     - See the example structure above.
     - **Important:** Add a reverse relation in the `User` model.

2. **生成并应用数据库迁移 (Generate and Apply Database Migration for Orders):**

   - 操作 (Action):

      保存 

     ```
     schema.prisma
     ```

      文件后，在 

     ```
     backend
     ```

      文件夹的终端中运行：

     Bash

     ```
     npx prisma migrate dev --name addOrderTable
     ```

   - 英文 (English):

      After saving 

     ```
     schema.prisma
     ```

     , run in the 

     ```
     backend
     ```

      terminal:

     Bash

     ```
     npx prisma migrate dev --name addOrderTable
     ```

3. **创建后端API - 创建订单 (Create Backend API - Create Order):**

   - 操作 (Action):

     - 在 `backend` 创建一个新的文件夹和文件，例如 `backend/payments/index.ts` (如果尚不存在) 或 `backend/orders/index.ts`。

     - 添加一个新的路由 (e.g., `POST /api/orders/create`) 来创建订单。

     - 这个路由接收用户ID (从认证信息中获取)、购买的商品信息 (e.g., 套餐ID, 金额)。

     - 它会在 `Order` 表中插入一条新记录，状态为 `PENDING`。

     - 示例 (Conceptual code in 

       ```
       backend/payments/index.ts
       ```

        or 

       ```
       backend/orders/index.ts
       ```

       ):

       TypeScript

       ```
       // router, prisma, isAuthenticated as before
       
       router.post('/create-order', isAuthenticated, async (req, res) => {
         try {
           const userId = req.user.id;
           const { itemId, amount, paymentMethod, itemDescription } = req.body; // Get these from request body
       
           if (!itemId || !amount || !paymentMethod) {
             return res.status(400).json({ message: "Missing order details" });
           }
       
           const order = await prisma.order.create({
             data: {
               userId: userId,
               itemId: itemId,
               amount: amount, // Ensure this is a Decimal or correct type
               paymentMethod: paymentMethod, // e.g., "ALIPAY"
               itemDescription: itemDescription,
               status: 'PENDING', // Prisma will use the enum if defined
             },
           });
           // For now, we just return the created order.
           // Later, this is where you'd generate parameters for Alipay SDK.
           res.status(201).json({ orderId: order.id, message: "Order created successfully" });
         } catch (error) {
           console.error("Failed to create order:", error);
           res.status(500).json({ message: "Error creating order" });
         }
       });
       ```

     - **注册路由 (Register the route):** 确保在 `backend/server.ts` 中引入并使用这个新的支付/订单路由。

   - 英文 (English):

     - Create a new folder/file in `backend`, e.g., `backend/payments/index.ts` (if not already created) or `backend/orders/index.ts`.
     - Add a new route (e.g., `POST /api/orders/create`) to create an order.
     - This route takes user ID (from auth) and item details (e.g., package ID, amount).
     - It inserts a new record into the `Order` table with `PENDING` status.
     - See the conceptual code example above.
     - **Register the route:** Ensure this new payment/order route is imported and used in `backend/server.ts`.

4. **前端 - 调用创建订单API (Frontend - Call Create Order API):**

   - 操作 (Action):

     - 在你的支付页面/组件中 (e.g., 

       ```
       frontend/src/pages/PricingPage.tsx
       ```

       )，当用户点击“支付宝支付”按钮时：

       1. 收集需要购买的商品信息 (套餐ID, 金额, 描述等)。
       2. 调用后端创建订单的 API (e.g., `POST /api/orders/create`)。

     - 你可能需要在 `frontend/src/lib/api/` 下创建一个新的 API 服务文件，例如 `paymentService.ts` 或者在 `apiService.ts` 中添加一个函数。

     - 示例 (Conceptual code for an API call function in 

       ```
       frontend/src/lib/api/paymentService.ts
       ```

       ):

       TypeScript

       ```
       import { apiService } from './apiService'; // Assuming you have a base apiService
       
       export const createOrder = async (orderDetails: {
         itemId: string;
         amount: number; // or string, ensure backend handles conversion
         paymentMethod: 'ALIPAY';
         itemDescription?: string;
       }) => {
         try {
           const response = await apiService.post('/orders/create', orderDetails);
           return response.data; // Should contain { orderId, message }
         } catch (error) {
           console.error('Error creating order:', error);
           throw error; // Rethrow to be handled by the component
         }
       };
       ```

     - 在组件中调用这个函数:

       TypeScript

       ```
       // In your PricingPage.tsx or similar component
       // ...
       const handleAlipaySubmit = async (packageDetails) => {
         try {
           const orderData = await createOrder({
             itemId: packageDetails.id,
             amount: packageDetails.price,
             paymentMethod: 'ALIPAY',
             itemDescription: packageDetails.name
           });
           console.log("Order created:", orderData);
           // TODO: Next step - guide user to Alipay with orderData.orderId
           // For now, maybe show a message: "Order created! Proceed to Alipay (ID: ${orderData.orderId})"
           alert(`订单已创建 (ID: ${orderData.orderId})！接下来将引导您完成支付。`);
         } catch (error) {
           // Show error toast/message
           alert("创建订单失败，请稍后再试。");
         }
       };
       // ...
       // <Button onClick={() => handleAlipaySubmit(selectedPackage)}>支付宝支付</Button>
       ```

   - 英文 (English):

     - In your payment page/component (e.g., 

       ```
       frontend/src/pages/PricingPage.tsx
       ```

       ), when the user clicks the "Alipay Pay" button:

       1. Gather item details (package ID, amount, description).
       2. Call the backend API to create the order (e.g., `POST /api/orders/create`).

     - You might need a new API service file under `frontend/src/lib/api/`, like `paymentService.ts`, or add a function to `apiService.ts`.

     - See conceptual code examples above.

5. **前端 - 支付引导 (Frontend - Payment Guidance):**

   - 操作 (Action):
     - 在后端成功创建订单并返回订单ID后，前端应该给用户明确的指示。
     - **初期简化版 (Initial simplified version):** 显示一个消息，例如 "订单已创建，订单号：{orderId}。请准备使用支付宝完成支付。" (Order created, Order ID: {orderId}. Please prepare to complete payment with Alipay.)
     - **后续实际对接 (Actual future integration):** 这里你会需要支付宝的SDK，后端可能会返回跳转URL或者支付所需的参数，前端再根据这些信息引导用户到支付宝的支付网关或调起支付宝App。目前，我们只做到提示。
   - 英文 (English):
     - After the backend successfully creates an order and returns the order ID, the frontend should give clear instructions to the user.
     - **Initial simplified version:** Display a message like "Order created, Order ID: {orderId}. Please prepare to complete payment with Alipay."
     - **Actual future integration:** This is where you'd need Alipay's SDK. The backend might return a redirect URL or parameters needed for payment, and the frontend would then guide the user to Alipay's payment gateway or invoke the Alipay app. For now, we'll just show a message.

------

这是一个初步的框架。每一步都可能涉及到更多细节，尤其是在具体编写 TypeScript/React 代码和 Prisma schema 时。如果你在某一步卡住了，或者需要某个文件更具体的修改建议，请随时告诉我。