// 性能监控系统
export interface PerformanceMetric {
  name: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  metadata?: Record<string, any>;
}

export interface PerformanceReport {
  sessionId: string;
  totalTime: number;
  phases: {
    warmUp: number;
    preparation: number;
    validation: number;
    activation: number;
  };
  bottlenecks: string[];
  metrics: PerformanceMetric[];
  timestamp: number;
}

/**
 * 性能监控器
 * 负责监控面试启动流程的各个阶段性能
 */
export class PerformanceMonitor {
  private metrics: Map<string, PerformanceMetric> = new Map();
  private sessionId: string;
  private startTime: number;

  constructor(sessionId: string) {
    this.sessionId = sessionId;
    this.startTime = performance.now();
  }

  /**
   * 开始监控一个指标
   */
  public startMetric(name: string, metadata?: Record<string, any>): void {
    const metric: PerformanceMetric = {
      name,
      startTime: performance.now(),
      metadata
    };
    
    this.metrics.set(name, metric);
    console.log(`📊 PerformanceMonitor: Started tracking ${name}`);
  }

  /**
   * 结束监控一个指标
   */
  public endMetric(name: string, metadata?: Record<string, any>): number {
    const metric = this.metrics.get(name);
    if (!metric) {
      console.warn(`⚠️ PerformanceMonitor: Metric ${name} not found`);
      return 0;
    }

    metric.endTime = performance.now();
    metric.duration = metric.endTime - metric.startTime;
    
    if (metadata) {
      metric.metadata = { ...metric.metadata, ...metadata };
    }

    console.log(`📊 PerformanceMonitor: ${name} completed in ${metric.duration.toFixed(2)}ms`);
    return metric.duration;
  }

  /**
   * 记录一个即时指标
   */
  public recordMetric(name: string, duration: number, metadata?: Record<string, any>): void {
    const metric: PerformanceMetric = {
      name,
      startTime: performance.now() - duration,
      endTime: performance.now(),
      duration,
      metadata
    };
    
    this.metrics.set(name, metric);
    console.log(`📊 PerformanceMonitor: Recorded ${name}: ${duration.toFixed(2)}ms`);
  }

  /**
   * 获取指标
   */
  public getMetric(name: string): PerformanceMetric | undefined {
    return this.metrics.get(name);
  }

  /**
   * 获取所有指标
   */
  public getAllMetrics(): PerformanceMetric[] {
    return Array.from(this.metrics.values());
  }

  /**
   * 生成性能报告
   */
  public generateReport(): PerformanceReport {
    const totalTime = performance.now() - this.startTime;
    const metrics = this.getAllMetrics();
    
    // 计算各阶段耗时
    const phases = {
      warmUp: this.getMetric('warmUp')?.duration || 0,
      preparation: this.getMetric('preparation')?.duration || 0,
      validation: this.getMetric('validation')?.duration || 0,
      activation: this.getMetric('activation')?.duration || 0
    };

    // 识别性能瓶颈
    const bottlenecks = this.identifyBottlenecks(metrics);

    const report: PerformanceReport = {
      sessionId: this.sessionId,
      totalTime,
      phases,
      bottlenecks,
      metrics,
      timestamp: Date.now()
    };

    console.log('📊 PerformanceMonitor: Generated performance report:', report);
    return report;
  }

  /**
   * 发送性能报告到服务器
   */
  public async sendReport(): Promise<void> {
    const report = this.generateReport();
    
    try {
      // 发送到服务器进行分析
      await fetch('/api/performance/report', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(report)
      });
      
      console.log('📊 PerformanceMonitor: Report sent to server');
    } catch (error) {
      console.error('❌ PerformanceMonitor: Failed to send report:', error);
    }
  }

  /**
   * 清理资源
   */
  public cleanup(): void {
    this.metrics.clear();
    console.log('🧹 PerformanceMonitor: Cleaned up');
  }

  // 私有方法
  private identifyBottlenecks(metrics: PerformanceMetric[]): string[] {
    const bottlenecks: string[] = [];
    const thresholds = {
      warmUp: 2000,        // 2秒
      preparation: 3000,   // 3秒
      validation: 1000,    // 1秒
      activation: 1000,    // 1秒
      webSocketConnect: 2000,  // 2秒
      audioSetup: 1500,    // 1.5秒
      creditsCheck: 500    // 0.5秒
    };

    metrics.forEach(metric => {
      if (metric.duration && metric.name in thresholds) {
        const threshold = thresholds[metric.name as keyof typeof thresholds];
        if (metric.duration > threshold) {
          bottlenecks.push(`${metric.name} (${metric.duration.toFixed(2)}ms > ${threshold}ms)`);
        }
      }
    });

    return bottlenecks;
  }
}

/**
 * 全局性能监控管理器
 */
export class GlobalPerformanceManager {
  private static instance: GlobalPerformanceManager;
  private monitors: Map<string, PerformanceMonitor> = new Map();

  public static getInstance(): GlobalPerformanceManager {
    if (!GlobalPerformanceManager.instance) {
      GlobalPerformanceManager.instance = new GlobalPerformanceManager();
    }
    return GlobalPerformanceManager.instance;
  }

  /**
   * 创建会话监控器
   */
  public createMonitor(sessionId: string): PerformanceMonitor {
    const monitor = new PerformanceMonitor(sessionId);
    this.monitors.set(sessionId, monitor);
    return monitor;
  }

  /**
   * 获取会话监控器
   */
  public getMonitor(sessionId: string): PerformanceMonitor | undefined {
    return this.monitors.get(sessionId);
  }

  /**
   * 移除会话监控器
   */
  public removeMonitor(sessionId: string): void {
    const monitor = this.monitors.get(sessionId);
    if (monitor) {
      monitor.cleanup();
      this.monitors.delete(sessionId);
    }
  }

  /**
   * 生成全局性能统计
   */
  public generateGlobalStats(): {
    totalSessions: number;
    averageTime: number;
    commonBottlenecks: string[];
  } {
    const reports = Array.from(this.monitors.values()).map(m => m.generateReport());
    
    const totalSessions = reports.length;
    const averageTime = reports.reduce((sum, r) => sum + r.totalTime, 0) / totalSessions;
    
    // 统计常见瓶颈
    const bottleneckCounts: Record<string, number> = {};
    reports.forEach(report => {
      report.bottlenecks.forEach(bottleneck => {
        bottleneckCounts[bottleneck] = (bottleneckCounts[bottleneck] || 0) + 1;
      });
    });
    
    const commonBottlenecks = Object.entries(bottleneckCounts)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 5)
      .map(([bottleneck]) => bottleneck);

    return {
      totalSessions,
      averageTime,
      commonBottlenecks
    };
  }

  /**
   * 清理所有监控器
   */
  public cleanup(): void {
    this.monitors.forEach(monitor => monitor.cleanup());
    this.monitors.clear();
  }
}

// 导出全局实例
export const globalPerformanceManager = GlobalPerformanceManager.getInstance();

// 开发环境下暴露到全局
if (process.env.NODE_ENV === 'development') {
  (window as any).__performanceManager__ = globalPerformanceManager;
}
