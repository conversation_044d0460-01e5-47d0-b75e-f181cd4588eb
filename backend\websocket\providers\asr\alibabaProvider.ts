// 阿里ASR提供商
import crypto from 'crypto';
import { ASRServiceInterface, ASRResult, ASROptions, ASRConfig, ASRProvider, ASRError } from '../../../types/asr.js';

export class AlibabaProvider implements ASRServiceInterface {
  private config: ASRConfig;
  private alibabaConfig: {
    APPKEY: string;
    URL: string;
    ACCESS_KEY_ID: string;
    ACCESS_KEY_SECRET: string;
    ENDPOINT: string;
    API_VERSION: string;
  };
  private RPCClient: any = null;

  constructor() {
    this.config = {
      provider: ASRProvider.ALIBABA,
      priority: 2,
      timeout: 12000,
      retryCount: 2,
      weight: 1.1,
      enabled: true
    };

    this.alibabaConfig = {
      APPKEY: process.env.ALIBABA_APPKEY || 'FI6ogoG2KyQTDt22',
      URL: 'wss://nls-gateway.cn-shanghai.aliyuncs.com/ws/v1',
      ACCESS_KEY_ID: process.env.ALIBABA_ACCESS_KEY_ID || 'LTAI5tCp7CrNwmLBLcTvyaoU',
      ACCESS_KEY_SECRET: process.env.ALIBABA_ACCESS_KEY_SECRET || '******************************',
      ENDPOINT: 'http://nls-meta.cn-shanghai.aliyuncs.com',
      API_VERSION: '2019-02-28'
    };

    this.loadAlibabaSDK();
  }

  getName(): string {
    return ASRProvider.ALIBABA;
  }

  isAvailable(): boolean {
    return this.config.enabled && 
           !!this.alibabaConfig.APPKEY && 
           !!this.alibabaConfig.ACCESS_KEY_ID && 
           !!this.alibabaConfig.ACCESS_KEY_SECRET &&
           !!this.RPCClient;
  }

  getConfig(): ASRConfig {
    return { ...this.config };
  }

  updateConfig(config: Partial<ASRConfig>): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * 异步加载阿里云SDK
   */
  private async loadAlibabaSDK(): Promise<void> {
    try {
      if (!this.RPCClient) {
        const popCore = await import('@alicloud/pop-core');
        this.RPCClient = popCore.RPCClient;
        console.log('✅ Alibaba SDK loaded successfully');
      }
    } catch (error) {
      console.error('❌ Failed to load Alibaba SDK:', error);
      this.config.enabled = false;
    }
  }

  /**
   * 阿里语音识别
   */
  async recognize(audioBuffer: Buffer, options?: ASROptions): Promise<ASRResult> {
    const startTime = Date.now();
    
    if (!this.isAvailable()) {
      throw this.createError('Alibaba ASR service not available', 'SERVICE_UNAVAILABLE', false);
    }

    console.log(`🎯 Alibaba ASR: Starting recognition, audio size: ${audioBuffer.length} bytes`);

    try {
      // 1. 获取Token
      const token = await this.getAlibabaToken();
      if (!token) {
        throw this.createError('Failed to get Alibaba token', 'AUTHENTICATION_FAILED', true);
      }

      // 2. 使用WebSocket连接进行识别
      return await this.performWebSocketRecognition(audioBuffer, token, options, startTime);
    } catch (error) {
      console.error('❌ Alibaba ASR error:', error);
      if (error instanceof Error && (error as ASRError).provider) {
        throw error;
      }
      throw this.createError(`Alibaba ASR error: ${(error as Error).message}`, 'PROCESSING_ERROR', true);
    }
  }

  /**
   * 获取阿里云Token
   */
  private async getAlibabaToken(): Promise<string | null> {
    try {
      console.log('🔑 Getting Alibaba ASR token...');

      if (!this.RPCClient) {
        await this.loadAlibabaSDK();
        if (!this.RPCClient) {
          throw new Error('Alibaba SDK not available');
        }
      }

      const client = new this.RPCClient({
        accessKeyId: this.alibabaConfig.ACCESS_KEY_ID,
        accessKeySecret: this.alibabaConfig.ACCESS_KEY_SECRET,
        endpoint: this.alibabaConfig.ENDPOINT,
        apiVersion: this.alibabaConfig.API_VERSION
      });

      const result = await client.request('CreateToken');
      console.log('✅ Alibaba Token obtained:', result.Token?.Id ? 'Success' : 'Failed');
      return result.Token?.Id || null;
    } catch (error) {
      console.error('❌ Failed to get Alibaba token:', error);
      return null;
    }
  }

  /**
   * 执行WebSocket识别
   */
  private async performWebSocketRecognition(
    audioBuffer: Buffer, 
    token: string, 
    options?: ASROptions,
    startTime: number = Date.now()
  ): Promise<ASRResult> {
    return new Promise((resolve, reject) => {
      let transcriptionResult = '';
      let isCompleted = false;
      let websocket: any = null;

      // 设置超时
      const timeout = setTimeout(() => {
        if (!isCompleted && websocket) {
          isCompleted = true;
          websocket.close();
          reject(this.createError('Alibaba ASR timeout', 'TIMEOUT', true));
        }
      }, options?.timeout || this.config.timeout);

      try {
        const socketUrl = `${this.alibabaConfig.URL}?token=${token}`;
        
        // 动态导入ws模块
        import('ws').then(({ default: WebSocket }) => {
          websocket = new WebSocket(socketUrl);
          const taskId = this.generateUUID();

          websocket.on('open', () => {
            console.log('🔗 Alibaba ASR WebSocket connected');

            // 发送开始转写消息
            const startMessage = {
              header: {
                appkey: this.alibabaConfig.APPKEY,
                namespace: "SpeechTranscriber",
                name: "StartTranscription",
                task_id: taskId,
                message_id: this.generateUUID()
              },
              payload: {
                format: "pcm",
                sample_rate: 16000,
                enable_intermediate_result: true,
                enable_punctuation_prediction: true,
                enable_inverse_text_normalization: true
              }
            };

            websocket.send(JSON.stringify(startMessage));
          });

          websocket.on('message', (data: any) => {
            try {
              const message = JSON.parse(data.toString());
              console.log('📥 Alibaba ASR response:', JSON.stringify(message, null, 2));

              if (message.header.name === "TranscriptionStarted") {
                console.log('🎤 Alibaba ASR transcription started, sending audio...');
                this.sendAudioData(websocket, audioBuffer, taskId);
              } else if (message.header.name === "TranscriptionResultChanged") {
                // 中间结果
                if (message.payload && message.payload.result) {
                  console.log('📝 Alibaba ASR intermediate result:', message.payload.result);
                }
              } else if (message.header.name === "TranscriptionCompleted") {
                // 最终结果
                console.log('✅ Alibaba ASR completed');
                isCompleted = true;
                clearTimeout(timeout);

                if (message.payload && message.payload.result) {
                  transcriptionResult = message.payload.result;
                }

                websocket.close();
                const processingTime = Date.now() - startTime;
                const cleanedText = this.cleanText(transcriptionResult);
                
                console.log(`✅ Alibaba ASR completed in ${processingTime}ms: "${cleanedText}"`);
                
                resolve({
                  text: cleanedText,
                  confidence: 0.85,
                  provider: ASRProvider.ALIBABA,
                  processingTime,
                  timestamp: Date.now(),
                  isPartial: false
                });
              } else if (message.header.name === "TaskFailed") {
                console.error('❌ Alibaba ASR task failed:', message);
                isCompleted = true;
                clearTimeout(timeout);
                websocket.close();
                reject(this.createError(`Alibaba ASR task failed: ${message.header.status_text}`, 'PROCESSING_ERROR', true));
              }
            } catch (parseError) {
              console.error('❌ Error parsing Alibaba ASR response:', parseError);
            }
          });

          websocket.on('error', (error: any) => {
            console.error('❌ Alibaba ASR WebSocket error:', error);
            if (!isCompleted) {
              isCompleted = true;
              clearTimeout(timeout);
              reject(this.createError(`Alibaba ASR WebSocket error: ${error.message}`, 'NETWORK_ERROR', true));
            }
          });

          websocket.on('close', () => {
            console.log('🔌 Alibaba ASR connection closed');
            if (!isCompleted) {
              isCompleted = true;
              clearTimeout(timeout);
              const cleanedText = this.cleanText(transcriptionResult);
              if (cleanedText.trim()) {
                const processingTime = Date.now() - startTime;
                resolve({
                  text: cleanedText,
                  confidence: 0.85,
                  provider: ASRProvider.ALIBABA,
                  processingTime,
                  timestamp: Date.now(),
                  isPartial: false
                });
              } else {
                reject(this.createError('Alibaba ASR: No transcription result', 'PROCESSING_ERROR', true));
              }
            }
          });

        }).catch((importError) => {
          console.error('❌ Failed to import WebSocket module:', importError);
          clearTimeout(timeout);
          reject(this.createError(`Failed to import WebSocket: ${importError.message}`, 'PROCESSING_ERROR', false));
        });

      } catch (error) {
        console.error('❌ Error setting up Alibaba ASR WebSocket:', error);
        clearTimeout(timeout);
        reject(this.createError(`Alibaba ASR setup error: ${(error as Error).message}`, 'PROCESSING_ERROR', true));
      }
    });
  }

  /**
   * 发送音频数据
   */
  private sendAudioData(websocket: any, audioBuffer: Buffer, taskId: string): void {
    const pcm16Data = this.convertToPCM16(audioBuffer);
    const chunkSize = 3200;
    let offset = 0;

    const sendAudioChunk = () => {
      if (offset < pcm16Data.length) {
        const chunk = pcm16Data.slice(offset, offset + chunkSize);
        websocket.send(chunk);
        offset += chunkSize;
        setTimeout(sendAudioChunk, 40);
      } else {
        // 发送结束消息
        const stopMessage = {
          header: {
            appkey: this.alibabaConfig.APPKEY,
            namespace: "SpeechTranscriber",
            name: "StopTranscription",
            task_id: taskId,
            message_id: this.generateUUID()
          }
        };
        websocket.send(JSON.stringify(stopMessage));
      }
    };

    sendAudioChunk();
  }

  /**
   * 转换为PCM16格式
   */
  private convertToPCM16(audioBuffer: Buffer): Buffer {
    // 简单实现，假设输入已经是合适的格式
    return audioBuffer;
  }

  /**
   * 生成UUID
   */
  private generateUUID(): string {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }

  /**
   * 清理文本
   */
  private cleanText(text: string): string {
    if (!text) return '';
    return text.trim().replace(/\s+/g, ' ');
  }

  /**
   * 创建ASR错误
   */
  private createError(message: string, code: string, retryable: boolean): ASRError {
    const error = new Error(message) as ASRError;
    error.provider = ASRProvider.ALIBABA;
    error.code = code;
    error.retryable = retryable;
    return error;
  }

  async destroy(): Promise<void> {
    console.log('🧹 Alibaba ASR provider destroyed');
    // Alibaba provider doesn't need special cleanup
  }
}
