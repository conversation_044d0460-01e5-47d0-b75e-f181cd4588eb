import WebSocketClient from 'ws';
import crypto from 'crypto';

/**
 * 🔥 Phase 2: 统一文本状态管理器
 */
class TranscriptionStateManager {
  private currentText: string = '';
  private lastSentText: string = '';
  private isSessionActive: boolean = true;

  updateText(newText: string): void {
    this.currentText = newText;
  }

  getCurrentText(): string {
    return this.currentText;
  }

  hasNewContent(): boolean {
    return this.currentText !== this.lastSentText && this.currentText.trim().length > 0;
  }

  markAsFinal(): string {
    const finalText = this.currentText;
    this.lastSentText = finalText;
    this.currentText = '';
    return finalText;
  }

  reset(): void {
    this.currentText = '';
    this.lastSentText = '';
  }

  getDebugInfo(): object {
    return {
      currentText: this.currentText,
      lastSentText: this.lastSentText,
      hasNewContent: this.hasNewContent(),
      isSessionActive: this.isSessionActive
    };
  }
}

/**
 * 🔥 Phase 2: 智能句子边界检测器
 */
class SentenceBoundaryDetector {
  private readonly SENTENCE_ENDINGS = ['。', '！', '？', '.', '!', '?'];
  private readonly MIN_SENTENCE_LENGTH = 3;
  private MAX_SILENCE_FOR_CONTINUATION = 2000; // 2秒，可自适应调整

  // 🔥 Phase 3: 自适应阈值调整
  private silenceHistory: number[] = [];
  private readonly MAX_HISTORY_SIZE = 10;

  shouldFinalizeSentence(
    text: string,
    silenceDuration: number,
    previousText: string = ''
  ): boolean {
    // 策略1：明确的句子结束符
    if (this.hasDefinitiveEnding(text)) {
      console.log(`🎯 Sentence boundary detected: definitive ending in "${text}"`);
      return true;
    }

    // 策略2：长静音 + 最小长度（使用自适应阈值）
    if (silenceDuration > this.getAdaptiveSilenceThreshold() &&
        text.length >= this.MIN_SENTENCE_LENGTH) {
      console.log(`🎯 Sentence boundary detected: adaptive silence (${silenceDuration}ms > ${this.getAdaptiveSilenceThreshold()}ms) + sufficient length (${text.length})`);
      this.recordSilenceDuration(silenceDuration);
      return true;
    }

    // 策略3：语义完整性检测
    if (this.isSemanticallComplete(text)) {
      console.log(`🎯 Sentence boundary detected: semantically complete "${text}"`);
      return true;
    }

    // 策略4：Phase 3高级检测 - 基于NLP的语义完整性
    if (this.isAdvancedSemanticComplete(text, previousText)) {
      console.log(`🎯 Sentence boundary detected: advanced semantic analysis "${text}"`);
      return true;
    }

    return false;
  }

  private hasDefinitiveEnding(text: string): boolean {
    return this.SENTENCE_ENDINGS.some(ending => text.trim().endsWith(ending));
  }

  private isSemanticallComplete(text: string): boolean {
    // 简单的语义完整性检测
    // 包含逗号且长度超过10个字符，可能是完整的语义单元
    return text.includes('，') && text.length > 10;
  }

  // 🔥 Phase 3: 高级语义完整性检测
  private isAdvancedSemanticComplete(text: string, previousText: string): boolean {
    // 检测常见的完整语义模式
    const completePatterns = [
      /.*的话$/,           // "...的话"
      /.*就是.*$/,         // "...就是..."
      /.*这样.*$/,         // "...这样"
      /.*所以.*$/,         // "...所以..."
      /.*因为.*$/,         // "...因为..."
      /.*但是.*$/,         // "...但是..."
      /.*然后.*$/,         // "...然后..."
    ];

    return completePatterns.some(pattern => pattern.test(text.trim()));
  }

  // 🔥 Phase 3: 自适应静音阈值
  private getAdaptiveSilenceThreshold(): number {
    if (this.silenceHistory.length < 3) {
      return this.MAX_SILENCE_FOR_CONTINUATION; // 使用默认值
    }

    // 计算历史静音时长的平均值和标准差
    const avg = this.silenceHistory.reduce((a, b) => a + b, 0) / this.silenceHistory.length;
    const variance = this.silenceHistory.reduce((a, b) => a + Math.pow(b - avg, 2), 0) / this.silenceHistory.length;
    const stdDev = Math.sqrt(variance);

    // 自适应阈值 = 平均值 - 0.5 * 标准差（更敏感）
    const adaptiveThreshold = Math.max(1000, avg - 0.5 * stdDev); // 最小1秒

    console.log(`🔧 Adaptive silence threshold: ${adaptiveThreshold.toFixed(0)}ms (avg: ${avg.toFixed(0)}, stdDev: ${stdDev.toFixed(0)})`);
    return adaptiveThreshold;
  }

  private recordSilenceDuration(duration: number): void {
    this.silenceHistory.push(duration);
    if (this.silenceHistory.length > this.MAX_HISTORY_SIZE) {
      this.silenceHistory.shift(); // 保持历史记录大小
    }
  }
}

/**
 * 🔥 Phase 3: 性能监控和自动调优
 */
class PerformanceMonitor {
  private metrics = {
    totalSentences: 0,
    totalLatency: 0,
    averageLatency: 0,
    accuracyScore: 0,
    errorCount: 0,
    successRate: 0
  };

  private latencyHistory: number[] = [];
  private readonly MAX_HISTORY_SIZE = 50;

  recordSentenceProcessed(latency: number, isSuccess: boolean): void {
    this.metrics.totalSentences++;

    if (isSuccess) {
      this.metrics.totalLatency += latency;
      this.latencyHistory.push(latency);

      if (this.latencyHistory.length > this.MAX_HISTORY_SIZE) {
        this.latencyHistory.shift();
      }

      this.metrics.averageLatency = this.metrics.totalLatency / this.metrics.totalSentences;
    } else {
      this.metrics.errorCount++;
    }

    this.metrics.successRate = ((this.metrics.totalSentences - this.metrics.errorCount) / this.metrics.totalSentences) * 100;
  }

  getPerformanceReport(): object {
    return {
      ...this.metrics,
      recentLatencies: this.latencyHistory.slice(-10),
      recommendations: this.generateRecommendations()
    };
  }

  private generateRecommendations(): string[] {
    const recommendations: string[] = [];

    if (this.metrics.averageLatency > 500) {
      recommendations.push('Consider reducing VAD EOS threshold for faster response');
    }

    if (this.metrics.successRate < 95) {
      recommendations.push('High error rate detected, check network stability');
    }

    if (this.latencyHistory.length > 5) {
      const recentAvg = this.latencyHistory.slice(-5).reduce((a, b) => a + b, 0) / 5;
      if (recentAvg > this.metrics.averageLatency * 1.5) {
        recommendations.push('Recent performance degradation detected');
      }
    }

    return recommendations;
  }
}

// IAT流式API配置
interface IATConfig {
  APPID: string;
  API_KEY: string;
  API_SECRET: string;
  HOST_URL: string;
}

// 简化的IAT会话状态
interface IATSession {
  sessionId: string;
  connection: WebSocketClient | null;
  isConnected: boolean;
  isConnecting: boolean;
  accumulatedText: string;
  vadEos: number; // 可配置的静音检测时间
  firstFrameSent: boolean; // 🔥 新增：是否已发送首帧
  frameStatus: number; // 🔥 新增：当前帧状态
  lastSendTime: number; // 🔥 新增：最后发送数据的时间戳
  onTranscription: (text: string, isFinal: boolean) => void;
  onLLMTrigger: (text: string) => void;
  onError: (error: Error) => void;
  reconnectAttempts: number; // 重连尝试次数
  heartbeatInterval: NodeJS.Timeout | null; // 心跳定时器
  lastActivityTime: number; // 最后活动时间
  audioQueue: Buffer[];      // 待发送的音频队列
  queueDurationMs: number;   // 队列累计时长
  lastVoiceTime: number;    // 最近一次检测到有效语音的时间

  // 🔥 新增：自动重建相关字段
  autoRebuildEnabled: boolean; // 是否启用自动重建
  rebuildAttempts: number;     // 重建尝试次数
  isRebuilding: boolean;       // 是否正在重建中
  lastEndFrameTime: number;    // 最后发送结束帧的时间
  pendingAudioBuffer: Buffer[]; // 等待重建期间的音频缓冲

  // 🔥 Phase 2: 智能优化字段
  stateManager: TranscriptionStateManager; // 统一状态管理器
  boundaryDetector: SentenceBoundaryDetector; // 智能边界检测器

  // 🔥 Phase 3: 高级功能字段
  performanceMonitor: PerformanceMonitor; // 性能监控器
  sentenceStartTime: number; // 句子开始时间（用于延迟计算）
}

// 简化的IAT管理器 - 纯粹的API包装器
export class SessionIATManager {
  private config: IATConfig;
  private sessions: Map<string, IATSession> = new Map();
  
  // 🔥 新增：帧状态常量
  private readonly FRAME_STATUS = {
    STATUS_FIRST_FRAME: 0,
    STATUS_CONTINUE_FRAME: 1,
    STATUS_LAST_FRAME: 2
  };
  
  // 重连和心跳配置
  private readonly MAX_RECONNECT_ATTEMPTS = 5;
  private readonly RECONNECT_DELAY = 2000; // 2秒
  private readonly HEARTBEAT_INTERVAL = 5000; // 5秒检查一次是否需要发送心跳
  private readonly INACTIVITY_TIMEOUT = 30000; // 30秒无活动触发重连
  private readonly MAX_SILENCE_DURATION = 8000; // 8秒无音频数据就发送心跳
  // 🆕 最大缓冲时长（毫秒）
  private readonly MAX_QUEUE_DURATION_MS = 3000;
  private readonly SILENCE_THRESHOLD_AMPLITUDE = 400; // 小于此幅度视为静音

  // 🔥 新增：自动重建会话配置
  private readonly AUTO_REBUILD_ENABLED = true; // 启用自动重建
  private readonly REBUILD_DELAY_MS = 500; // 重建延迟500ms
  private readonly MAX_REBUILD_ATTEMPTS = 10; // 最大重建尝试次数

  constructor(config: IATConfig) {
    this.config = config;
    console.log('✅ SessionIATManager initialized as API wrapper');
  }

  /**
   * 计算PCM缓冲区的时长(ms) 16kHz / 16bit / mono
   */
  private getBufferDurationMs(buffer: Buffer): number {
    const sampleCount = buffer.length / 2; // 16bit = 2字节
    return (sampleCount / 16000) * 1000;
  }

  /**
   * 创建新的IAT会话
   */
  async createSession(
    sessionId: string,
    onTranscription: (text: string, isFinal: boolean) => void,
    onLLMTrigger: (text: string) => void,
    onError: (error: Error) => void,
    vadEos: number = 1500 // 🎯 优化：降低到1.5秒静音检测，提高响应速度
  ): Promise<void> {
    console.log(`[${sessionId}] Creating new IAT session with vad_eos=${vadEos}ms`);

    // 🔥 修复：确保旧会话完全清理
    if (this.sessions.has(sessionId)) {
      console.warn(`[${sessionId}] Session already exists, force closing old session`);
      this.forceCloseSession(sessionId);
      // 等待一小段时间确保清理完成
      await new Promise(resolve => setTimeout(resolve, 200));
    }

    const session: IATSession = {
      sessionId,
      connection: null,
      isConnected: false,
      isConnecting: false,
      accumulatedText: '',
      vadEos,
      firstFrameSent: false, // 🔥 初始化为false
      frameStatus: this.FRAME_STATUS.STATUS_FIRST_FRAME,
      lastSendTime: 0,
      onTranscription,
      onLLMTrigger,
      onError,
      reconnectAttempts: 0,
      heartbeatInterval: null,
      lastActivityTime: Date.now(),
      audioQueue: [],
      queueDurationMs: 0,
      lastVoiceTime: 0,

      // 🔥 新增：初始化自动重建字段
      autoRebuildEnabled: this.AUTO_REBUILD_ENABLED,
      rebuildAttempts: 0,
      isRebuilding: false,
      lastEndFrameTime: 0,
      pendingAudioBuffer: [],

      // 🔥 Phase 2: 初始化智能优化组件
      stateManager: new TranscriptionStateManager(),
      boundaryDetector: new SentenceBoundaryDetector(),

      // 🔥 Phase 3: 初始化高级功能组件
      performanceMonitor: new PerformanceMonitor(),
      sentenceStartTime: Date.now(),
    };

    this.sessions.set(sessionId, session);
    await this.connectToIAT(session);
  }

  /**
   * 连接到IAT服务
   */
  private async connectToIAT(session: IATSession): Promise<void> {
    if (session.isConnecting) {
      console.log(`[${session.sessionId}] Already connecting, skipping`);
      return;
    }

    session.isConnecting = true;
    console.log(`[${session.sessionId}] Connecting to IAT service`);

    try {
      const authUrl = this.generateAuthUrl();
      session.connection = new WebSocketClient(authUrl);

      // 设置连接超时
      const timeout = setTimeout(() => {
        if (session.isConnecting) {
          console.error(`[${session.sessionId}] IAT connection timeout`);
          session.connection?.close();
          session.isConnecting = false;
          session.onError(new Error('IAT connection timeout'));
        }
      }, 30000);

      session.connection.on('open', () => {
        console.log(`[${session.sessionId}] IAT WebSocket connected`);
        clearTimeout(timeout);
        session.isConnected = true;
        session.isConnecting = false;
        session.reconnectAttempts = 0; // 重置重连尝试次数
        session.lastActivityTime = Date.now(); // 更新最后活动时间

        // === 发送首帧 ===
        let firstAudioBase64 = '';
        if (session.audioQueue.length > 0) {
          const firstBuf = session.audioQueue.shift() as Buffer;
          firstAudioBase64 = firstBuf.toString('base64');
          session.queueDurationMs -= this.getBufferDurationMs(firstBuf);
          if (session.queueDurationMs < 0) session.queueDurationMs = 0;
        }

        this.sendFirstFrame(session, firstAudioBase64);

        // 设置心跳定时器
        this.startHeartbeat(session);

        // === flush 剩余队列 ===
        if (session.audioQueue.length > 0) {
          setTimeout(() => {
            console.log(`[${session.sessionId}] 🚚 Flushing queued audio (${session.audioQueue.length} chunks, ~${session.queueDurationMs.toFixed(0)}ms) after 120ms delay`);

            const queued = [...session.audioQueue];
            session.audioQueue = [];
            session.queueDurationMs = 0;

            queued.forEach(buf => {
              const b64 = buf.toString('base64');
              this.sendAudioFrame(session, b64);
            });
          }, 120);
        }
      });

      session.connection.on('message', (data) => {
        session.lastActivityTime = Date.now(); // 更新最后活动时间
        this.handleIATMessage(session, data);
      });

      session.connection.on('close', (code, reason) => {
        console.log(`[${session.sessionId}] IAT WebSocket closed. Code: ${code}, Reason: ${reason.toString()}`);

        // 增加一个错误对象来打印堆栈，帮助我们追溯调用来源
        console.error(new Error("Stack trace for IAT close event:"));

        clearTimeout(timeout);
        this.stopHeartbeat(session);
        session.isConnected = false;
        session.isConnecting = false;
        session.connection = null;
        
        // 尝试自动重连
        this.attemptReconnect(session);
      });

      session.connection.on('error', (error) => {
        console.error(`[${session.sessionId}] IAT WebSocket error:`, error);
        clearTimeout(timeout);
        this.stopHeartbeat(session);
        session.isConnected = false;
        session.isConnecting = false;
        session.connection = null;
        session.onError(error);
        
        // 尝试自动重连
        this.attemptReconnect(session);
      });

    } catch (error) {
      console.error(`[${session.sessionId}] Failed to create IAT connection:`, error);
      session.isConnecting = false;
      session.onError(error as Error);
      
      // 尝试自动重连
      this.attemptReconnect(session);
    }
  }
  
  /**
   * 尝试重新连接（改进版本）
   */
  private attemptReconnect(session: IATSession): void {
    if (session.reconnectAttempts >= this.MAX_RECONNECT_ATTEMPTS) {
      console.error(`[${session.sessionId}] Maximum reconnect attempts reached (${this.MAX_RECONNECT_ATTEMPTS})`);
      return;
    }

    // 🔥 修复：确保旧连接完全清理
    if (session.connection) {
      try {
        session.connection.close();
        session.connection = null;
      } catch (error) {
        console.error(`[${session.sessionId}] Error closing old connection:`, error);
      }
    }

    session.reconnectAttempts++;
    const delay = this.RECONNECT_DELAY * Math.pow(1.5, session.reconnectAttempts - 1); // 指数退避策略

    console.log(`[${session.sessionId}] Attempting to reconnect in ${delay}ms (attempt ${session.reconnectAttempts}/${this.MAX_RECONNECT_ATTEMPTS})`);

    setTimeout(() => {
      // 🔥 修复：添加更严格的状态检查
      if (!session.isConnected && !session.isConnecting && this.sessions.has(session.sessionId)) {
        console.log(`[${session.sessionId}] Executing reconnect attempt ${session.reconnectAttempts}`);
        this.connectToIAT(session);
      } else {
        console.log(`[${session.sessionId}] Skipping reconnect - session state changed`);
      }
    }, delay);
  }
  
  /**
   * 开始心跳
   */
  private startHeartbeat(session: IATSession): void {
    this.stopHeartbeat(session); // 先停止现有的心跳
    
    session.heartbeatInterval = setInterval(() => {
      // 检查连接状态
      if (!session.isConnected || !session.connection) {
        this.stopHeartbeat(session);
        return;
      }
      
      // 检查距离上次发送数据的时间
      const timeSinceLastSend = Date.now() - session.lastSendTime;
      const inactiveTime = Date.now() - session.lastActivityTime;
      
      // 如果超过8秒没有发送数据，发送心跳帧
      if (timeSinceLastSend > this.MAX_SILENCE_DURATION) {
        console.log(`[${session.sessionId}] 💓 Sending heartbeat frame after ${timeSinceLastSend}ms of inactivity`);
        
        // 发送心跳帧（使用CONTINUE_FRAME状态）
        try {
          const heartbeatFrame = {
            data: {
              status: this.FRAME_STATUS.STATUS_CONTINUE_FRAME,
              format: 'audio/L16;rate=16000',
              encoding: 'raw',
              audio: '' // 空音频数据
            }
          };
          
          session.connection.send(JSON.stringify(heartbeatFrame));
          session.lastSendTime = Date.now(); // 更新最后发送时间
          session.lastActivityTime = Date.now(); // 更新最后活动时间
          console.log(`[${session.sessionId}] ✅ Heartbeat frame sent successfully`);
        } catch (error) {
          console.error(`[${session.sessionId}] ❌ Failed to send heartbeat:`, error);
          
          // 如果发送心跳失败，尝试重新连接
          if (session.connection) {
            try {
              session.connection.close();
            } catch (closeError) {
              console.error(`[${session.sessionId}] Error closing connection after heartbeat failure:`, closeError);
            }
          }
        }
      }
      // 如果长时间无活动，主动重连
      else if (inactiveTime > this.INACTIVITY_TIMEOUT) {
        console.warn(`[${session.sessionId}] ⚠️ Connection inactive for ${inactiveTime}ms, forcing reconnection`);
        
        // 强制关闭当前连接
        if (session.connection) {
          try {
            session.connection.close();
          } catch (closeError) {
            console.error(`[${session.sessionId}] Error closing inactive connection:`, closeError);
          }
        }
        
        // 标记为未连接，触发重连
        session.isConnected = false;
        session.isConnecting = false;
        this.attemptReconnect(session);
      }

      // === Phase 2优化：智能静音检测和句子边界检测 ===
      const timeSinceVoice = Date.now() - (session.lastVoiceTime || 0);
      const currentText = session.stateManager.getCurrentText();

      // 使用智能边界检测器判断是否应该结束句子
      const shouldFinalize = session.boundaryDetector.shouldFinalizeSentence(
        currentText,
        timeSinceVoice,
        session.accumulatedText
      );

      if (session.firstFrameSent && session.frameStatus !== this.FRAME_STATUS.STATUS_LAST_FRAME && shouldFinalize) {
        console.log(`[${session.sessionId}] 🎯 Smart boundary detection triggered:`, {
          silenceDuration: timeSinceVoice,
          vadEos: session.vadEos,
          currentText: currentText,
          textLength: currentText.length
        });

        // 🔥 Phase 1&2：在发送结束帧前，先触发最终结果
        this.triggerFinalResult(session);

        this.sendEndFrame(session);
      }
    }, this.HEARTBEAT_INTERVAL); // 每5秒检查一次
    
    console.log(`[${session.sessionId}] 💓 Heartbeat monitoring started with interval ${this.HEARTBEAT_INTERVAL}ms`);
  }
  
  /**
   * 停止心跳
   */
  private stopHeartbeat(session: IATSession): void {
    if (session.heartbeatInterval) {
      clearInterval(session.heartbeatInterval);
      session.heartbeatInterval = null;
      console.log(`[${session.sessionId}] Heartbeat stopped`);
    }
  }

  /**
   * 生成IAT认证URL
   */
  private generateAuthUrl(): string {
    const host = 'iat-api.xfyun.cn';
    const path = '/v2/iat';
    const date = new Date().toUTCString();
    
    // 构建签名字符串
    const signatureOrigin = `host: ${host}\ndate: ${date}\nGET ${path} HTTP/1.1`;
    
    // 使用HMAC-SHA256计算签名
    const signature = crypto.createHmac('sha256', this.config.API_SECRET)
      .update(signatureOrigin)
      .digest('base64');
    
    // 构建authorization字符串
    const authorizationOrigin = `api_key="${this.config.API_KEY}", algorithm="hmac-sha256", headers="host date request-line", signature="${signature}"`;
    const authorization = Buffer.from(authorizationOrigin).toString('base64');
    
    // 构建完整的URL
    const params = new URLSearchParams({
      host: host,
      date: date,
      authorization: authorization
    });
    
    return `${this.config.HOST_URL}?${params.toString()}`;
  }

  /**
   * 发送首帧参数（增强版本，不依赖传入的音频数据）
   */
  private sendFirstFrame(session: IATSession, audioBase64: string = ''): void {
    const firstFrame = {
      common: {
        app_id: this.config.APPID
      },
      business: {
        language: 'zh_cn',
        domain: 'iat',
        accent: 'mandarin',
        vad_eos: session.vadEos, // 使用可配置的静音检测时间
        dwa: 'wpgs'              // 开启动态修正
      },
      data: {
        status: this.FRAME_STATUS.STATUS_FIRST_FRAME,
        format: 'audio/L16;rate=16000',
        encoding: 'raw',
        audio: audioBase64 // 首帧可携带首段音频
      }
    };

    // 🔥 新增：详细记录首帧信息
    console.log(`[${session.sessionId}] 🚀 Sending first frame:`, {
      appId: this.config.APPID,
      vadEos: session.vadEos,
      frameStatus: this.FRAME_STATUS.STATUS_FIRST_FRAME,
      timestamp: new Date().toISOString()
    });

    try {
      session.connection?.send(JSON.stringify(firstFrame));
      session.firstFrameSent = true; // 标记首帧已发送
      session.frameStatus = this.FRAME_STATUS.STATUS_CONTINUE_FRAME; // 更新帧状态
      session.lastSendTime = Date.now(); // 🔥 更新最后发送时间
      session.lastActivityTime = Date.now(); // 更新最后活动时间

      console.log(`[${session.sessionId}] ✅ First frame sent successfully`);
    } catch (error) {
      console.error(`[${session.sessionId}] ❌ Error sending first frame:`, error);
      throw error;
    }
  }

  /**
   * 处理IAT消息 - 增强日志版本
   */
  private handleIATMessage(session: IATSession, data: any): void {
    try {
      const response = JSON.parse(data.toString());

      // 🔥 新增：详细记录所有ASR响应
      console.log(`[${session.sessionId}] 📨 ASR Response:`, {
        code: response.code,
        message: response.message,
        sid: response.sid,
        dataStatus: response.data?.status,
        hasResult: !!response.data?.result,
        timestamp: new Date().toISOString()
      });

      if (response.code !== 0) {
        console.error(`[${session.sessionId}] ❌ IAT error details:`, {
          code: response.code,
          message: response.message,
          sid: response.sid,
          fullResponse: response
        });

        // 🔥 修复：对invalid handle错误进行特殊处理
        if (response.code === 10165) { // invalid handle
          console.log(`[${session.sessionId}] 🔄 Invalid handle detected, forcing session recreation`);
          // 强制清理当前会话
          this.forceCloseSession(session.sessionId);
          // 通知上层重新创建会话
          session.onError(new Error(`IAT error (${response.code}): ${response.message}`));
          return;
        }

        session.onError(new Error(`IAT error (${response.code}): ${response.message}`));
        return;
      }

      if (response.data && response.data.result) {
        const result = response.data.result;
        let text = '';

        // 🔥 新增：详细记录识别结果结构
        console.log(`[${session.sessionId}] 🔍 Recognition result structure:`, {
          status: response.data.status,
          resultExists: !!result,
          wsCount: result.ws ? result.ws.length : 0,
          rawResult: result
        });

        // 解析识别结果
        if (result.ws) {
          result.ws.forEach((ws: any, wsIndex: number) => {
            if (ws.cw) {
              ws.cw.forEach((cw: any, cwIndex: number) => {
                text += cw.w;
                // 🔥 新增：记录每个词的详细信息
                console.log(`[${session.sessionId}] 📝 Word[${wsIndex}][${cwIndex}]:`, {
                  word: cw.w,
                  confidence: cw.sc
                });
              });
            }
          });
        }

        // 🔥 新增：记录解析后的文本
        console.log(`[${session.sessionId}] 📄 Parsed text: "${text}" (length: ${text.length})`);

        if (text.trim()) {
          // 🔥 过滤纯标点符号的识别结果
          const isPurePunctuation = /^[。，、；：？！""''（）【】《》\s]*$/.test(text.trim());

          if (isPurePunctuation) {
            console.log(`[${session.sessionId}] ⚠️ Filtering out pure punctuation result: "${text}"`);
            return; // 直接返回，不处理纯标点符号
          }

          const isFinal = response.data.status === 2;
          console.log(`[${session.sessionId}] 🎯 Processing text: "${text}", isFinal: ${isFinal}`);
          // 新增：包含关键字 TranscriptionResult 以便快速检索
          console.log(`[${session.sessionId}] TranscriptionResult: "${text}" isFinal=${isFinal}`);

          // 🔥 Phase 1修复：始终更新累积文本状态，无论是否为最终结果
          session.accumulatedText = text;
          console.log(`[${session.sessionId}] 📝 Updated accumulatedText: "${session.accumulatedText}"`);

          // 🔥 Phase 2优化：同时更新状态管理器
          session.stateManager.updateText(text);
          console.log(`[${session.sessionId}] 🔧 StateManager updated:`, session.stateManager.getDebugInfo());

          // 🔥 Phase 3优化：记录句子开始时间（如果是新句子）
          if (!session.stateManager.hasNewContent()) {
            session.sentenceStartTime = Date.now();
          }

          if (isFinal) {
            // 🔥 Phase 3：计算并记录性能指标
            const processingLatency = Date.now() - session.sentenceStartTime;
            session.performanceMonitor.recordSentenceProcessed(processingLatency, true);

            console.log(`[${session.sessionId}] 📊 Performance metrics:`, {
              latency: processingLatency,
              report: session.performanceMonitor.getPerformanceReport()
            });

            // 最终结果，触发LLM处理
            console.log(`[${session.sessionId}] 🎯 Final result detected, accumulated text: "${session.accumulatedText}"`);
            console.log(`[${session.sessionId}] ✅ Final text: "${text}", accumulated: "${session.accumulatedText}", triggering LLM`);
            session.onLLMTrigger(session.accumulatedText.trim());
            session.accumulatedText = ''; // 清空累积文本
          }

          // 发送转录结果到前端
          session.onTranscription(text, isFinal);
        } else {
          console.log(`[${session.sessionId}] ⚠️ Empty text after parsing, skipping`);
        }
      } else {
        console.log(`[${session.sessionId}] ⚠️ No result data in response`);
      }

    } catch (error) {
      console.error(`[${session.sessionId}] Error parsing IAT response:`, error);
    }
  }

  /**
   * 发送音频数据 - 增强版本（使用帧状态）
   */
  async sendAudio(sessionId: string, audioBuffer: Buffer): Promise<void> {
    const session = this.sessions.get(sessionId);
    if (!session) {
      console.warn(`[${sessionId}] Session not found`);
      return;
    }

    // 🔥 新增：如果会话正在重建中，将音频数据加入待处理缓冲
    if (session.isRebuilding) {
      session.pendingAudioBuffer.push(audioBuffer);
      console.log(`[${sessionId}] 🔄 Session rebuilding, buffering audio (${session.pendingAudioBuffer.length} buffers)`);

      // 限制待处理缓冲区大小，避免内存溢出
      const maxPendingBuffers = 20; // 最多保留20个缓冲
      if (session.pendingAudioBuffer.length > maxPendingBuffers) {
        session.pendingAudioBuffer.shift(); // 移除最旧的缓冲
      }
      return;
    }

    // 🔥 新增：如果会话刚发送结束帧不久，也加入待处理缓冲
    const timeSinceEndFrame = Date.now() - session.lastEndFrameTime;
    if (session.lastEndFrameTime > 0 && timeSinceEndFrame < this.REBUILD_DELAY_MS + 200) {
      session.pendingAudioBuffer.push(audioBuffer);
      console.log(`[${sessionId}] ⏳ Recent end frame (${timeSinceEndFrame}ms ago), buffering audio`);
      return;
    }

    // 如果连接尚未就绪，则进入队列
    if (!session.isConnected || !session.connection) {
      session.audioQueue.push(audioBuffer);
      session.queueDurationMs += this.getBufferDurationMs(audioBuffer);

      // 超过最大缓冲时长则丢弃最旧数据
      while (session.queueDurationMs > this.MAX_QUEUE_DURATION_MS && session.audioQueue.length > 0) {
        const shifted = session.audioQueue.shift();
        if (shifted) {
          session.queueDurationMs -= this.getBufferDurationMs(shifted);
        }
      }

      console.log(`[${sessionId}] 🌀 Queueing audio chunk. queueLen=${session.audioQueue.length}, duration≈${session.queueDurationMs.toFixed(0)}ms`);
      return;
    }

    // 将音频数据转换为base64
    const audioBase64 = audioBuffer.toString('base64');
    
    // 如果首帧还未发送（虽然在连接建立时应该已发送，这是额外保障）
    if (!session.firstFrameSent) {
      console.log(`[${sessionId}] 🚀 First audio data received, but first frame not sent yet. Sending first frame now.`);
      this.sendFirstFrame(session, audioBase64);
      
      // 由于首帧没有包含音频，我们需要立即发送一个包含音频的后续帧
      setTimeout(() => {
        this.sendAudioFrame(session, audioBase64);
      }, 100);
      return;
    }

    // 发送音频帧
    this.sendAudioFrame(session, audioBase64);
  }
  
  /**
   * 发送音频帧 - 新方法，从sendAudio中提取出来
   */
  private sendAudioFrame(session: IATSession, audioBase64: string): void {
    // 检查连接状态
    if (!session.connection || !session.isConnected) {
      console.warn(`[${session.sessionId}] ⚠️ Cannot send audio frame: connection not ready`);
      return;
    }
    
    // 检查音频数据质量
    const audioBuffer = Buffer.from(audioBase64, 'base64');
    const audioValidation = this.validateAudioData(audioBuffer);
    const maxAmp = audioValidation.sampleInfo.maxAmplitude;
    const isValidAudio = audioValidation.isValid || maxAmp > 0;
    
    // 即使是静音数据，也发送（但记录警告）
    if (!isValidAudio) {
      console.warn(`[${session.sessionId}] ⚠️ Sending low quality audio data:`, audioValidation.errors);
    }

    // 音频数据帧（使用CONTINUE_FRAME状态）
    const audioFrame = {
      data: {
        status: this.FRAME_STATUS.STATUS_CONTINUE_FRAME,
        format: 'audio/L16;rate=16000',
        encoding: 'raw',
        audio: audioBase64
      }
    };

    try {
      console.log(`[${session.sessionId}] 📤 Sending audio frame:`, {
        frameSize: JSON.stringify(audioFrame).length,
        audioDataSize: audioBase64.length,
        status: this.FRAME_STATUS.STATUS_CONTINUE_FRAME,
        quality: isValidAudio ? 'normal' : 'low'
      });

      session.connection.send(JSON.stringify(audioFrame));
      session.lastSendTime = Date.now(); // 更新最后发送时间
      session.lastActivityTime = Date.now(); // 更新最后活动时间

      console.log(`[${session.sessionId}] ✅ Audio frame sent successfully`);

      // === 更新静音检测 ===
      if (maxAmp > this.SILENCE_THRESHOLD_AMPLITUDE) {
        session.lastVoiceTime = Date.now();
      }
    } catch (error) {
      console.error(`[${session.sessionId}] ❌ Error sending audio data:`, error);
      // 如果发送失败，尝试重连
      session.connection?.close();
    }
  }

  /**
   * 验证音频数据格式和质量
   */
  private validateAudioData(audioBuffer: Buffer): { isValid: boolean; errors: string[]; sampleInfo: any } {
    const errors: string[] = [];
    const sampleInfo: any = {};

    // 检查缓冲区大小
    if (!audioBuffer || audioBuffer.length === 0) {
      errors.push('Audio buffer is empty');
      return { isValid: false, errors, sampleInfo };
    }

    // 检查缓冲区大小是否合理（16位PCM，16kHz，单声道）
    const expectedSampleSize = 2; // 16位 = 2字节
    if (audioBuffer.length % expectedSampleSize !== 0) {
      errors.push(`Audio buffer size (${audioBuffer.length}) is not aligned to sample size (${expectedSampleSize})`);
    }

    // 计算样本数和时长
    const sampleCount = audioBuffer.length / expectedSampleSize;
    const durationMs = (sampleCount / 16000) * 1000; // 16kHz采样率

    sampleInfo.bufferSize = audioBuffer.length;
    sampleInfo.sampleCount = sampleCount;
    sampleInfo.durationMs = durationMs;
    sampleInfo.sampleRate = 16000;
    sampleInfo.channels = 1;
    sampleInfo.bitDepth = 16;

    // 检查时长是否合理（不应该太短或太长）
    if (durationMs < 1) {
      errors.push(`Audio duration too short: ${durationMs}ms`);
    } else if (durationMs > 5000) {
      errors.push(`Audio duration too long: ${durationMs}ms`);
    }

    // 检查音频数据是否全为零（静音）
    let isAllZero = true;
    let maxAmplitude = 0;
    for (let i = 0; i < audioBuffer.length; i += 2) {
      const sample = audioBuffer.readInt16LE(i);
      if (sample !== 0) {
        isAllZero = false;
      }
      maxAmplitude = Math.max(maxAmplitude, Math.abs(sample));
    }

    sampleInfo.isAllZero = isAllZero;
    sampleInfo.maxAmplitude = maxAmplitude;
    sampleInfo.amplitudeRatio = maxAmplitude / 32767; // 16位最大值

    if (isAllZero) {
      errors.push('Audio data is all zeros (complete silence)');
    } else if (maxAmplitude < 100) {
      errors.push(`Audio amplitude too low: ${maxAmplitude} (ratio: ${sampleInfo.amplitudeRatio.toFixed(4)})`);
    }

    // 如果有错误但不是致命的，仍然允许发送（只记录警告）
    const fatalErrors = errors.filter(error =>
      error.includes('empty') ||
      error.includes('all zeros') ||
      error.includes('not aligned')
    );

    return {
      isValid: fatalErrors.length === 0,
      errors: fatalErrors.length > 0 ? fatalErrors : errors,
      sampleInfo
    };
  }

  /**
   * 强制关闭会话（用于invalid handle错误）
   */
  private forceCloseSession(sessionId: string): void {
    const session = this.sessions.get(sessionId);
    if (!session) {
      console.warn(`[${sessionId}] Session not found for force closing`);
      return;
    }

    console.log(`[${sessionId}] Force closing IAT session`);

    // 停止心跳
    this.stopHeartbeat(session);

    // 强制关闭连接，不发送结束帧
    if (session.connection) {
      try {
        session.connection.close();
      } catch (error) {
        console.error(`[${sessionId}] Error force closing connection:`, error);
      }
      session.connection = null;
    }

    // 重置会话状态 & 清空队列
    session.isConnected = false;
    session.isConnecting = false;
    session.reconnectAttempts = 0;
    session.firstFrameSent = false; // 🔥 重置首帧状态
    session.audioQueue = [];
    session.queueDurationMs = 0;

    this.sessions.delete(sessionId);
  }

  /**
   * 关闭会话 - 增强版本
   */
  async closeSession(sessionId: string): Promise<void> {
    const session = this.sessions.get(sessionId);
    if (!session) {
      console.warn(`[${sessionId}] Session not found for closing`);
      return;
    }

    console.log(`[${sessionId}] Closing IAT session`);

    // 停止心跳
    this.stopHeartbeat(session);

    // 发送结束帧
    if (session.isConnected && session.connection) {
      const endFrame = {
        data: {
          status: this.FRAME_STATUS.STATUS_LAST_FRAME,
          format: 'audio/L16;rate=16000',
          encoding: 'raw',
          audio: ''
        }
      };
      try {
        console.log(`[${sessionId}] 📤 Sending end frame with status ${this.FRAME_STATUS.STATUS_LAST_FRAME}`);
        session.connection.send(JSON.stringify(endFrame));
        // 更新状态
        session.frameStatus = this.FRAME_STATUS.STATUS_LAST_FRAME;
        session.lastSendTime = Date.now();
        // 等待一小段时间让结束帧发送完成
        await new Promise(resolve => setTimeout(resolve, 200));
      } catch (error) {
        console.error(`[${sessionId}] ❌ Error sending end frame:`, error);
      }
    }

    // 关闭连接
    if (session.connection) {
      try {
        session.connection.close();
      } catch (error) {
        console.error(`[${sessionId}] ❌ Error closing connection:`, error);
      }
      session.connection = null;
    }

    // 重置会话状态 & 清空队列
    session.isConnected = false;
    session.isConnecting = false;
    session.firstFrameSent = false; 
    session.frameStatus = this.FRAME_STATUS.STATUS_FIRST_FRAME;
    session.lastSendTime = 0;
    session.audioQueue = [];
    session.queueDurationMs = 0;

    this.sessions.delete(sessionId);
    console.log(`[${sessionId}] ✅ Session closed and removed`);
  }

  /**
   * 获取会话状态
   */
  getSessionStatus(sessionId: string): { isConnected: boolean; accumulatedText: string } | null {
    const session = this.sessions.get(sessionId);
    if (!session) return null;

    return {
      isConnected: session.isConnected,
      accumulatedText: session.accumulatedText
    };
  }

  /**
   * 销毁管理器
   */
  async destroy(): Promise<void> {
    console.log('Destroying SessionIATManager');
    
    const sessionIds = Array.from(this.sessions.keys());
    await Promise.all(sessionIds.map(id => this.closeSession(id)));
    
    this.sessions.clear();
  }

  /**
   * 🔥 Phase 2优化：智能触发最终结果处理
   */
  private triggerFinalResult(session: IATSession): void {
    // Phase 1: 获取当前累积的文本作为最终结果
    const finalText = session.accumulatedText || '';

    // Phase 2: 同时从状态管理器获取文本
    const stateManagerText = session.stateManager.getCurrentText();

    console.log(`[${session.sessionId}] 🔍 triggerFinalResult called:`, {
      accumulatedText: finalText,
      stateManagerText: stateManagerText,
      stateManagerDebug: session.stateManager.getDebugInfo()
    });

    // Phase 2: 优先使用状态管理器的文本，如果为空则使用累积文本
    const textToFinalize = stateManagerText || finalText;

    if (textToFinalize.trim().length > 0) {
      console.log(`[${session.sessionId}] 🎯 Triggering final result: "${textToFinalize}"`);

      // 调用最终结果回调，设置isFinal=true
      if (session.onTranscription) {
        session.onTranscription(textToFinalize, true); // isFinal = true
        console.log(`[${session.sessionId}] ✅ Final result sent to frontend: "${textToFinalize}"`);
      } else {
        console.log(`[${session.sessionId}] ⚠️ No onTranscription callback available`);
      }

      // Phase 2: 使用状态管理器标记为最终并清空
      session.stateManager.markAsFinal();

      // Phase 1: 同时清空累积文本，准备下一句话
      session.accumulatedText = '';
      console.log(`[${session.sessionId}] 🔄 Cleared all text states for next sentence`);
    } else {
      console.log(`[${session.sessionId}] ⚠️ No text to finalize (empty or whitespace only)`);
      console.log(`[${session.sessionId}] 🔍 Debug:`, {
        accumulatedText: session.accumulatedText,
        accumulatedLength: session.accumulatedText?.length || 0,
        stateManagerDebug: session.stateManager.getDebugInfo()
      });
    }
  }

  /**
   * 发送结束帧（status=2），并准备自动重建会话
   */
  private sendEndFrame(session: IATSession): void {
    if (!session.connection || !session.isConnected) return;

    const endFrame = {
      data: {
        status: this.FRAME_STATUS.STATUS_LAST_FRAME,
        format: 'audio/L16;rate=16000',
        encoding: 'raw',
        audio: ''
      }
    };

    try {
      session.connection.send(JSON.stringify(endFrame));
      console.log(`[${session.sessionId}] 📤 End frame sent`);
      session.frameStatus = this.FRAME_STATUS.STATUS_LAST_FRAME;
      session.lastSendTime = Date.now();
      session.lastEndFrameTime = Date.now(); // 🔥 记录结束帧时间

      // 🔥 新增：启动自动重建逻辑
      if (session.autoRebuildEnabled && session.rebuildAttempts < this.MAX_REBUILD_ATTEMPTS) {
        console.log(`[${session.sessionId}] 🔄 Scheduling session rebuild in ${this.REBUILD_DELAY_MS}ms (attempt ${session.rebuildAttempts + 1}/${this.MAX_REBUILD_ATTEMPTS})`);

        // 延迟重建，给当前会话一些时间处理最终结果
        setTimeout(() => {
          this.rebuildSession(session);
        }, this.REBUILD_DELAY_MS);
      } else {
        console.log(`[${session.sessionId}] ⚠️ Auto-rebuild disabled or max attempts reached (${session.rebuildAttempts}/${this.MAX_REBUILD_ATTEMPTS})`);
      }

    } catch (err) {
      console.error(`[${session.sessionId}] ❌ Failed to send end frame:`, err);
    }
  }

  /**
   * 🔥 新增：重建会话方法
   */
  private async rebuildSession(session: IATSession): Promise<void> {
    if (session.isRebuilding) {
      console.log(`[${session.sessionId}] ⚠️ Session is already rebuilding, skipping`);
      return;
    }

    session.isRebuilding = true;
    session.rebuildAttempts++;

    console.log(`[${session.sessionId}] 🔄 Starting session rebuild (attempt ${session.rebuildAttempts}/${this.MAX_REBUILD_ATTEMPTS})`);

    try {
      // 保存当前会话的回调函数
      const { onTranscription, onLLMTrigger, onError, vadEos } = session;

      // 强制关闭当前连接
      if (session.connection) {
        try {
          session.connection.close();
        } catch (error) {
          console.error(`[${session.sessionId}] Error closing connection during rebuild:`, error);
        }
        session.connection = null;
      }

      // 重置会话状态
      session.isConnected = false;
      session.isConnecting = false;
      session.firstFrameSent = false;
      session.frameStatus = this.FRAME_STATUS.STATUS_FIRST_FRAME;
      session.lastSendTime = 0;
      session.lastActivityTime = Date.now();
      session.lastVoiceTime = 0;

      // 停止心跳
      this.stopHeartbeat(session);

      console.log(`[${session.sessionId}] 🔄 Reconnecting to IAT service for rebuild`);

      // 重新连接
      await this.connectToIAT(session);

      // 如果有待处理的音频数据，重新发送
      if (session.pendingAudioBuffer.length > 0) {
        console.log(`[${session.sessionId}] 🔄 Resending ${session.pendingAudioBuffer.length} pending audio buffers`);

        // 延迟一点时间确保连接稳定
        setTimeout(() => {
          const pendingBuffers = [...session.pendingAudioBuffer];
          session.pendingAudioBuffer = []; // 清空待处理缓冲

          pendingBuffers.forEach((buffer, index) => {
            setTimeout(() => {
              this.sendAudio(session.sessionId, buffer);
            }, index * 50); // 每50ms发送一个缓冲
          });
        }, 200);
      }

      session.isRebuilding = false;
      console.log(`[${session.sessionId}] ✅ Session rebuild completed successfully`);

    } catch (error) {
      console.error(`[${session.sessionId}] ❌ Session rebuild failed:`, error);
      session.isRebuilding = false;

      // 如果重建失败且还有尝试次数，再次尝试
      if (session.rebuildAttempts < this.MAX_REBUILD_ATTEMPTS) {
        console.log(`[${session.sessionId}] 🔄 Retrying rebuild in ${this.REBUILD_DELAY_MS * 2}ms`);
        setTimeout(() => {
          this.rebuildSession(session);
        }, this.REBUILD_DELAY_MS * 2);
      } else {
        console.error(`[${session.sessionId}] ❌ Max rebuild attempts reached, giving up`);
        session.onError(new Error('Session rebuild failed after maximum attempts'));
      }
    }
  }
}
