import express, { Request, Response } from 'express';
import { authenticateToken, AuthenticatedRequest } from '../middleware/auth';
import prisma from '../lib/prisma';

const router = express.Router();

// 消费类型枚举
export enum UsageType {
  MOCK_INTERVIEW = 'MOCK_INTERVIEW',
  FORMAL_INTERVIEW = 'FORMAL_INTERVIEW',
  CREDIT_RECHARGE = 'CREDIT_RECHARGE'
}

/**
 * 获取用户消费记录列表
 */
router.get('/', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user!.userId;
    const {
      page = 1,
      pageSize = 10,
      type
    } = req.query;

    const skip = (Number(page) - 1) * Number(pageSize);
    
    // 构建查询条件
    const where: any = { userId: userId };
    
    if (type && type !== 'all') {
      where.type = type;
    }

    // 获取总数和数据
    const [total, records] = await Promise.all([
      prisma.usageRecord.count({ where }),
      prisma.usageRecord.findMany({
        where,
        skip,
        take: Number(pageSize),
        orderBy: { createdAt: 'desc' },
        select: {
          id: true,
          type: true,
          amount: true,
          reason: true,
          createdAt: true,
        }
      })
    ]);

    // 格式化返回数据
    const formattedRecords = records.map(record => ({
      id: record.id,
      date: record.createdAt.toISOString(),
      type: record.type.toLowerCase(),
      amount: record.amount,
      reason: record.reason
    }));

    return res.status(200).json({
      success: true,
      records: formattedRecords,
      total
    });

  } catch (error: any) {
    console.error('获取消费记录失败:', error);
    return res.status(500).json({ 
      success: false,
      message: '获取消费记录失败，请稍后再试' 
    });
  }
});

/**
 * 创建消费记录
 */
router.post('/', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user!.userId;
    const { type, amount, reason } = req.body;

    // 验证必填字段
    if (!type || amount === undefined || !reason) {
      return res.status(400).json({ 
        success: false,
        message: '缺少必要的消费记录信息' 
      });
    }

    // 验证消费类型
    if (!Object.values(UsageType).includes(type)) {
      return res.status(400).json({ 
        success: false,
        message: '无效的消费类型' 
      });
    }

    // 创建消费记录
    const record = await prisma.usageRecord.create({
      data: {
        userId: userId,
        type: type,
        amount: Number(amount),
        reason: reason,
      },
      select: {
        id: true,
        type: true,
        amount: true,
        reason: true,
        createdAt: true,
      }
    });

    return res.status(201).json({
      success: true,
      message: '消费记录创建成功',
      record: {
        id: record.id,
        date: record.createdAt.toISOString(),
        type: record.type.toLowerCase(),
        amount: record.amount,
        reason: record.reason
      }
    });

  } catch (error: any) {
    console.error('创建消费记录失败:', error);
    return res.status(500).json({ 
      success: false,
      message: '创建消费记录失败，请稍后再试' 
    });
  }
});

// 扣费并创建消费记录的组合函数
export const deductCreditsAndCreateRecord = async (
  userId: string,
  type: 'mock' | 'formal',
  reason: string
): Promise<{ success: boolean; message: string; newBalance?: any }> => {
  try {
    // 获取用户当前余额
    let userBalance = await prisma.userBalance.findUnique({
      where: { userId: userId },
    });

    // 如果用户余额记录不存在，创建一个默认记录
    if (!userBalance) {
      userBalance = await prisma.userBalance.create({
        data: {
          userId: userId,
          mockInterviewCredits: 2, // 新用户默认2次模拟面试
          formalInterviewCredits: 0, // 新用户默认0次正式面试
          mianshijunBalance: 0,
        },
      });
    }

    // 检查余额是否足够
    const fieldName = type === 'mock' ? 'mockInterviewCredits' : 'formalInterviewCredits';
    const currentCredits = userBalance[fieldName];
    
    if (currentCredits <= 0) {
      return {
        success: false,
        message: '您的面试机会已用完，可以考虑充值获取更多次数哦'
      };
    }

    // 使用优化的事务进行扣费和记录创建
    const result = await prisma.$transaction(async (tx) => {
      // 使用乐观锁进行扣费，减少锁定时间
      const currentBalance = await tx.userBalance.findUnique({
        where: { userId: userId },
        select: { [fieldName]: true, updatedAt: true }
      });

      if (!currentBalance || (currentBalance as any)[fieldName] <= 0) {
        throw new Error(`余额不足，无法进行${type === 'mock' ? '模拟' : '正式'}面试`);
      }

      // 扣费
      const updatedBalance = await tx.userBalance.update({
        where: {
          userId: userId,
          updatedAt: (currentBalance as any).updatedAt // 乐观锁
        },
        data: {
          [fieldName]: {
            decrement: 1
          }
        }
      });

      // 异步创建消费记录（不阻塞主流程）
      setImmediate(async () => {
        try {
          await prisma.usageRecord.create({
            data: {
              userId: userId,
              type: type === 'mock' ? UsageType.MOCK_INTERVIEW : UsageType.FORMAL_INTERVIEW,
              amount: -1, // 负数表示消耗
              reason: reason,
            }
          });
        } catch (error) {
          console.error('创建使用记录失败:', error);
          // 记录失败不影响主流程
        }
      });

      return updatedBalance;
    }, {
      timeout: 5000, // 5秒超时
      isolationLevel: 'ReadCommitted' // 使用读已提交隔离级别
    });

    return {
      success: true,
      message: '准备就绪！即将开始面试...',
      newBalance: result
    };
  } catch (error) {
    console.error('面试启动失败:', error);
    return {
      success: false,
      message: '网络状况不佳，请您再试一次或稍后重试'
    };
  }
};

export default router;
