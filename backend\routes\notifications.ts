import express, { Request, Response } from 'express';
import { authenticateToken, AuthenticatedRequest } from '../middleware/auth';
import prisma from '../lib/prisma';

const router = express.Router();

/**
 * 获取用户通知列表
 */
router.get('/', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user!.userId;
    const { page = '1', limit = '10', unreadOnly = 'false' } = req.query;
    
    const pageNum = parseInt(page as string);
    const limitNum = parseInt(limit as string);
    const skip = (pageNum - 1) * limitNum;

    const where: any = {
      userId,
      notification: {
        status: 'PUBLISHED',
        OR: [
          { expiresAt: null },
          { expiresAt: { gt: new Date() } }
        ]
      }
    };

    if (unreadOnly === 'true') {
      where.isRead = false;
    }

    const [userNotifications, total] = await Promise.all([
      prisma.userNotification.findMany({
        where,
        include: {
          notification: {
            select: {
              id: true,
              title: true,
              content: true,
              type: true,
              priority: true,
              publishedAt: true,
              expiresAt: true
            }
          }
        },
        orderBy: [
          { isRead: 'asc' }, // 未读在前
          { notification: { publishedAt: 'desc' } } // 最新发布在前
        ],
        skip,
        take: limitNum
      }),
      prisma.userNotification.count({ where })
    ]);

    return res.json({
      success: true,
      data: {
        notifications: userNotifications,
        pagination: {
          page: pageNum,
          limit: limitNum,
          total,
          pages: Math.ceil(total / limitNum)
        }
      }
    });

  } catch (error: any) {
    console.error('获取用户通知失败:', error);
    return res.status(500).json({
      success: false,
      message: '获取通知失败'
    });
  }
});

/**
 * 获取未读通知数量
 */
router.get('/unread-count', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user!.userId;

    const unreadCount = await prisma.userNotification.count({
      where: {
        userId,
        isRead: false,
        notification: {
          status: 'PUBLISHED',
          OR: [
            { expiresAt: null },
            { expiresAt: { gt: new Date() } }
          ]
        }
      }
    });

    return res.json({
      success: true,
      data: { unreadCount }
    });

  } catch (error: any) {
    console.error('获取未读通知数量失败:', error);
    return res.status(500).json({
      success: false,
      message: '获取未读通知数量失败'
    });
  }
});

/**
 * 标记通知为已读
 */
router.put('/:id/read', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user!.userId;
    const { id } = req.params;

    const userNotification = await prisma.userNotification.update({
      where: {
        id,
        userId // 确保只能操作自己的通知
      },
      data: {
        isRead: true,
        readAt: new Date()
      }
    });

    return res.json({
      success: true,
      message: '通知已标记为已读',
      data: { userNotification }
    });

  } catch (error: any) {
    console.error('标记通知已读失败:', error);
    return res.status(500).json({
      success: false,
      message: '操作失败'
    });
  }
});

/**
 * 标记所有通知为已读
 */
router.put('/read-all', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user!.userId;

    const result = await prisma.userNotification.updateMany({
      where: {
        userId,
        isRead: false
      },
      data: {
        isRead: true,
        readAt: new Date()
      }
    });

    return res.json({
      success: true,
      message: `已标记 ${result.count} 条通知为已读`,
      data: { updatedCount: result.count }
    });

  } catch (error: any) {
    console.error('标记所有通知已读失败:', error);
    return res.status(500).json({
      success: false,
      message: '操作失败'
    });
  }
});

/**
 * 获取通知详情
 */
router.get('/:id', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user!.userId;
    const { id } = req.params;

    const userNotification = await prisma.userNotification.findFirst({
      where: {
        id,
        userId
      },
      include: {
        notification: true
      }
    });

    if (!userNotification) {
      return res.status(404).json({
        success: false,
        message: '通知不存在'
      });
    }

    // 如果是未读通知，自动标记为已读
    if (!userNotification.isRead) {
      await prisma.userNotification.update({
        where: { id },
        data: {
          isRead: true,
          readAt: new Date()
        }
      });
    }

    return res.json({
      success: true,
      data: { notification: userNotification }
    });

  } catch (error: any) {
    console.error('获取通知详情失败:', error);
    return res.status(500).json({
      success: false,
      message: '获取通知详情失败'
    });
  }
});

export default router;
