import React, { useRef, useState } from 'react';
import { FileText, FileSpreadsheet, File as FileIcon, Upload } from 'lucide-react';

interface FileUploadProps {
  file: File | null;
  onChange: (file: File | null) => void;
}

const FileUpload: React.FC<FileUploadProps> = ({ file, onChange }) => {
  const [isDragging, setIsDragging] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleDragEnter = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const validateFile = (file: File): boolean => {
    const allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'text/plain'];
    const maxSize = 10 * 1024 * 1024; // 10MB
    
    if (!allowedTypes.includes(file.type)) {
      setError('文件格式不支持，请上传 PDF、DOCX 或 TXT 格式的文件');
      return false;
    }
    
    if (file.size > maxSize) {
      setError('文件大小超过 10MB 限制');
      return false;
    }
    
    setError(null);
    return true;
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      const newFile = e.dataTransfer.files[0];
      if (validateFile(newFile)) {
        onChange(newFile);
      }
    }
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const newFile = e.target.files[0];
      if (validateFile(newFile)) {
        onChange(newFile);
      }
    }
  };

  const handleButtonClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const renderFileIcon = (fileType: string) => {
    if (fileType.includes('pdf')) {
      return <FileText size={28} className="text-red-500" />;
    } else if (fileType.includes('word') || fileType.includes('docx')) {
      return <FileSpreadsheet size={28} className="text-blue-500" />;
    } else {
      return <FileIcon size={28} className="text-gray-500" />;
    }
  };

  return (
    <div>
      <div
        className={`upload-area border-2 border-dashed rounded-lg flex flex-col items-center justify-center p-10 h-64 cursor-pointer transition-all ${
          isDragging ? 'border-emerald-500 bg-emerald-50' : 'border-gray-300'
        } ${file ? 'bg-emerald-50' : 'bg-gray-50 hover:bg-gray-100'}`}
        onDragEnter={handleDragEnter}
        onDragLeave={handleDragLeave}
        onDragOver={handleDragOver}
        onDrop={handleDrop}
        onClick={handleButtonClick}
      >
        <input
          type="file"
          ref={fileInputRef}
          className="hidden"
          accept=".pdf,.docx,.doc,.txt"
          onChange={handleFileInputChange}
        />
        
        {file ? (
          <div className="text-center">
            {renderFileIcon(file.type)}
            <p className="mt-2 font-medium text-gray-700">{file.name}</p>
            <p className="text-sm text-gray-500">
              {(file.size / 1024 / 1024).toFixed(2)} MB
            </p>
            <button 
              className="mt-4 text-emerald-600 hover:text-emerald-800 text-sm"
              onClick={(e) => {
                e.stopPropagation();
                onChange(null);
              }}
            >
              移除文件
            </button>
          </div>
        ) : (
          <>
            <div className="flex space-x-4 mb-3">
              <FileText size={28} className="text-red-500" />
              <FileSpreadsheet size={28} className="text-blue-500" />
              <FileIcon size={28} className="text-yellow-500" />
            </div>
            <div className="p-3 rounded-full bg-emerald-100 mb-3">
              <Upload size={24} className="text-emerald-600" />
            </div>
            <p className="text-gray-700 mb-1">将文件拖放到此处，或点击上传</p>
            <p className="text-sm text-gray-500">支持 pdf、docx、txt，最大不超过10mb</p>
          </>
        )}
      </div>
      
      {error && (
        <p className="text-red-500 mt-2 text-sm">{error}</p>
      )}
    </div>
  );
};

export default FileUpload;
