import { PrismaClient } from '@prisma/client';
import RedisService from './redisService';
import EmailService from './emailService';

interface AlertConfig {
  emailFailureThreshold: number; // 邮件发送失败阈值
  smsFailureThreshold: number;   // 短信发送失败阈值
  timeWindow: number;            // 时间窗口（分钟）
  alertEmail: string;            // 告警邮箱
}

interface SystemHealth {
  redis: boolean;
  database: boolean;
  email: boolean;
  sms: boolean;
  timestamp: number;
}

export class MonitoringService {
  private prisma: PrismaClient;
  private redis: RedisService;
  private emailService: EmailService;
  private alertConfig: AlertConfig;

  constructor() {
    this.prisma = new PrismaClient();
    this.redis = RedisService.getInstance();
    this.emailService = new EmailService();
    this.alertConfig = {
      emailFailureThreshold: 5,
      smsFailureThreshold: 5,
      timeWindow: 10, // 10分钟
      alertEmail: process.env.ALERT_EMAIL || '<EMAIL>'
    };
  }

  /**
   * 记录发送失败事件
   */
  async recordFailure(type: 'EMAIL' | 'SMS', error: string, identifier?: string): Promise<void> {
    const key = `failure:${type.toLowerCase()}:${Date.now()}`;
    const failureData = {
      type,
      error,
      identifier,
      timestamp: Date.now()
    };

    try {
      // 存储到Redis，保留24小时
      await this.redis.set(key, JSON.stringify(failureData), 24 * 60 * 60);
      
      // 检查是否需要发送告警
      await this.checkAndSendAlert(type);
    } catch (error) {
      console.error('Failed to record failure:', error);
    }
  }

  /**
   * 检查并发送告警
   */
  private async checkAndSendAlert(type: 'EMAIL' | 'SMS'): Promise<void> {
    const threshold = type === 'EMAIL' ? 
      this.alertConfig.emailFailureThreshold : 
      this.alertConfig.smsFailureThreshold;

    const timeWindow = this.alertConfig.timeWindow * 60 * 1000; // 转换为毫秒
    const now = Date.now();
    const windowStart = now - timeWindow;

    try {
      // 获取时间窗口内的失败记录
      const pattern = `failure:${type.toLowerCase()}:*`;
      const keys = await this.redis.keys(pattern);
      
      let failureCount = 0;
      for (const key of keys) {
        const data = await this.redis.get(key);
        if (data) {
          const failure = JSON.parse(data);
          if (failure.timestamp >= windowStart) {
            failureCount++;
          }
        }
      }

      // 如果失败次数超过阈值，发送告警
      if (failureCount >= threshold) {
        await this.sendAlert(type, failureCount, this.alertConfig.timeWindow);
        
        // 设置告警冷却期，避免重复告警
        const cooldownKey = `alert_cooldown:${type.toLowerCase()}`;
        await this.redis.set(cooldownKey, 'true', 30 * 60); // 30分钟冷却期
      }
    } catch (error) {
      console.error('Failed to check alert conditions:', error);
    }
  }

  /**
   * 发送告警邮件
   */
  private async sendAlert(type: 'EMAIL' | 'SMS', failureCount: number, timeWindow: number): Promise<void> {
    // 检查是否在冷却期
    const cooldownKey = `alert_cooldown:${type.toLowerCase()}`;
    const inCooldown = await this.redis.get(cooldownKey);
    if (inCooldown) {
      return;
    }

    const subject = `🚨 面试君系统告警 - ${type === 'EMAIL' ? '邮件' : '短信'}服务异常`;
    const content = `
      <h2>系统告警通知</h2>
      <p><strong>告警时间:</strong> ${new Date().toLocaleString('zh-CN')}</p>
      <p><strong>告警类型:</strong> ${type === 'EMAIL' ? '邮件发送' : '短信发送'}服务异常</p>
      <p><strong>失败次数:</strong> ${failureCount} 次</p>
      <p><strong>时间窗口:</strong> ${timeWindow} 分钟</p>
      <p><strong>建议操作:</strong></p>
      <ul>
        <li>检查${type === 'EMAIL' ? '邮件' : '短信'}服务配置</li>
        <li>验证API密钥是否有效</li>
        <li>检查网络连接状态</li>
        <li>查看详细错误日志</li>
      </ul>
      <p>请及时处理以确保用户正常使用验证码功能。</p>
    `;

    try {
      // 使用内部邮件服务发送告警（如果邮件服务正常）
      if (type !== 'EMAIL') {
        await this.emailService.sendVerificationCode(this.alertConfig.alertEmail, content);
      }
      console.log(`Alert sent for ${type} service failures`);
    } catch (error) {
      console.error('Failed to send alert email:', error);
      // 如果邮件发送失败，记录到日志文件或其他告警渠道
    }
  }

  /**
   * 系统健康检查
   */
  async healthCheck(): Promise<SystemHealth> {
    const health: SystemHealth = {
      redis: false,
      database: false,
      email: false,
      sms: false,
      timestamp: Date.now()
    };

    // 检查Redis连接
    try {
      await this.redis.ping();
      health.redis = true;
    } catch (error) {
      console.error('Redis health check failed:', error);
    }

    // 检查数据库连接
    try {
      await this.prisma.$queryRaw`SELECT 1`;
      health.database = true;
    } catch (error) {
      console.error('Database health check failed:', error);
    }

    // 检查邮件服务
    try {
      health.email = await this.emailService.testConnection();
    } catch (error) {
      console.error('Email service health check failed:', error);
    }

    // 检查短信服务
    try {
      const SmsService = (await import('./smsService')).default;
      const smsService = new SmsService();
      health.sms = await smsService.testConnection();
    } catch (error) {
      console.error('SMS service health check failed:', error);
    }

    return health;
  }

  /**
   * 获取系统统计信息
   */
  async getSystemStats(): Promise<any> {
    try {
      const now = Date.now();
      const last24h = now - 24 * 60 * 60 * 1000;

      // 安全地获取最近24小时的发送统计
      let emailSent = 0, smsSent = 0, emailFailed = 0, smsFailed = 0;

      try {
        emailSent = await this.prisma.verificationLog.count({
          where: {
            type: 'EMAIL',
            status: 'SUCCESS',
            createdAt: {
              gte: new Date(last24h)
            }
          }
        });

        smsSent = await this.prisma.verificationLog.count({
          where: {
            type: 'SMS',
            status: 'SUCCESS',
            createdAt: {
              gte: new Date(last24h)
            }
          }
        });

        emailFailed = await this.prisma.verificationLog.count({
          where: {
            type: 'EMAIL',
            status: 'FAILED',
            createdAt: {
              gte: new Date(last24h)
            }
          }
        });

        smsFailed = await this.prisma.verificationLog.count({
          where: {
            type: 'SMS',
            status: 'FAILED',
            createdAt: {
              gte: new Date(last24h)
            }
          }
        });
      } catch (dbError) {
        console.warn('Database query failed, using default values:', dbError);
        // 使用默认值，不影响整体功能
      }

      return {
        last24Hours: {
          email: {
            sent: emailSent,
            failed: emailFailed,
            successRate: emailSent + emailFailed > 0 ? (emailSent / (emailSent + emailFailed) * 100).toFixed(2) : '100.00'
          },
          sms: {
            sent: smsSent,
            failed: smsFailed,
            successRate: smsSent + smsFailed > 0 ? (smsSent / (smsSent + smsFailed) * 100).toFixed(2) : '100.00'
          }
        },
        timestamp: now
      };
    } catch (error) {
      console.error('Failed to get system stats:', error);
      return {
        last24Hours: {
          email: { sent: 0, failed: 0, successRate: '100.00' },
          sms: { sent: 0, failed: 0, successRate: '100.00' }
        },
        timestamp: Date.now()
      };
    }
  }
}

export default MonitoringService;
