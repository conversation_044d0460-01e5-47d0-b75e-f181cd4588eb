import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { targetPositionService, TargetPosition, TargetPositionCreateInput, TargetPositionUpdateInput } from '../lib/api/apiService';
import { queryKeys, optimisticUpdates, handleQueryError } from '../lib/queryClient';
import { useToast } from './useToast';

// 获取意向岗位列表的Hook
export const usePositionsQuery = (options?: {
  enabled?: boolean;
  staleTime?: number;
  refetchInterval?: number;
}) => {
  const { showError } = useToast();
  
  return useQuery({
    queryKey: queryKeys.positions.list(),
    queryFn: async () => {
      const startTime = Date.now();
      try {
        console.log('🔍 React Query: Fetching positions from API');
        const positions = await targetPositionService.getTargetPositions();
        
        // 性能监控
        const duration = Date.now() - startTime;
        console.log(`✅ React Query: Positions fetched in ${duration}ms`);
        
        return positions;
      } catch (error) {
        handleQueryError(error, 'usePositionsQuery');
        throw error;
      }
    },
    staleTime: options?.staleTime ?? 5 * 60 * 1000, // 5分钟
    enabled: options?.enabled ?? true,
    refetchInterval: options?.refetchInterval,
    
    // 错误处理
    onError: (error: any) => {
      console.error('❌ React Query: Failed to fetch positions', error);
      if (!error?.message?.includes('Unauthorized')) {
        showError('获取岗位列表失败，请稍后重试');
      }
    },
    
    // 成功回调
    onSuccess: (data) => {
      console.log(`✅ React Query: Successfully loaded ${data.length} positions`);
    },
  });
};

// 创建意向岗位的Mutation Hook
export const useCreatePositionMutation = () => {
  const queryClient = useQueryClient();
  const { showSuccess, showError } = useToast();
  
  return useMutation({
    mutationFn: async (data: TargetPositionCreateInput) => {
      console.log('🔄 React Query: Creating position', data);
      return await targetPositionService.createTargetPosition(data);
    },
    
    // 乐观更新
    onMutate: async (newPosition) => {
      // 取消正在进行的查询
      await queryClient.cancelQueries({ queryKey: queryKeys.positions.list() });
      
      // 获取当前数据快照
      const previousPositions = queryClient.getQueryData(queryKeys.positions.list());
      
      // 乐观更新
      const optimisticPosition = {
        id: `temp-${Date.now()}`, // 临时ID
        ...newPosition,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
      
      optimisticUpdates.addPosition(optimisticPosition);
      
      // 返回回滚数据
      return { previousPositions };
    },
    
    // 成功处理
    onSuccess: (data, variables) => {
      console.log('✅ React Query: Position created successfully', data);
      showSuccess(`岗位 "${variables.positionName}" 添加成功！`);
      
      // 使相关查询失效以获取最新数据
      queryClient.invalidateQueries({ queryKey: queryKeys.positions.all });
    },
    
    // 错误处理
    onError: (error: any, variables, context) => {
      console.error('❌ React Query: Failed to create position', error);
      showError(`添加岗位失败：${error.message || '请稍后重试'}`);
      
      // 回滚乐观更新
      if (context?.previousPositions) {
        queryClient.setQueryData(queryKeys.positions.list(), context.previousPositions);
      }
    },
    
    // 最终处理（无论成功失败）
    onSettled: () => {
      // 确保数据是最新的
      queryClient.invalidateQueries({ queryKey: queryKeys.positions.list() });
    },
  });
};

// 更新意向岗位的Mutation Hook
export const useUpdatePositionMutation = () => {
  const queryClient = useQueryClient();
  const { showSuccess, showError } = useToast();
  
  return useMutation({
    mutationFn: async ({ id, data }: { id: string; data: TargetPositionUpdateInput }) => {
      console.log('🔄 React Query: Updating position', id, data);
      return await targetPositionService.updateTargetPosition(id, data);
    },
    
    // 乐观更新
    onMutate: async ({ id, data }) => {
      await queryClient.cancelQueries({ queryKey: queryKeys.positions.list() });
      
      const previousPositions = queryClient.getQueryData(queryKeys.positions.list());
      
      // 乐观更新
      optimisticUpdates.updatePosition(id, data);
      
      return { previousPositions };
    },
    
    // 成功处理
    onSuccess: (data, variables) => {
      console.log('✅ React Query: Position updated successfully', data);
      showSuccess(`岗位更新成功！`);
      
      queryClient.invalidateQueries({ queryKey: queryKeys.positions.all });
    },
    
    // 错误处理
    onError: (error: any, variables, context) => {
      console.error('❌ React Query: Failed to update position', error);
      showError(`更新岗位失败：${error.message || '请稍后重试'}`);
      
      if (context?.previousPositions) {
        queryClient.setQueryData(queryKeys.positions.list(), context.previousPositions);
      }
    },
    
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.positions.list() });
    },
  });
};

// 删除意向岗位的Mutation Hook
export const useDeletePositionMutation = () => {
  const queryClient = useQueryClient();
  const { showSuccess, showError } = useToast();
  
  return useMutation({
    mutationFn: async (id: string) => {
      console.log('🔄 React Query: Deleting position', id);
      await targetPositionService.deleteTargetPosition(id);
      return id;
    },
    
    // 乐观更新
    onMutate: async (id) => {
      await queryClient.cancelQueries({ queryKey: queryKeys.positions.list() });
      
      const previousPositions = queryClient.getQueryData(queryKeys.positions.list());
      
      // 乐观删除
      optimisticUpdates.removePosition(id);
      
      return { previousPositions };
    },
    
    // 成功处理
    onSuccess: (deletedId) => {
      console.log('✅ React Query: Position deleted successfully', deletedId);
      showSuccess('岗位删除成功！');
      
      queryClient.invalidateQueries({ queryKey: queryKeys.positions.all });
    },
    
    // 错误处理
    onError: (error: any, variables, context) => {
      console.error('❌ React Query: Failed to delete position', error);
      showError(`删除岗位失败：${error.message || '请稍后重试'}`);
      
      if (context?.previousPositions) {
        queryClient.setQueryData(queryKeys.positions.list(), context.previousPositions);
      }
    },
    
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.positions.list() });
    },
  });
};

// 组合Hook：提供完整的岗位管理功能
export const usePositionManagement = () => {
  const positionsQuery = usePositionsQuery();
  const createMutation = useCreatePositionMutation();
  const updateMutation = useUpdatePositionMutation();
  const deleteMutation = useDeletePositionMutation();
  
  return {
    // 查询状态
    positions: positionsQuery.data ?? [],
    isLoading: positionsQuery.isLoading,
    isError: positionsQuery.isError,
    error: positionsQuery.error,
    isFetching: positionsQuery.isFetching,
    
    // 操作方法
    createPosition: createMutation.mutate,
    updatePosition: (id: string, data: TargetPositionUpdateInput) => 
      updateMutation.mutate({ id, data }),
    deletePosition: deleteMutation.mutate,
    
    // 操作状态
    isCreating: createMutation.isPending,
    isUpdating: updateMutation.isPending,
    isDeleting: deleteMutation.isPending,
    isOperating: createMutation.isPending || updateMutation.isPending || deleteMutation.isPending,
    
    // 刷新方法
    refetch: positionsQuery.refetch,
    
    // 缓存控制
    invalidate: () => {
      const queryClient = useQueryClient();
      queryClient.invalidateQueries({ queryKey: queryKeys.positions.all });
    },
  };
};
