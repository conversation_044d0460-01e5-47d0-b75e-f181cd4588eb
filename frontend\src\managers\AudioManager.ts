// 音频资源管理器
import { InterviewConfig } from './InterviewSessionManager';

export interface AudioProcessor {
  id: string;
  audioContext: AudioContext;
  mediaStreamSource?: MediaStreamAudioSourceNode;
  scriptProcessor?: ScriptProcessorNode;
  analyser?: AnalyserNode;
  status: 'idle' | 'active' | 'error';
  lastActivity: number;
}

export interface AudioProcessorPool {
  [id: string]: AudioProcessor;
}

/**
 * 音频资源管理器
 * 负责音频处理器的创建、复用、池化管理
 */
export class AudioManager {
  private processorPool: AudioProcessorPool = {};
  private contextCache: Map<string, AudioContext> = new Map(); // 智能缓存
  private maxCacheSize = 1; // 最大缓存大小（重构为1）
  private currentStream: MediaStream | null = null;
  private activeProcessorId: string | null = null;

  /**
   * 简化初始化 - 移除预热逻辑，改为空操作
   */
  public async preInitialize(): Promise<void> {
    console.log('🔧 AudioManager: Pre-initialization disabled (using on-demand audio setup)');

    // 不进行任何预初始化操作，音频上下文将在需要时创建
    console.log('✅ AudioManager: Ready (no pre-initialization needed)');
  }

  /**
   * 设置处理器 - 为特定配置创建音频处理器
   */
  public async setupProcessors(config: InterviewConfig): Promise<void> {
    console.log('🔧 AudioManager: Setting up audio processors', config);

    // Mock模式跳过音频处理器设置
    if (config.interviewType === 'mock') {
      console.log('🎯 AudioManager: Skipping audio processor setup for mock interview mode');
      return;
    }

    try {
      // 获取屏幕共享流
      if (!config.sharedStream) {
        throw new Error('No shared stream available for audio processing');
      }

      this.currentStream = config.sharedStream;

      // 创建或复用音频处理器
      const processorId = this.generateProcessorId(config);
      await this.createAudioProcessor(processorId, config.sharedStream);

      this.activeProcessorId = processorId;
      console.log(`✅ AudioManager: Audio processor ${processorId} setup completed`);
    } catch (error) {
      console.error('❌ AudioManager: Failed to setup processors:', error);
      throw error;
    }
  }

  /**
   * 激活处理器 - 开始实际的音频处理
   */
  public async activateProcessors(): Promise<void> {
    console.log('⚡ AudioManager: Activating audio processors');

    // Mock模式跳过音频处理器激活
    if (!this.activeProcessorId) {
      console.log('🎯 AudioManager: No active processor (likely mock mode), skipping activation');
      return;
    }

    try {
      const processor = this.processorPool[this.activeProcessorId];
      if (!processor) {
        throw new Error(`Processor ${this.activeProcessorId} not found`);
      }

      // 启动音频处理
      await this.startAudioProcessing(processor);
      processor.status = 'active';
      processor.lastActivity = Date.now();

      console.log(`✅ AudioManager: Processor ${this.activeProcessorId} activated`);
    } catch (error) {
      console.error('❌ AudioManager: Failed to activate processors:', error);
      throw error;
    }
  }

  /**
   * 获取活跃的音频处理器
   */
  public getActiveProcessor(): AudioProcessor | undefined {
    if (!this.activeProcessorId) return undefined;
    return this.processorPool[this.activeProcessorId];
  }

  /**
   * 获取音频数据
   */
  public getAudioData(): Float32Array | null {
    const processor = this.getActiveProcessor();
    if (!processor || !processor.analyser) return null;
    
    try {
      const bufferLength = processor.analyser.frequencyBinCount;
      const dataArray = new Float32Array(bufferLength);
      processor.analyser.getFloatFrequencyData(dataArray);
      return dataArray;
    } catch (error) {
      console.error('❌ AudioManager: Failed to get audio data:', error);
      return null;
    }
  }

  /**
   * 获取音频音量
   */
  public getAudioVolume(): number {
    const processor = this.getActiveProcessor();
    if (!processor || !processor.analyser) return 0;
    
    try {
      const bufferLength = processor.analyser.frequencyBinCount;
      const dataArray = new Uint8Array(bufferLength);
      processor.analyser.getByteFrequencyData(dataArray);
      
      // 计算平均音量
      let sum = 0;
      for (let i = 0; i < bufferLength; i++) {
        sum += dataArray[i];
      }
      return sum / bufferLength / 255; // 归一化到0-1
    } catch (error) {
      console.error('❌ AudioManager: Failed to get audio volume:', error);
      return 0;
    }
  }

  /**
   * 清理资源
   */
  public async cleanup(): Promise<void> {
    console.log('🧹 AudioManager: Cleaning up resources');
    
    // 停止所有处理器
    Object.values(this.processorPool).forEach(processor => {
      this.stopAudioProcessor(processor);
    });
    
    // 关闭预初始化的上下文
    this.preInitializedContexts.forEach(context => {
      if (context.state !== 'closed') {
        context.close();
      }
    });
    
    this.processorPool = {};
    this.preInitializedContexts = [];
    this.currentStream = null;
    this.activeProcessorId = null;
    
    console.log('✅ AudioManager: Cleanup completed');
  }

  // 私有方法（重构版）

  /**
   * 获取或创建智能音频上下文
   */
  private async getSmartAudioContext(processorId: string): Promise<AudioContext> {
    // 检查缓存中是否有可复用的上下文
    const cachedContext = this.contextCache.get(processorId);
    if (cachedContext && cachedContext.state !== 'closed') {
      console.log('♻️ AudioManager: Reusing cached audio context');
      if (cachedContext.state === 'suspended') {
        await cachedContext.resume();
      }
      return cachedContext;
    }

    // 创建新的音频上下文
    const audioContext = new AudioContext({
      sampleRate: 16000,
      latencyHint: 'interactive'
    });

    // 确保上下文处于运行状态
    if (audioContext.state === 'suspended') {
      await audioContext.resume();
    }

    // 缓存上下文（限制缓存大小）
    if (this.contextCache.size >= this.maxCacheSize) {
      const firstKey = this.contextCache.keys().next().value;
      const oldContext = this.contextCache.get(firstKey);
      if (oldContext) {
        oldContext.close();
      }
      this.contextCache.delete(firstKey);
    }

    this.contextCache.set(processorId, audioContext);
    console.log('🆕 AudioManager: Created smart audio context');
    return audioContext;
  }

  private async createAudioProcessor(processorId: string, stream: MediaStream): Promise<void> {
    // 使用智能音频上下文
    const audioContext = await this.getSmartAudioContext(processorId);
    
    // 创建音频处理链
    const mediaStreamSource = audioContext.createMediaStreamSource(stream);
    const analyser = audioContext.createAnalyser();
    const scriptProcessor = audioContext.createScriptProcessor(4096, 1, 1);
    
    // 配置分析器
    analyser.fftSize = 2048;
    analyser.smoothingTimeConstant = 0.8;
    
    // 连接音频节点
    mediaStreamSource.connect(analyser);
    analyser.connect(scriptProcessor);
    scriptProcessor.connect(audioContext.destination);
    
    // 设置音频处理回调
    scriptProcessor.onaudioprocess = (event) => {
      this.handleAudioProcess(processorId, event);
    };
    
    // 添加到处理器池
    this.processorPool[processorId] = {
      id: processorId,
      audioContext,
      mediaStreamSource,
      scriptProcessor,
      analyser,
      status: 'idle',
      lastActivity: Date.now()
    };
    
    console.log(`✅ AudioManager: Audio processor ${processorId} created`);
  }

  private async startAudioProcessing(processor: AudioProcessor): Promise<void> {
    if (!processor.audioContext || !processor.scriptProcessor) {
      throw new Error('Audio processor not properly initialized');
    }
    
    // 确保音频上下文处于运行状态
    if (processor.audioContext.state === 'suspended') {
      await processor.audioContext.resume();
    }
    
    console.log(`🎵 AudioManager: Started audio processing for ${processor.id}`);
  }

  private stopAudioProcessor(processor: AudioProcessor): void {
    try {
      if (processor.scriptProcessor) {
        processor.scriptProcessor.disconnect();
      }
      if (processor.mediaStreamSource) {
        processor.mediaStreamSource.disconnect();
      }
      if (processor.analyser) {
        processor.analyser.disconnect();
      }
      if (processor.audioContext && processor.audioContext.state !== 'closed') {
        processor.audioContext.close();
      }
      
      processor.status = 'idle';
      console.log(`🔇 AudioManager: Stopped audio processor ${processor.id}`);
    } catch (error) {
      console.error(`❌ AudioManager: Failed to stop processor ${processor.id}:`, error);
    }
  }

  private handleAudioProcess(processorId: string, event: AudioProcessingEvent): void {
    const processor = this.processorPool[processorId];
    if (!processor) return;
    
    try {
      // 获取音频数据
      const inputBuffer = event.inputBuffer;
      const inputData = inputBuffer.getChannelData(0);
      
      // 更新活动时间
      processor.lastActivity = Date.now();
      
      // 触发音频数据事件
      window.dispatchEvent(new CustomEvent('audio-data', {
        detail: {
          processorId,
          audioData: inputData,
          sampleRate: inputBuffer.sampleRate,
          timestamp: Date.now()
        }
      }));
    } catch (error) {
      console.error(`❌ AudioManager: Audio processing error for ${processorId}:`, error);
    }
  }

  private generateProcessorId(config: InterviewConfig): string {
    const timestamp = Date.now();
    const type = config.interviewType;
    return `audio_${type}_${timestamp}`;
  }
}
