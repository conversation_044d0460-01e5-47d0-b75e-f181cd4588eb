-- Create<PERSON><PERSON>
CREATE TYPE "UsageType" AS ENUM ('MOCK_INTERVIEW', 'FORMAL_INTERVIEW', 'CREDIT_RECHARGE');

-- CreateEnum
CREATE TYPE "NotificationType" AS ENUM ('ANNOUNCEMENT', 'SYSTEM_UPDATE', 'MAINTENANCE', 'PROMOTION');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "NotificationPriority" AS ENUM ('LOW', 'NORMAL', 'HIGH', 'URGENT');

-- C<PERSON><PERSON>num
CREATE TYPE "NotificationStatus" AS ENUM ('DRAFT', 'PUBLISHED', 'EXPIRED', 'ARCHIVED');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "TargetType" AS ENUM ('ALL_USERS', 'NEW_USERS', 'DATE_RANGE', 'ROLE_BASED');

-- CreateTable
CREATE TABLE "usage_records" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "type" "UsageType" NOT NULL,
    "amount" INTEGER NOT NULL,
    "reason" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "usage_records_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "mock_interview_records" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "sessionId" TEXT NOT NULL,
    "companyName" TEXT NOT NULL,
    "positionName" TEXT NOT NULL,
    "interviewLanguage" TEXT NOT NULL,
    "answerStyle" TEXT NOT NULL,
    "status" TEXT NOT NULL,
    "startTime" TIMESTAMP(3) NOT NULL,
    "endTime" TIMESTAMP(3),
    "totalDuration" INTEGER,
    "totalQuestions" INTEGER NOT NULL DEFAULT 0,
    "averageScore" DOUBLE PRECISION,
    "overallFeedback" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "mock_interview_records_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "mock_interview_question_records" (
    "id" TEXT NOT NULL,
    "recordId" TEXT NOT NULL,
    "questionId" TEXT NOT NULL,
    "questionText" TEXT NOT NULL,
    "questionType" TEXT NOT NULL,
    "difficulty" TEXT NOT NULL,
    "expectedDuration" INTEGER NOT NULL,
    "context" TEXT,
    "keywords" JSONB NOT NULL,
    "answerText" TEXT,
    "answerDuration" INTEGER,
    "score" INTEGER,
    "strengths" JSONB,
    "improvements" JSONB,
    "keywordsCovered" JSONB,
    "missingKeywords" JSONB,
    "overallAssessment" TEXT,
    "detailedAnalysis" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "mock_interview_question_records_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "interview_statistics" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "totalMockInterviews" INTEGER NOT NULL DEFAULT 0,
    "totalFormalInterviews" INTEGER NOT NULL DEFAULT 0,
    "averageMockScore" DOUBLE PRECISION,
    "averageFormalScore" DOUBLE PRECISION,
    "totalInterviewTime" INTEGER NOT NULL DEFAULT 0,
    "favoriteQuestionType" TEXT,
    "weakestQuestionType" TEXT,
    "improvementTrend" JSONB,
    "lastInterviewDate" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "interview_statistics_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "notifications" (
    "id" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "content" TEXT NOT NULL,
    "type" "NotificationType" NOT NULL DEFAULT 'ANNOUNCEMENT',
    "priority" "NotificationPriority" NOT NULL DEFAULT 'NORMAL',
    "status" "NotificationStatus" NOT NULL DEFAULT 'DRAFT',
    "publishedAt" TIMESTAMP(3),
    "expiresAt" TIMESTAMP(3),
    "createdBy" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "notifications_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "notification_targets" (
    "id" TEXT NOT NULL,
    "notificationId" TEXT NOT NULL,
    "targetType" "TargetType" NOT NULL,
    "targetValue" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "notification_targets_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "user_notifications" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "notificationId" TEXT NOT NULL,
    "isRead" BOOLEAN NOT NULL DEFAULT false,
    "readAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "user_notifications_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "referral_codes" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "code" TEXT NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "expiresAt" TIMESTAMP(3),

    CONSTRAINT "referral_codes_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "referral_relations" (
    "id" TEXT NOT NULL,
    "inviterId" TEXT NOT NULL,
    "inviteeId" TEXT NOT NULL,
    "referralCode" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "firstPaymentAt" TIMESTAMP(3),
    "rewardGranted" BOOLEAN NOT NULL DEFAULT false,
    "rewardGrantedAt" TIMESTAMP(3),

    CONSTRAINT "referral_relations_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "referral_rewards" (
    "id" TEXT NOT NULL,
    "inviterId" TEXT NOT NULL,
    "inviteeId" TEXT NOT NULL,
    "relationId" TEXT NOT NULL,
    "rewardAmount" INTEGER NOT NULL,
    "rewardType" TEXT NOT NULL DEFAULT 'REFERRAL_BONUS',
    "orderId" TEXT,
    "codeUsageId" TEXT,
    "status" TEXT NOT NULL DEFAULT 'PENDING',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "processedAt" TIMESTAMP(3),

    CONSTRAINT "referral_rewards_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "usage_records_userId_idx" ON "usage_records"("userId");

-- CreateIndex
CREATE INDEX "usage_records_createdAt_idx" ON "usage_records"("createdAt");

-- CreateIndex
CREATE UNIQUE INDEX "mock_interview_records_sessionId_key" ON "mock_interview_records"("sessionId");

-- CreateIndex
CREATE INDEX "mock_interview_records_userId_idx" ON "mock_interview_records"("userId");

-- CreateIndex
CREATE INDEX "mock_interview_records_sessionId_idx" ON "mock_interview_records"("sessionId");

-- CreateIndex
CREATE INDEX "mock_interview_question_records_recordId_idx" ON "mock_interview_question_records"("recordId");

-- CreateIndex
CREATE UNIQUE INDEX "interview_statistics_userId_key" ON "interview_statistics"("userId");

-- CreateIndex
CREATE INDEX "interview_statistics_userId_idx" ON "interview_statistics"("userId");

-- CreateIndex
CREATE INDEX "notifications_status_idx" ON "notifications"("status");

-- CreateIndex
CREATE INDEX "notifications_publishedAt_idx" ON "notifications"("publishedAt");

-- CreateIndex
CREATE INDEX "notifications_createdBy_idx" ON "notifications"("createdBy");

-- CreateIndex
CREATE INDEX "notification_targets_notificationId_idx" ON "notification_targets"("notificationId");

-- CreateIndex
CREATE INDEX "user_notifications_userId_idx" ON "user_notifications"("userId");

-- CreateIndex
CREATE INDEX "user_notifications_notificationId_idx" ON "user_notifications"("notificationId");

-- CreateIndex
CREATE INDEX "user_notifications_isRead_idx" ON "user_notifications"("isRead");

-- CreateIndex
CREATE UNIQUE INDEX "user_notifications_userId_notificationId_key" ON "user_notifications"("userId", "notificationId");

-- CreateIndex
CREATE UNIQUE INDEX "referral_codes_code_key" ON "referral_codes"("code");

-- CreateIndex
CREATE INDEX "referral_codes_userId_idx" ON "referral_codes"("userId");

-- CreateIndex
CREATE INDEX "referral_codes_code_idx" ON "referral_codes"("code");

-- CreateIndex
CREATE UNIQUE INDEX "referral_relations_inviteeId_key" ON "referral_relations"("inviteeId");

-- CreateIndex
CREATE INDEX "referral_relations_inviterId_idx" ON "referral_relations"("inviterId");

-- CreateIndex
CREATE INDEX "referral_relations_referralCode_idx" ON "referral_relations"("referralCode");

-- CreateIndex
CREATE INDEX "referral_rewards_inviterId_idx" ON "referral_rewards"("inviterId");

-- CreateIndex
CREATE INDEX "referral_rewards_inviteeId_idx" ON "referral_rewards"("inviteeId");

-- CreateIndex
CREATE INDEX "referral_rewards_relationId_idx" ON "referral_rewards"("relationId");

-- CreateIndex
CREATE INDEX "referral_rewards_orderId_idx" ON "referral_rewards"("orderId");

-- CreateIndex
CREATE INDEX "referral_rewards_codeUsageId_idx" ON "referral_rewards"("codeUsageId");
