import { useEffect } from 'react';

/**
 * 自定义Hook用于设置页面标题
 * @param title 页面标题，会自动添加 " - 面试君" 后缀
 */
const useDocumentTitle = (title: string) => {
  useEffect(() => {
    const previousTitle = document.title;
    
    // 设置新标题，格式为 "页面名称 - 面试君"
    document.title = `${title} - 面试君`;
    
    // 清理函数：组件卸载时恢复之前的标题
    return () => {
      document.title = previousTitle;
    };
  }, [title]);
};

export default useDocumentTitle;
