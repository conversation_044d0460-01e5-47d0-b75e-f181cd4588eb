# 验证码登录系统环境配置与部署指南

## 1. 环境变量配置

### 1.1 后端环境变量 (.env)

```bash
# 数据库配置
DATABASE_URL="postgresql://username:password@localhost:5432/mianshijun"

# JWT配置
JWT_SECRET="your-super-secret-jwt-key-change-in-production"
JWT_EXPIRES_IN="24h"

# Redis配置
REDIS_URL="redis://localhost:6379"
REDIS_PASSWORD="your-redis-password"

# 邮件服务配置 (Gmail示例)
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"  # Gmail应用专用密码
SMTP_FROM="面试君 <<EMAIL>>"

# 阿里云短信服务配置
ALIBABA_ACCESS_KEY_ID="your-access-key-id"
ALIBABA_ACCESS_KEY_SECRET="your-access-key-secret"
ALIBABA_SMS_SIGN_NAME="面试君"
ALIBABA_SMS_TEMPLATE_CODE="SMS_123456789"

# 安全配置
RATE_LIMIT_WINDOW="60000"  # 1分钟
RATE_LIMIT_MAX_REQUESTS="5"
DAILY_SEND_LIMIT="20"

# 验证码配置
VERIFICATION_CODE_LENGTH="6"
VERIFICATION_CODE_EXPIRES="300"  # 5分钟
MAX_VERIFICATION_ATTEMPTS="3"
LOCK_DURATION="600"  # 10分钟

# 服务端口
PORT="3000"
```

### 1.2 前端环境变量 (.env)

```bash
# API基础URL
VITE_API_URL="http://localhost:3000"

# 应用配置
VITE_APP_NAME="面试君"
VITE_APP_VERSION="1.0.0"

# 功能开关
VITE_ENABLE_SMS_LOGIN="true"
VITE_ENABLE_EMAIL_LOGIN="true"
VITE_ENABLE_PASSWORD_LOGIN="true"  # 兼容模式
```

## 2. 服务依赖安装与配置

### 2.1 Redis安装配置

#### Ubuntu/Debian系统
```bash
# 安装Redis
sudo apt update
sudo apt install redis-server

# 配置Redis
sudo nano /etc/redis/redis.conf

# 修改以下配置
bind 127.0.0.1
port 6379
requirepass your-redis-password
maxmemory 256mb
maxmemory-policy allkeys-lru

# 启动Redis服务
sudo systemctl start redis-server
sudo systemctl enable redis-server

# 测试连接
redis-cli -a your-redis-password ping
```

#### Docker方式
```bash
# 创建Redis容器
docker run -d \
  --name redis-mianshijun \
  -p 6379:6379 \
  -e REDIS_PASSWORD=your-redis-password \
  redis:7-alpine \
  redis-server --requirepass your-redis-password

# 测试连接
docker exec -it redis-mianshijun redis-cli -a your-redis-password ping
```

### 2.2 邮件服务配置

#### Gmail配置步骤
1. 登录Gmail账户
2. 启用两步验证
3. 生成应用专用密码：
   - 访问 https://myaccount.google.com/apppasswords
   - 选择"邮件"和"其他设备"
   - 生成16位应用密码
   - 将密码设置为`SMTP_PASS`环境变量

#### QQ邮箱配置
```bash
SMTP_HOST="smtp.qq.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASS="your-authorization-code"  # QQ邮箱授权码
```

#### 阿里云邮件推送配置
```bash
SMTP_HOST="smtpdm.aliyun.com"
SMTP_PORT="25"
SMTP_USER="<EMAIL>"
SMTP_PASS="your-smtp-password"
```

### 2.3 阿里云短信服务配置

#### 开通短信服务
1. 登录阿里云控制台
2. 开通短信服务
3. 创建签名和模板
4. 获取AccessKey

#### 短信模板示例
```
模板名称：登录验证码
模板内容：【${sign_name}】您的登录验证码是${code}，5分钟内有效，请勿泄露。
模板类型：验证码
```

#### API调用测试
```bash
# 安装阿里云CLI工具
npm install -g @alicloud/cli

# 配置访问密钥
aliyun configure

# 测试短信发送
aliyun dysmsapi SendSms \
  --PhoneNumbers "***********" \
  --SignName "面试君" \
  --TemplateCode "SMS_123456789" \
  --TemplateParam '{"code":"123456"}'
```

## 3. 数据库迁移

### 3.1 Prisma迁移脚本

```bash
# 生成迁移文件
npx prisma migrate dev --name add-verification-system

# 应用迁移
npx prisma migrate deploy

# 生成Prisma客户端
npx prisma generate
```

### 3.2 手动SQL迁移（如需要）

```sql
-- 创建验证码类型枚举
CREATE TYPE "VerificationType" AS ENUM ('EMAIL', 'SMS');
CREATE TYPE "VerificationPurpose" AS ENUM ('LOGIN', 'REGISTER', 'RESET_PASSWORD');
CREATE TYPE "SendStatus" AS ENUM ('SUCCESS', 'FAILED', 'PENDING');

-- 创建验证码记录表
CREATE TABLE "VerificationCode" (
    "id" TEXT NOT NULL,
    "identifier" TEXT NOT NULL,
    "code" TEXT NOT NULL,
    "type" "VerificationType" NOT NULL,
    "purpose" "VerificationPurpose" NOT NULL,
    "expiresAt" TIMESTAMP(3) NOT NULL,
    "attempts" INTEGER NOT NULL DEFAULT 0,
    "isUsed" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    CONSTRAINT "VerificationCode_pkey" PRIMARY KEY ("id")
);

-- 创建发送记录表
CREATE TABLE "VerificationLog" (
    "id" TEXT NOT NULL,
    "identifier" TEXT NOT NULL,
    "type" "VerificationType" NOT NULL,
    "purpose" "VerificationPurpose" NOT NULL,
    "status" "SendStatus" NOT NULL,
    "errorMessage" TEXT,
    "ipAddress" TEXT,
    "userAgent" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "VerificationLog_pkey" PRIMARY KEY ("id")
);

-- 创建索引
CREATE INDEX "VerificationCode_identifier_type_purpose_idx" ON "VerificationCode"("identifier", "type", "purpose");
CREATE INDEX "VerificationCode_expiresAt_idx" ON "VerificationCode"("expiresAt");
CREATE INDEX "VerificationLog_identifier_createdAt_idx" ON "VerificationLog"("identifier", "createdAt");
CREATE INDEX "VerificationLog_createdAt_idx" ON "VerificationLog"("createdAt");
```

## 4. 开发环境启动

### 4.1 后端启动

```bash
# 安装依赖
npm install

# 安装新增依赖
npm install nodemailer @types/nodemailer @alicloud/pop-core ioredis @types/ioredis

# 数据库迁移
npx prisma migrate dev

# 启动开发服务器
npm run dev
```

### 4.2 前端启动

```bash
# 进入前端目录
cd frontend

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

### 4.3 完整启动脚本

```bash
#!/bin/bash
# start-dev.sh

echo "🚀 启动面试君验证码登录系统开发环境"

# 检查Redis服务
echo "📡 检查Redis服务..."
redis-cli ping > /dev/null 2>&1
if [ $? -ne 0 ]; then
    echo "❌ Redis服务未启动，请先启动Redis"
    exit 1
fi
echo "✅ Redis服务正常"

# 检查数据库连接
echo "🗄️ 检查数据库连接..."
npx prisma db pull > /dev/null 2>&1
if [ $? -ne 0 ]; then
    echo "❌ 数据库连接失败，请检查DATABASE_URL配置"
    exit 1
fi
echo "✅ 数据库连接正常"

# 应用数据库迁移
echo "🔄 应用数据库迁移..."
npx prisma migrate deploy

# 启动后端服务
echo "🖥️ 启动后端服务..."
npm run dev &
BACKEND_PID=$!

# 等待后端启动
sleep 3

# 启动前端服务
echo "🌐 启动前端服务..."
cd frontend
npm run dev &
FRONTEND_PID=$!

echo "✅ 开发环境启动完成！"
echo "📱 前端地址: http://localhost:5173"
echo "🔧 后端地址: http://localhost:3000"
echo "📊 Redis监控: redis-cli monitor"

# 等待用户中断
wait $BACKEND_PID $FRONTEND_PID
```

## 5. 生产环境部署

### 5.1 服务器环境准备

```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装Node.js 18+
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 安装PM2进程管理器
sudo npm install -g pm2

# 安装Nginx
sudo apt install nginx

# 安装PostgreSQL
sudo apt install postgresql postgresql-contrib

# 安装Redis
sudo apt install redis-server
```

### 5.2 应用部署脚本

```bash
#!/bin/bash
# deploy.sh

APP_NAME="mianshijun"
APP_DIR="/var/www/$APP_NAME"
BACKUP_DIR="/var/backups/$APP_NAME"

echo "🚀 开始部署面试君验证码登录系统"

# 创建备份
echo "💾 创建备份..."
sudo mkdir -p $BACKUP_DIR
sudo cp -r $APP_DIR $BACKUP_DIR/$(date +%Y%m%d_%H%M%S) 2>/dev/null || true

# 拉取最新代码
echo "📥 拉取最新代码..."
cd $APP_DIR
git pull origin main

# 安装后端依赖
echo "📦 安装后端依赖..."
npm ci --production

# 构建前端
echo "🏗️ 构建前端..."
cd frontend
npm ci
npm run build

# 数据库迁移
echo "🗄️ 执行数据库迁移..."
cd ..
npx prisma migrate deploy

# 重启应用
echo "🔄 重启应用..."
pm2 restart $APP_NAME || pm2 start ecosystem.config.js

# 重载Nginx配置
echo "🌐 重载Nginx配置..."
sudo nginx -t && sudo systemctl reload nginx

echo "✅ 部署完成！"
```

### 5.3 PM2配置文件

```javascript
// ecosystem.config.js
module.exports = {
  apps: [{
    name: 'mianshijun',
    script: './backend/server.js',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true,
    max_memory_restart: '1G',
    node_args: '--max_old_space_size=1024'
  }]
};
```

### 5.4 Nginx配置

```nginx
# /etc/nginx/sites-available/mianshijun
server {
    listen 80;
    server_name mianshijun.xyz www.mianshijun.xyz;
    
    # 重定向到HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name mianshijun.xyz www.mianshijun.xyz;
    
    # SSL配置
    ssl_certificate /etc/letsencrypt/live/mianshijun.xyz/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/mianshijun.xyz/privkey.pem;
    
    # 前端静态文件
    location / {
        root /var/www/mianshijun/frontend/dist;
        try_files $uri $uri/ /index.html;
        
        # 缓存静态资源
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
    
    # API代理
    location /api/ {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # WebSocket支持
    location /ws/ {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 安全头
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
}
```

## 6. 监控与日志

### 6.1 日志配置

```javascript
// backend/utils/logger.js
const winston = require('winston');

const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: { service: 'mianshijun-verification' },
  transports: [
    new winston.transports.File({ filename: 'logs/error.log', level: 'error' }),
    new winston.transports.File({ filename: 'logs/combined.log' }),
  ],
});

if (process.env.NODE_ENV !== 'production') {
  logger.add(new winston.transports.Console({
    format: winston.format.simple()
  }));
}

module.exports = logger;
```

### 6.2 健康检查接口

```typescript
// backend/api/health.ts
export default async function handler(req: VercelRequest, res: VercelResponse) {
  try {
    // 检查数据库连接
    await prisma.$queryRaw`SELECT 1`;
    
    // 检查Redis连接
    const redis = new Redis(process.env.REDIS_URL);
    await redis.ping();
    await redis.disconnect();
    
    res.status(200).json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      services: {
        database: 'connected',
        redis: 'connected'
      }
    });
  } catch (error) {
    res.status(503).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error.message
    });
  }
}
```

### 6.3 监控脚本

```bash
#!/bin/bash
# monitor.sh

# 检查应用状态
check_app() {
    response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000/api/health)
    if [ $response -eq 200 ]; then
        echo "✅ 应用健康检查通过"
    else
        echo "❌ 应用健康检查失败 (HTTP $response)"
        # 重启应用
        pm2 restart mianshijun
    fi
}

# 检查Redis状态
check_redis() {
    redis-cli ping > /dev/null 2>&1
    if [ $? -eq 0 ]; then
        echo "✅ Redis服务正常"
    else
        echo "❌ Redis服务异常，尝试重启"
        sudo systemctl restart redis-server
    fi
}

# 检查磁盘空间
check_disk() {
    usage=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
    if [ $usage -gt 80 ]; then
        echo "⚠️ 磁盘使用率过高: ${usage}%"
        # 清理日志文件
        find /var/www/mianshijun/logs -name "*.log" -mtime +7 -delete
    fi
}

# 执行检查
echo "🔍 开始系统监控检查 - $(date)"
check_app
check_redis
check_disk
echo "✅ 监控检查完成"
```

## 7. 安全配置

### 7.1 防火墙配置

```bash
# 启用UFW防火墙
sudo ufw enable

# 允许SSH
sudo ufw allow ssh

# 允许HTTP和HTTPS
sudo ufw allow 80
sudo ufw allow 443

# 只允许本地访问Redis
sudo ufw allow from 127.0.0.1 to any port 6379

# 查看防火墙状态
sudo ufw status
```

### 7.2 SSL证书配置

```bash
# 安装Certbot
sudo apt install certbot python3-certbot-nginx

# 获取SSL证书
sudo certbot --nginx -d mianshijun.xyz -d www.mianshijun.xyz

# 设置自动续期
sudo crontab -e
# 添加以下行
0 12 * * * /usr/bin/certbot renew --quiet
```

这个配置指南涵盖了验证码登录系统从开发到生产的完整部署流程，包括环境配置、服务依赖、数据库迁移、应用部署、监控日志和安全配置等各个方面。
