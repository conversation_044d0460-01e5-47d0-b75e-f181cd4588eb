// AI模拟面试记录相关路由
import express from 'express';
import { authenticateToken } from '../middleware/auth.js';
import { MockInterviewRecordService } from '../services/mockInterviewRecordService.js';
import { logger } from '../utils/logger.js';

const router = express.Router();
const recordService = new MockInterviewRecordService();

/**
 * 获取用户的面试记录列表
 * GET /api/mock-interviews/records
 */
router.get('/records', authenticateToken, async (req, res) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({ error: 'User not authenticated' });
    }

    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const offset = (page - 1) * limit;

    const records = await recordService.getUserRecords(userId, limit, offset);
    
    res.json({
      success: true,
      data: {
        records,
        pagination: {
          page,
          limit,
          hasMore: records.length === limit
        }
      }
    });
  } catch (error) {
    logger.error('❌ Failed to get mock interview records:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Failed to retrieve interview records' 
    });
  }
});

/**
 * 获取用户的面试分析数据
 * GET /api/mock-interviews/analytics
 */
router.get('/analytics', authenticateToken, async (req, res) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({ error: 'User not authenticated' });
    }

    const analytics = await recordService.getInterviewAnalytics(userId);
    
    res.json({
      success: true,
      data: analytics
    });
  } catch (error) {
    logger.error('❌ Failed to get interview analytics:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Failed to retrieve interview analytics' 
    });
  }
});

/**
 * 获取用户的统计数据
 * GET /api/mock-interviews/statistics
 */
router.get('/statistics', authenticateToken, async (req, res) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({ error: 'User not authenticated' });
    }

    const statistics = await recordService.getUserStatistics(userId);
    
    res.json({
      success: true,
      data: statistics
    });
  } catch (error) {
    logger.error('❌ Failed to get user statistics:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Failed to retrieve user statistics' 
    });
  }
});

/**
 * 获取特定面试记录的详细信息
 * GET /api/mock-interviews/records/:sessionId
 */
router.get('/records/:sessionId', authenticateToken, async (req, res) => {
  try {
    const userId = req.user?.id;
    const { sessionId } = req.params;
    
    if (!userId) {
      return res.status(401).json({ error: 'User not authenticated' });
    }

    // 这里需要添加获取单个记录详情的方法
    // 暂时返回空实现
    res.json({
      success: true,
      data: {
        message: 'Detailed record retrieval not yet implemented',
        sessionId
      }
    });
  } catch (error) {
    logger.error('❌ Failed to get interview record details:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Failed to retrieve interview record details' 
    });
  }
});

/**
 * 删除面试记录
 * DELETE /api/mock-interviews/records/:sessionId
 */
router.delete('/records/:sessionId', authenticateToken, async (req, res) => {
  try {
    const userId = req.user?.id;
    const { sessionId } = req.params;
    
    if (!userId) {
      return res.status(401).json({ error: 'User not authenticated' });
    }

    // 这里需要添加删除记录的方法
    // 暂时返回空实现
    res.json({
      success: true,
      data: {
        message: 'Record deletion not yet implemented',
        sessionId
      }
    });
  } catch (error) {
    logger.error('❌ Failed to delete interview record:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Failed to delete interview record' 
    });
  }
});

/**
 * 获取面试记录的导出数据
 * GET /api/mock-interviews/export
 */
router.get('/export', authenticateToken, async (req, res) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({ error: 'User not authenticated' });
    }

    const format = req.query.format as string || 'json';
    
    if (format !== 'json' && format !== 'csv') {
      return res.status(400).json({ 
        success: false, 
        error: 'Invalid format. Supported formats: json, csv' 
      });
    }

    // 获取所有记录
    const records = await recordService.getUserRecords(userId, 1000, 0);
    
    if (format === 'csv') {
      // 生成CSV格式
      const csvHeader = 'Date,Company,Position,Language,Score,Duration,Status\n';
      const csvRows = records.map(record => {
        const date = record.createdAt.toISOString().split('T')[0];
        const duration = record.totalDuration ? Math.round(record.totalDuration / 60) : 0;
        return `${date},${record.companyName},${record.positionName},${record.interviewLanguage},${record.averageScore || 0},${duration},${record.status}`;
      }).join('\n');
      
      const csvContent = csvHeader + csvRows;
      
      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', 'attachment; filename="mock_interview_records.csv"');
      res.send(csvContent);
    } else {
      // JSON格式
      res.json({
        success: true,
        data: {
          exportDate: new Date().toISOString(),
          totalRecords: records.length,
          records: records.map(record => ({
            date: record.createdAt.toISOString(),
            sessionId: record.sessionId,
            company: record.companyName,
            position: record.positionName,
            language: record.interviewLanguage,
            answerStyle: record.answerStyle,
            status: record.status,
            duration: record.totalDuration,
            averageScore: record.averageScore,
            totalQuestions: record.totalQuestions,
            questionBreakdown: record.questions.map(q => ({
              type: q.questionType,
              difficulty: q.difficulty,
              score: q.score,
              duration: q.answerDuration
            }))
          }))
        }
      });
    }
  } catch (error) {
    logger.error('❌ Failed to export interview records:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Failed to export interview records' 
    });
  }
});

/**
 * 获取面试趋势数据（用于图表展示）
 * GET /api/mock-interviews/trends
 */
router.get('/trends', authenticateToken, async (req, res) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({ error: 'User not authenticated' });
    }

    const days = parseInt(req.query.days as string) || 30;
    
    // 获取最近的记录
    const records = await recordService.getUserRecords(userId, 100, 0);
    
    // 按日期分组计算趋势
    const now = new Date();
    const startDate = new Date(now.getTime() - days * 24 * 60 * 60 * 1000);
    
    const filteredRecords = records.filter(record => 
      record.createdAt >= startDate && record.status === 'completed'
    );
    
    // 按日期分组
    const dailyData: Record<string, { count: number; totalScore: number; scores: number[] }> = {};
    
    filteredRecords.forEach(record => {
      const date = record.createdAt.toISOString().split('T')[0];
      if (!dailyData[date]) {
        dailyData[date] = { count: 0, totalScore: 0, scores: [] };
      }
      dailyData[date].count++;
      if (record.averageScore) {
        dailyData[date].totalScore += record.averageScore;
        dailyData[date].scores.push(record.averageScore);
      }
    });
    
    // 生成趋势数据
    const trendData = Object.entries(dailyData).map(([date, data]) => ({
      date,
      interviewCount: data.count,
      averageScore: data.scores.length > 0 ? data.totalScore / data.scores.length : 0,
      scores: data.scores
    })).sort((a, b) => a.date.localeCompare(b.date));
    
    res.json({
      success: true,
      data: {
        period: `${days} days`,
        startDate: startDate.toISOString().split('T')[0],
        endDate: now.toISOString().split('T')[0],
        trends: trendData
      }
    });
  } catch (error) {
    logger.error('❌ Failed to get interview trends:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Failed to retrieve interview trends' 
    });
  }
});

export default router;
