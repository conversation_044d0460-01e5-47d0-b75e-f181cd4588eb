// 面试准备进度组件
import React from 'react';
import { Wifi, Volume2, Shield, CheckCircle, AlertCircle, Loader } from 'lucide-react';
import { useSessionProgress, useSessionResources, useSessionPhase, useLastError } from '../../stores/interviewSessionStore';
import { SessionPhase } from '../../managers/InterviewSessionManager';

interface ProgressStepProps {
  name: string;
  progress: number;
  status: 'idle' | 'preparing' | 'ready' | 'error';
  icon: React.ReactNode;
  error?: string;
}

const ProgressStep: React.FC<ProgressStepProps> = ({ name, progress, status, icon, error }) => {
  const getStatusColor = () => {
    switch (status) {
      case 'ready': return 'text-green-600';
      case 'preparing': return 'text-blue-600';
      case 'error': return 'text-red-600';
      default: return 'text-gray-400';
    }
  };

  const getProgressColor = () => {
    switch (status) {
      case 'ready': return 'bg-green-500';
      case 'preparing': return 'bg-blue-500';
      case 'error': return 'bg-red-500';
      default: return 'bg-gray-300';
    }
  };

  const getStatusIcon = () => {
    switch (status) {
      case 'ready': return <CheckCircle className="w-5 h-5 text-green-600" />;
      case 'preparing': return <Loader className="w-5 h-5 text-blue-600 animate-spin" />;
      case 'error': return <AlertCircle className="w-5 h-5 text-red-600" />;
      default: return icon;
    }
  };

  return (
    <div className="flex items-center space-x-4 p-4 bg-white rounded-lg border border-gray-200 shadow-sm">
      <div className={`flex-shrink-0 ${getStatusColor()}`}>
        {getStatusIcon()}
      </div>
      
      <div className="flex-1 min-w-0">
        <div className="flex items-center justify-between mb-2">
          <h3 className="text-sm font-medium text-gray-900">{name}</h3>
          <span className="text-sm text-gray-500">{progress}%</span>
        </div>
        
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className={`h-2 rounded-full transition-all duration-300 ${getProgressColor()}`}
            style={{ width: `${progress}%` }}
          />
        </div>
        
        {error && (
          <p className="mt-2 text-xs text-red-600">{error}</p>
        )}
      </div>
    </div>
  );
};

interface PhaseIndicatorProps {
  phase: SessionPhase;
  overallProgress: number;
}

const PhaseIndicator: React.FC<PhaseIndicatorProps> = ({ phase, overallProgress }) => {
  const getPhaseText = () => {
    switch (phase) {
      case 'idle': return '等待开始';
      case 'warming': return '系统预热中...';
      case 'preparing': return '准备面试环境...';
      case 'ready': return '准备就绪！';
      case 'active': return '面试进行中';
      case 'error': return '准备失败';
      default: return '未知状态';
    }
  };

  const getPhaseColor = () => {
    switch (phase) {
      case 'ready': return 'text-green-600';
      case 'active': return 'text-blue-600';
      case 'error': return 'text-red-600';
      case 'warming':
      case 'preparing': return 'text-orange-600';
      default: return 'text-gray-600';
    }
  };

  return (
    <div className="text-center mb-6">
      <h2 className={`text-xl font-semibold mb-2 ${getPhaseColor()}`}>
        {getPhaseText()}
      </h2>
      
      {(phase === 'warming' || phase === 'preparing') && (
        <div className="w-full bg-gray-200 rounded-full h-3 mb-2">
          <div
            className="h-3 bg-gradient-to-r from-blue-500 to-green-500 rounded-full transition-all duration-500"
            style={{ width: `${overallProgress}%` }}
          />
        </div>
      )}
      
      <p className="text-sm text-gray-600">
        {phase === 'ready' && '所有系统已就绪，可以开始面试'}
        {phase === 'preparing' && '正在为您准备最佳的面试环境'}
        {phase === 'warming' && '正在初始化系统组件'}
        {phase === 'error' && '系统准备过程中遇到问题，请重试'}
      </p>
    </div>
  );
};

interface InterviewPreparationProgressProps {
  visible: boolean;
  onReady?: () => void;
  onError?: (error: string) => void;
}

const InterviewPreparationProgress: React.FC<InterviewPreparationProgressProps> = ({
  visible,
  onReady,
  onError
}) => {
  const progress = useSessionProgress();
  const resources = useSessionResources();
  const phase = useSessionPhase();
  const lastError = useLastError();

  // 监听状态变化
  React.useEffect(() => {
    if (phase === 'ready' && onReady) {
      onReady();
    }
  }, [phase, onReady]);

  React.useEffect(() => {
    if (phase === 'error' && lastError && onError) {
      onError(lastError);
    }
  }, [phase, lastError, onError]);

  if (!visible) {
    return null;
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-8 max-w-md w-full mx-4 shadow-2xl">
        <PhaseIndicator phase={phase} overallProgress={progress.overall} />
        
        <div className="space-y-4">
          <ProgressStep
            name="网络连接"
            progress={progress.webSocket}
            status={resources.webSocket.status}
            icon={<Wifi className="w-5 h-5" />}
            error={resources.webSocket.error}
          />
          
          <ProgressStep
            name="音频系统"
            progress={progress.audio}
            status={resources.audio.status}
            icon={<Volume2 className="w-5 h-5" />}
            error={resources.audio.error}
          />
          
          <ProgressStep
            name="权限验证"
            progress={progress.credits}
            status={resources.credits.status}
            icon={<Shield className="w-5 h-5" />}
            error={resources.credits.error}
          />
        </div>
        
        {phase === 'error' && (
          <div className="mt-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center">
              <AlertCircle className="w-5 h-5 text-red-600 mr-2" />
              <h3 className="text-sm font-medium text-red-800">准备失败</h3>
            </div>
            <p className="mt-2 text-sm text-red-700">
              {lastError || '系统准备过程中遇到未知错误，请刷新页面重试'}
            </p>
          </div>
        )}
        
        {phase === 'ready' && (
          <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-center">
              <CheckCircle className="w-5 h-5 text-green-600 mr-2" />
              <h3 className="text-sm font-medium text-green-800">准备完成</h3>
            </div>
            <p className="mt-2 text-sm text-green-700">
              所有系统已就绪，即将开始面试...
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default InterviewPreparationProgress;
