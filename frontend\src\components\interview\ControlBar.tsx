import React, { useState, useCallback } from 'react';
import AudioWaveform from './AudioWaveform';
import ConfirmDialog from '../ui/ConfirmDialog';

interface ControlBarProps {
  isListening: boolean;
  onToggleListening: () => void;
  onRefresh: () => void;
  mode?: 'live' | 'mock';
  onSendMessage?: (message: string) => void;
  isInterviewStarted?: boolean;
  hasMessages?: boolean;
}

const ControlBar: React.FC<ControlBarProps> = ({
  isListening,
  onToggleListening,
  onRefresh,
  mode = 'live',
  onSendMessage,
  isInterviewStarted = false,
  hasMessages = false
}) => {
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [userInput, setUserInput] = useState('');

  const handleRefresh = () => {
    setShowConfirmDialog(true);
  };

  const handleConfirmRefresh = () => {
    setShowConfirmDialog(false);
    onRefresh();
  };

  const handleCancelRefresh = () => {
    setShowConfirmDialog(false);
  };

  // 处理发送消息
  const handleSendMessage = useCallback(() => {
    if (userInput.trim() && onSendMessage) {
      onSendMessage(userInput.trim());
      setUserInput('');
    }
  }, [userInput, onSendMessage]);

  // 处理回车键发送
  const handleKeyPress = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  }, [handleSendMessage]);
  return (
    <div className="p-3 bg-white border-t border-gray-200">
      <div className="flex items-center gap-3">
        <button
          onClick={handleRefresh}
          className="p-2 text-gray-500 bg-gray-100 rounded-full hover:bg-gray-200 transition-colors"
          aria-label="刷新"
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="w-5 h-5">
            <path d="M21.5 2v6h-6M2.5 22v-6h6M2 11.5a10 10 0 0 1 18.8-4.3M22 12.5a10 10 0 0 1-18.8 4.2" />
          </svg>
        </button>

        {/* 只在live模式显示音频相关控件 */}
        {mode === 'live' && (
          <>
            <AudioWaveform isListening={isListening} className="w-[120px]" />

            <button
              onClick={onToggleListening}
              className={`p-2 rounded-full transition-colors ${
                isListening
                  ? 'bg-red-100 text-red-600 hover:bg-red-200'
                  : 'bg-blue-100 text-blue-600 hover:bg-blue-200'
              }`}
              aria-label={isListening ? '暂停识别' : '开始识别'}
            >
              {isListening ? (
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="w-5 h-5">
                  <rect x="6" y="4" width="4" height="16" />
                  <rect x="14" y="4" width="4" height="16" />
                </svg>
              ) : (
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="w-5 h-5">
                  <polygon points="13 2 3 14 12 14 11 22 21 10 12 10 13 2" />
                </svg>
              )}
            </button>
          </>
        )}

        {/* mock模式显示输入框或状态信息 */}
        {mode === 'mock' && (
          <>
            {isInterviewStarted && hasMessages ? (
              <div className="flex-1 flex gap-3">
                <textarea
                  value={userInput}
                  onChange={(e) => setUserInput(e.target.value)}
                  onKeyDown={handleKeyPress}
                  placeholder="请输入您的回答..."
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
                  rows={1}
                />
                <button
                  onClick={handleSendMessage}
                  disabled={!userInput.trim()}
                  className="px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:bg-gray-300 disabled:cursor-not-allowed"
                >
                  发送
                </button>
              </div>
            ) : (
              <div className="flex-1 flex justify-center">
                <span className="text-sm text-gray-600">AI模拟面试进行中</span>
              </div>
            )}
          </>
        )}
      </div>

      {/* 确认刷新弹窗 */}
      <ConfirmDialog
        isOpen={showConfirmDialog}
        title="确认刷新面试会话"
        message="刷新将会清除当前的面试记录和聊天历史，同时重新获取屏幕共享和系统音频。此操作无法撤销，确定要继续吗？"
        confirmText="确认刷新"
        cancelText="取消"
        onConfirm={handleConfirmRefresh}
        onCancel={handleCancelRefresh}
        variant="warning"
      />
    </div>
  );
}

export default ControlBar;
