import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { targetPositionService, TargetPosition } from '../lib/api/apiService';

// 意向岗位状态接口
interface PositionState {
  // 数据状态
  positions: TargetPosition[];
  isLoading: boolean;
  error: string | null;
  lastFetchTime: number;
  
  // 缓存配置
  cacheTimeout: number; // 缓存超时时间（毫秒）
  
  // 操作方法
  fetchPositions: (force?: boolean) => Promise<void>;
  addPosition: (position: TargetPosition) => void;
  updatePosition: (id: string, updates: Partial<TargetPosition>) => void;
  removePosition: (id: string) => void;
  clearPositions: () => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  
  // 缓存相关方法
  isCacheValid: () => boolean;
  shouldRefresh: () => boolean;
  invalidateCache: () => void;
}

// 缓存配置常量
const CACHE_DURATION = 5 * 60 * 1000; // 5分钟缓存
const STALE_THRESHOLD = 10 * 60 * 1000; // 10分钟后强制刷新

// 创建意向岗位状态管理
export const usePositionStore = create<PositionState>()(
  persist(
    (set, get) => ({
      // 初始状态
      positions: [],
      isLoading: false,
      error: null,
      lastFetchTime: 0,
      cacheTimeout: CACHE_DURATION,

      // 获取岗位列表（带智能缓存）
      fetchPositions: async (force = false) => {
        const state = get();
        
        // 检查是否需要获取数据
        if (!force && state.isCacheValid() && state.positions.length > 0) {
          console.log('🎯 PositionStore: Using cached data');
          return;
        }

        // 避免重复请求
        if (state.isLoading) {
          console.log('🎯 PositionStore: Request already in progress');
          return;
        }

        console.log('🎯 PositionStore: Fetching positions from API');
        set({ isLoading: true, error: null });

        try {
          const positions = await targetPositionService.getTargetPositions();

          // 🔍 调试：检查获取到的岗位数据结构
          console.log('🔍 PositionStore: Raw positions data:', positions);
          positions.forEach((pos, index) => {
            if (!pos.id) {
              console.error(`❌ PositionStore: Position ${index} missing ID:`, pos);
            }
          });

          set({
            positions,
            isLoading: false,
            error: null,
            lastFetchTime: Date.now()
          });

          console.log('✅ PositionStore: Positions fetched successfully', positions.length);
        } catch (error: any) {
          console.error('❌ PositionStore: Failed to fetch positions', error);
          
          set({
            isLoading: false,
            error: error.message || '获取岗位列表失败'
          });
          
          throw error; // 重新抛出错误供组件处理
        }
      },

      // 添加新岗位
      addPosition: (position: TargetPosition) => {
        set(state => ({
          positions: [...state.positions, position],
          lastFetchTime: Date.now() // 更新缓存时间
        }));
        console.log('✅ PositionStore: Position added', position.positionName);
      },

      // 更新岗位
      updatePosition: (id: string, updates: Partial<TargetPosition>) => {
        set(state => ({
          positions: state.positions.map(pos => 
            pos.id === id ? { ...pos, ...updates } : pos
          ),
          lastFetchTime: Date.now()
        }));
        console.log('✅ PositionStore: Position updated', id);
      },

      // 删除岗位
      removePosition: (id: string) => {
        set(state => ({
          positions: state.positions.filter(pos => pos.id !== id),
          lastFetchTime: Date.now()
        }));
        console.log('✅ PositionStore: Position removed', id);
      },

      // 清空岗位列表
      clearPositions: () => {
        set({
          positions: [],
          lastFetchTime: 0,
          error: null
        });
        console.log('🗑️ PositionStore: Positions cleared');
      },

      // 设置加载状态
      setLoading: (loading: boolean) => {
        set({ isLoading: loading });
      },

      // 设置错误状态
      setError: (error: string | null) => {
        set({ error });
      },

      // 检查缓存是否有效
      isCacheValid: () => {
        const state = get();
        const now = Date.now();
        const timeSinceLastFetch = now - state.lastFetchTime;
        return timeSinceLastFetch < state.cacheTimeout;
      },

      // 检查是否应该刷新数据
      shouldRefresh: () => {
        const state = get();
        const now = Date.now();
        const timeSinceLastFetch = now - state.lastFetchTime;
        return timeSinceLastFetch > STALE_THRESHOLD || state.positions.length === 0;
      },

      // 使缓存失效
      invalidateCache: () => {
        set({ lastFetchTime: 0 });
        console.log('🔄 PositionStore: Cache invalidated');
      }
    }),
    {
      name: 'position-storage',
      // 只持久化核心数据，不持久化加载状态
      partialize: (state) => ({
        positions: state.positions,
        lastFetchTime: state.lastFetchTime
      }),
      // 🎯 启用水合状态跟踪
      onRehydrateStorage: () => (state) => {
        console.log('🎯 PositionStore: Rehydration completed', state);
      }
    }
  )
);

// 导出便捷的选择器钩子
export const usePositions = () => {
  const positions = usePositionStore(state => state.positions);
  // 🎯 确保始终返回数组，防止 persist 恢复时的类型问题
  return Array.isArray(positions) ? positions : [];
};
export const usePositionsLoading = () => usePositionStore(state => state.isLoading);
export const usePositionsError = () => usePositionStore(state => state.error);
export const usePositionActions = () => usePositionStore(state => ({
  fetchPositions: state.fetchPositions,
  addPosition: state.addPosition,
  updatePosition: state.updatePosition,
  removePosition: state.removePosition,
  clearPositions: state.clearPositions,
  invalidateCache: state.invalidateCache
}));

// 导出用于性能监控的钩子
export const usePositionCacheInfo = () => usePositionStore(state => ({
  lastFetchTime: state.lastFetchTime,
  isCacheValid: state.isCacheValid(),
  shouldRefresh: state.shouldRefresh(),
  positionCount: state.positions.length
}));
