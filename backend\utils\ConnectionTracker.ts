/**
 * 后端连接追踪器 - 诊断连接创建源头
 */

export interface BackendConnectionInfo {
  id: string;
  type: 'websocket' | 'asr' | 'dashscope' | 'whisper';
  source: string;
  timestamp: number;
  status: 'creating' | 'connected' | 'disconnected' | 'error';
  config?: any;
  lastActivity?: number;
  metadata?: Record<string, any>;
}

export interface BackendConnectionReport {
  totalConnections: number;
  activeConnections: number;
  connectionsByType: Record<string, number>;
  connectionsBySource: Record<string, number>;
  duplicateConnections: BackendConnectionInfo[];
  timeline: BackendConnectionInfo[];
}

class BackendConnectionTracker {
  private static instance: BackendConnectionTracker;
  private connections: Map<string, BackendConnectionInfo> = new Map();
  private isEnabled: boolean = true;

  private constructor() {
    this.isEnabled = process.env.NODE_ENV === 'development';
    
    if (this.isEnabled) {
      console.log('🔍 BackendConnectionTracker: Initialized');
      
      // 定期生成报告
      setInterval(() => {
        this.generatePeriodicReport();
      }, 30000); // 每30秒
    }
  }

  public static getInstance(): BackendConnectionTracker {
    if (!BackendConnectionTracker.instance) {
      BackendConnectionTracker.instance = new BackendConnectionTracker();
    }
    return BackendConnectionTracker.instance;
  }

  /**
   * 追踪连接创建
   */
  public trackConnection(
    id: string,
    type: 'websocket' | 'asr' | 'dashscope' | 'whisper',
    source: string,
    config?: any
  ): void {
    if (!this.isEnabled) return;

    const connectionInfo: BackendConnectionInfo = {
      id,
      type,
      source,
      timestamp: Date.now(),
      status: 'creating',
      config,
      metadata: {
        nodeVersion: process.version,
        platform: process.platform,
        pid: process.pid
      }
    };

    this.connections.set(id, connectionInfo);
    
    console.log(`🔍 BackendConnectionTracker: New ${type} connection tracked`, {
      id,
      source,
      total: this.connections.size
    });

    // 检查是否有重复连接
    this.checkForDuplicates(connectionInfo);
  }

  /**
   * 更新连接状态
   */
  public updateConnectionStatus(
    id: string,
    status: BackendConnectionInfo['status'],
    metadata?: Record<string, any>
  ): void {
    if (!this.isEnabled) return;

    const connection = this.connections.get(id);
    if (connection) {
      connection.status = status;
      connection.lastActivity = Date.now();
      if (metadata) {
        connection.metadata = { ...connection.metadata, ...metadata };
      }
      
      console.log(`🔍 BackendConnectionTracker: Connection ${id} status updated to ${status}`);
    }
  }

  /**
   * 移除连接追踪
   */
  public removeConnection(id: string): void {
    if (!this.isEnabled) return;

    const connection = this.connections.get(id);
    if (connection) {
      connection.status = 'disconnected';
      console.log(`🔍 BackendConnectionTracker: Connection ${id} removed`);
      
      // 保留一段时间用于分析，然后删除
      setTimeout(() => {
        this.connections.delete(id);
      }, 60000); // 1分钟后删除
    }
  }

  /**
   * 生成连接报告
   */
  public generateReport(): BackendConnectionReport {
    const connections = Array.from(this.connections.values());
    const activeConnections = connections.filter(c => c.status === 'connected');
    
    const connectionsByType: Record<string, number> = {};
    const connectionsBySource: Record<string, number> = {};
    
    connections.forEach(conn => {
      connectionsByType[conn.type] = (connectionsByType[conn.type] || 0) + 1;
      connectionsBySource[conn.source] = (connectionsBySource[conn.source] || 0) + 1;
    });

    // 检测重复连接
    const duplicateConnections = this.findDuplicateConnections();

    return {
      totalConnections: connections.length,
      activeConnections: activeConnections.length,
      connectionsByType,
      connectionsBySource,
      duplicateConnections,
      timeline: connections.sort((a, b) => a.timestamp - b.timestamp)
    };
  }

  /**
   * 检查重复连接
   */
  private checkForDuplicates(newConnection: BackendConnectionInfo): void {
    const duplicates = Array.from(this.connections.values()).filter(conn => 
      conn.id !== newConnection.id &&
      conn.type === newConnection.type &&
      conn.source === newConnection.source &&
      conn.status !== 'disconnected'
    );

    if (duplicates.length > 0) {
      console.warn(`⚠️ BackendConnectionTracker: Potential duplicate connection detected!`, {
        new: newConnection,
        existing: duplicates
      });
    }
  }

  /**
   * 查找重复连接
   */
  private findDuplicateConnections(): BackendConnectionInfo[] {
    const connections = Array.from(this.connections.values());
    const duplicates: BackendConnectionInfo[] = [];
    
    const seen = new Set<string>();
    connections.forEach(conn => {
      const key = `${conn.type}_${conn.source}`;
      if (seen.has(key) && conn.status !== 'disconnected') {
        duplicates.push(conn);
      }
      seen.add(key);
    });
    
    return duplicates;
  }

  /**
   * 定期报告
   */
  private generatePeriodicReport(): void {
    const report = this.generateReport();
    
    if (report.totalConnections > 0) {
      console.log('🔍 BackendConnectionTracker: Periodic Report', {
        total: report.totalConnections,
        active: report.activeConnections,
        byType: report.connectionsByType,
        bySource: report.connectionsBySource
      });
      
      if (report.duplicateConnections.length > 0) {
        console.warn('⚠️ Backend duplicate connections detected:', report.duplicateConnections);
      }
    }
  }

  /**
   * 导出报告
   */
  public exportReport(): void {
    const report = this.generateReport();
    console.log('🔍 BackendConnectionTracker: Full Report', report);
  }

  /**
   * 清理所有追踪数据
   */
  public clearAll(): void {
    this.connections.clear();
    console.log('🔍 BackendConnectionTracker: All tracking data cleared');
  }
}

// 导出单例实例
export const backendConnectionTracker = BackendConnectionTracker.getInstance();
