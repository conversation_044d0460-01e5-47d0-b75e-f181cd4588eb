好的，这里是为您准备的详细操作清单，帮助您完成支付套餐与面巾展示页面的创建。

**重要提示:**

- 请确保您已经打开了 `local-mianshijun` 项目的 `frontend` 文件夹。
- 以下路径均以 `frontend/src/` 为根目录。
- 如果您不确定某个文件或文件夹是否存在，请创建它。
- 代码块可以直接复制粘贴到对应文件中。

------

**操作清单 (Action Checklist)**

**第 1 步：确认/创建组件文件夹结构 (Confirm/Create Component Folder Structure)**

请确保您的 frontend/src/components/ 目录下有如下结构。如果这些 Pricing, Layout 文件夹或其中的文件不存在，请先创建它们。

(Please ensure you have the following structure within your frontend/src/components/ directory. If the Pricing or Layout folders, or the files within them, do not exist, please create them first.)

```
frontend/
└── src/
    ├── components/
    │   ├── Pricing/
    │   │   ├── PackageCard.tsx
    │   │   ├── PointsPackage.tsx
    │   │   └── TabNavigation.tsx
    │   ├── layout/             // (或者直接在 components/ 下)
    │   │   ├── Footer.tsx
    │   │   ├── Layout.tsx
    │   │   └── Navbar.tsx
    │   └── ui/                 // (TabNavigation.tsx 也可能在这里)
    │       └── (其他通用UI组件)
    └── pages/
        └── PricingPage.tsx
```

- **中文**: 确认或创建 `frontend/src/components/Pricing/` 文件夹。
- **English**: Confirm or create the `frontend/src/components/Pricing/` folder.
- **中文**: 确认或创建 `frontend/src/components/layout/` 文件夹 (如果您的 `Footer.tsx`, `Layout.tsx`, `Navbar.tsx` 在这里的话)。如果它们直接在 `components/` 下，那也可以。
- **English**: Confirm or create the `frontend/src/components/layout/` folder (if your `Footer.tsx`, `Layout.tsx`, `Navbar.tsx` are located here). If they are directly under `components/`, that's fine too.

**第 2 步：放置/更新 `PackageCard.tsx` 组件 (Place/Update `PackageCard.tsx` Component)**

- **中文**:

  1. 确保 `PackageCard.tsx` 文件位于 `frontend/src/components/Pricing/PackageCard.tsx`。
  2. 您提供的 `PackageCard.tsx` 文件内容看起来是合适的，它包含了展示套餐标题、描述、价格、特性、备注和购买按钮的功能。请确保其内容如下 (或者与您提供的版本一致，如果它满足需求)。

- **English**:

  1. Ensure the `PackageCard.tsx` file is located at `frontend/src/components/Pricing/PackageCard.tsx`.
  2. The content of the `PackageCard.tsx` file you provided seems suitable. It includes functionality to display package title, description, price, features, notes, and a buy button. Please ensure its content is as follows (or consistent with the version you provided, if it meets the requirements).

  TypeScript

  ```
  // 文件路径: frontend/src/components/Pricing/PackageCard.tsx
  // (File Path: frontend/src/components/Pricing/PackageCard.tsx)
  import React from 'react';
  import { motion } from 'framer-motion';
  import { Check, CheckCircle } from 'lucide-react';
  
  interface PackageProps {
    pkg: { // Changed from 'package' to 'pkg' to avoid conflict with reserved keyword
      id: string;
      title: string;
      description: string;
      price: number;
      features: string[];
      notes: string[];
      color: string;
      borderColor: string;
      popular: boolean;
      popularText?: string;
      buyButtonText?: string;
    };
  }
  
  const PackageCard: React.FC<PackageProps> = ({ pkg }) => {
    return (
      <motion.div
        whileHover={{ y: -5 }}
        className={`rounded-xl border ${pkg.borderColor} overflow-hidden ${pkg.color} relative h-full backdrop-blur-sm shadow-lg`}
      >
        {pkg.popular && pkg.popularText && (
          <div className="absolute top-0 right-0 left-0">
            <div className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white p-2 relative">
              <div className="text-sm font-medium text-center">
                {pkg.popularText}
              </div>
              <CheckCircle className="absolute -bottom-3 right-3 h-6 w-6 text-white bg-blue-600 rounded-full p-1" />
            </div>
          </div>
        )}
  
        <div className={`p-6 ${pkg.popular ? 'pt-12' : 'pt-6'}`}> {/* Increased padding */}
          <h3 className="text-xl font-bold text-gray-800 mb-2">{pkg.title}</h3> {/* Increased font size */}
          <p className="text-sm text-gray-600 mb-5 h-10">{pkg.description}</p> {/* Added height for alignment */}
  
          <div className="space-y-2.5 mb-5"> {/* Increased bottom margin */}
            {pkg.features.map((feature, index) => (
              <div key={index} className="flex items-start gap-2">
                <Check className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
                <span className="text-sm text-gray-700">{feature}</span>
              </div>
            ))}
          </div>
  
          <div className="space-y-1.5 mb-5"> {/* Increased bottom margin and spacing */}
            {pkg.notes.map((note, index) => (
              <div key={index} className="text-xs text-gray-500 flex items-center gap-1.5"> {/* Increased gap */}
                <span className="h-1.5 w-1.5 rounded-full bg-gray-300" /> {/* Increased size */}
                <span>{note}</span>
              </div>
            ))}
          </div>
  
          <div className="text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-indigo-600 mb-6"> {/* Increased font size and margin */}
            ¥{pkg.price}
            {/* <span className="text-base text-gray-700">元</span> Removed 元 as ¥ is present */}
          </div>
  
          <button
            className={`w-full py-3 rounded-lg text-center text-sm font-medium transition-all ${
              pkg.popular
                ? 'bg-gradient-to-r from-blue-600 to-indigo-600 text-white hover:shadow-xl focus:ring-4 focus:ring-blue-300'
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300 focus:ring-4 focus:ring-gray-200'
            }`}
          >
            {pkg.buyButtonText || '立即购买'}
          </button>
        </div>
      </motion.div>
    );
  };
  
  export default PackageCard;
  ```

**第 3 步：放置/更新 `PointsPackage.tsx` 组件 (Place/Update `PointsPackage.tsx` Component)**

- **中文**:

  1. 确保 `PointsPackage.tsx` 文件位于 `frontend/src/components/Pricing/PointsPackage.tsx`。
  2. 您提供的 `PointsPackage.tsx` 文件内容看起来是合适的。请确保其内容如下。

- **English**:

  1. Ensure the `PointsPackage.tsx` file is located at `frontend/src/components/Pricing/PointsPackage.tsx`.
  2. The content of the `PointsPackage.tsx` file you provided seems suitable. Please ensure its content is as follows.

  TypeScript

  ```
  // 文件路径: frontend/src/components/Pricing/PointsPackage.tsx
  // (File Path: frontend/src/components/Pricing/PointsPackage.tsx)
  import React from 'react';
  import { motion } from 'framer-motion';
  import { Sparkles } from 'lucide-react'; // Or another icon like ShoppingCart if preferred
  
  interface PointsPackageProps {
    pkg: { // Changed from 'package' to 'pkg' for consistency
      id: string;
      points: number;
      price: number;
      buyButtonText?: string;
    };
  }
  
  const PointsPackage: React.FC<PointsPackageProps> = ({ pkg }) => {
    return (
      <motion.div
        whileHover={{ scale: 1.02, y: -3 }}
        className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl border border-gray-200 p-5 flex justify-between items-center backdrop-blur-sm shadow-lg"
      >
        <div className="flex items-baseline">
          <span className="text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-indigo-600">
            {pkg.points}
          </span>
          <span className="text-base text-gray-600 ml-1.5">面巾</span>
        </div>
  
        <motion.button
          whileHover={{ scale: 1.05, boxShadow: "0px 5px 15px rgba(0, 0, 0, 0.1)"}}
          whileTap={{ scale: 0.95 }}
          className="bg-gradient-to-r from-gray-800 to-gray-900 text-white px-5 py-2.5 rounded-full flex items-center gap-2 text-sm font-medium hover:shadow-md transition-shadow"
          // className="bg-blue-600 text-white px-5 py-2 rounded-lg flex items-center gap-2 text-sm font-medium hover:bg-blue-700 transition-colors"
        >
          <span>¥{pkg.price} {pkg.buyButtonText || '购买'}</span>
          <Sparkles className="h-4 w-4" />
        </motion.button>
      </motion.div>
    );
  };
  
  export default PointsPackage;
  ```

**第 4 步：放置/更新 `TabNavigation.tsx` 组件 (Place/Update `TabNavigation.tsx` Component)**

- **中文**:

  1. 确保 `TabNavigation.tsx` 文件位于 `frontend/src/components/Pricing/TabNavigation.tsx` (或 `frontend/src/components/ui/TabNavigation.tsx`，根据您的项目结构调整)。
  2. 您提供的 `TabNavigation.tsx` 文件内容看起来是合适的。请确保其内容如下。

- **English**:

  1. Ensure the `TabNavigation.tsx` file is located at `frontend/src/components/Pricing/TabNavigation.tsx` (or `frontend/src/components/ui/TabNavigation.tsx`, adjust based on your project structure).
  2. The content of the `TabNavigation.tsx` file you provided seems suitable. Please ensure its content is as follows.

  TypeScript

  ```
  // 文件路径: frontend/src/components/Pricing/TabNavigation.tsx (或 ui 目录下)
  // (File Path: frontend/src/components/Pricing/TabNavigation.tsx (or under ui folder))
  import React from 'react';
  import { motion } from 'framer-motion';
  
  interface TabNavigationProps {
    tabs: string[];
    activeTab: string;
    onTabChange: (tab: string) => void;
  }
  
  const TabNavigation: React.FC<TabNavigationProps> = ({
    tabs,
    activeTab,
    onTabChange,
  }) => {
    return (
      <div className="border-b border-gray-200 mb-8">
        <div className="flex space-x-8 justify-center"> {/* Centered tabs */}
          {tabs.map((tab) => (
            <button
              key={tab}
              onClick={() => onTabChange(tab)}
              className={`relative py-4 px-2 text-lg font-medium transition-colors ${ // Increased font size and padding
                activeTab === tab
                  ? 'text-blue-600'
                  : 'text-gray-500 hover:text-gray-800'
              }`}
            >
              {tab}
              {activeTab === tab && (
                <motion.div
                  layoutId="activePricingTab" // Unique layoutId
                  className="absolute bottom-0 left-0 right-0 h-1 bg-blue-600 rounded-t-sm" // Thicker underline
                  initial={false}
                  transition={{ type: 'spring', stiffness: 400, damping: 30 }}
                />
              )}
            </button>
          ))}
        </div>
      </div>
    );
  };
  
  export default TabNavigation;
  ```

**第 5 步：更新 `PricingPage.tsx` 页面 (Update `PricingPage.tsx` Page)**

- **中文**:

  1. 确保 `PricingPage.tsx` 文件位于 `frontend/src/pages/PricingPage.tsx`。
  2. **重要**: 用下面的代码替换您 `PricingPage.tsx` 的全部内容。这段代码引入了标签页导航，并使用了您任务描述中提供的套餐和面巾数据。

- **English**:

  1. Ensure the `PricingPage.tsx` file is located at `frontend/src/pages/PricingPage.tsx`.
  2. **Important**: Replace the entire content of your `PricingPage.tsx` with the code below. This code introduces tab navigation and uses the package and Mianjin data provided in your task description.

  TypeScript

  ```
  // 文件路径: frontend/src/pages/PricingPage.tsx
  // (File Path: frontend/src/pages/PricingPage.tsx)
  import React, { useState } from 'react';
  import { motion } from 'framer-motion';
  import PackageCard from '../components/Pricing/PackageCard';
  import PointsPackage from '../components/Pricing/PointsPackage';
  import TabNavigation from '../components/Pricing/TabNavigation'; // 确认路径正确
  import Layout from '../components/layout/Layout'; // 确认路径正确, 如果 Layout 包裹 Navbar 和 Footer
  import { CheckCircle, Gift, Repeat } from 'lucide-react'; // Added Repeat for exchange icon
  
  const PricingPage: React.FC = () => {
    const [activeTab, setActiveTab] = useState('套餐礼包'); // '套餐礼包' or '面巾值'
  
    const tabs = ['套餐礼包', '面巾值'];
  
    const packagesData = [
      {
        id: 'basic',
        title: '基础礼包',
        description: '体验AI面试的智能力量',
        price: 68,
        features: ['AI模拟面试2次', 'AI正式面试2次', '附赠100面巾'],
        notes: ['智能题库实时更新', 'AI深度分析报告'],
        color: 'bg-gradient-to-br from-gray-50 to-gray-100',
        borderColor: 'border-gray-200',
        popular: false,
        buyButtonText: '¥68 购买',
      },
      {
        id: 'advanced',
        title: '高级礼包',
        description: '高性价比，满足进阶需求', // Updated description
        price: 138,
        features: ['AI模拟面试4次', 'AI正式面试4次', '附赠400面巾'],
        notes: ['智能题库实时更新', 'AI深度分析报告', '面试时长无忧'], // Updated notes
        color: 'bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50', // More vibrant gradient
        borderColor: 'border-blue-300',
        popular: true,
        popularText: '75%用户的选择',
        buyButtonText: '¥138 购买',
      },
      {
        id: 'super',
        title: '超级礼包',
        description: '全面提升，面试通关利器', // Updated description
        price: 298,
        features: ['AI模拟面试10次', 'AI正式面试8次', '附赠800面巾'],
        notes: ['智能题库实时更新', 'AI深度分析报告', '优先客服支持'], // Updated notes
        color: 'bg-gradient-to-br from-gray-50 to-gray-100',
        borderColor: 'border-gray-200',
        popular: false,
        buyButtonText: '¥298 购买',
      },
    ];
  
    const pointsPackagesData = [
      { id: 'points_100', points: 100, price: 30, buyButtonText: '¥30 购买' },
      { id: 'points_200', points: 200, price: 60, buyButtonText: '¥60 购买' },
      { id: 'points_1000', points: 1000, price: 90, buyButtonText: '¥90 购买' }, // Corrected price as per task
    ];
  
    const redemptionRules = [
      { amount: 100, description: 'AI模拟面试 (1次)'},
      { amount: 200, description: 'AI正式面试 (1次)'}
    ];
  
    return (
      // Layout 组件会提供 Navbar 和 Footer
      // <Layout>
        <div className="container mx-auto px-4 py-12 min-h-[calc(100vh-150px)]"> {/* Adjust min-h based on your navbar/footer height */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-center mb-12"
          >
            <h1 className="text-4xl font-extrabold text-gray-800 mb-3">
              灵活套餐，助您成功
            </h1>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              选择适合您的面试提升方案，或按需充值面巾，自由兑换各项服务。
            </p>
          </motion.div>
  
          <TabNavigation tabs={tabs} activeTab={activeTab} onTabChange={setActiveTab} />
  
          {activeTab === '套餐礼包' && (
            <motion.div
              key="packages"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
              className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16"
            >
              {packagesData.map((pkg) => (
                <PackageCard key={pkg.id} pkg={pkg} />
              ))}
            </motion.div>
          )}
  
          {activeTab === '面巾值' && (
            <motion.div
              key="points"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
              className="max-w-2xl mx-auto space-y-6 mb-16"
            >
              <h2 className="text-2xl font-bold text-gray-800 text-center mb-6">充值面巾</h2>
              {pointsPackagesData.map((pkg) => (
                <PointsPackage key={pkg.id} pkg={pkg} />
              ))}
            </motion.div>
          )}
  
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            className="bg-white bg-opacity-70 backdrop-blur-md p-8 rounded-xl shadow-xl border border-gray-200"
          >
            <h3 className="text-2xl font-bold text-gray-800 mb-6 flex items-center gap-3 justify-center">
              <Repeat className="h-7 w-7 text-blue-600" />
              面巾兑换规则
            </h3>
            <div className="grid md:grid-cols-2 gap-x-8 gap-y-4 max-w-xl mx-auto">
              {redemptionRules.map((rule, index) => (
                 <div key={index} className="flex items-center gap-4 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-100">
                  <div className="flex-shrink-0 h-12 w-12 rounded-full bg-gradient-to-br from-blue-500 to-indigo-600 text-white flex items-center justify-center font-bold text-lg shadow-md">
                    {rule.amount}
                  </div>
                  <div>
                    <p className="font-semibold text-gray-700">{rule.amount} 面巾</p>
                    <p className="text-sm text-gray-600">{rule.description}</p>
                  </div>
                </div>
              ))}
            </div>
            <p className="text-center text-xs text-gray-500 mt-6">
              * 面巾是面试君平台虚拟道具，可用于兑换平台内指定服务。最终解释权归面试君所有。
            </p>
          </motion.div>
        </div>
      // </Layout>
    );
  };
  
  export default PricingPage;
  ```

  - 注意

    :

    - 代码中的 `import Layout from '../components/layout/Layout';` 和 `<Layout>` 组件的使用。如果您的 `PricingPage` 是通过路由在 `Layout` 组件内部渲染的（通常是这样），那么您不需要在 `PricingPage.tsx` 内部再次包裹 `<Layout>`。如果不是，则需要确保 `Layout` 组件被正确使用以包含导航栏和页脚。我暂时注释掉了 `<Layout>`，假设您的路由配置会处理它。
    - `PackageCard` 和 `PointsPackage` 的导入路径 (`../components/Pricing/...`) 可能需要根据您的实际文件结构进行调整。
    - `TabNavigation` 的导入路径也需要确认。

**第 6 步：确认/更新 `Layout.tsx` (Confirm/Update `Layout.tsx`)**

- **中文**:

  1. 确保 `Layout.tsx` 文件位于 `frontend/src/components/layout/Layout.tsx` (或 `frontend/src/components/Layout.tsx`)。
  2. 此文件通常包含应用的整体布局，如导航栏 (Navbar) 和页脚 (Footer)。您提供的 `Layout.tsx` 应该可以工作。

- **English**:

  1. Ensure the `Layout.tsx` file is located at `frontend/src/components/layout/Layout.tsx` (or `frontend/src/components/Layout.tsx`).
  2. This file typically contains the overall application layout, such as the Navbar and Footer. The `Layout.tsx` you provided should work.

  TypeScript

  ```
  // 文件路径: frontend/src/components/layout/Layout.tsx (或 components/Layout.tsx)
  // (File Path: frontend/src/components/layout/Layout.tsx (or components/Layout.tsx))
  import React from 'react';
  import { Outlet } from 'react-router-dom';
  import Navbar from './Navbar'; // 确认路径
  import Footer from './Footer'; // 确认路径
  
  const Layout: React.FC = () => {
    return (
      <div className="min-h-screen flex flex-col bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 selection:bg-blue-500 selection:text-white">
        <Navbar />
        <main className="flex-grow">
          <Outlet /> {/* 子页面 (如 PricingPage) 会在这里渲染 */}
        </main>
        <Footer />
      </div>
    );
  };
  
  export default Layout;
  ```

  - **注意**: 确保 `Navbar` 和 `Footer` 的导入路径正确。

**第 7 步：确认/更新 `Navbar.tsx` (Confirm/Update `Navbar.tsx`)**

- **中文**:

  1. 确保 `Navbar.tsx` 文件位于 `frontend/src/components/layout/Navbar.tsx` (或 `frontend/src/components/Navbar.tsx`)。
  2. 您提供的 `Navbar.tsx` 中已经有了指向 `/pricing` 的链接 "套餐价格"，这是很好的。

- **English**:

  1. Ensure the `Navbar.tsx` file is located at `frontend/src/components/layout/Navbar.tsx` (or `frontend/src/components/Navbar.tsx`).
  2. The `Navbar.tsx` you provided already includes a link "套餐价格" pointing to `/pricing`, which is good.

  TypeScript

  ```
  // 文件路径: frontend/src/components/layout/Navbar.tsx (或 components/Navbar.tsx)
  // (File Path: frontend/src/components/layout/Navbar.tsx (or components/Navbar.tsx))
  import React from 'react';
  import { Link, NavLink } from 'react-router-dom'; // Use NavLink for active styling
  import { Briefcase, Users, Bell, DollarSign, LogIn, UserCircle } from 'lucide-react'; // Added icons
  
  const Navbar: React.FC = () => {
    const isLoggedIn = false; // Replace with actual auth state
  
    const navLinkClasses = ({ isActive }: { isActive: boolean }) =>
    `px-3 py-2 rounded-md text-sm font-medium transition-colors duration-150 ease-in-out ${
      isActive
        ? 'bg-blue-600 text-white shadow-sm'
        : 'text-gray-700 hover:bg-blue-100 hover:text-blue-700'
    }`;
  
  
    return (
      <header className="bg-white bg-opacity-90 backdrop-blur-md shadow-sm sticky top-0 z-50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <Link to="/" className="flex items-center gap-2 shrink-0">
                <Briefcase className="text-blue-600 h-7 w-7" />
                <span className="font-bold text-xl text-gray-800">面试君</span>
              </Link>
            </div>
  
            <nav className="hidden md:flex items-center space-x-2 lg:space-x-4">
              <NavLink to="/" className={navLinkClasses}>首页</NavLink>
              <NavLink to="/interview-prep" className={navLinkClasses}>模拟面试</NavLink> {/* Example link */}
              <NavLink to="/pricing" className={navLinkClasses}>套餐价格</NavLink>
              <NavLink to="/community" className={navLinkClasses}>交流社区</NavLink> {/* Example link */}
              <NavLink to="/about" className={navLinkClasses}>关于我们</NavLink>
            </nav>
  
            <div className="flex items-center gap-3 sm:gap-4">
              <button className="text-gray-500 hover:text-blue-600 p-2 rounded-full hover:bg-blue-100 transition-colors">
                <Bell className="h-5 w-5" />
                <span className="sr-only">Notifications</span>
              </button>
  
              {isLoggedIn ? (
                 <NavLink to="/profile" className="p-2 rounded-full hover:bg-gray-100">
                   <UserCircle className="h-7 w-7 text-gray-600" />
                 </NavLink>
              ) : (
                <Link
                  to="/login"
                  className="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-full hover:bg-blue-700 transition-colors shadow-sm flex items-center gap-1.5"
                >
                  <LogIn className="h-4 w-4" />
                  登录/注册
                </Link>
              )}
            </div>
          </div>
        </div>
      </header>
    );
  };
  
  export default Navbar;
  ```

**第 8 步：确认/更新 `Footer.tsx` (Confirm/Update `Footer.tsx`)**

- **中文**:

  1. 确保 `Footer.tsx` 文件位于 `frontend/src/components/layout/Footer.tsx` (或 `frontend/src/components/Footer.tsx`)。
  2. 您提供的 `Footer.tsx` 应该可以工作。

- **English**:

  1. Ensure the `Footer.tsx` file is located at `frontend/src/components/layout/Footer.tsx` (or `frontend/src/components/Footer.tsx`).
  2. The `Footer.tsx` you provided should work.

  TypeScript

  ```
  // 文件路径: frontend/src/components/layout/Footer.tsx (或 components/Footer.tsx)
  // (File Path: frontend/src/components/layout/Footer.tsx (or components/Footer.tsx))
  import React from 'react';
  import { Briefcase, Mail, Phone, MessageCircle, Github, Linkedin, Twitter } from 'lucide-react'; // Added social icons
  
  const Footer: React.FC = () => {
    const currentYear = new Date().getFullYear();
  
    return (
      <footer className="bg-white bg-opacity-80 backdrop-blur-sm py-10 mt-12 border-t border-gray-200">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-8">
            {/* Column 1: Brand and Mission */}
            <div className="md:col-span-2 lg:col-span-1">
              <div className="flex items-center gap-2 mb-4">
                <Briefcase className="text-blue-600 h-7 w-7" />
                <span className="font-bold text-xl text-gray-800">面试君</span>
              </div>
              <p className="text-gray-600 text-sm leading-relaxed">
                致力于通过AI技术，助力求职者提升面试技能，自信应对各类挑战，斩获理想Offer。
              </p>
            </div>
  
            {/* Column 2: Product Links */}
            <div>
              <h3 className="font-semibold text-gray-800 mb-4">核心服务</h3>
              <ul className="space-y-2.5 text-sm">
                <li><a href="#" className="text-gray-600 hover:text-blue-600 transition-colors">AI模拟面试</a></li>
                <li><a href="#" className="text-gray-600 hover:text-blue-600 transition-colors">AI正式面试</a></li>
                <li><a href="/pricing" className="text-gray-600 hover:text-blue-600 transition-colors">套餐与价格</a></li>
                <li><a href="#" className="text-gray-600 hover:text-blue-600 transition-colors">面试评估报告</a></li>
              </ul>
            </div>
  
            {/* Column 3: Support Links */}
            <div>
              <h3 className="font-semibold text-gray-800 mb-4">用户支持</h3>
              <ul className="space-y-2.5 text-sm">
                <li><a href="#" className="text-gray-600 hover:text-blue-600 transition-colors">帮助中心</a></li>
                <li><a href="#" className="text-gray-600 hover:text-blue-600 transition-colors">常见问题解答</a></li>
                <li><a href="#" className="text-gray-600 hover:text-blue-600 transition-colors">联系客服</a></li>
                <li><a href="#" className="text-gray-600 hover:text-blue-600 transition-colors">服务条款</a></li>
              </ul>
            </div>
  
            {/* Column 4: Contact Info */}
            <div>
              <h3 className="font-semibold text-gray-800 mb-4">保持联系</h3>
              <ul className="space-y-3 text-sm">
                <li className="flex items-center gap-2.5 text-gray-600 hover:text-blue-600 transition-colors">
                  <Mail className="h-4 w-4 flex-shrink-0" />
                  <a href="mailto:<EMAIL>"><EMAIL></a>
                </li>
                <li className="flex items-center gap-2.5 text-gray-600 hover:text-blue-600 transition-colors">
                  <Phone className="h-4 w-4 flex-shrink-0" />
                  <span>************ (工作日 9:00-18:00)</span>
                </li>
                <li className="flex items-center gap-2.5 text-gray-600 hover:text-blue-600 transition-colors">
                  <MessageCircle className="h-4 w-4 flex-shrink-0" />
                  <span>微信公众号: 面试君Pro</span>
                </li>
              </ul>
              <div className="mt-5 flex space-x-4">
                  <a href="#" className="text-gray-500 hover:text-blue-600 transition-colors"><Github className="h-5 w-5" /></a>
                  <a href="#" className="text-gray-500 hover:text-blue-600 transition-colors"><Linkedin className="h-5 w-5" /></a>
                  <a href="#" className="text-gray-500 hover:text-blue-600 transition-colors"><Twitter className="h-5 w-5" /></a>
              </div>
            </div>
          </div>
  
          <div className="pt-8 border-t border-gray-200 text-center">
            <p className="text-sm text-gray-500">
              &copy; {currentYear} 面试君 (Mianshijun.com). 保留所有权利.
            </p>
            <p className="text-xs text-gray-400 mt-1">
              为梦想助力，与面试君同行.
            </p>
          </div>
        </div>
      </footer>
    );
  };
  
  export default Footer;
  ```

**第 9 步：配置路由 (Configure Routing)**

- **中文**:

  1. 打开您的路由配置文件，通常是 `frontend/src/App.tsx` 或 `frontend/src/main.tsx` (如果您使用 React Router)。
  2. 确保您有一个指向 `PricingPage` 的路由。

- **English**:

  1. Open your routing configuration file, typically `frontend/src/App.tsx` or `frontend/src/main.tsx` (if you are using React Router).
  2. Ensure you have a route pointing to `PricingPage`.

  修改 frontend/src/App.tsx (示例):

  (Modify frontend/src/App.tsx (example)):

  TypeScript

  ```
  // 文件路径: frontend/src/App.tsx
  // (File Path: frontend/src/App.tsx)
  import React from 'react';
  import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
  import Layout from './components/layout/Layout'; // 确认路径
  import HomePage from './pages/HomePage'; // 假设您有首页
  import PricingPage from './pages/PricingPage'; // 引入 PricingPage
  // 其他页面导入...
  // import LoginPage from './pages/StandaloneLoginPage';
  // import RegisterPage from './pages/RegisterPage'; // 假设有注册页
  // import DashboardPage from './pages/DashboardPage'; // 假设有仪表盘页
  // import InterviewSetupPage from './pages/InterviewSetupPage';
  // import AIInterviewPage from './pages/AIInterviewPage';
  // import ProfilePage from './pages/Profile';
  // import SettingsPage from './pages/Settings';
  import NotFoundPage from './pages/NotFoundPage';
  
  
  function App() {
    return (
      <Router>
        <Routes>
          <Route path="/" element={<Layout />}>
            <Route index element={<HomePage />} />
            <Route path="pricing" element={<PricingPage />} /> {/* 新增定价页面路由 */}
            {/* <Route path="login" element={<LoginPage />} /> */}
            {/* <Route path="register" element={<RegisterPage />} /> */}
            {/* <Route path="dashboard" element={<DashboardPage />} /> */}
            {/* <Route path="interview-setup" element={<InterviewSetupPage />} /> */}
            {/* <Route path="ai-interview/:sessionId" element={<AIInterviewPage />} /> */}
            {/* <Route path="profile" element={<ProfilePage />} /> */}
            {/* <Route path="settings" element={<SettingsPage />} /> */}
            <Route path="*" element={<NotFoundPage />} />
          </Route>
        </Routes>
      </Router>
    );
  }
  
  export default App;
  ```

  - 注意

    :

    - 确保您已经安装了 `react-router-dom`。如果没有，请在终端中运行 `npm install react-router-dom` 或 `yarn add react-router-dom`。
    - 这里的 `HomePage` 是一个示例，请替换为您实际的首页组件。
    - `Layout` 组件作为父路由，使得所有嵌套的子路由（包括 `PricingPage`）都会拥有 `Navbar` 和 `Footer`。

**第 10 步：运行和测试 (Run and Test)**

- 中文

  :

  1. 在您的项目 `frontend` 文件夹的终端中，运行 `npm run dev` 或 `yarn dev` (或您项目的启动命令)。
  2. 在浏览器中打开您的应用，并导航到 `/pricing` 页面 (例如 `http://localhost:5173/pricing`)。
  3. 检查页面是否按预期显示，套餐和面巾信息是否正确，标签页是否可以切换。

- English

  :

  1. In your terminal, in the `frontend` folder of your project, run `npm run dev` or `yarn dev` (or your project's start command).
  2. Open your application in the browser and navigate to the `/pricing` page (e.g., `http://localhost:5173/pricing`).
  3. Check if the page displays as expected, if the package and Mianjin information is correct, and if the tabs can be switched.

------

请按照这些步骤操作。如果您在任何步骤中遇到问题，或者某个文件的实际内容与您提供的有很大差异，请告诉我，我会提供进一步的帮助！