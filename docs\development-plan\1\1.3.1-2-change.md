# 用户注册和登录功能改进文档

本文档详细记录了对面试君项目的用户注册、登录功能以及界面组件的修改内容，以便于后续参考或复制实现。

## 目录

- [修改概述](#修改概述)
- [修改文件列表](#修改文件列表)
- [详细修改内容](#详细修改内容)
  - [StandaloneLoginPage.tsx](#standaloneloginpagetsx)
  - [StandaloneApp.tsx](#standaloneapptsx)
  - [Header.tsx](#headertsx)
  - [Sidebar.tsx](#sidebartsx)
- [功能测试](#功能测试)
- [已知问题](#已知问题)

## 修改概述

本次修改主要针对以下几个方面：

1. **用户注册和登录功能**：
   - 实现了完整的注册和登录流程
   - 添加了API调用和错误处理
   - 优化了表单验证和用户体验

2. **界面组件更新**：
   - 修改了Header组件，使其始终显示通知图标和用户头像
   - 更新了Sidebar组件，根据登录状态显示不同的用户信息
   - 调整了路由结构，确保登录后正确跳转到Dashboard

## 修改文件列表

1. `frontend/src/pages/StandaloneLoginPage.tsx` - 登录和注册页面
2. `frontend/src/StandaloneApp.tsx` - 应用路由配置
3. `frontend/src/components/Header.tsx` - 顶部导航栏组件
4. `frontend/src/components/Sidebar.tsx` - 侧边栏组件

## 详细修改内容

### StandaloneLoginPage.tsx

主要修改了表单提交函数`handleSubmit`，实现了实际的API调用和响应处理。

```tsx
const handleSubmit = async (e: React.FormEvent) => {
  e.preventDefault();
  
  if (isLoading) return;
  
  // 表单验证
  try {
    if (mode === 'register') {
      registerSchema.parse(formData);
    } else {
      loginSchema.parse(formData);
    }
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errorMessages = error.errors.map(err => `${err.path}: ${err.message}`).join(', ');
      setErrorMessage(errorMessages);
      return;
    }
  }
  
  setIsLoading(true);
  setErrorMessage('');
  setSuccessMessage('');
  
  try {
    if (mode === 'register') {
      // 调用注册API
      await registerUser(formData as RegisterFormValues);
      setSuccessMessage('注册成功！请登录您的账号');
      setMode('login');
      setFormData({ email: formData.email, password: '', confirmPassword: '' });
    } else {
      // 调用登录API
      const response = await loginUser(formData.email, formData.password);
      setSuccessMessage('登录成功！正在跳转...');
      
      // 保存token和用户信息
      if (response.token) {
        localStorage.setItem('token', response.token);
        localStorage.setItem('user', JSON.stringify(response.user));
        
        // 更新全局状态
        login(response.token, response.user);
        
        // 跳转到Dashboard
        navigate('/app/dashboard');
      }
    }
  } catch (error) {
    console.error('操作失败:', error);
    setErrorMessage(error instanceof Error ? error.message : '操作失败，请稍后再试');
  } finally {
    setIsLoading(false);
  }
};
```

### StandaloneApp.tsx

修改了路由配置，确保登录后正确跳转到Dashboard。

```tsx
import { BrowserRouter, Routes, Route } from 'react-router-dom';
import StandaloneLoginPage from './pages/StandaloneLoginPage';
import Layout from './components/Layout';
import Dashboard from './components/Dashboard';
import ApiTest from './components/ApiTest';

const StandaloneApp = () => {
  return (
    <BrowserRouter>
      <Routes>
        <Route path="/ai-login" element={<StandaloneLoginPage />} />
        <Route path="/new-register" element={<StandaloneLoginPage initialMode="register" />} />
        <Route path="/api-test" element={<ApiTest />} />
        <Route path="/app" element={<Layout />}>
          <Route path="dashboard" element={<Dashboard />} />
        </Route>
        <Route path="/" element={<StandaloneLoginPage />} />
      </Routes>
    </BrowserRouter>
  );
};

export default StandaloneApp;
```

### Header.tsx

对Header组件进行了以下修改：

1. 移除了导航栏中的链接
2. 始终显示通知图标和头像，不再根据登录状态条件渲染
3. 头像内容根据登录状态显示不同的字母
4. 下拉菜单中的"退出登录"选项只在用户登录后显示

```tsx
import React, { useState, useRef, useEffect } from 'react';
import { Bell, Search, LogOut, User, Settings } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import useAuthStore from '../stores/authStore';

interface HeaderProps {
  greeting?: string;
}

const Header: React.FC<HeaderProps> = ({ greeting }) => {
  const navigate = useNavigate();
  const { isAuthenticated, user, logout } = useAuthStore();
  const [showUserMenu, setShowUserMenu] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);
  
  // 处理用户菜单显示/隐藏
  const toggleUserMenu = () => {
    setShowUserMenu(!showUserMenu);
  };
  
  // 处理菜单项点击
  const handleMenuItemClick = (action: string) => {
    setShowUserMenu(false);
    
    switch (action) {
      case 'profile':
        navigate('/app/profile');
        break;
      case 'settings':
        navigate('/app/settings');
        break;
      default:
        break;
    }
  };
  
  // 处理登出
  const handleLogout = () => {
    setShowUserMenu(false);
    logout();
    navigate('/ai-login');
  };
  
  // 点击外部关闭菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setShowUserMenu(false);
      }
    };
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);
  
  return (
    <header className="bg-white border-b border-gray-100 py-4 px-8 flex items-center justify-between">
      <div className="flex items-center gap-4">
        {greeting && (
          <h1 className="text-xl font-bold text-gray-800">
            {greeting}
          </h1>
        )}
      </div>
      
      <div className="flex items-center gap-6">
        <div className="relative">
          <input
            type="text"
            placeholder="搜索面试技巧..."
            className="w-64 px-4 py-2 pl-10 bg-gray-50 rounded-xl text-sm focus:outline-none focus:ring-2 focus:ring-sky-500"
          />
          <Search className="w-4 h-4 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" />
        </div>
        
        {/* 通知图标 - 始终显示 */}
        <button className="relative p-2 text-gray-500 hover:text-gray-700 transition-colors">
          <Bell className="w-5 h-5" />
          <span className="absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full"></span>
        </button>
        
        {/* 用户头像 - 始终显示 */}
        <div className="relative" ref={menuRef}>
          <button 
            onClick={toggleUserMenu}
            className="h-10 w-10 rounded-xl bg-gradient-to-br from-violet-500 to-purple-500 flex items-center justify-center text-white font-medium shadow-lg hover:shadow-xl transition-all"
            title="点击显示菜单"
          >
            {isAuthenticated 
              ? (user?.name?.[0]?.toUpperCase() || user?.email?.[0]?.toUpperCase() || 'U')
              : 'A'
            }
          </button>
          
          {/* 用户下拉菜单 */}
          {showUserMenu && (
            <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-xl py-1 z-10 border border-gray-100">
              <div className="px-4 py-2 border-b border-gray-100">
                <p className="text-sm font-medium text-gray-700 truncate">{user?.name || '用户'}</p>
                <p className="text-xs text-gray-500 truncate">{user?.email}</p>
              </div>
              
              <button 
                onClick={() => handleMenuItemClick('profile')}
                className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
              >
                <User className="w-4 h-4 mr-2" />
                个人中心
              </button>
              
              <button 
                onClick={() => handleMenuItemClick('settings')}
                className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
              >
                <Settings className="w-4 h-4 mr-2" />
                设置
              </button>
              
              {isAuthenticated && (
                <>
                  <div className="border-t border-gray-100 my-1"></div>
                  
                  <button 
                    onClick={handleLogout}
                    className="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-gray-100"
                  >
                    <LogOut className="w-4 h-4 mr-2" />
                    退出登录
                  </button>
                </>
              )}
            </div>
          )}
        </div>
      </div>
    </header>
  );
};

export default Header;
```

### Sidebar.tsx

对Sidebar组件进行了以下修改：

1. 添加了用户认证状态管理
2. 添加了邮箱掩码函数，用于隐藏部分邮箱字符
3. 更新了用户信息显示部分，根据登录状态显示不同的内容

```tsx
import React from 'react';
import { Home, UserCheck, Award, CreditCard, Gift, Settings } from 'lucide-react';
import useAuthStore from '../stores/authStore';

// 邮箱掩码函数，将邮箱中间部分替换为星号
const maskEmail = (email: string): string => {
  if (!email || !email.includes('@')) return email;
  
  const [username, domain] = email.split('@');
  if (username.length <= 3) return email; // 如果用户名太短，不做处理
  
  const maskedUsername = username.substring(0, 3) + '****';
  return `${maskedUsername}@${domain}`;
};

const Sidebar: React.FC = () => {
  const { isAuthenticated, user } = useAuthStore();
  const menuItems = [
    { icon: <Home size={20} />, text: '主页', active: true },
    { icon: <UserCheck size={20} />, text: 'AI模拟面试', active: false },
    { icon: <Award size={20} />, text: 'AI正式面试', active: false },
    { icon: <CreditCard size={20} />, text: '充值中心', active: false },
    { icon: <Gift size={20} />, text: '分享有礼', active: false },
  ];

  return (
    <div className="w-72 min-h-screen bg-white p-8 flex flex-col">
      <div className="flex items-center gap-3 mb-12">
        <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-purple-500 to-indigo-500 flex items-center justify-center text-white font-bold text-xl shadow-lg">
          M
        </div>
        <span className="text-xl font-bold text-gray-800">面试君</span>
      </div>
      
      <nav className="flex-1">
        <ul className="space-y-2">
          {menuItems.map((item, index) => (
            <li key={index}>
              <a 
                href="#" 
                className={`flex items-center gap-4 px-4 py-3 rounded-xl transition-all ${
                  item.active 
                    ? 'bg-gray-900 text-white shadow-lg' 
                    : 'text-gray-600 hover:bg-gray-50'
                }`}
              >
                {item.icon}
                <span className="font-medium">{item.text}</span>
              </a>
            </li>
          ))}
        </ul>
      </nav>
      
      <div className="bg-gradient-to-br from-sky-50 to-indigo-50 rounded-xl p-6">
        <div className="flex items-center justify-between mb-4">
          <span className="font-medium text-gray-800">面试余额</span>
          <button className="p-2 bg-white rounded-lg hover:shadow-md transition-shadow">
            <Settings className="w-4 h-4 text-gray-600" />
          </button>
        </div>
        
        <div className="space-y-3">
          <div className="bg-white bg-opacity-60 backdrop-blur-sm rounded-lg p-3 flex items-center justify-between">
            <span className="text-sm text-gray-600">模拟面试</span>
            <span className="font-medium text-gray-800">4 次</span>
          </div>
          <div className="bg-white bg-opacity-60 backdrop-blur-sm rounded-lg p-3 flex items-center justify-between">
            <span className="text-sm text-gray-600">正式面试</span>
            <span className="font-medium text-gray-800">3 次</span>
          </div>
        </div>
        
        <div className="mt-4 pt-4 border-t border-gray-100">
          <div className="text-sm text-gray-500 text-center">
            {isAuthenticated 
              ? maskEmail(user?.email || '未知邮箱') 
              : '未登录'
            }
          </div>
        </div>
      </div>
    </div>
  );
};

export default Sidebar;
```

## 功能测试

修改完成后，进行了以下测试：

1. **注册功能**：
   - 填写有效的邮箱和密码，成功注册
   - 尝试使用已注册的邮箱，显示适当的错误信息
   - 验证密码不匹配时的错误提示

2. **登录功能**：
   - 使用正确的凭据登录，成功跳转到Dashboard
   - 使用错误的凭据登录，显示适当的错误信息

3. **界面组件**：
   - 验证Header中的通知图标和头像始终显示
   - 验证点击头像时显示下拉菜单
   - 验证Sidebar中根据登录状态显示不同的用户信息

## 已知问题

1. **后端连接问题**：
   - 错误：`net::ERR_CONNECTION_REFUSED`
   - 原因：后端服务器可能未运行
   - 解决方案：启动后端服务器 `cd backend && npm run dev`

2. **登录状态保持**：
   - 刷新页面后可能丢失登录状态
   - 解决方案：在应用启动时检查localStorage中的token，并恢复登录状态

3. **响应式设计**：
   - 在小屏幕设备上，界面布局可能不够优化
   - 解决方案：添加媒体查询和响应式样式
