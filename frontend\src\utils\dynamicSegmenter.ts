import { VADDetector } from './vadDetector';
import { AudioBufferManager, BufferTriggerInfo } from './audioBufferManager';
import { VADResult } from '../types/vadTypes';

export interface AudioSegment {
  id: string;
  audioData: Float32Array;
  startTime: number;
  endTime: number;
  vadResults: VADResult[];
  confidence: number;
  segmentType: 'speech' | 'silence' | 'mixed';
  triggerReason: string;
  duration: number;
}

export interface SegmentationConfig {
  minSegmentDuration: number;    // 最小段时长（ms）
  maxSegmentDuration: number;    // 最大段时长（ms）
  confidenceThreshold: number;   // 置信度阈值
  historySize: number;           // 历史段数量
}

export const DEFAULT_SEGMENTATION_CONFIG: SegmentationConfig = {
  minSegmentDuration: 500,       // 500ms
  maxSegmentDuration: 15000,     // 15秒
  confidenceThreshold: 0.3,      // 🔥 临时降低到30%置信度进行测试
  historySize: 10                // 保存10个历史段
};

/**
 * 动态音频分片器
 * 根据VAD结果和缓冲区状态进行智能音频分片
 */
export class DynamicSegmenter {
  private vadDetector: VADDetector;
  private bufferManager: AudioBufferManager;
  private config: SegmentationConfig;
  
  private currentSegment: Partial<AudioSegment> | null = null;
  private segmentHistory: AudioSegment[] = [];
  private segmentCounter: number = 0;

  constructor(
    vadDetector: VADDetector,
    bufferManager: AudioBufferManager,
    config: Partial<SegmentationConfig> = {}
  ) {
    this.vadDetector = vadDetector;
    this.bufferManager = bufferManager;
    this.config = { ...DEFAULT_SEGMENTATION_CONFIG, ...config };
    
    console.log('DynamicSegmenter initialized with config:', this.config);
  }

  /**
   * 处理音频数据块
   */
  processAudioChunk(audioData: Float32Array): AudioSegment | null {
    console.log('🔧 DynamicSegmenter processAudioChunk called!'); // 🔥 关键调试：检查分段器是否被调用

    // 1. VAD检测
    const vadResult = this.vadDetector.detectVoiceActivity(audioData);

    // 2. 缓冲区管理和触发检测
    const triggerInfo = this.bufferManager.addAudioChunk(audioData, vadResult);

    // 3. 更新当前段
    this.updateCurrentSegment(audioData, vadResult);

    // 4. 检查是否需要完成当前段
    if (triggerInfo.shouldTrigger) {
      console.log('🎯 Segment trigger detected, finalizing segment'); // 🔥 调试：段触发
      return this.finalizeCurrentSegment(triggerInfo);
    }

    return null;
  }

  /**
   * 更新当前音频段
   */
  private updateCurrentSegment(audioData: Float32Array, vadResult: VADResult): void {
    if (!this.currentSegment) {
      // 创建新段
      this.currentSegment = {
        id: `segment-${++this.segmentCounter}-${Date.now()}`,
        startTime: vadResult.timestamp,
        endTime: vadResult.timestamp,
        vadResults: [vadResult],
        confidence: vadResult.confidence,
        segmentType: vadResult.isSpeech ? 'speech' : 'silence'
      };
      
      console.log(`🆕 New segment started: ${this.currentSegment.id}`);
    } else {
      // 更新现有段
      this.currentSegment.endTime = vadResult.timestamp;
      this.currentSegment.vadResults!.push(vadResult);
      
      // 更新段的元数据
      this.updateSegmentMetadata();
    }
  }

  /**
   * 更新段的元数据
   */
  private updateSegmentMetadata(): void {
    if (!this.currentSegment || !this.currentSegment.vadResults) return;
    
    const vadResults = this.currentSegment.vadResults;
    const speechCount = vadResults.filter(r => r.isSpeech).length;
    const totalCount = vadResults.length;
    const speechRatio = speechCount / totalCount;
    
    // 更新段类型
    if (speechRatio === 0) {
      this.currentSegment.segmentType = 'silence';
    } else if (speechRatio === 1) {
      this.currentSegment.segmentType = 'speech';
    } else {
      this.currentSegment.segmentType = 'mixed';
    }
    
    // 更新置信度（加权平均）
    const totalConfidence = vadResults.reduce((sum, r) => sum + r.confidence, 0);
    this.currentSegment.confidence = totalConfidence / totalCount;
    
    // 计算时长
    if (this.currentSegment.startTime && this.currentSegment.endTime) {
      this.currentSegment.duration = this.currentSegment.endTime - this.currentSegment.startTime;
    }
  }

  /**
   * 完成当前音频段
   */
  private finalizeCurrentSegment(triggerInfo: BufferTriggerInfo): AudioSegment | null {
    if (!this.currentSegment) {
      console.warn('No current segment to finalize');
      return null;
    }
    
    // 获取完整的音频数据
    const audioData = this.bufferManager.getRecognitionBuffer();
    
    // 创建最终的音频段
    const finalSegment: AudioSegment = {
      id: this.currentSegment.id!,
      audioData,
      startTime: this.currentSegment.startTime!,
      endTime: this.currentSegment.endTime!,
      vadResults: this.currentSegment.vadResults!,
      confidence: this.currentSegment.confidence!,
      segmentType: this.currentSegment.segmentType!,
      triggerReason: triggerInfo.reason,
      duration: this.currentSegment.duration || 0
    };
    
    // 验证段的有效性
    if (!this.isValidSegment(finalSegment)) {
      console.warn(`Invalid segment detected: ${finalSegment.id}`, {
        duration: finalSegment.duration,
        confidence: finalSegment.confidence,
        audioLength: finalSegment.audioData.length
      });
      
      // 重置当前段但不返回无效段
      this.currentSegment = null;
      return null;
    }
    
    // 添加到历史记录
    this.addToHistory(finalSegment);
    
    // 重置当前段
    this.currentSegment = null;
    
    console.log(`✅ Segment finalized: ${finalSegment.id}`, {
      type: finalSegment.segmentType,
      duration: finalSegment.duration,
      confidence: finalSegment.confidence.toFixed(2),
      triggerReason: finalSegment.triggerReason,
      audioLength: finalSegment.audioData.length
    });
    
    return finalSegment;
  }

  /**
   * 验证音频段的有效性
   */
  private isValidSegment(segment: AudioSegment): boolean {
    console.log(`🔍 验证音频段 ${segment.id}:`, {
      duration: segment.duration,
      minDuration: this.config.minSegmentDuration,
      confidence: segment.confidence,
      confidenceThreshold: this.config.confidenceThreshold,
      audioDataLength: segment.audioData.length,
      vadResultsLength: segment.vadResults.length
    });

    // 检查时长
    if (segment.duration < this.config.minSegmentDuration) {
      console.log(`❌ 段时长不足: ${segment.duration}ms < ${this.config.minSegmentDuration}ms`);
      return false;
    }

    if (segment.duration > this.config.maxSegmentDuration) {
      console.warn(`⚠️ 段时长超过最大值: ${segment.duration}ms > ${this.config.maxSegmentDuration}ms`);
      // 不拒绝，但记录警告
    }

    // 检查置信度
    if (segment.confidence < this.config.confidenceThreshold) {
      console.log(`❌ 置信度不足: ${segment.confidence.toFixed(3)} < ${this.config.confidenceThreshold}`);
      return false;
    }

    // 检查音频数据
    if (segment.audioData.length === 0) {
      console.log(`❌ 音频数据为空`);
      return false;
    }

    // 检查VAD结果
    if (segment.vadResults.length === 0) {
      console.log(`❌ VAD结果为空`);
      return false;
    }

    console.log(`✅ 音频段验证通过: ${segment.id}`);
    return true;
  }

  /**
   * 添加到历史记录
   */
  private addToHistory(segment: AudioSegment): void {
    this.segmentHistory.push(segment);
    
    // 保持历史记录大小
    if (this.segmentHistory.length > this.config.historySize) {
      const removed = this.segmentHistory.shift();
      console.log(`📚 Removed old segment from history: ${removed?.id}`);
    }
  }

  /**
   * 获取段历史记录
   */
  getSegmentHistory(): AudioSegment[] {
    return [...this.segmentHistory];
  }

  /**
   * 获取最近的段
   */
  getRecentSegments(count: number): AudioSegment[] {
    return this.segmentHistory.slice(-count);
  }

  /**
   * 获取当前段信息
   */
  getCurrentSegmentInfo(): Partial<AudioSegment> | null {
    if (!this.currentSegment) return null;
    
    return {
      id: this.currentSegment.id,
      startTime: this.currentSegment.startTime,
      endTime: this.currentSegment.endTime,
      segmentType: this.currentSegment.segmentType,
      confidence: this.currentSegment.confidence,
      duration: this.currentSegment.duration
    };
  }

  /**
   * 获取分片统计信息
   */
  getSegmentationStats(): {
    totalSegments: number;
    averageDuration: number;
    averageConfidence: number;
    segmentTypeDistribution: Record<string, number>;
    triggerReasonDistribution: Record<string, number>;
  } {
    if (this.segmentHistory.length === 0) {
      return {
        totalSegments: 0,
        averageDuration: 0,
        averageConfidence: 0,
        segmentTypeDistribution: {},
        triggerReasonDistribution: {}
      };
    }
    
    const totalDuration = this.segmentHistory.reduce((sum, s) => sum + s.duration, 0);
    const totalConfidence = this.segmentHistory.reduce((sum, s) => sum + s.confidence, 0);
    
    const typeDistribution: Record<string, number> = {};
    const reasonDistribution: Record<string, number> = {};
    
    for (const segment of this.segmentHistory) {
      typeDistribution[segment.segmentType] = (typeDistribution[segment.segmentType] || 0) + 1;
      reasonDistribution[segment.triggerReason] = (reasonDistribution[segment.triggerReason] || 0) + 1;
    }
    
    return {
      totalSegments: this.segmentHistory.length,
      averageDuration: totalDuration / this.segmentHistory.length,
      averageConfidence: totalConfidence / this.segmentHistory.length,
      segmentTypeDistribution: typeDistribution,
      triggerReasonDistribution: reasonDistribution
    };
  }

  /**
   * 强制完成当前段
   */
  forceFinalize(): AudioSegment | null {
    if (!this.currentSegment) return null;
    
    const triggerInfo: BufferTriggerInfo = {
      shouldTrigger: true,
      reason: 'manual',
      confidence: 1.0,
      audioLength: 0,
      segmentDuration: this.currentSegment.duration || 0
    };
    
    return this.finalizeCurrentSegment(triggerInfo);
  }

  /**
   * 重置分片器
   */
  reset(): void {
    this.currentSegment = null;
    this.segmentHistory = [];
    this.segmentCounter = 0;
    this.bufferManager.reset();
    this.vadDetector.reset();
    
    console.log('DynamicSegmenter reset');
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<SegmentationConfig>): void {
    this.config = { ...this.config, ...newConfig };
    console.log('DynamicSegmenter config updated:', this.config);
  }

  /**
   * 获取当前配置
   */
  getConfig(): SegmentationConfig {
    return { ...this.config };
  }
}
