import type { VercelRequest, VercelResponse } from '@vercel/node';
import { z } from 'zod';
import { PrismaClient } from '@prisma/client';
import VerificationService from '../../services/verificationService';

const sendCodeSchema = z.object({
  identifier: z.string().min(1, '邮箱或手机号不能为空'),
  type: z.enum(['EMAIL', 'SMS'], { message: '验证码类型无效' }),
  purpose: z.enum(['LOGIN']).default('LOGIN')
});

export default async function handler(req: VercelRequest, res: VercelResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      message: 'Method not allowed'
    });
  }

  try {
    // 验证请求参数
    const { identifier, type, purpose } = sendCodeSchema.parse(req.body);

    // 验证邮箱或手机号格式
    if (type === 'EMAIL') {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(identifier)) {
        return res.status(400).json({
          success: false,
          message: '请输入有效的邮箱地址',
          error: { code: 'INVALID_EMAIL' }
        });
      }
    } else if (type === 'SMS') {
      const phoneRegex = /^1[3-9]\d{9}$/;
      if (!phoneRegex.test(identifier)) {
        return res.status(400).json({
          success: false,
          message: '请输入有效的手机号',
          error: { code: 'INVALID_PHONE' }
        });
      }
    }

    // 注释掉用户存在检查，支持自动注册
    // 检查用户是否存在（登录场景）
    // if (purpose === 'LOGIN') {
    //   const prisma = new PrismaClient();
    //   try {
    //     const user = await prisma.user.findFirst({
    //       where: type === 'EMAIL'
    //         ? { email: identifier }
    //         : { phoneNumber: identifier }
    //     });

    //     if (!user) {
    //       return res.status(404).json({
    //         success: false,
    //         message: type === 'EMAIL' ? '该邮箱尚未注册' : '该手机号尚未注册',
    //         error: { code: 'USER_NOT_FOUND' }
    //       });
    //     }
    //   } finally {
    //     await prisma.$disconnect();
    //   }
    // }

    // 获取客户端信息
    const ipAddress = (req.headers['x-forwarded-for'] as string)?.split(',')[0] || 
                     (req.headers['x-real-ip'] as string) || 
                     req.connection?.remoteAddress || 
                     'unknown';
    const userAgent = req.headers['user-agent'] || 'unknown';

    // 发送验证码
    const verificationService = new VerificationService();
    const result = await verificationService.sendVerificationCode(
      identifier, 
      type, 
      purpose,
      ipAddress,
      userAgent
    );

    if (result.success) {
      return res.status(200).json({
        success: true,
        message: result.message,
        data: {
          expiresIn: result.expiresIn,
          canResendAfter: 60
        }
      });
    } else {
      return res.status(429).json({
        success: false,
        message: result.message,
        error: { code: 'RATE_LIMITED' }
      });
    }

  } catch (error: any) {
    console.error('Send verification code error:', {
      message: error?.message,
      stack: error?.stack,
      name: error?.name,
      code: error?.code,
      fullError: error
    });

    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: error.errors[0].message,
        error: { code: 'VALIDATION_ERROR', details: error.errors }
      });
    }

    return res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: {
        code: 'INTERNAL_ERROR',
        details: error?.message || 'Unknown error'
      }
    });
  }
}
