// 后端日志保存系统
import fs from 'fs';
import path from 'path';

interface LogEntry {
  timestamp: number;
  level: 'log' | 'info' | 'warn' | 'error' | 'debug';
  message: string;
  source?: string;
}

class BackendLogSaver {
  private logs: LogEntry[] = [];
  private logDir = 'E:\\Data\\Own\\Entrepreneurship\\local-mianshijun\\docs\\development-plan\\log';
  private maxLogs = 1000; // 最大日志条数
  private autoSaveInterval: NodeJS.Timeout | null = null;
  private isCapturing = false;

  constructor() {
    this.ensureLogDirectory();
  }

  // 确保日志目录存在
  private ensureLogDirectory(): void {
    if (!fs.existsSync(this.logDir)) {
      fs.mkdirSync(this.logDir, { recursive: true });
      console.log(`📁 Created backend log directory: ${this.logDir}`);
    }
  }

  // 开始捕获日志
  startCapturing(): void {
    if (this.isCapturing) {
      console.log('⚠️ Backend log capturing is already active');
      return;
    }

    this.isCapturing = true;
    console.log('🎯 Backend log capturing started');

    // 重写console方法
    const originalConsole = {
      log: console.log,
      info: console.info,
      warn: console.warn,
      error: console.error,
      debug: console.debug
    };

    // 包装console.log
    console.log = (...args: any[]) => {
      originalConsole.log(...args);
      this.addLog('log', this.formatMessage(args));
    };

    // 包装console.info
    console.info = (...args: any[]) => {
      originalConsole.info(...args);
      this.addLog('info', this.formatMessage(args));
    };

    // 包装console.warn
    console.warn = (...args: any[]) => {
      originalConsole.warn(...args);
      this.addLog('warn', this.formatMessage(args));
    };

    // 包装console.error
    console.error = (...args: any[]) => {
      originalConsole.error(...args);
      this.addLog('error', this.formatMessage(args));
    };

    // 包装console.debug
    console.debug = (...args: any[]) => {
      originalConsole.debug(...args);
      this.addLog('debug', this.formatMessage(args));
    };

    // 启动自动保存
    this.setupAutoSave();
  }

  // 停止捕获日志
  stopCapturing(): void {
    if (!this.isCapturing) {
      return;
    }

    this.isCapturing = false;
    
    // 停止自动保存
    if (this.autoSaveInterval) {
      clearInterval(this.autoSaveInterval);
      this.autoSaveInterval = null;
    }

    console.log('🛑 Backend log capturing stopped');
  }

  // 添加日志条目
  private addLog(level: LogEntry['level'], message: string, source?: string): void {
    const logEntry: LogEntry = {
      timestamp: Date.now(),
      level,
      message,
      source
    };

    this.logs.push(logEntry);

    // 限制日志数量
    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(-this.maxLogs);
    }
  }

  // 格式化消息
  private formatMessage(args: any[]): string {
    return args.map(arg => {
      if (typeof arg === 'object') {
        try {
          return JSON.stringify(arg, null, 2);
        } catch {
          return String(arg);
        }
      }
      return String(arg);
    }).join(' ');
  }

  // 生成文件名
  private generateFileName(): string {
    const now = new Date();
    const timestamp = now.toISOString()
      .replace(/:/g, '-')
      .replace(/\./g, '-')
      .slice(0, 19); // YYYY-MM-DDTHH-MM-SS
    
    return `backend-${timestamp}.txt`;
  }

  // 保存日志到文件
  async saveToFile(): Promise<string | null> {
    if (this.logs.length === 0) {
      console.log('没有后端日志需要保存');
      return null;
    }

    try {
      this.ensureLogDirectory();
      
      const fileName = this.generateFileName();
      const filePath = path.join(this.logDir, fileName);
      
      // 准备日志内容
      const logHeader = [
        `=== BACKEND LOG ===`,
        `Time: ${new Date().toISOString()}`,
        `Total Entries: ${this.logs.length}`,
        `===========================================\n`
      ].join('\n');
      
      // 格式化日志条目
      const formattedLogs = this.logs.map((log, index) => {
        const timestamp = new Date(log.timestamp).toISOString();
        const level = log.level.toUpperCase();
        return `[${index + 1}] ${timestamp} [${level}] ${log.message}`;
      }).join('\n');
      
      const logContent = logHeader + formattedLogs + '\n';
      
      // 写入文件
      fs.writeFileSync(filePath, logContent, 'utf8');
      
      console.log(`✅ 后端日志已自动保存: ${fileName} (${this.logs.length} 条记录)`);
      return fileName;
    } catch (error) {
      console.error('❌ 后端日志保存失败:', error);
      return null;
    }
  }

  // 设置自动保存
  private setupAutoSave(): void {
    // 每10分钟自动保存一次（仅在日志较多时）
    this.autoSaveInterval = setInterval(() => {
      if (this.logs.length > 100) { // 超过100条日志时自动保存
        console.log(`🔄 自动保存后端日志: ${this.logs.length} 条记录`);
        this.saveToFile().then(() => {
          this.clearLogs(); // 保存后清空日志
        });
      }
    }, 10 * 60 * 1000); // 10分钟
  }

  // 清空日志
  clearLogs(): void {
    this.logs = [];
    console.log('🧹 后端日志已清空');
  }

  // 获取日志统计
  getStats(): { total: number; byLevel: Record<string, number> } {
    const byLevel: Record<string, number> = {};
    
    this.logs.forEach(log => {
      byLevel[log.level] = (byLevel[log.level] || 0) + 1;
    });

    return {
      total: this.logs.length,
      byLevel
    };
  }

  // 手动保存并清空（供外部调用）
  async saveAndClear(): Promise<string | null> {
    const fileName = await this.saveToFile();
    if (fileName) {
      this.clearLogs();
    }
    return fileName;
  }
}

// 创建全局实例
const backendLogSaver = new BackendLogSaver();

// 开发环境下自动启用
if (process.env.NODE_ENV === 'development') {
  backendLogSaver.startCapturing();
  console.log('🎯 后端日志捕获已启用');
  console.log('- 日志将自动保存到指定目录');
  console.log('- 超过100条日志时自动保存并清空');
}

export default backendLogSaver;
