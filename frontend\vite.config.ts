import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  optimizeDeps: {
    exclude: ['lucide-react'],
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@new-mianshijun/common': path.resolve(__dirname, '../packages/common/src'),
    },
  },
  server: {
    port: 5173, // 明确设置端口号
    strictPort: true, // 如果端口被占用，则会抛出错误而不是尝试下一个可用端口
    hmr: {
      protocol: 'ws',
      host: 'localhost',
      port: 5173, // 确保HMR使用同样的端口
    },
    proxy: {
      '/api': {
        target: 'http://localhost:3000',
        changeOrigin: true,
        secure: false,
      },
    },
  },
  build: {
    rollupOptions: {
      output: {
        manualChunks: undefined,
      },
    },
  },
});
