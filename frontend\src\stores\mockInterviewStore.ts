import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';

// AI模拟面试专用的消息接口
export interface MockMessage {
  id: string;
  content: string;
  type: 'interviewer' | 'ai-suggestion'; // 在mock模式下：interviewer=AI问题，ai-suggestion=用户回答
  timestamp: number;
  questionId?: string; // AI问题的唯一标识
  isFinal?: boolean; // 用户回答是否为最终版本
}

// AI模拟面试配置接口
export interface MockInterviewConfig {
  selectedPositionId: string | null;
  interviewLanguage: 'chinese' | 'english';
  answerStyle: 'keywords_conversational' | 'conversational';
  audioCollection: boolean;
  screenShareStatus: 'idle' | 'pending' | 'sharing' | 'denied'; // 复用状态类型，但在mock模式下表示麦克风状态
  sharedStream: MediaStream | null; // 在mock模式下存储麦克风流
  companyName?: string;
  positionName?: string;
}

// AI模拟面试会话状态
export interface MockInterviewSession {
  id: string;
  companyName: string;
  positionName: string;
  startTime: number;
  elapsedTime: number;
  messages: MockMessage[];
  isActive: boolean;
  currentQuestionId: string | null;
  interviewStage: 'preparing' | 'in_progress' | 'waiting_for_answer' | 'generating_question' | 'finished';
}

// AI模拟面试状态管理接口
interface MockInterviewState {
  // 配置状态
  config: MockInterviewConfig;
  
  // 会话状态
  session: MockInterviewSession;
  
  // UI状态
  isStartingInterview: boolean;
  isGeneratingQuestion: boolean;
  
  // 配置管理 Actions
  setSelectedPosition: (positionId: string | null) => void;
  setInterviewLanguage: (language: 'chinese' | 'english') => void;
  setAnswerStyle: (style: 'keywords_conversational' | 'conversational') => void;
  setAudioCollection: (enabled: boolean) => void;
  setScreenShareStatus: (status: MockInterviewConfig['screenShareStatus']) => void;
  setSharedStream: (stream: MediaStream | null) => void;
  setCompanyName: (name: string) => void;
  setPositionName: (name: string) => void;
  resetConfig: () => void;
  
  // 会话管理 Actions
  initializeSession: (sessionData: Partial<MockInterviewSession>) => void;
  addMessage: (message: MockMessage) => void;
  updateMessage: (messageId: string, updater: (msg: MockMessage) => MockMessage) => void;
  setMessages: (messages: MockMessage[]) => void;
  clearMessages: () => void;
  setCurrentQuestionId: (questionId: string | null) => void;
  setInterviewStage: (stage: MockInterviewSession['interviewStage']) => void;
  updateElapsedTime: (elapsedTime: number) => void;
  endSession: () => void;
  
  // UI状态管理 Actions
  setIsStartingInterview: (isStarting: boolean) => void;
  setIsGeneratingQuestion: (isGenerating: boolean) => void;
  
  // 选择器
  getConfig: () => MockInterviewConfig;
  getSession: () => MockInterviewSession;
  getMessages: () => MockMessage[];
  getCurrentQuestion: () => MockMessage | null;
  getLatestUserAnswer: () => MockMessage | null;
}

// 默认配置
const defaultConfig: MockInterviewConfig = {
  selectedPositionId: null,
  interviewLanguage: 'chinese',
  answerStyle: 'keywords_conversational',
  audioCollection: false,
  screenShareStatus: 'idle',
  sharedStream: null,
  companyName: '',
  positionName: '',
};

// 默认会话
const defaultSession: MockInterviewSession = {
  id: `mock-session-${Date.now()}`,
  companyName: '模拟公司',
  positionName: '模拟岗位',
  startTime: Date.now(),
  elapsedTime: 0,
  messages: [],
  isActive: false,
  currentQuestionId: null,
  interviewStage: 'preparing',
};

// 创建AI模拟面试Store
export const useMockInterviewStore = create<MockInterviewState>()(
  subscribeWithSelector((set, get) => ({
    // 初始状态
    config: defaultConfig,
    session: defaultSession,
    isStartingInterview: false,
    isGeneratingQuestion: false,

    // 配置管理 Actions
    setSelectedPosition: (positionId) =>
      set((state) => ({
        config: { ...state.config, selectedPositionId: positionId }
      })),
      
    setInterviewLanguage: (language) =>
      set((state) => ({
        config: { ...state.config, interviewLanguage: language }
      })),
      
    setAnswerStyle: (style) =>
      set((state) => ({
        config: { ...state.config, answerStyle: style }
      })),
      
    setAudioCollection: (enabled) =>
      set((state) => ({
        config: { ...state.config, audioCollection: enabled }
      })),
      
    setScreenShareStatus: (status) =>
      set((state) => ({
        config: { ...state.config, screenShareStatus: status }
      })),
      
    setSharedStream: (stream) => {
      const oldStream = get().config.sharedStream;
      if (oldStream && oldStream !== stream) {
        oldStream.getTracks().forEach(track => track.stop());
        console.log('Old mock interview stream stopped in store setter.');
      }
      set((state) => ({
        config: {
          ...state.config,
          sharedStream: stream,
          audioCollection: !!(stream && stream.getAudioTracks().length > 0)
        }
      }));
    },
    
    setCompanyName: (name) =>
      set((state) => ({
        config: { ...state.config, companyName: name },
        session: { ...state.session, companyName: name }
      })),
      
    setPositionName: (name) =>
      set((state) => ({
        config: { ...state.config, positionName: name },
        session: { ...state.session, positionName: name }
      })),
      
    resetConfig: () => {
      const currentStream = get().config.sharedStream;
      if (currentStream) {
        currentStream.getTracks().forEach(track => track.stop());
        console.log('Mock interview stream stopped on resetConfig.');
      }
      set({ 
        config: { ...defaultConfig, sharedStream: null },
        session: defaultSession,
        isStartingInterview: false,
        isGeneratingQuestion: false
      });
    },

    // 会话管理 Actions
    initializeSession: (sessionData) =>
      set((state) => ({
        session: {
          ...defaultSession,
          ...sessionData,
          id: sessionData.id || `mock-session-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
          isActive: true
        }
      })),
      
    addMessage: (message) =>
      set((state) => ({
        session: {
          ...state.session,
          messages: [...state.session.messages, message]
        }
      })),
      
    updateMessage: (messageId, updater) =>
      set((state) => ({
        session: {
          ...state.session,
          messages: state.session.messages.map(msg => 
            msg.id === messageId ? updater(msg) : msg
          )
        }
      })),
      
    setMessages: (messages) =>
      set((state) => ({
        session: { ...state.session, messages }
      })),
      
    clearMessages: () =>
      set((state) => ({
        session: { ...state.session, messages: [] }
      })),
      
    setCurrentQuestionId: (questionId) =>
      set((state) => ({
        session: { ...state.session, currentQuestionId: questionId }
      })),
      
    setInterviewStage: (stage) =>
      set((state) => ({
        session: { ...state.session, interviewStage: stage }
      })),
      
    updateElapsedTime: (elapsedTime) =>
      set((state) => ({
        session: { ...state.session, elapsedTime }
      })),
      
    endSession: () =>
      set((state) => ({
        session: { ...state.session, isActive: false, interviewStage: 'finished' }
      })),

    // UI状态管理 Actions
    setIsStartingInterview: (isStarting) =>
      set({ isStartingInterview: isStarting }),
      
    setIsGeneratingQuestion: (isGenerating) =>
      set({ isGeneratingQuestion: isGenerating }),

    // 选择器
    getConfig: () => get().config,
    getSession: () => get().session,
    getMessages: () => get().session.messages,
    
    getCurrentQuestion: () => {
      const { session } = get();
      if (!session.currentQuestionId) return null;
      return session.messages.find(msg => 
        msg.type === 'interviewer' && msg.questionId === session.currentQuestionId
      ) || null;
    },
    
    getLatestUserAnswer: () => {
      const { session } = get();
      const userAnswers = session.messages.filter(msg => msg.type === 'ai-suggestion');
      return userAnswers.length > 0 ? userAnswers[userAnswers.length - 1] : null;
    },
  }))
);

// 选择器 Hooks
export const useMockConfig = () => useMockInterviewStore((state) => state.config);
export const useMockSession = () => useMockInterviewStore((state) => state.session);
export const useMockMessages = () => useMockInterviewStore((state) => state.session.messages);
export const useMockInterviewStage = () => useMockInterviewStore((state) => state.session.interviewStage);

// Actions Hooks
export const useMockConfigActions = () => useMockInterviewStore((state) => ({
  setSelectedPosition: state.setSelectedPosition,
  setInterviewLanguage: state.setInterviewLanguage,
  setAnswerStyle: state.setAnswerStyle,
  setAudioCollection: state.setAudioCollection,
  setScreenShareStatus: state.setScreenShareStatus,
  setSharedStream: state.setSharedStream,
  setCompanyName: state.setCompanyName,
  setPositionName: state.setPositionName,
  resetConfig: state.resetConfig,
}));

export const useMockSessionActions = () => useMockInterviewStore((state) => ({
  initializeSession: state.initializeSession,
  addMessage: state.addMessage,
  updateMessage: state.updateMessage,
  setMessages: state.setMessages,
  clearMessages: state.clearMessages,
  setCurrentQuestionId: state.setCurrentQuestionId,
  setInterviewStage: state.setInterviewStage,
  updateElapsedTime: state.updateElapsedTime,
  endSession: state.endSession,
}));

export default useMockInterviewStore;
