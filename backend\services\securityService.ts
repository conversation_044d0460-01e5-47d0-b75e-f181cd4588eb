import { PrismaClient } from '@prisma/client';
import RedisService from './redisService';
import crypto from 'crypto';

interface SecurityConfig {
  ipRateLimit: {
    maxRequests: number;
    timeWindow: number; // 分钟
    blockDuration: number; // 分钟
  };
  deviceFingerprint: {
    enabled: boolean;
    maxDevicesPerUser: number;
  };
  anomalyDetection: {
    enabled: boolean;
    maxFailedAttempts: number;
    suspiciousTimeWindow: number; // 分钟
  };
}

interface SecurityEvent {
  type: 'IP_BLOCKED' | 'SUSPICIOUS_ACTIVITY' | 'DEVICE_LIMIT_EXCEEDED' | 'RATE_LIMIT_EXCEEDED';
  ip: string;
  userAgent?: string;
  identifier?: string;
  timestamp: number;
  details?: any;
}

export class SecurityService {
  private prisma: PrismaClient;
  private redis: RedisService;
  private config: SecurityConfig;

  constructor() {
    this.prisma = new PrismaClient();
    this.redis = RedisService.getInstance();
    this.config = {
      ipRateLimit: {
        maxRequests: 10, // 每个IP每10分钟最多10次请求
        timeWindow: 10,
        blockDuration: 30 // 封禁30分钟
      },
      deviceFingerprint: {
        enabled: true,
        maxDevicesPerUser: 5 // 每个用户最多5个设备
      },
      anomalyDetection: {
        enabled: true,
        maxFailedAttempts: 5, // 5次失败后标记为可疑
        suspiciousTimeWindow: 15 // 15分钟内
      }
    };
  }

  /**
   * 检查IP频率限制
   */
  async checkIpRateLimit(ip: string): Promise<{ allowed: boolean; remainingRequests?: number; resetTime?: number }> {
    const key = `ip_rate_limit:${ip}`;
    const blockKey = `ip_blocked:${ip}`;

    try {
      // 检查IP是否被封禁
      const isBlocked = await this.redis.get(blockKey);
      if (isBlocked) {
        const blockData = JSON.parse(isBlocked);
        return {
          allowed: false,
          resetTime: blockData.unblockTime
        };
      }

      // 检查频率限制
      const requestData = await this.redis.get(key);
      const now = Date.now();
      const timeWindow = this.config.ipRateLimit.timeWindow * 60 * 1000;

      if (!requestData) {
        // 首次请求
        await this.redis.set(key, JSON.stringify({
          count: 1,
          firstRequest: now,
          lastRequest: now
        }), this.config.ipRateLimit.timeWindow * 60);

        return {
          allowed: true,
          remainingRequests: this.config.ipRateLimit.maxRequests - 1
        };
      }

      const data = JSON.parse(requestData);
      
      // 检查时间窗口是否过期
      if (now - data.firstRequest > timeWindow) {
        // 重置计数器
        await this.redis.set(key, JSON.stringify({
          count: 1,
          firstRequest: now,
          lastRequest: now
        }), this.config.ipRateLimit.timeWindow * 60);

        return {
          allowed: true,
          remainingRequests: this.config.ipRateLimit.maxRequests - 1
        };
      }

      // 检查是否超过限制
      if (data.count >= this.config.ipRateLimit.maxRequests) {
        // 封禁IP
        const unblockTime = now + this.config.ipRateLimit.blockDuration * 60 * 1000;
        await this.redis.set(blockKey, JSON.stringify({
          blockedAt: now,
          unblockTime: unblockTime,
          reason: 'Rate limit exceeded'
        }), this.config.ipRateLimit.blockDuration * 60);

        // 记录安全事件
        await this.recordSecurityEvent({
          type: 'IP_BLOCKED',
          ip,
          timestamp: now,
          details: { reason: 'Rate limit exceeded', requestCount: data.count }
        });

        return {
          allowed: false,
          resetTime: unblockTime
        };
      }

      // 更新计数器
      data.count += 1;
      data.lastRequest = now;
      await this.redis.set(key, JSON.stringify(data), this.config.ipRateLimit.timeWindow * 60);

      return {
        allowed: true,
        remainingRequests: this.config.ipRateLimit.maxRequests - data.count
      };

    } catch (error) {
      console.error('IP rate limit check failed:', error);
      // 出错时允许请求，但记录错误
      return { allowed: true };
    }
  }

  /**
   * 生成设备指纹
   */
  generateDeviceFingerprint(userAgent: string, ip: string, additionalData?: any): string {
    const fingerprintData = {
      userAgent,
      ip: this.hashIp(ip), // 对IP进行哈希处理保护隐私
      ...additionalData
    };
    
    return crypto
      .createHash('sha256')
      .update(JSON.stringify(fingerprintData))
      .digest('hex');
  }

  /**
   * 检查设备指纹
   */
  async checkDeviceFingerprint(identifier: string, fingerprint: string): Promise<{ allowed: boolean; isNewDevice: boolean }> {
    if (!this.config.deviceFingerprint.enabled) {
      return { allowed: true, isNewDevice: false };
    }

    const key = `user_devices:${identifier}`;

    try {
      const devicesData = await this.redis.get(key);
      const devices = devicesData ? JSON.parse(devicesData) : [];

      // 检查设备是否已存在
      const existingDevice = devices.find((d: any) => d.fingerprint === fingerprint);
      if (existingDevice) {
        // 更新最后使用时间
        existingDevice.lastUsed = Date.now();
        await this.redis.set(key, JSON.stringify(devices), 30 * 24 * 60 * 60); // 30天过期
        return { allowed: true, isNewDevice: false };
      }

      // 检查设备数量限制
      if (devices.length >= this.config.deviceFingerprint.maxDevicesPerUser) {
        await this.recordSecurityEvent({
          type: 'DEVICE_LIMIT_EXCEEDED',
          ip: '',
          identifier,
          timestamp: Date.now(),
          details: { deviceCount: devices.length, fingerprint }
        });

        return { allowed: false, isNewDevice: true };
      }

      // 添加新设备
      devices.push({
        fingerprint,
        firstUsed: Date.now(),
        lastUsed: Date.now()
      });

      await this.redis.set(key, JSON.stringify(devices), 30 * 24 * 60 * 60);
      return { allowed: true, isNewDevice: true };

    } catch (error) {
      console.error('Device fingerprint check failed:', error);
      return { allowed: true, isNewDevice: false };
    }
  }

  /**
   * 异常行为检测
   */
  async detectAnomalousActivity(identifier: string, ip: string, action: string): Promise<{ isSuspicious: boolean; riskScore: number }> {
    if (!this.config.anomalyDetection.enabled) {
      return { isSuspicious: false, riskScore: 0 };
    }

    const key = `user_activity:${identifier}`;
    const timeWindow = this.config.anomalyDetection.suspiciousTimeWindow * 60 * 1000;
    const now = Date.now();

    try {
      const activityData = await this.redis.get(key);
      const activities = activityData ? JSON.parse(activityData) : [];

      // 添加当前活动
      activities.push({
        action,
        ip,
        timestamp: now
      });

      // 清理过期活动
      const recentActivities = activities.filter((a: any) => now - a.timestamp <= timeWindow);

      // 计算风险分数
      let riskScore = 0;

      // 1. 频繁失败尝试
      const failedAttempts = recentActivities.filter((a: any) => a.action.includes('FAILED')).length;
      if (failedAttempts >= this.config.anomalyDetection.maxFailedAttempts) {
        riskScore += 50;
      }

      // 2. 多IP访问
      const uniqueIps = new Set(recentActivities.map((a: any) => a.ip)).size;
      if (uniqueIps > 3) {
        riskScore += 30;
      }

      // 3. 高频请求
      if (recentActivities.length > 20) {
        riskScore += 20;
      }

      const isSuspicious = riskScore >= 50;

      if (isSuspicious) {
        await this.recordSecurityEvent({
          type: 'SUSPICIOUS_ACTIVITY',
          ip,
          identifier,
          timestamp: now,
          details: { riskScore, failedAttempts, uniqueIps, activityCount: recentActivities.length }
        });
      }

      // 保存活动记录
      await this.redis.set(key, JSON.stringify(recentActivities), this.config.anomalyDetection.suspiciousTimeWindow * 60);

      return { isSuspicious, riskScore };

    } catch (error) {
      console.error('Anomaly detection failed:', error);
      return { isSuspicious: false, riskScore: 0 };
    }
  }

  /**
   * 记录安全事件
   */
  private async recordSecurityEvent(event: SecurityEvent): Promise<void> {
    try {
      const key = `security_event:${Date.now()}:${Math.random().toString(36).substr(2, 9)}`;
      await this.redis.set(key, JSON.stringify(event), 7 * 24 * 60 * 60); // 保留7天
      
      console.warn('Security event recorded:', event);
    } catch (error) {
      console.error('Failed to record security event:', error);
    }
  }

  /**
   * 对IP进行哈希处理（保护隐私）
   */
  private hashIp(ip: string): string {
    return crypto
      .createHash('sha256')
      .update(ip + process.env.JWT_SECRET)
      .digest('hex')
      .substr(0, 16);
  }

  /**
   * 获取安全统计信息
   */
  async getSecurityStats(): Promise<any> {
    try {
      const now = Date.now();
      const last24h = now - 24 * 60 * 60 * 1000;

      // 获取安全事件统计
      const eventKeys = await this.redis.keys('security_event:*');
      const recentEvents = [];

      for (const key of eventKeys) {
        const eventData = await this.redis.get(key);
        if (eventData) {
          const event = JSON.parse(eventData);
          if (event.timestamp >= last24h) {
            recentEvents.push(event);
          }
        }
      }

      const eventsByType = recentEvents.reduce((acc: any, event) => {
        acc[event.type] = (acc[event.type] || 0) + 1;
        return acc;
      }, {});

      return {
        last24Hours: {
          totalEvents: recentEvents.length,
          eventsByType,
          topIps: this.getTopIps(recentEvents)
        },
        timestamp: now
      };

    } catch (error) {
      console.error('Failed to get security stats:', error);
      return null;
    }
  }

  /**
   * 获取最活跃的IP地址
   */
  private getTopIps(events: SecurityEvent[]): any[] {
    const ipCounts = events.reduce((acc: any, event) => {
      acc[event.ip] = (acc[event.ip] || 0) + 1;
      return acc;
    }, {});

    return Object.entries(ipCounts)
      .sort(([, a], [, b]) => (b as number) - (a as number))
      .slice(0, 10)
      .map(([ip, count]) => ({ ip, count }));
  }
}

export default SecurityService;
