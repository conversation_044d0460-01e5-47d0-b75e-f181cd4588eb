好的，我们继续完成 **任务 2.1: 面试准备页面基础**。这次我们特别关注您提供的UI设计图（特别是 `上传简历-1.png` 和 `上传简历-2.png` 中右侧主体部分所展示的风格）。

(Okay, let's continue with **Task 2.1: Interview Setup Page Basics**. This time, we will pay special attention to the UI design you provided (specifically the style shown in the main content area on the right side of `上传简历-1.png` and `上传简历-2.png`).)

**设计图分析 (Design Image Analysis):**

从您提供的 上传简历-1.png 图片来看，主体内容区域特点：

(From the 上传简历-1.png image you provided, the main content area features:)

1. **清晰的标题 (Clear Title)**: "上传简历" (Upload Resume)。
2. **引导性副标题 (Guiding Subtitle)**: "请上传简历, AI将依此生成更适合您的问题与答案" (Please upload your resume, AI will generate more suitable questions and answers for you based on this).
3. **卡片式布局 (Card-style Layout)**: 内容包含在一个带有圆角和轻微阴影的白色卡片中。
4. **标签页切换 (Tabs)**: "上传简历" (Upload Resume) 和 "粘贴文本" (Paste Text) 两个标签页。
5. **文件上传区域 (File Upload Area)**: 虚线框，中间有上传图标和提示文字 "将文件拖放到此处，或点击上传" (Drag and drop files here, or click to upload)，以及支持的文件格式和大小限制 "支持pdf、docx、txt，最大不超过10mb" (Supports pdf, docx, txt, maximum not exceeding 10mb)。
6. **操作按钮 (Action Buttons)**: 右下角有 "返回" (Back) 和 "继续" (Continue) 按钮，其中 "继续" 按钮是主操作按钮，颜色更突出。

**任务调整 (Task Adjustment):**

根据您的UI设计图，任务2.1最初只要求输入“面试岗位/角色”，但UI图显示的是一个更完整的“简历上传”和“岗位设定”（可能在“继续”之后）的流程的一部分。

为了更贴近您的UI愿景，同时保持任务2.1的聚焦，我们将实现一个**简化的“面试准备”页面**，它将包含：

- 一个标题，例如“开始您的面试旅程” (Start Your Interview Journey) 或 “面试设置” (Interview Setup)。
- 一个输入框，用于填写“面试岗位/角色”。
- 一个“继续”或“下一步”按钮，初始禁用，在输入岗位后启用。
- 整体风格将参考您图片中右侧卡片区域的简洁、专业感。

简历上传和标签页功能将在后续任务中实现。

(Resume upload and tab functionality will be implemented in subsequent tasks.)

------

## 面试准备页面基础操作清单 (Task 2.1: Interview Setup Page Basics - Checklist, UI-Inspired)

项目根目录 (Project Root Directory): local-mianshijun/

前端代码目录 (Frontend Code Directory): local-mianshijun/frontend/

请在您的 Cursor 编辑器中按照以下步骤操作：

(Please follow these steps in your Cursor editor:)

------

### 1. 创建/修改面试准备页面文件 (Create/Modify Interview Setup Page File)

- 中文

  :

  1. 确保在 `frontend/src/pages/` 目录下，您已经有 `InterviewSetupPage.tsx` 文件。如果之前步骤已创建，我们将修改它。如果没有，请创建它。

- English

  :

  1. Ensure you have the `InterviewSetupPage.tsx` file in the `frontend/src/pages/` directory. If created in previous steps, we will modify it. If not, please create it.

------

### 2. 编写页面代码 (Write Page Code)

- **中文**: 打开 `frontend/src/pages/InterviewSetupPage.tsx` 文件，用以下代码**完全替换**其内容。这段代码将创建一个更接近您UI风格的页面，包含标题、描述、一个输入框和一个按钮。

- **English**: Open the `frontend/src/pages/InterviewSetupPage.tsx` file and **completely replace** its content with the following code. This code will create a page closer to your UI style, including a title, description, an input field, and a button.

- **操作 (Action)**:

  TypeScript

  ```
  // frontend/src/pages/InterviewSetupPage.tsx
  import React, { useState } from 'react';
  import { useNavigate } from 'react-router-dom';
  import Input from '../components/ui/Input'; // 您的通用输入框组件 (Your common Input component)
  import Button from '../components/ui/Button'; // 您的通用按钮组件 (Your common Button component)
  import { FileText, ArrowRight } from 'lucide-react'; // 引入图标 (Importing icons)
  
  const InterviewSetupPage: React.FC = () => {
    const [interviewRole, setInterviewRole] = useState<string>('');
    const [isButtonDisabled, setIsButtonDisabled] = useState<boolean>(true);
    const navigate = useNavigate();
  
    const handleRoleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
      const roleValue = event.target.value;
      setInterviewRole(roleValue);
      setIsButtonDisabled(roleValue.trim() === '');
    };
  
    const handleNextStep = () => {
      if (!isButtonDisabled) {
        console.log('面试岗位/角色 (Interview Position/Role):', interviewRole);
        // 后续任务：可以将 interviewRole 存储到 Zustand 或 Context
        // Future task: Store interviewRole in Zustand or Context
        // navigate('/interview/resume-upload'); // 例如，导航到简历上传页 (Example: navigate to resume upload page)
        alert(`已设置面试岗位为: ${interviewRole}\n(下一步功能待开发 - Next feature to be developed)`);
      }
    };
  
    return (
      <div className="min-h-screen bg-slate-100 flex flex-col items-center justify-center p-4">
        <div className="w-full max-w-2xl"> {/* 控制整体卡片区域的最大宽度 (Controls max-width of the card area) */}
          {/* 可以选择性添加一个返回首页或仪表盘的链接 */}
          {/* Optional: Add a link back to homepage or dashboard */}
          {/* <Link to="/dashboard" className="text-sm text-blue-600 hover:underline mb-4 inline-block">&larr; 返回仪表盘</Link> */}
  
          <div className="bg-white shadow-xl rounded-xl p-6 sm:p-8 md:p-10">
            {/* 页面头部信息，参考UI图中的标题和副标题风格 */}
            {/* Page header info, referencing title and subtitle style from UI */}
            <div className="text-center mb-8">
              <FileText className="w-12 h-12 text-sky-500 mx-auto mb-3" />
              <h1 className="text-2xl sm:text-3xl font-bold text-slate-800">
                开启您的面试之旅 (Start Your Interview Journey)
              </h1>
              <p className="mt-2 text-sm text-slate-600">
                首先，请告诉我们您计划面试的岗位或角色。
                (First, please tell us the position or role you plan to interview for.)
              </p>
            </div>
  
            {/* 主要内容区域 */}
            {/* Main content area */}
            <div className="space-y-6">
              <Input
                id="interview-role"
                label="面试岗位/角色 (Interview Position/Role)"
                type="text"
                value={interviewRole}
                onChange={handleRoleInputChange}
                placeholder="例如：软件工程师、产品经理 (e.g., Software Engineer, Product Manager)"
                className="text-base" // 确保输入框字体大小适中 (Ensure input font size is appropriate)
              />
  
              <div className="flex justify-end space-x-3 pt-4">
                {/* "返回" 按钮 (可以暂时不需要，或者链接到上一页/仪表盘) */}
                {/* "Back" button (can be omitted for now, or link to previous page/dashboard) */}
                {/* <Button
                  variant="outline"
                  onClick={() => navigate(-1)} // 返回上一页 (Go back to previous page)
                >
                  返回 (Back)
                </Button> */}
  
                <Button
                  onClick={handleNextStep}
                  disabled={isButtonDisabled}
                  className="min-w-[120px]" // 给按钮一个最小宽度 (Give button a min-width)
                >
                  {isButtonDisabled ? '请输入岗位 (Enter Role)' : (
                    <>
                      继续 (Continue)
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </>
                  )}
                </Button>
              </div>
            </div>
          </div>
  
          {/* 页脚版权信息 (可以从全局 Footer 组件获取，如果 InterviewSetupPage 不在主 Layout 内) */}
          {/* Footer copyright info (can be from global Footer if InterviewSetupPage is not within main Layout) */}
          <div className="mt-8 text-center">
            <p className="text-xs text-slate-500">
              &copy; {new Date().getFullYear()} 面试君. AI 助力，面试无忧.
            </p>
          </div>
        </div>
      </div>
    );
  };
  
  export default InterviewSetupPage;
  ```

- **代码解释与UI调整说明 (Code Explanation and UI Adjustment Notes)**:

  - **整体布局 (Overall Layout)**: 页面背景使用浅灰色 (`bg-slate-100`)，内容区为一个居中的白色卡片 (`bg-white shadow-xl rounded-xl`)，最大宽度设为 `max-w-2xl`，这与您UI图中右侧主体部分的风格类似。 (The page background uses a light gray (`bg-slate-100`), and the content area is a centered white card (`bg-white shadow-xl rounded-xl`) with a max-width of `max-w-2xl`, similar to the style of the main right section in your UI image.)

  - **页面头部 (Page Header)**: 添加了图标 (`FileText`)、主标题 (“开启您的面试之旅”) 和副标题/描述，使其更具引导性。 (Added an icon (`FileText`), main title ("Start Your Interview Journey"), and subtitle/description to make it more guiding.)

  - 输入框 (`Input` Component)

    :

    - `label`: "面试岗位/角色 (Interview Position/Role)"
    - `placeholder`: "例如：软件工程师、产品经理 (e.g., Software Engineer, Product Manager)"
    - 使用了您项目中已有的通用 `Input` 组件。 (Used the existing common `Input` component from your project.)

  - 按钮 (`Button` Component)

    :

    - 主按钮为 "继续" (Continue)，并添加了一个向右箭头图标 (`ArrowRight`)。
    - 当输入框为空时，按钮文本为 "请输入岗位 (Enter Role)" 并保持禁用。
    - 按钮使用了您项目中已有的通用 `Button` 组件，并稍微调整了样式（如 `min-w-[120px]`）。 (The main button is "Continue" with a right arrow icon (`ArrowRight`). When the input is empty, the button text is "请输入岗位 (Enter Role)" and remains disabled. It uses the existing common `Button` component from your project with slight style adjustments like `min-w-[120px]`.)

  - 状态管理 (`useState`)

    :

    - `interviewRole`: 存储用户输入的岗位名称。 (Stores the job role entered by the user.)
    - `isButtonDisabled`: 控制“继续”按钮的禁用状态。 (Controls the disabled state of the "Continue" button.)

  - 逻辑 (`handleRoleInputChange`, `handleNextStep`)

    :

    - `handleRoleInputChange`: 当输入框内容改变时，更新 `interviewRole` 和 `isButtonDisabled`状态。 (When input changes, updates `interviewRole` and `isButtonDisabled` states.)
    - `handleNextStep`: 点击“继续”按钮时触发。目前仅打印信息并显示提示。 (Triggered when "Continue" is clicked. Currently only logs info and shows an alert.)

  - **页脚 (Footer)**: 添加了一个简单的版权声明，使其看起来更完整。如果这个页面是通过 `Layout.tsx` 加载的，那么它会自动拥有全局页脚，这里的可以移除。 (Added a simple copyright notice to make it look more complete. If this page is loaded via `Layout.tsx`, it will automatically have the global footer, and this one can be removed.)

------

### 3. (可选) 添加到应用路由 (Optional: Add to Application Routes)

- **中文**: 如果您还没有为 `/interview-setup` 设置路由，或者希望它作为独立页面（不包含侧边栏和通用头部），请确保在 `frontend/src/App.tsx` (或 `StandaloneApp.tsx`，取决于您的主应用入口) 中正确配置。

- **English**: If you haven't set up a route for `/interview-setup` yet, or if you want it to be a standalone page (without the sidebar and common header), ensure it's correctly configured in `frontend/src/App.tsx` (or `StandaloneApp.tsx`, depending on your main app entry point).

- **示例 (如果作为独立页面/Example if as a standalone page)**:

  TypeScript

  ```
  // In frontend/src/App.tsx (or StandaloneApp.tsx)
  import InterviewSetupPage from './pages/InterviewSetupPage'; // 新增导入 (New import)
  
  // ...
  <Routes>
    {/* ... 其他路由 (other routes) ... */}
    <Route path="/interview-setup" element={<InterviewSetupPage />} /> {/* 可以不使用 Layout 包裹 (Can be outside Layout wrapper) */}
    {/* 如果希望它在登录后才能访问，可以这样: (If you want it accessible after login:)
    <Route path="/interview-setup" element={
      <ProtectedRoute>
        <InterviewSetupPage />
      </ProtectedRoute>
    } />
    */}
  </Routes>
  // ...
  ```

- **如果它应该在主布局内 (If it should be within the main layout)**:

  TypeScript

  ```
  // In frontend/src/App.tsx
  // ...
  <Route path="/" element={<Layout />}>
    {/* ... 其他布局内路由 (other routes within layout) ... */}
    <Route path="interview-setup" element={
      <ProtectedRoute> {/* 通常设置页面也应该是受保护的 (Usually setup pages are also protected) */}
        <InterviewSetupPage />
      </ProtectedRoute>
    } />
  </Route>
  // ...
  ```

- **根据您的 `project-progress.md`，此页面可能已经添加过路由。请检查并按需调整。**

- **According to your `project-progress.md`, a route for this page might have already been added. Please check and adjust as needed.**

------

### 4. 测试页面功能 (Test Page Functionality)

- 中文

  :

  1. **启动/重启开发服务器** (与之前步骤相同)。

  2. 访问页面

     :

     - 在浏览器地址栏输入您应用的本地地址加上 `/interview-setup` (例如 `http://localhost:5173/interview-setup`)。

  3. 测试交互

     :

     - 检查页面是否能正确显示新的标题、描述和图标。
     - 检查是否有“面试岗位/角色”的输入框。
     - “继续”按钮初始时应为禁用状态，并且文本可能是“请输入岗位”。
     - 在输入框中输入内容。当您输入时，“继续”按钮应该变为启用状态，文本变为“继续”，并显示箭头图标。
     - 当输入框内容被清空时，按钮应该再次变为禁用。
     - 点击启用的“继续”按钮，检查浏览器控制台是否打印出您输入的岗位信息，并且是否弹出了提示。

- English

  :

  1. **Start/Restart Development Server** (same as previous steps).

  2. Access the Page

     :

     - Enter your application's local address plus `/interview-setup` in the browser's address bar (e.g., `http://localhost:5173/interview-setup`).

  3. Test Interactions

     :

     - Check if the page correctly displays the new title, description, and icon.
     - Check if there is an input field for "面试岗位/角色 (Interview Position/Role)".
     - The "Continue" button should initially be disabled, and its text might be "请输入岗位 (Enter Role)".
     - Try typing content into the input field. As you type, the "Continue" button should become enabled, its text change to "继续 (Continue)", and show an arrow icon.
     - When the input field is cleared, the button should become disabled again.
     - Click the enabled "Continue" button and check if your browser's console prints the position information you entered and if an alert pops up.

------

### 5. 提交代码到 GitHub (Commit Code to GitHub)

- **中文**: 完成修改和测试后，将代码提交到 GitHub。

- **English**: After completing modifications and testing, commit your code to GitHub.

- 操作 (Action):

  (与之前的 git add ., git commit -m "...", git push 步骤相同。提交信息可以写：Feat: Update InterviewSetupPage UI and basic logic)

  (Same as previous git add ., git commit -m "...", git push steps. Commit message can be: Feat: Update InterviewSetupPage UI and basic logic)

  Bash

  ```
  git add .
  git commit -m "Feat: Update InterviewSetupPage UI and basic logic for Task 2.1"
  git push origin main # 或者您的主分支名称 (or your main branch name)
  ```

------

这个版本的 `InterviewSetupPage.tsx` 更贴近您提供的UI图中卡片式、简洁专业的风格，并保留了任务2.1的核心功能。

(This version of `InterviewSetupPage.tsx` is closer to the card-style, clean, and professional look shown in your UI image, while retaining the core functionality of Task 2.1.)

如果您希望进一步调整样式以更精确地匹配 `上传简历-1.png` 中的“上传简历”区域（例如，添加标签页切换的UI占位符，或调整按钮位置到右下角），请告诉我，我们可以在此基础上继续修改。

(If you'd like to further adjust the styling to more precisely match the "Upload Resume" area in `上传简历-1.png` (e.g., adding UI placeholders for tab switching, or moving buttons to the bottom-right), please let me know, and we can continue modifying it based on this.)