import React, { useState, useRef, useEffect } from 'react';

interface VerificationCodeInputProps {
  length?: number;
  value: string;
  onChange: (value: string) => void;
  disabled?: boolean;
  error?: boolean;
  className?: string;
}

const VerificationCodeInput: React.FC<VerificationCodeInputProps> = ({
  length = 6,
  value,
  onChange,
  disabled = false,
  error = false,
  className = ''
}) => {
  const [focusedIndex, setFocusedIndex] = useState<number>(-1);
  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);

  // 初始化输入框引用数组
  useEffect(() => {
    inputRefs.current = inputRefs.current.slice(0, length);
  }, [length]);

  // 处理输入变化
  const handleInputChange = (index: number, inputValue: string) => {
    if (disabled) return;

    // 只允许数字输入
    const numericValue = inputValue.replace(/[^0-9]/g, '');
    
    if (numericValue.length > 1) {
      // 如果粘贴了多个字符，处理批量输入
      const newValue = value.split('');
      const pastedChars = numericValue.slice(0, length - index);
      
      for (let i = 0; i < pastedChars.length && index + i < length; i++) {
        newValue[index + i] = pastedChars[i];
      }
      
      const finalValue = newValue.join('').slice(0, length);
      onChange(finalValue);
      
      // 聚焦到下一个空位或最后一个位置
      const nextIndex = Math.min(index + pastedChars.length, length - 1);
      setTimeout(() => {
        inputRefs.current[nextIndex]?.focus();
      }, 0);
    } else {
      // 单个字符输入
      const newValue = value.split('');
      newValue[index] = numericValue;
      onChange(newValue.join(''));
      
      // 自动聚焦到下一个输入框
      if (numericValue && index < length - 1) {
        setTimeout(() => {
          inputRefs.current[index + 1]?.focus();
        }, 0);
      }
    }
  };

  // 处理键盘事件
  const handleKeyDown = (index: number, e: React.KeyboardEvent<HTMLInputElement>) => {
    if (disabled) return;

    if (e.key === 'Backspace') {
      e.preventDefault();
      const newValue = value.split('');
      
      if (newValue[index]) {
        // 如果当前位置有值，清除当前位置
        newValue[index] = '';
        onChange(newValue.join(''));
      } else if (index > 0) {
        // 如果当前位置没有值，清除前一个位置并聚焦
        newValue[index - 1] = '';
        onChange(newValue.join(''));
        setTimeout(() => {
          inputRefs.current[index - 1]?.focus();
        }, 0);
      }
    } else if (e.key === 'ArrowLeft' && index > 0) {
      e.preventDefault();
      inputRefs.current[index - 1]?.focus();
    } else if (e.key === 'ArrowRight' && index < length - 1) {
      e.preventDefault();
      inputRefs.current[index + 1]?.focus();
    } else if (e.key === 'Delete') {
      e.preventDefault();
      const newValue = value.split('');
      newValue[index] = '';
      onChange(newValue.join(''));
    }
  };

  // 处理聚焦
  const handleFocus = (index: number) => {
    setFocusedIndex(index);
    // 选中当前输入框的内容
    setTimeout(() => {
      inputRefs.current[index]?.select();
    }, 0);
  };

  // 处理失焦
  const handleBlur = () => {
    setFocusedIndex(-1);
  };

  // 处理粘贴
  const handlePaste = (e: React.ClipboardEvent) => {
    e.preventDefault();
    if (disabled) return;

    const pastedData = e.clipboardData.getData('text');
    const numericData = pastedData.replace(/[^0-9]/g, '').slice(0, length);
    
    if (numericData) {
      onChange(numericData.padEnd(length, '').slice(0, length));
      // 聚焦到最后一个有值的位置
      const lastIndex = Math.min(numericData.length - 1, length - 1);
      setTimeout(() => {
        inputRefs.current[lastIndex]?.focus();
      }, 0);
    }
  };

  return (
    <div className={`flex gap-2 justify-center ${className}`}>
      {Array.from({ length }, (_, index) => (
        <input
          key={index}
          ref={(el) => (inputRefs.current[index] = el)}
          type="text"
          inputMode="numeric"
          maxLength={1}
          value={value[index] || ''}
          onChange={(e) => handleInputChange(index, e.target.value)}
          onKeyDown={(e) => handleKeyDown(index, e)}
          onFocus={() => handleFocus(index)}
          onBlur={handleBlur}
          onPaste={handlePaste}
          disabled={disabled}
          className={`
            w-12 h-12 text-center text-lg font-semibold border-2 rounded-lg
            transition-all duration-200 ease-in-out
            bg-white dark:bg-gray-700 text-gray-900 dark:text-white
            ${error
              ? 'border-red-500 dark:border-red-400 bg-red-50 dark:bg-red-900/20 text-red-900 dark:text-red-200'
              : focusedIndex === index
                ? 'border-blue-500 dark:border-blue-400 bg-blue-50 dark:bg-blue-900/20 shadow-md ring-2 ring-blue-200 dark:ring-blue-800'
                : value[index]
                  ? 'border-green-500 dark:border-green-400 bg-green-50 dark:bg-green-900/20 text-green-900 dark:text-green-200'
                  : 'border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 hover:border-gray-400 dark:hover:border-gray-500'
            }
            ${disabled
              ? 'bg-gray-100 dark:bg-gray-800 text-gray-400 dark:text-gray-500 cursor-not-allowed'
              : 'focus:outline-none'
            }
          `}
          autoComplete="one-time-code"
        />
      ))}
    </div>
  );
};

export default VerificationCodeInput;
