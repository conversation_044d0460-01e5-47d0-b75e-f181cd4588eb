# 智能语音识别架构优化实施方案

## 项目概述

### 问题分析
当前语音识别系统存在输出断断续续的问题，主要原因包括：

1. **固定时间分片问题**：采用固定320ms分片，不考虑语音的自然停顿和语义边界
2. **缺乏VAD检测**：没有智能检测语音活动的开始和结束
3. **流式识别连续性差**：每个音频块独立处理，缺乏上下文连接
4. **音频预处理不当**：固定分帧处理，缺乏智能缓冲策略
5. **文本合并机制缺失**：没有智能合并多个识别片段的机制

### 解决目标
设计一个四层智能语音识别架构，实现类似手机输入法的丝滑连贯语音转文字效果。

## 总体架构设计

### 四层智能语音识别架构

```
第一层：智能音频采集层
├── VAD语音活动检测
├── 动态音频分片
├── 音频质量增强
└── 智能缓冲管理

第二层：流式识别处理层
├── 多ASR服务并行
├── 增量识别管理
├── 上下文保持机制
└── 识别状态机

第三层：智能合并优化层
├── 文本片段合并
├── 语义连贯检查
├── 实时纠错机制
└── 置信度评估

第四层：用户体验层
├── 平滑文字显示
├── 实时反馈
├── 错误修正界面
└── 用户交互优化
```

## 分阶段实施计划

### 阶段一：基础VAD和智能分片（1-2周）

#### 1.1 实现VAD（语音活动检测）算法

**目标**：替换固定320ms分片为基于语音活动的动态分片

**技术方案**：
- 基于能量阈值的VAD算法
- 结合频谱特征的增强检测
- 自适应阈值调整

**实现文件**：
```
frontend/src/utils/vadDetector.ts
frontend/src/hooks/useVAD.ts
frontend/src/types/vadTypes.ts
```

**核心算法**：
```typescript
export class VADDetector {
  private energyThreshold: number = 0.01;
  private silenceThreshold: number = 0.005;
  private minSpeechDuration: number = 300; // ms
  private minSilenceDuration: number = 500; // ms
  
  detectVoiceActivity(audioData: Float32Array): VADResult {
    const energy = this.calculateEnergy(audioData);
    const spectralCentroid = this.calculateSpectralCentroid(audioData);
    const zeroCrossingRate = this.calculateZeroCrossingRate(audioData);
    
    const isSpeech = this.classifyAsSpeech(energy, spectralCentroid, zeroCrossingRate);
    
    return {
      isSpeech,
      energy,
      confidence: this.calculateConfidence(energy, spectralCentroid, zeroCrossingRate),
      timestamp: Date.now()
    };
  }
}
```

#### 1.2 音频缓冲区重构

**目标**：实现多级智能缓冲策略

**技术方案**：
- 短期缓冲（1-2秒）：用于实时VAD检测
- 中期缓冲（5-10秒）：用于上下文识别
- 长期缓冲（30秒）：用于语义修正

**实现文件**：
```
frontend/src/utils/audioBufferManager.ts
frontend/src/utils/circularBuffer.ts
```

**核心实现**：
```typescript
export class AudioBufferManager {
  private shortTermBuffer: CircularBuffer<Float32Array>;
  private mediumTermBuffer: CircularBuffer<Float32Array>;
  private longTermBuffer: CircularBuffer<Float32Array>;
  
  constructor() {
    this.shortTermBuffer = new CircularBuffer(100); // 2秒@50fps
    this.mediumTermBuffer = new CircularBuffer(500); // 10秒@50fps
    this.longTermBuffer = new CircularBuffer(1500); // 30秒@50fps
  }
  
  addAudioChunk(audioData: Float32Array, vadResult: VADResult): void {
    this.shortTermBuffer.push(audioData);
    this.mediumTermBuffer.push(audioData);
    this.longTermBuffer.push(audioData);
    
    if (this.shouldTriggerRecognition(vadResult)) {
      this.triggerRecognition();
    }
  }
}
```

#### 1.3 动态音频分片

**目标**：根据VAD结果进行智能分片

**技术方案**：
- 语音段检测：检测连续语音的开始和结束
- 停顿分析：识别自然停顿点
- 动态调整：根据语音特征调整分片大小

**实现文件**：
```
frontend/src/utils/dynamicSegmenter.ts
```

### 阶段二：流式识别优化（2-3周）

#### 2.1 流式识别状态机

**目标**：管理连续识别过程的状态转换

**状态定义**：
- IDLE：空闲状态
- LISTENING：监听语音
- PROCESSING：处理识别
- MERGING：合并结果
- OUTPUTTING：输出文本

**实现文件**：
```
backend/src/services/streamingASRManager.ts
backend/src/types/asrTypes.ts
```

**核心状态机**：
```typescript
export class StreamingASRManager {
  private state: ASRState = ASRState.IDLE;
  private recognitionSessions: Map<string, RecognitionSession> = new Map();
  
  async processAudioChunk(
    sessionId: string, 
    audioData: Buffer, 
    vadInfo: VADInfo
  ): Promise<RecognitionResult | null> {
    
    const session = this.getOrCreateSession(sessionId);
    
    switch (this.state) {
      case ASRState.IDLE:
        if (vadInfo.isSpeech) {
          this.transitionTo(ASRState.LISTENING);
          session.startNewSegment();
        }
        break;
        
      case ASRState.LISTENING:
        session.addAudioChunk(audioData);
        if (this.shouldStartRecognition(session)) {
          this.transitionTo(ASRState.PROCESSING);
          return await this.performRecognition(session);
        }
        break;
        
      case ASRState.PROCESSING:
        session.addAudioChunk(audioData);
        if (vadInfo.isEndOfSpeech) {
          this.transitionTo(ASRState.MERGING);
        }
        break;
        
      case ASRState.MERGING:
        const finalResult = await this.mergeAndFinalize(session);
        this.transitionTo(ASRState.IDLE);
        return finalResult;
    }
    
    return null;
  }
}
```

#### 2.2 增量识别结果管理

**目标**：管理多个音频片段的识别结果

**技术方案**：
- 结果队列管理
- 时间戳对齐
- 重复内容去除
- 增量更新机制

**实现文件**：
```
backend/src/services/incrementalResultManager.ts
backend/src/utils/resultQueue.ts
```

#### 2.3 上下文保持机制

**目标**：保持识别结果的语义连续性

**技术方案**：
- 上下文窗口管理
- 语义关联分析
- 历史结果缓存

**实现文件**：
```
backend/src/services/contextManager.ts
```

### 阶段三：智能合并和用户体验（1-2周）

#### 3.1 智能文本合并算法

**目标**：将多个识别片段合并为连贯文本

**技术方案**：
- 基于时间戳的初步合并
- 基于语义相似度的优化合并
- 基于语言模型的最终优化

**实现文件**：
```
backend/src/services/textMerger.ts
backend/src/utils/textSimilarity.ts
```

**核心合并算法**：
```typescript
export class TextMerger {
  private contextWindow: TextSegment[] = [];
  private readonly maxContextSize = 10;
  
  mergeTextSegments(newSegment: TextSegment): MergedText {
    // 1. 添加到上下文窗口
    this.contextWindow.push(newSegment);
    if (this.contextWindow.length > this.maxContextSize) {
      this.contextWindow.shift();
    }
    
    // 2. 检测重复内容
    const deduplicatedSegments = this.removeDuplicates(this.contextWindow);
    
    // 3. 语义连贯性检查
    const coherentSegments = this.ensureCoherence(deduplicatedSegments);
    
    // 4. 智能合并
    const mergedText = this.performIntelligentMerge(coherentSegments);
    
    return {
      text: mergedText,
      confidence: this.calculateOverallConfidence(coherentSegments),
      segments: coherentSegments
    };
  }
}
```

#### 3.2 前端平滑显示优化

**目标**：实现丝滑的文字显示效果

**技术方案**：
- 打字机效果
- 实时更新动画
- 错误修正动画

**实现文件**：
```
frontend/src/components/SmoothTextDisplay.tsx
frontend/src/hooks/useTextAnimation.ts
frontend/src/utils/textAnimations.ts
```

## 详细技术实现方案

### 1. VAD检测算法详细实现

```typescript
// frontend/src/utils/vadDetector.ts
export interface VADResult {
  isSpeech: boolean;
  energy: number;
  confidence: number;
  timestamp: number;
  spectralFeatures?: SpectralFeatures;
}

export interface SpectralFeatures {
  spectralCentroid: number;
  zeroCrossingRate: number;
  spectralRolloff: number;
  mfcc?: number[];
}

export class VADDetector {
  private energyThreshold: number = 0.01;
  private silenceThreshold: number = 0.005;
  private minSpeechDuration: number = 300; // ms
  private minSilenceDuration: number = 500; // ms
  private adaptiveThreshold: boolean = true;
  private recentEnergyHistory: number[] = [];
  
  constructor(private audioContext: AudioContext) {
    this.initializeDetector();
  }
  
  detectVoiceActivity(audioData: Float32Array): VADResult {
    const energy = this.calculateEnergy(audioData);
    const spectralCentroid = this.calculateSpectralCentroid(audioData);
    const zeroCrossingRate = this.calculateZeroCrossingRate(audioData);
    const spectralRolloff = this.calculateSpectralRolloff(audioData);
    
    // 自适应阈值调整
    if (this.adaptiveThreshold) {
      this.updateAdaptiveThreshold(energy);
    }
    
    const isSpeech = this.classifyAsSpeech(energy, spectralCentroid, zeroCrossingRate);
    const confidence = this.calculateConfidence(energy, spectralCentroid, zeroCrossingRate);
    
    return {
      isSpeech,
      energy,
      confidence,
      timestamp: Date.now(),
      spectralFeatures: {
        spectralCentroid,
        zeroCrossingRate,
        spectralRolloff
      }
    };
  }
  
  private calculateEnergy(audioData: Float32Array): number {
    let sum = 0;
    for (let i = 0; i < audioData.length; i++) {
      sum += audioData[i] * audioData[i];
    }
    return Math.sqrt(sum / audioData.length);
  }
  
  private calculateSpectralCentroid(audioData: Float32Array): number {
    // 使用FFT计算频谱重心
    const fftSize = 1024;
    const fft = this.performFFT(audioData, fftSize);
    
    let weightedSum = 0;
    let magnitudeSum = 0;
    
    for (let i = 0; i < fft.length / 2; i++) {
      const magnitude = Math.sqrt(fft[i * 2] ** 2 + fft[i * 2 + 1] ** 2);
      const frequency = (i * this.audioContext.sampleRate) / fftSize;
      
      weightedSum += frequency * magnitude;
      magnitudeSum += magnitude;
    }
    
    return magnitudeSum > 0 ? weightedSum / magnitudeSum : 0;
  }
  
  private calculateZeroCrossingRate(audioData: Float32Array): number {
    let crossings = 0;
    for (let i = 1; i < audioData.length; i++) {
      if ((audioData[i] >= 0) !== (audioData[i - 1] >= 0)) {
        crossings++;
      }
    }
    return crossings / audioData.length;
  }
  
  private calculateSpectralRolloff(audioData: Float32Array): number {
    const fftSize = 1024;
    const fft = this.performFFT(audioData, fftSize);
    
    let totalEnergy = 0;
    const magnitudes: number[] = [];
    
    for (let i = 0; i < fft.length / 2; i++) {
      const magnitude = Math.sqrt(fft[i * 2] ** 2 + fft[i * 2 + 1] ** 2);
      magnitudes.push(magnitude);
      totalEnergy += magnitude;
    }
    
    const threshold = 0.85 * totalEnergy;
    let cumulativeEnergy = 0;
    
    for (let i = 0; i < magnitudes.length; i++) {
      cumulativeEnergy += magnitudes[i];
      if (cumulativeEnergy >= threshold) {
        return (i * this.audioContext.sampleRate) / fftSize;
      }
    }
    
    return (magnitudes.length * this.audioContext.sampleRate) / fftSize;
  }
  
  private classifyAsSpeech(
    energy: number, 
    spectralCentroid: number, 
    zeroCrossingRate: number
  ): boolean {
    // 多特征融合的语音检测
    const energyScore = energy > this.energyThreshold ? 1 : 0;
    const spectralScore = spectralCentroid > 1000 && spectralCentroid < 4000 ? 1 : 0;
    const zcrScore = zeroCrossingRate > 0.1 && zeroCrossingRate < 0.3 ? 1 : 0;
    
    const totalScore = energyScore + spectralScore + zcrScore;
    return totalScore >= 2; // 至少满足两个条件
  }
  
  private calculateConfidence(
    energy: number, 
    spectralCentroid: number, 
    zeroCrossingRate: number
  ): number {
    // 基于多特征的置信度计算
    const energyConf = Math.min(energy / this.energyThreshold, 1);
    const spectralConf = spectralCentroid > 1000 && spectralCentroid < 4000 ? 1 : 0.5;
    const zcrConf = zeroCrossingRate > 0.1 && zeroCrossingRate < 0.3 ? 1 : 0.5;
    
    return (energyConf + spectralConf + zcrConf) / 3;
  }
  
  private updateAdaptiveThreshold(energy: number): void {
    this.recentEnergyHistory.push(energy);
    if (this.recentEnergyHistory.length > 100) {
      this.recentEnergyHistory.shift();
    }
    
    // 计算动态阈值
    const avgEnergy = this.recentEnergyHistory.reduce((a, b) => a + b, 0) / this.recentEnergyHistory.length;
    const stdEnergy = Math.sqrt(
      this.recentEnergyHistory.reduce((sum, val) => sum + Math.pow(val - avgEnergy, 2), 0) / this.recentEnergyHistory.length
    );
    
    this.energyThreshold = avgEnergy + 2 * stdEnergy;
  }
  
  private performFFT(audioData: Float32Array, fftSize: number): Float32Array {
    // 简化的FFT实现或使用Web Audio API的AnalyserNode
    // 这里可以使用现有的FFT库如fft.js
    return new Float32Array(fftSize * 2); // 占位符
  }
  
  private initializeDetector(): void {
    // 初始化检测器参数
    console.log('VAD Detector initialized');
  }
}
```

### 2. 智能音频缓冲管理详细实现

```typescript
// frontend/src/utils/circularBuffer.ts
export class CircularBuffer<T> {
  private buffer: T[];
  private head: number = 0;
  private tail: number = 0;
  private size: number = 0;

  constructor(private capacity: number) {
    this.buffer = new Array(capacity);
  }

  push(item: T): void {
    this.buffer[this.tail] = item;
    this.tail = (this.tail + 1) % this.capacity;

    if (this.size < this.capacity) {
      this.size++;
    } else {
      this.head = (this.head + 1) % this.capacity;
    }
  }

  getAll(): T[] {
    const result: T[] = [];
    for (let i = 0; i < this.size; i++) {
      const index = (this.head + i) % this.capacity;
      result.push(this.buffer[index]);
    }
    return result;
  }

  getLast(count: number): T[] {
    const actualCount = Math.min(count, this.size);
    const result: T[] = [];

    for (let i = actualCount - 1; i >= 0; i--) {
      const index = (this.tail - 1 - i + this.capacity) % this.capacity;
      result.unshift(this.buffer[index]);
    }

    return result;
  }

  clear(): void {
    this.head = 0;
    this.tail = 0;
    this.size = 0;
  }

  isFull(): boolean {
    return this.size === this.capacity;
  }

  isEmpty(): boolean {
    return this.size === 0;
  }

  getSize(): number {
    return this.size;
  }
}

// frontend/src/utils/audioBufferManager.ts
export interface BufferTriggerInfo {
  shouldTrigger: boolean;
  reason: 'speech_detected' | 'buffer_full' | 'silence_timeout' | 'manual';
  confidence: number;
  audioLength: number;
}

export class AudioBufferManager {
  private shortTermBuffer: CircularBuffer<Float32Array>;
  private mediumTermBuffer: CircularBuffer<Float32Array>;
  private longTermBuffer: CircularBuffer<Float32Array>;
  private vadHistory: CircularBuffer<VADResult>;

  private lastTriggerTime: number = 0;
  private speechStartTime: number = 0;
  private silenceStartTime: number = 0;
  private isInSpeech: boolean = false;

  private readonly minTriggerInterval = 1000; // 最小触发间隔1秒
  private readonly maxSpeechDuration = 10000; // 最大语音段10秒
  private readonly maxSilenceDuration = 2000; // 最大静音2秒

  constructor() {
    this.shortTermBuffer = new CircularBuffer(100); // 2秒@50fps
    this.mediumTermBuffer = new CircularBuffer(500); // 10秒@50fps
    this.longTermBuffer = new CircularBuffer(1500); // 30秒@50fps
    this.vadHistory = new CircularBuffer(50); // 保存最近50个VAD结果
  }

  addAudioChunk(audioData: Float32Array, vadResult: VADResult): BufferTriggerInfo {
    // 添加到所有缓冲区
    this.shortTermBuffer.push(audioData);
    this.mediumTermBuffer.push(audioData);
    this.longTermBuffer.push(audioData);
    this.vadHistory.push(vadResult);

    // 更新语音状态
    this.updateSpeechState(vadResult);

    // 检查是否需要触发识别
    return this.checkTriggerConditions();
  }

  private updateSpeechState(vadResult: VADResult): void {
    const now = Date.now();

    if (vadResult.isSpeech && !this.isInSpeech) {
      // 语音开始
      this.isInSpeech = true;
      this.speechStartTime = now;
      console.log('Speech started');
    } else if (!vadResult.isSpeech && this.isInSpeech) {
      // 可能的语音结束
      if (this.silenceStartTime === 0) {
        this.silenceStartTime = now;
      }
    } else if (vadResult.isSpeech && this.isInSpeech) {
      // 语音继续，重置静音计时
      this.silenceStartTime = 0;
    }
  }

  private checkTriggerConditions(): BufferTriggerInfo {
    const now = Date.now();

    // 检查最小触发间隔
    if (now - this.lastTriggerTime < this.minTriggerInterval) {
      return {
        shouldTrigger: false,
        reason: 'manual',
        confidence: 0,
        audioLength: 0
      };
    }

    // 检查语音检测触发
    if (this.isInSpeech && this.speechStartTime > 0) {
      const speechDuration = now - this.speechStartTime;

      // 语音段过长，强制触发
      if (speechDuration > this.maxSpeechDuration) {
        return this.createTriggerInfo('buffer_full', 0.8);
      }

      // 检测到静音结束
      if (this.silenceStartTime > 0) {
        const silenceDuration = now - this.silenceStartTime;
        if (silenceDuration > this.maxSilenceDuration) {
          this.isInSpeech = false;
          this.speechStartTime = 0;
          this.silenceStartTime = 0;
          return this.createTriggerInfo('silence_timeout', 0.9);
        }
      }
    }

    // 检查缓冲区满
    if (this.shortTermBuffer.isFull()) {
      return this.createTriggerInfo('buffer_full', 0.7);
    }

    return {
      shouldTrigger: false,
      reason: 'manual',
      confidence: 0,
      audioLength: 0
    };
  }

  private createTriggerInfo(reason: BufferTriggerInfo['reason'], confidence: number): BufferTriggerInfo {
    this.lastTriggerTime = Date.now();
    const audioData = this.getRecognitionBuffer();

    return {
      shouldTrigger: true,
      reason,
      confidence,
      audioLength: audioData.length
    };
  }

  getRecognitionBuffer(): Float32Array {
    // 根据当前状态选择合适的缓冲区
    if (this.isInSpeech) {
      // 语音进行中，使用短期缓冲区
      const chunks = this.shortTermBuffer.getAll();
      return this.combineAudioChunks(chunks);
    } else {
      // 语音结束，使用中期缓冲区获取完整语音段
      const chunks = this.mediumTermBuffer.getLast(200); // 最近4秒
      return this.combineAudioChunks(chunks);
    }
  }

  private combineAudioChunks(chunks: Float32Array[]): Float32Array {
    if (chunks.length === 0) {
      return new Float32Array(0);
    }

    const totalLength = chunks.reduce((sum, chunk) => sum + chunk.length, 0);
    const combined = new Float32Array(totalLength);

    let offset = 0;
    for (const chunk of chunks) {
      combined.set(chunk, offset);
      offset += chunk.length;
    }

    return combined;
  }

  getContextBuffer(duration: number = 5000): Float32Array {
    // 获取指定时长的上下文音频（用于语义分析）
    const framesNeeded = Math.floor((duration / 1000) * 50); // 50fps
    const chunks = this.longTermBuffer.getLast(framesNeeded);
    return this.combineAudioChunks(chunks);
  }

  getVADHistory(): VADResult[] {
    return this.vadHistory.getAll();
  }

  reset(): void {
    this.shortTermBuffer.clear();
    this.mediumTermBuffer.clear();
    this.longTermBuffer.clear();
    this.vadHistory.clear();

    this.lastTriggerTime = 0;
    this.speechStartTime = 0;
    this.silenceStartTime = 0;
    this.isInSpeech = false;
  }
}
```

### 3. 动态音频分片器实现

```typescript
// frontend/src/utils/dynamicSegmenter.ts
export interface AudioSegment {
  audioData: Float32Array;
  startTime: number;
  endTime: number;
  vadResults: VADResult[];
  confidence: number;
  segmentType: 'speech' | 'silence' | 'mixed';
}

export class DynamicSegmenter {
  private currentSegment: AudioSegment | null = null;
  private segmentHistory: AudioSegment[] = [];
  private readonly maxHistorySize = 10;

  constructor(
    private vadDetector: VADDetector,
    private bufferManager: AudioBufferManager
  ) {}

  processAudioChunk(audioData: Float32Array): AudioSegment | null {
    const vadResult = this.vadDetector.detectVoiceActivity(audioData);
    const triggerInfo = this.bufferManager.addAudioChunk(audioData, vadResult);

    // 更新当前段
    this.updateCurrentSegment(audioData, vadResult);

    // 检查是否需要完成当前段
    if (triggerInfo.shouldTrigger) {
      return this.finalizeCurrentSegment(triggerInfo);
    }

    return null;
  }

  private updateCurrentSegment(audioData: Float32Array, vadResult: VADResult): void {
    if (!this.currentSegment) {
      this.currentSegment = {
        audioData: audioData.slice(),
        startTime: vadResult.timestamp,
        endTime: vadResult.timestamp,
        vadResults: [vadResult],
        confidence: vadResult.confidence,
        segmentType: vadResult.isSpeech ? 'speech' : 'silence'
      };
    } else {
      // 扩展当前段
      const combinedAudio = new Float32Array(this.currentSegment.audioData.length + audioData.length);
      combinedAudio.set(this.currentSegment.audioData);
      combinedAudio.set(audioData, this.currentSegment.audioData.length);

      this.currentSegment.audioData = combinedAudio;
      this.currentSegment.endTime = vadResult.timestamp;
      this.currentSegment.vadResults.push(vadResult);

      // 更新段类型和置信度
      this.updateSegmentMetadata();
    }
  }

  private updateSegmentMetadata(): void {
    if (!this.currentSegment) return;

    const vadResults = this.currentSegment.vadResults;
    const speechCount = vadResults.filter(r => r.isSpeech).length;
    const totalCount = vadResults.length;

    // 更新段类型
    if (speechCount === 0) {
      this.currentSegment.segmentType = 'silence';
    } else if (speechCount === totalCount) {
      this.currentSegment.segmentType = 'speech';
    } else {
      this.currentSegment.segmentType = 'mixed';
    }

    // 更新置信度
    const avgConfidence = vadResults.reduce((sum, r) => sum + r.confidence, 0) / totalCount;
    this.currentSegment.confidence = avgConfidence;
  }

  private finalizeCurrentSegment(triggerInfo: BufferTriggerInfo): AudioSegment | null {
    if (!this.currentSegment) return null;

    // 获取完整的识别缓冲区数据
    const recognitionBuffer = this.bufferManager.getRecognitionBuffer();

    const finalSegment: AudioSegment = {
      ...this.currentSegment,
      audioData: recognitionBuffer
    };

    // 添加到历史记录
    this.segmentHistory.push(finalSegment);
    if (this.segmentHistory.length > this.maxHistorySize) {
      this.segmentHistory.shift();
    }

    // 重置当前段
    this.currentSegment = null;

    console.log(`Finalized segment: ${finalSegment.segmentType}, duration: ${finalSegment.endTime - finalSegment.startTime}ms, confidence: ${finalSegment.confidence.toFixed(2)}`);

    return finalSegment;
  }

  getSegmentHistory(): AudioSegment[] {
    return [...this.segmentHistory];
  }

  getCurrentSegmentInfo(): Partial<AudioSegment> | null {
    if (!this.currentSegment) return null;

    return {
      startTime: this.currentSegment.startTime,
      endTime: this.currentSegment.endTime,
      segmentType: this.currentSegment.segmentType,
      confidence: this.currentSegment.confidence
    };
  }

  reset(): void {
    this.currentSegment = null;
    this.segmentHistory = [];
    this.bufferManager.reset();
  }
}
```

## 后端流式识别管理器实现

### 4. 流式识别状态机详细实现

```typescript
// backend/src/types/asrTypes.ts
export enum ASRState {
  IDLE = 'idle',
  LISTENING = 'listening',
  PROCESSING = 'processing',
  MERGING = 'merging',
  OUTPUTTING = 'outputting'
}

export interface RecognitionSession {
  sessionId: string;
  state: ASRState;
  audioBuffer: Buffer[];
  startTime: number;
  lastActivityTime: number;
  vadInfo: VADInfo[];
  partialResults: RecognitionResult[];
  contextHistory: string[];
}

export interface VADInfo {
  isSpeech: boolean;
  energy: number;
  confidence: number;
  timestamp: number;
  isEndOfSpeech?: boolean;
}

export interface RecognitionResult {
  text: string;
  confidence: number;
  timestamp: number;
  service: string;
  isPartial: boolean;
  segmentId: string;
}

// backend/src/services/streamingASRManager.ts
export class StreamingASRManager {
  private state: ASRState = ASRState.IDLE;
  private recognitionSessions: Map<string, RecognitionSession> = new Map();
  private asrServices: ASRService[];
  private textMerger: TextMerger;
  private contextManager: ContextManager;

  constructor() {
    this.asrServices = [
      new IflytekASRService(),
      new AlibabaASRService(),
      new BaiduASRService(),
      new OpenAIASRService()
    ];
    this.textMerger = new TextMerger();
    this.contextManager = new ContextManager();
  }

  async processAudioChunk(
    sessionId: string,
    audioData: Buffer,
    vadInfo: VADInfo
  ): Promise<RecognitionResult | null> {

    const session = this.getOrCreateSession(sessionId);
    session.lastActivityTime = Date.now();
    session.vadInfo.push(vadInfo);

    console.log(`[${sessionId}] Processing audio chunk, state: ${this.state}, VAD: ${vadInfo.isSpeech}`);

    switch (this.state) {
      case ASRState.IDLE:
        return await this.handleIdleState(session, audioData, vadInfo);

      case ASRState.LISTENING:
        return await this.handleListeningState(session, audioData, vadInfo);

      case ASRState.PROCESSING:
        return await this.handleProcessingState(session, audioData, vadInfo);

      case ASRState.MERGING:
        return await this.handleMergingState(session, audioData, vadInfo);

      case ASRState.OUTPUTTING:
        return await this.handleOutputtingState(session, audioData, vadInfo);

      default:
        console.warn(`Unknown ASR state: ${this.state}`);
        return null;
    }
  }

  private async handleIdleState(
    session: RecognitionSession,
    audioData: Buffer,
    vadInfo: VADInfo
  ): Promise<RecognitionResult | null> {

    if (vadInfo.isSpeech && vadInfo.confidence > 0.7) {
      console.log(`[${session.sessionId}] Speech detected, transitioning to LISTENING`);
      this.transitionTo(ASRState.LISTENING);
      session.startTime = Date.now();
      session.audioBuffer = [audioData];
      session.partialResults = [];
      return null;
    }

    return null;
  }

  private async handleListeningState(
    session: RecognitionSession,
    audioData: Buffer,
    vadInfo: VADInfo
  ): Promise<RecognitionResult | null> {

    session.audioBuffer.push(audioData);

    // 检查是否应该开始识别
    if (this.shouldStartRecognition(session)) {
      console.log(`[${session.sessionId}] Starting recognition, transitioning to PROCESSING`);
      this.transitionTo(ASRState.PROCESSING);
      return await this.performRecognition(session);
    }

    // 检查是否超时
    if (this.isSessionTimeout(session)) {
      console.log(`[${session.sessionId}] Session timeout, returning to IDLE`);
      this.transitionTo(ASRState.IDLE);
      this.cleanupSession(session);
    }

    return null;
  }

  private async handleProcessingState(
    session: RecognitionSession,
    audioData: Buffer,
    vadInfo: VADInfo
  ): Promise<RecognitionResult | null> {

    // 继续收集音频数据
    session.audioBuffer.push(audioData);

    // 检查是否检测到语音结束
    if (vadInfo.isEndOfSpeech || this.detectSpeechEnd(session)) {
      console.log(`[${session.sessionId}] Speech end detected, transitioning to MERGING`);
      this.transitionTo(ASRState.MERGING);
      return await this.finalizeRecognition(session);
    }

    // 检查是否需要进行增量识别
    if (this.shouldPerformIncrementalRecognition(session)) {
      return await this.performIncrementalRecognition(session);
    }

    return null;
  }

  private async handleMergingState(
    session: RecognitionSession,
    audioData: Buffer,
    vadInfo: VADInfo
  ): Promise<RecognitionResult | null> {

    // 合并所有识别结果
    const mergedResult = await this.mergeAndFinalize(session);

    if (mergedResult) {
      console.log(`[${session.sessionId}] Merging complete, transitioning to OUTPUTTING`);
      this.transitionTo(ASRState.OUTPUTTING);
      return mergedResult;
    }

    // 如果合并失败，返回IDLE
    this.transitionTo(ASRState.IDLE);
    this.cleanupSession(session);
    return null;
  }

  private async handleOutputtingState(
    session: RecognitionSession,
    audioData: Buffer,
    vadInfo: VADInfo
  ): Promise<RecognitionResult | null> {

    // 输出完成，检查是否有新的语音输入
    if (vadInfo.isSpeech && vadInfo.confidence > 0.7) {
      console.log(`[${session.sessionId}] New speech detected, transitioning to LISTENING`);
      this.transitionTo(ASRState.LISTENING);
      session.startTime = Date.now();
      session.audioBuffer = [audioData];
      session.partialResults = [];
    } else {
      // 返回IDLE状态
      this.transitionTo(ASRState.IDLE);
      this.cleanupSession(session);
    }

    return null;
  }

  private shouldStartRecognition(session: RecognitionSession): boolean {
    const bufferDuration = this.calculateBufferDuration(session.audioBuffer);
    const recentVAD = session.vadInfo.slice(-10); // 最近10个VAD结果
    const speechRatio = recentVAD.filter(v => v.isSpeech).length / recentVAD.length;

    return bufferDuration >= 1000 && speechRatio > 0.6; // 1秒且60%为语音
  }

  private shouldPerformIncrementalRecognition(session: RecognitionSession): boolean {
    const bufferDuration = this.calculateBufferDuration(session.audioBuffer);
    const timeSinceLastRecognition = Date.now() - (session.partialResults[session.partialResults.length - 1]?.timestamp || session.startTime);

    return bufferDuration >= 2000 && timeSinceLastRecognition >= 1500; // 2秒缓冲且1.5秒间隔
  }

  private detectSpeechEnd(session: RecognitionSession): boolean {
    const recentVAD = session.vadInfo.slice(-20); // 最近20个VAD结果
    const silenceCount = recentVAD.filter(v => !v.isSpeech).length;

    return silenceCount >= 15; // 75%为静音认为语音结束
  }

  private async performRecognition(session: RecognitionSession): Promise<RecognitionResult | null> {
    const audioBuffer = Buffer.concat(session.audioBuffer);

    // 并行调用多个ASR服务
    const recognitionPromises = this.asrServices.map(async (service) => {
      try {
        const result = await service.recognize(audioBuffer);
        return {
          ...result,
          service: service.getName(),
          timestamp: Date.now(),
          isPartial: true,
          segmentId: `${session.sessionId}-${Date.now()}`
        };
      } catch (error) {
        console.error(`ASR service ${service.getName()} failed:`, error);
        return null;
      }
    });

    const results = await Promise.allSettled(recognitionPromises);
    const successfulResults = results
      .filter((result): result is PromiseFulfilledResult<RecognitionResult> =>
        result.status === 'fulfilled' && result.value !== null
      )
      .map(result => result.value);

    if (successfulResults.length > 0) {
      const bestResult = this.selectBestResult(successfulResults);
      session.partialResults.push(bestResult);
      return bestResult;
    }

    return null;
  }

  private async performIncrementalRecognition(session: RecognitionSession): Promise<RecognitionResult | null> {
    // 只使用最近的音频数据进行增量识别
    const recentAudio = session.audioBuffer.slice(-5); // 最近5个音频块
    const audioBuffer = Buffer.concat(recentAudio);

    // 使用最快的ASR服务进行增量识别
    const fastService = this.asrServices[0]; // 假设第一个是最快的

    try {
      const result = await fastService.recognize(audioBuffer);
      const incrementalResult: RecognitionResult = {
        ...result,
        service: fastService.getName(),
        timestamp: Date.now(),
        isPartial: true,
        segmentId: `${session.sessionId}-incremental-${Date.now()}`
      };

      session.partialResults.push(incrementalResult);
      return incrementalResult;
    } catch (error) {
      console.error('Incremental recognition failed:', error);
      return null;
    }
  }

  private async finalizeRecognition(session: RecognitionSession): Promise<RecognitionResult | null> {
    const audioBuffer = Buffer.concat(session.audioBuffer);

    // 使用所有ASR服务进行最终识别
    const finalPromises = this.asrServices.map(async (service) => {
      try {
        const result = await service.recognize(audioBuffer);
        return {
          ...result,
          service: service.getName(),
          timestamp: Date.now(),
          isPartial: false,
          segmentId: `${session.sessionId}-final-${Date.now()}`
        };
      } catch (error) {
        console.error(`Final ASR service ${service.getName()} failed:`, error);
        return null;
      }
    });

    const results = await Promise.allSettled(finalPromises);
    const successfulResults = results
      .filter((result): result is PromiseFulfilledResult<RecognitionResult> =>
        result.status === 'fulfilled' && result.value !== null
      )
      .map(result => result.value);

    if (successfulResults.length > 0) {
      const bestResult = this.selectBestResult(successfulResults);
      session.partialResults.push(bestResult);
      return bestResult;
    }

    return null;
  }

  private async mergeAndFinalize(session: RecognitionSession): Promise<RecognitionResult | null> {
    if (session.partialResults.length === 0) {
      return null;
    }

    // 使用文本合并器合并所有部分结果
    const mergedText = await this.textMerger.mergeResults(session.partialResults);

    // 添加到上下文历史
    this.contextManager.addToHistory(session.sessionId, mergedText.text);

    const finalResult: RecognitionResult = {
      text: mergedText.text,
      confidence: mergedText.confidence,
      timestamp: Date.now(),
      service: 'merged',
      isPartial: false,
      segmentId: `${session.sessionId}-merged-${Date.now()}`
    };

    return finalResult;
  }

  private selectBestResult(results: RecognitionResult[]): RecognitionResult {
    // 根据置信度和服务优先级选择最佳结果
    return results.reduce((best, current) => {
      const bestScore = this.calculateResultScore(best);
      const currentScore = this.calculateResultScore(current);
      return currentScore > bestScore ? current : best;
    });
  }

  private calculateResultScore(result: RecognitionResult): number {
    // 综合考虑置信度、服务优先级、文本长度等因素
    const serviceWeight = this.getServiceWeight(result.service);
    const lengthBonus = Math.min(result.text.length / 10, 2); // 长度奖励，最多2分

    return result.confidence * serviceWeight + lengthBonus;
  }

  private getServiceWeight(serviceName: string): number {
    const weights: Record<string, number> = {
      'iflytek': 1.2,
      'alibaba': 1.1,
      'baidu': 1.0,
      'openai': 0.9
    };
    return weights[serviceName.toLowerCase()] || 1.0;
  }

  private calculateBufferDuration(audioBuffer: Buffer[]): number {
    // 假设16kHz采样率，16位，单声道
    const totalBytes = audioBuffer.reduce((sum, buffer) => sum + buffer.length, 0);
    const totalSamples = totalBytes / 2; // 16位 = 2字节
    return (totalSamples / 16000) * 1000; // 转换为毫秒
  }

  private isSessionTimeout(session: RecognitionSession): boolean {
    const timeout = 30000; // 30秒超时
    return Date.now() - session.lastActivityTime > timeout;
  }

  private transitionTo(newState: ASRState): void {
    console.log(`ASR State transition: ${this.state} -> ${newState}`);
    this.state = newState;
  }

  private getOrCreateSession(sessionId: string): RecognitionSession {
    if (!this.recognitionSessions.has(sessionId)) {
      const session: RecognitionSession = {
        sessionId,
        state: ASRState.IDLE,
        audioBuffer: [],
        startTime: Date.now(),
        lastActivityTime: Date.now(),
        vadInfo: [],
        partialResults: [],
        contextHistory: []
      };
      this.recognitionSessions.set(sessionId, session);
    }
    return this.recognitionSessions.get(sessionId)!;
  }

  private cleanupSession(session: RecognitionSession): void {
    session.audioBuffer = [];
    session.vadInfo = [];
    session.partialResults = [];
  }

  public getSessionState(sessionId: string): ASRState {
    const session = this.recognitionSessions.get(sessionId);
    return session ? session.state : ASRState.IDLE;
  }

  public cleanup(): void {
    // 清理超时的会话
    const now = Date.now();
    for (const [sessionId, session] of this.recognitionSessions.entries()) {
      if (now - session.lastActivityTime > 300000) { // 5分钟超时
        this.recognitionSessions.delete(sessionId);
        console.log(`Cleaned up expired session: ${sessionId}`);
      }
    }
  }
}
```

### 5. 智能文本合并器实现

```typescript
// backend/src/services/textMerger.ts
export interface TextSegment {
  text: string;
  confidence: number;
  timestamp: number;
  service: string;
  isPartial: boolean;
}

export interface MergedText {
  text: string;
  confidence: number;
  segments: TextSegment[];
  mergeStrategy: string;
}

export class TextMerger {
  private contextWindow: TextSegment[] = [];
  private readonly maxContextSize = 10;
  private readonly similarityThreshold = 0.8;

  async mergeResults(results: RecognitionResult[]): Promise<MergedText> {
    console.log(`Merging ${results.length} recognition results`);

    // 1. 转换为文本段
    const segments: TextSegment[] = results.map(result => ({
      text: result.text,
      confidence: result.confidence,
      timestamp: result.timestamp,
      service: result.service,
      isPartial: result.isPartial
    }));

    // 2. 去重处理
    const deduplicatedSegments = this.removeDuplicates(segments);

    // 3. 时间排序
    const sortedSegments = deduplicatedSegments.sort((a, b) => a.timestamp - b.timestamp);

    // 4. 语义连贯性检查
    const coherentSegments = await this.ensureCoherence(sortedSegments);

    // 5. 智能合并
    const mergedText = this.performIntelligentMerge(coherentSegments);

    // 6. 后处理优化
    const optimizedText = this.postProcessText(mergedText);

    const result: MergedText = {
      text: optimizedText,
      confidence: this.calculateOverallConfidence(coherentSegments),
      segments: coherentSegments,
      mergeStrategy: this.determineMergeStrategy(coherentSegments)
    };

    console.log(`Merge result: "${result.text}" (confidence: ${result.confidence.toFixed(2)})`);
    return result;
  }

  private removeDuplicates(segments: TextSegment[]): TextSegment[] {
    const result: TextSegment[] = [];

    for (const segment of segments) {
      const isDuplicate = result.some(existing =>
        this.calculateTextSimilarity(segment.text, existing.text) > this.similarityThreshold
      );

      if (!isDuplicate) {
        result.push(segment);
      } else {
        // 如果是重复，选择置信度更高的
        const existingIndex = result.findIndex(existing =>
          this.calculateTextSimilarity(segment.text, existing.text) > this.similarityThreshold
        );

        if (existingIndex !== -1 && segment.confidence > result[existingIndex].confidence) {
          result[existingIndex] = segment;
        }
      }
    }

    console.log(`Deduplication: ${segments.length} -> ${result.length} segments`);
    return result;
  }

  private calculateTextSimilarity(text1: string, text2: string): number {
    // 使用编辑距离计算相似度
    const distance = this.levenshteinDistance(text1, text2);
    const maxLength = Math.max(text1.length, text2.length);

    if (maxLength === 0) return 1;
    return 1 - (distance / maxLength);
  }

  private levenshteinDistance(str1: string, str2: string): number {
    const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));

    for (let i = 0; i <= str1.length; i++) matrix[0][i] = i;
    for (let j = 0; j <= str2.length; j++) matrix[j][0] = j;

    for (let j = 1; j <= str2.length; j++) {
      for (let i = 1; i <= str1.length; i++) {
        const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
        matrix[j][i] = Math.min(
          matrix[j][i - 1] + 1,     // deletion
          matrix[j - 1][i] + 1,     // insertion
          matrix[j - 1][i - 1] + indicator // substitution
        );
      }
    }

    return matrix[str2.length][str1.length];
  }

  private async ensureCoherence(segments: TextSegment[]): Promise<TextSegment[]> {
    // 简单的语义连贯性检查
    const result: TextSegment[] = [];

    for (let i = 0; i < segments.length; i++) {
      const current = segments[i];
      const previous = result[result.length - 1];

      if (!previous) {
        result.push(current);
        continue;
      }

      // 检查时间间隔
      const timeGap = current.timestamp - previous.timestamp;
      if (timeGap > 5000) { // 5秒间隔太大，可能不连贯
        console.log(`Large time gap detected: ${timeGap}ms`);
      }

      // 检查文本连贯性
      const isCoherent = this.checkTextCoherence(previous.text, current.text);
      if (isCoherent) {
        result.push(current);
      } else {
        console.log(`Incoherent text detected: "${previous.text}" -> "${current.text}"`);
        // 可以选择跳过或者降低置信度
        result.push({
          ...current,
          confidence: current.confidence * 0.8 // 降低置信度
        });
      }
    }

    return result;
  }

  private checkTextCoherence(previousText: string, currentText: string): boolean {
    // 简单的连贯性检查规则

    // 1. 检查是否有重复的开头
    const words1 = previousText.split(/\s+/);
    const words2 = currentText.split(/\s+/);

    if (words1.length > 0 && words2.length > 0) {
      const lastWord = words1[words1.length - 1];
      const firstWord = words2[0];

      // 如果当前文本以上一个文本的最后一个词开头，可能是重复
      if (lastWord === firstWord) {
        return false;
      }
    }

    // 2. 检查语言一致性（中文/英文）
    const isChinese1 = /[\u4e00-\u9fa5]/.test(previousText);
    const isChinese2 = /[\u4e00-\u9fa5]/.test(currentText);

    if (isChinese1 !== isChinese2) {
      return false; // 语言不一致
    }

    return true;
  }

  private performIntelligentMerge(segments: TextSegment[]): string {
    if (segments.length === 0) return '';
    if (segments.length === 1) return segments[0].text;

    let mergedText = '';

    for (let i = 0; i < segments.length; i++) {
      const current = segments[i];
      const previous = segments[i - 1];

      if (i === 0) {
        mergedText = current.text;
        continue;
      }

      // 智能连接逻辑
      const connector = this.determineConnector(previous.text, current.text);
      mergedText += connector + current.text;
    }

    return mergedText;
  }

  private determineConnector(previousText: string, currentText: string): string {
    // 确定两个文本段之间的连接符

    // 如果前一个文本以标点符号结尾，不需要额外连接符
    if (/[。！？，、；：]$/.test(previousText.trim())) {
      return '';
    }

    // 如果当前文本以标点符号开头，不需要额外连接符
    if (/^[。！？，、；：]/.test(currentText.trim())) {
      return '';
    }

    // 检查是否需要空格（英文）
    const isEnglish1 = /[a-zA-Z]$/.test(previousText.trim());
    const isEnglish2 = /^[a-zA-Z]/.test(currentText.trim());

    if (isEnglish1 && isEnglish2) {
      return ' ';
    }

    // 中文通常不需要连接符
    return '';
  }

  private postProcessText(text: string): string {
    // 后处理优化
    let result = text;

    // 1. 去除多余的空格
    result = result.replace(/\s+/g, ' ').trim();

    // 2. 修正标点符号
    result = result.replace(/\s+([。！？，、；：])/g, '$1');

    // 3. 首字母大写（英文）
    result = result.replace(/^[a-z]/, match => match.toUpperCase());

    // 4. 去除重复的标点符号
    result = result.replace(/([。！？])\1+/g, '$1');

    return result;
  }

  private calculateOverallConfidence(segments: TextSegment[]): number {
    if (segments.length === 0) return 0;

    // 加权平均置信度
    let totalWeight = 0;
    let weightedSum = 0;

    for (const segment of segments) {
      const weight = segment.text.length; // 以文本长度作为权重
      weightedSum += segment.confidence * weight;
      totalWeight += weight;
    }

    return totalWeight > 0 ? weightedSum / totalWeight : 0;
  }

  private determineMergeStrategy(segments: TextSegment[]): string {
    if (segments.length <= 1) return 'single';

    const services = new Set(segments.map(s => s.service));
    const hasPartial = segments.some(s => s.isPartial);

    if (services.size === 1) {
      return hasPartial ? 'single-service-incremental' : 'single-service-batch';
    } else {
      return hasPartial ? 'multi-service-incremental' : 'multi-service-batch';
    }
  }
}
```

### 6. 前端平滑显示组件实现

```typescript
// frontend/src/components/SmoothTextDisplay.tsx
import React, { useState, useEffect, useRef } from 'react';

export interface TextAnimation {
  id: string;
  fromText: string;
  toText: string;
  duration: number;
  type: 'typewriter' | 'fade' | 'slide';
}

export interface SmoothTextDisplayProps {
  text: string;
  isUpdating: boolean;
  animationType?: 'typewriter' | 'fade' | 'slide';
  animationSpeed?: number;
  className?: string;
}

export const SmoothTextDisplay: React.FC<SmoothTextDisplayProps> = ({
  text,
  isUpdating,
  animationType = 'typewriter',
  animationSpeed = 50,
  className = ''
}) => {
  const [displayText, setDisplayText] = useState('');
  const [isAnimating, setIsAnimating] = useState(false);
  const animationRef = useRef<number>();
  const timeoutRef = useRef<NodeJS.Timeout>();

  useEffect(() => {
    if (text !== displayText) {
      animateTextChange(displayText, text);
    }
  }, [text]);

  const animateTextChange = (fromText: string, toText: string) => {
    if (isAnimating) {
      // 如果正在动画中，取消当前动画
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    }

    setIsAnimating(true);

    switch (animationType) {
      case 'typewriter':
        animateTypewriter(fromText, toText);
        break;
      case 'fade':
        animateFade(fromText, toText);
        break;
      case 'slide':
        animateSlide(fromText, toText);
        break;
    }
  };

  const animateTypewriter = (fromText: string, toText: string) => {
    // 找到公共前缀
    let commonPrefixLength = 0;
    const minLength = Math.min(fromText.length, toText.length);

    for (let i = 0; i < minLength; i++) {
      if (fromText[i] === toText[i]) {
        commonPrefixLength++;
      } else {
        break;
      }
    }

    const commonPrefix = toText.substring(0, commonPrefixLength);
    const textToAdd = toText.substring(commonPrefixLength);
    const textToRemove = fromText.substring(commonPrefixLength);

    let currentStep = 0;
    const totalSteps = textToRemove.length + textToAdd.length;

    const animate = () => {
      if (currentStep < textToRemove.length) {
        // 删除阶段
        const removeCount = currentStep + 1;
        const currentText = commonPrefix + textToRemove.substring(0, textToRemove.length - removeCount);
        setDisplayText(currentText);
      } else {
        // 添加阶段
        const addCount = currentStep - textToRemove.length + 1;
        const currentText = commonPrefix + textToAdd.substring(0, addCount);
        setDisplayText(currentText);
      }

      currentStep++;

      if (currentStep <= totalSteps) {
        timeoutRef.current = setTimeout(animate, animationSpeed);
      } else {
        setIsAnimating(false);
      }
    };

    animate();
  };

  const animateFade = (fromText: string, toText: string) => {
    // 淡出当前文本
    setDisplayText('');

    timeoutRef.current = setTimeout(() => {
      // 淡入新文本
      setDisplayText(toText);
      setIsAnimating(false);
    }, 200);
  };

  const animateSlide = (fromText: string, toText: string) => {
    // 滑动效果的简单实现
    setDisplayText(toText);
    setIsAnimating(false);
  };

  useEffect(() => {
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return (
    <div className={`smooth-text-container ${className}`}>
      <span
        className={`text-content ${isUpdating ? 'updating' : ''} ${isAnimating ? 'animating' : ''}`}
      >
        {displayText}
      </span>
      {(isUpdating || isAnimating) && (
        <span className="cursor-blink animate-pulse">|</span>
      )}
    </div>
  );
};

// frontend/src/hooks/useTextAnimation.ts
export const useTextAnimation = (
  initialText: string = '',
  animationType: 'typewriter' | 'fade' | 'slide' = 'typewriter',
  animationSpeed: number = 50
) => {
  const [displayText, setDisplayText] = useState(initialText);
  const [isAnimating, setIsAnimating] = useState(false);
  const animationQueue = useRef<TextAnimation[]>([]);
  const currentAnimationRef = useRef<number>();

  const addTextAnimation = (newText: string, type?: TextAnimation['type']) => {
    const animation: TextAnimation = {
      id: `anim-${Date.now()}-${Math.random()}`,
      fromText: displayText,
      toText: newText,
      duration: Math.abs(newText.length - displayText.length) * animationSpeed,
      type: type || animationType
    };

    animationQueue.current.push(animation);

    if (!isAnimating) {
      processNextAnimation();
    }
  };

  const processNextAnimation = () => {
    if (animationQueue.current.length === 0) {
      setIsAnimating(false);
      return;
    }

    const nextAnimation = animationQueue.current.shift()!;
    setIsAnimating(true);

    executeAnimation(nextAnimation).then(() => {
      processNextAnimation();
    });
  };

  const executeAnimation = (animation: TextAnimation): Promise<void> => {
    return new Promise((resolve) => {
      switch (animation.type) {
        case 'typewriter':
          executeTypewriterAnimation(animation, resolve);
          break;
        case 'fade':
          executeFadeAnimation(animation, resolve);
          break;
        case 'slide':
          executeSlideAnimation(animation, resolve);
          break;
        default:
          setDisplayText(animation.toText);
          resolve();
      }
    });
  };

  const executeTypewriterAnimation = (animation: TextAnimation, onComplete: () => void) => {
    const { fromText, toText } = animation;

    // 实现打字机效果的具体逻辑
    let currentIndex = 0;
    const targetLength = toText.length;

    const typeNextChar = () => {
      if (currentIndex <= targetLength) {
        setDisplayText(toText.substring(0, currentIndex));
        currentIndex++;
        currentAnimationRef.current = requestAnimationFrame(() => {
          setTimeout(typeNextChar, animationSpeed);
        });
      } else {
        onComplete();
      }
    };

    typeNextChar();
  };

  const executeFadeAnimation = (animation: TextAnimation, onComplete: () => void) => {
    // 淡出淡入效果
    setDisplayText('');
    setTimeout(() => {
      setDisplayText(animation.toText);
      onComplete();
    }, 200);
  };

  const executeSlideAnimation = (animation: TextAnimation, onComplete: () => void) => {
    // 滑动效果
    setDisplayText(animation.toText);
    onComplete();
  };

  const clearAnimationQueue = () => {
    animationQueue.current = [];
    if (currentAnimationRef.current) {
      cancelAnimationFrame(currentAnimationRef.current);
    }
    setIsAnimating(false);
  };

  const setTextImmediately = (text: string) => {
    clearAnimationQueue();
    setDisplayText(text);
  };

  return {
    displayText,
    isAnimating,
    addTextAnimation,
    clearAnimationQueue,
    setTextImmediately
  };
};
```

## 实施时间表和检查清单

### 第1-2周：基础架构搭建

#### Week 1: VAD和音频缓冲
- [ ] **Day 1-2**: 实现VAD检测算法
  - [ ] 创建 `frontend/src/utils/vadDetector.ts`
  - [ ] 实现能量、频谱重心、过零率计算
  - [ ] 添加自适应阈值调整
  - [ ] 单元测试VAD算法

- [ ] **Day 3-4**: 实现音频缓冲管理
  - [ ] 创建 `frontend/src/utils/circularBuffer.ts`
  - [ ] 创建 `frontend/src/utils/audioBufferManager.ts`
  - [ ] 实现多级缓冲策略
  - [ ] 测试缓冲区性能

- [ ] **Day 5-7**: 动态音频分片
  - [ ] 创建 `frontend/src/utils/dynamicSegmenter.ts`
  - [ ] 集成VAD和缓冲管理
  - [ ] 实现智能触发机制
  - [ ] 端到端测试

#### Week 2: 前端集成和优化
- [ ] **Day 1-3**: 重构现有音频处理逻辑
  - [ ] 修改 `frontend/src/hooks/useInterviewSession.ts`
  - [ ] 集成新的VAD和分片系统
  - [ ] 测试WebSocket音频传输

- [ ] **Day 4-5**: 前端UI优化
  - [ ] 创建VAD状态指示器
  - [ ] 优化音频波形显示
  - [ ] 添加调试信息面板

- [ ] **Day 6-7**: 第一阶段测试
  - [ ] 功能测试
  - [ ] 性能测试
  - [ ] 用户体验测试

### 第3-4周：流式识别优化

#### Week 3: 后端状态机
- [ ] **Day 1-2**: 实现ASR状态机
  - [ ] 创建 `backend/src/types/asrTypes.ts`
  - [ ] 创建 `backend/src/services/streamingASRManager.ts`
  - [ ] 实现状态转换逻辑

- [ ] **Day 3-4**: 增量识别管理
  - [ ] 创建 `backend/src/services/incrementalResultManager.ts`
  - [ ] 实现结果队列管理
  - [ ] 添加时间戳对齐

- [ ] **Day 5-7**: 上下文保持机制
  - [ ] 创建 `backend/src/services/contextManager.ts`
  - [ ] 实现语义关联分析
  - [ ] 测试上下文连续性

#### Week 4: 多ASR服务协调
- [ ] **Day 1-3**: 重构现有ASR调用
  - [ ] 修改 `backend/websocket/interviewWs.ts`
  - [ ] 集成新的状态机
  - [ ] 优化服务选择策略

- [ ] **Day 4-5**: 并行识别优化
  - [ ] 实现服务负载均衡
  - [ ] 添加失败重试机制
  - [ ] 优化响应时间

- [ ] **Day 6-7**: 第二阶段测试
  - [ ] 集成测试
  - [ ] 压力测试
  - [ ] 准确率测试

### 第5-6周：智能合并和用户体验

#### Week 5: 文本合并算法
- [ ] **Day 1-3**: 实现智能合并
  - [ ] 创建 `backend/src/services/textMerger.ts`
  - [ ] 实现去重算法
  - [ ] 添加语义连贯性检查

- [ ] **Day 4-5**: 合并策略优化
  - [ ] 实现多种合并策略
  - [ ] 添加置信度评估
  - [ ] 优化文本后处理

- [ ] **Day 6-7**: 后端集成测试
  - [ ] 端到端文本合并测试
  - [ ] 性能优化
  - [ ] 错误处理完善

#### Week 6: 前端平滑显示
- [ ] **Day 1-3**: 实现平滑显示组件
  - [ ] 创建 `frontend/src/components/SmoothTextDisplay.tsx`
  - [ ] 创建 `frontend/src/hooks/useTextAnimation.ts`
  - [ ] 实现多种动画效果

- [ ] **Day 4-5**: 用户体验优化
  - [ ] 集成到现有界面
  - [ ] 优化动画性能
  - [ ] 添加用户配置选项

- [ ] **Day 6-7**: 第三阶段测试
  - [ ] 用户体验测试
  - [ ] 动画性能测试
  - [ ] 兼容性测试

### 第7周：测试和优化

#### 全面测试和优化
- [ ] **Day 1-2**: 系统集成测试
  - [ ] 端到端功能测试
  - [ ] 多场景测试
  - [ ] 边界条件测试

- [ ] **Day 3-4**: 性能优化
  - [ ] 内存使用优化
  - [ ] CPU使用优化
  - [ ] 网络传输优化

- [ ] **Day 5-6**: 用户验收测试
  - [ ] 真实场景测试
  - [ ] 用户反馈收集
  - [ ] 问题修复

- [ ] **Day 7**: 文档和部署
  - [ ] 技术文档编写
  - [ ] 部署指南
  - [ ] 维护手册

## 测试计划

### 单元测试
```typescript
// 测试文件结构
tests/
├── frontend/
│   ├── utils/
│   │   ├── vadDetector.test.ts
│   │   ├── audioBufferManager.test.ts
│   │   └── dynamicSegmenter.test.ts
│   └── components/
│       └── SmoothTextDisplay.test.tsx
└── backend/
    └── services/
        ├── streamingASRManager.test.ts
        ├── textMerger.test.ts
        └── contextManager.test.ts
```

### 集成测试
- WebSocket音频传输测试
- ASR服务协调测试
- 文本合并流程测试
- 前后端集成测试

### 性能测试
- 音频处理延迟测试
- 内存使用监控
- CPU使用率测试
- 并发用户测试

### 用户体验测试
- 语音识别准确率测试
- 文字显示流畅度测试
- 响应时间测试
- 错误恢复测试

## 预期效果

### 技术指标改进
1. **识别连续性**：从断断续续提升到连贯流畅
2. **响应延迟**：从平均2-3秒降低到500ms以内
3. **识别准确率**：提升10-15%
4. **用户体验**：接近手机输入法的丝滑效果

### 具体改进对比

| 指标 | 当前状态 | 优化后目标 | 改进幅度 |
|------|----------|------------|----------|
| 文字输出连续性 | 断断续续 | 连贯流畅 | 显著改善 |
| 平均响应延迟 | 2-3秒 | <500ms | 75%+ |
| 识别准确率 | 85% | 95%+ | 10%+ |
| 用户满意度 | 中等 | 高 | 显著提升 |

### 功能特性
1. **智能分片**：根据语音活动动态调整，避免机械切割
2. **流式处理**：连续识别和实时更新，保持语义连贯性
3. **智能合并**：自动去重和语义优化，输出连贯文本
4. **平滑显示**：丝滑的文字显示效果，类似手机输入法
5. **高准确率**：多ASR服务协同工作，提高识别准确率
6. **实时反馈**：即时的语音活动指示和处理状态反馈

## 风险评估和应对策略

### 技术风险
1. **VAD算法准确性**
   - 风险：可能误判语音活动
   - 应对：多特征融合，自适应阈值调整

2. **实时性能要求**
   - 风险：处理延迟影响用户体验
   - 应对：异步处理，优化算法复杂度

3. **多ASR服务协调**
   - 风险：服务不稳定或响应慢
   - 应对：失败重试，服务降级策略

### 实施风险
1. **开发时间压力**
   - 风险：功能复杂，开发周期紧张
   - 应对：分阶段实施，优先核心功能

2. **测试覆盖度**
   - 风险：测试不充分导致线上问题
   - 应对：自动化测试，多场景验证

## 成功标准

### 最小可行产品(MVP)标准
- [ ] VAD检测准确率 > 90%
- [ ] 音频分片智能化，无机械切割
- [ ] 识别结果连贯性显著改善
- [ ] 平均响应延迟 < 1秒

### 完整产品标准
- [ ] 识别准确率 > 95%
- [ ] 平均响应延迟 < 500ms
- [ ] 文字显示丝滑流畅
- [ ] 用户体验接近手机输入法
- [ ] 系统稳定性 > 99%

## 维护和扩展计划

### 短期维护（1-3个月）
- 监控系统性能指标
- 收集用户反馈并优化
- 修复发现的bug
- 调优算法参数

### 中期扩展（3-6个月）
- 添加更多ASR服务支持
- 实现语言模型集成
- 优化多语言支持
- 添加个性化配置

### 长期规划（6个月以上）
- 集成深度学习VAD模型
- 实现端到端语音理解
- 添加语音情感分析
- 支持实时语音翻译

---

**文档版本**: v1.0
**创建日期**: 2024年12月
**最后更新**: 2024年12月
**负责人**: 开发团队
**审核状态**: 待审核
```
```
