# Design Document

## Overview

"分享有礼"功能是一个完整的用户推荐奖励系统，通过邀请码和邀请链接机制实现用户增长激励。系统核心包括邀请码生成、用户注册关联、支付监听、奖励发放和数据统计等模块。该功能将无缝集成到现有的面试君平台中，保持UI风格一致性和用户体验连贯性。

## Architecture

### 系统架构图

```mermaid
graph TB
    subgraph "Frontend Layer"
        A[分享有礼页面] --> B[邀请码生成组件]
        A --> C[统计数据展示组件]
        A --> D[邀请记录组件]
        E[注册页面] --> F[邀请码输入组件]
    end
    
    subgraph "Backend API Layer"
        G[邀请API] --> H[邀请码生成服务]
        G --> I[邀请关系管理服务]
        J[认证API] --> K[注册服务增强]
        L[订单API] --> M[支付回调处理]
        N[奖励API] --> O[奖励发放服务]
    end
    
    subgraph "Database Layer"
        P[(User表)]
        Q[(UserBalance表)]
        R[(ReferralCode表)]
        S[(ReferralRelation表)]
        T[(ReferralReward表)]
        U[(Order表)]
        V[(UsageRecord表)]
    end
    
    subgraph "External Services"
        W[支付网关]
        X[通知服务]
    end
    
    A --> G
    E --> J
    L --> W
    M --> N
    O --> X
    
    H --> R
    I --> S
    K --> P
    M --> U
    O --> Q
    O --> T
    O --> V
```

### 数据流程图

```mermaid
sequenceDiagram
    participant U1 as 邀请用户
    participant UI as 分享有礼页面
    participant API as 后端API
    participant DB as 数据库
    participant U2 as 被邀请用户
    participant REG as 注册页面
    participant PAY as 支付系统
    
    U1->>UI: 访问分享有礼页面
    UI->>API: 获取/生成邀请码
    API->>DB: 查询/创建邀请码记录
    DB-->>API: 返回邀请码
    API-->>UI: 返回邀请码和链接
    UI-->>U1: 显示邀请码和分享链接
    
    U1->>U2: 分享邀请链接/码
    U2->>REG: 通过链接访问注册页面
    REG->>API: 验证邀请码有效性
    API->>DB: 查询邀请码状态
    DB-->>API: 返回验证结果
    API-->>REG: 返回验证状态
    
    U2->>REG: 完成注册
    REG->>API: 提交注册信息(含邀请码)
    API->>DB: 创建用户和邀请关系
    DB-->>API: 返回创建结果
    
    U2->>PAY: 完成首次充值
    PAY->>API: 支付成功回调
    API->>DB: 更新订单状态
    API->>DB: 检查邀请关系
    API->>DB: 发放邀请奖励
    API->>U1: 发送奖励通知
```

## Components and Interfaces

### 前端组件设计

#### 1. ReferralRewardsPage 主页面组件
```typescript
interface ReferralRewardsPageProps {}

interface ReferralStats {
  totalInvited: number;           // 总邀请人数
  successfulReferrals: number;    // 成功充值人数
  totalRewards: number;           // 累计奖励面巾值
  pendingRewards: number;         // 待发放奖励
}

interface ReferralCode {
  code: string;                   // 邀请码
  inviteLink: string;            // 邀请链接
  createdAt: string;             // 创建时间
  isActive: boolean;             // 是否有效
}
```

#### 2. InviteCodeGenerator 邀请码生成组件
```typescript
interface InviteCodeGeneratorProps {
  referralCode: ReferralCode;
  onCopyCode: (code: string) => void;
  onCopyLink: (link: string) => void;
  onShare: (platform: SharePlatform) => void;
}

type SharePlatform = 'wechat' | 'qq' | 'weibo' | 'copy';
```

#### 3. ReferralStats 统计展示组件
```typescript
interface ReferralStatsProps {
  stats: ReferralStats;
  isLoading: boolean;
}
```

#### 4. ReferralHistory 邀请记录组件
```typescript
interface ReferralHistoryProps {
  records: ReferralRecord[];
  onLoadMore: () => void;
  hasMore: boolean;
}

interface ReferralRecord {
  id: string;
  invitedUserEmail: string;       // 被邀请用户邮箱(脱敏)
  registeredAt: string;           // 注册时间
  firstPaymentAt?: string;        // 首次充值时间
  rewardAmount?: number;          // 奖励金额
  status: 'registered' | 'paid' | 'rewarded';
}
```

#### 5. InviteCodeInput 邀请码输入组件(注册页面)
```typescript
interface InviteCodeInputProps {
  value: string;
  onChange: (value: string) => void;
  onValidate: (code: string) => Promise<boolean>;
  error?: string;
  disabled?: boolean;
}
```

### 后端API接口设计

#### 1. 邀请码管理API
```typescript
// GET /api/referral/code - 获取用户邀请码
interface GetReferralCodeResponse {
  success: boolean;
  data: {
    code: string;
    inviteLink: string;
    createdAt: string;
    isActive: boolean;
  };
}

// POST /api/referral/validate - 验证邀请码
interface ValidateReferralCodeRequest {
  code: string;
}

interface ValidateReferralCodeResponse {
  success: boolean;
  valid: boolean;
  message?: string;
}
```

#### 2. 邀请统计API
```typescript
// GET /api/referral/stats - 获取邀请统计
interface GetReferralStatsResponse {
  success: boolean;
  data: {
    totalInvited: number;
    successfulReferrals: number;
    totalRewards: number;
    pendingRewards: number;
  };
}

// GET /api/referral/history - 获取邀请记录
interface GetReferralHistoryRequest {
  page?: number;
  limit?: number;
  status?: 'all' | 'registered' | 'paid' | 'rewarded';
}

interface GetReferralHistoryResponse {
  success: boolean;
  data: {
    records: ReferralRecord[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      hasMore: boolean;
    };
  };
}
```

#### 3. 奖励发放API
```typescript
// POST /api/referral/reward - 内部奖励发放接口
interface ProcessReferralRewardRequest {
  orderId: string;
  userId: string;
  amount: number;
}

interface ProcessReferralRewardResponse {
  success: boolean;
  rewardProcessed: boolean;
  rewardAmount?: number;
  message?: string;
}
```

### 服务层接口设计

#### 1. ReferralService 邀请服务
```typescript
class ReferralService {
  // 生成或获取用户邀请码
  async getOrCreateReferralCode(userId: string): Promise<ReferralCode>;
  
  // 验证邀请码有效性
  async validateReferralCode(code: string): Promise<boolean>;
  
  // 创建邀请关系
  async createReferralRelation(inviterCode: string, inviteeUserId: string): Promise<void>;
  
  // 获取邀请统计
  async getReferralStats(userId: string): Promise<ReferralStats>;
  
  // 获取邀请记录
  async getReferralHistory(userId: string, options: PaginationOptions): Promise<PaginatedReferralRecords>;
  
  // 处理邀请奖励
  async processReferralReward(orderId: string): Promise<RewardResult>;
}
```

#### 2. RewardService 奖励服务
```typescript
class RewardService {
  // 发放邀请奖励
  async grantReferralReward(inviterId: string, inviteeId: string, amount: number): Promise<void>;
  
  // 检查是否已发放奖励
  async hasRewardBeenGranted(inviterId: string, inviteeId: string): Promise<boolean>;
  
  // 创建奖励记录
  async createRewardRecord(data: RewardRecordData): Promise<void>;
  
  // 更新用户余额
  async updateUserBalance(userId: string, amount: number): Promise<void>;
}
```

## Data Models

### 数据库表结构设计

#### 1. ReferralCode 邀请码表
```sql
CREATE TABLE referral_codes (
  id VARCHAR(30) PRIMARY KEY,
  user_id VARCHAR(30) NOT NULL,
  code VARCHAR(20) UNIQUE NOT NULL,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  expires_at TIMESTAMP NULL,
  
  INDEX idx_user_id (user_id),
  INDEX idx_code (code),
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
```

#### 2. ReferralRelation 邀请关系表
```sql
CREATE TABLE referral_relations (
  id VARCHAR(30) PRIMARY KEY,
  inviter_id VARCHAR(30) NOT NULL,
  invitee_id VARCHAR(30) NOT NULL,
  referral_code VARCHAR(20) NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  first_payment_at TIMESTAMP NULL,
  reward_granted BOOLEAN DEFAULT false,
  reward_granted_at TIMESTAMP NULL,
  
  UNIQUE KEY unique_invitee (invitee_id),
  INDEX idx_inviter_id (inviter_id),
  INDEX idx_referral_code (referral_code),
  FOREIGN KEY (inviter_id) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (invitee_id) REFERENCES users(id) ON DELETE CASCADE
);
```

#### 3. ReferralReward 邀请奖励表
```sql
CREATE TABLE referral_rewards (
  id VARCHAR(30) PRIMARY KEY,
  inviter_id VARCHAR(30) NOT NULL,
  invitee_id VARCHAR(30) NOT NULL,
  relation_id VARCHAR(30) NOT NULL,
  reward_amount INT NOT NULL,
  reward_type VARCHAR(20) DEFAULT 'REFERRAL_BONUS',
  order_id VARCHAR(30) NULL,
  status VARCHAR(20) DEFAULT 'PENDING',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  processed_at TIMESTAMP NULL,
  
  INDEX idx_inviter_id (inviter_id),
  INDEX idx_invitee_id (invitee_id),
  INDEX idx_relation_id (relation_id),
  INDEX idx_order_id (order_id),
  FOREIGN KEY (inviter_id) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (invitee_id) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (relation_id) REFERENCES referral_relations(id) ON DELETE CASCADE,
  FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE SET NULL
);
```

#### 4. Prisma Schema 更新
```prisma
model ReferralCode {
  id        String   @id @default(cuid())
  userId    String
  code      String   @unique
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  expiresAt DateTime?
  
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@index([userId])
  @@index([code])
  @@map("referral_codes")
}

model ReferralRelation {
  id               String    @id @default(cuid())
  inviterId        String
  inviteeId        String    @unique
  referralCode     String
  createdAt        DateTime  @default(now())
  firstPaymentAt   DateTime?
  rewardGranted    Boolean   @default(false)
  rewardGrantedAt  DateTime?
  
  inviter          User      @relation("InviterRelations", fields: [inviterId], references: [id], onDelete: Cascade)
  invitee          User      @relation("InviteeRelation", fields: [inviteeId], references: [id], onDelete: Cascade)
  rewards          ReferralReward[]
  
  @@index([inviterId])
  @@index([referralCode])
  @@map("referral_relations")
}

model ReferralReward {
  id           String    @id @default(cuid())
  inviterId    String
  inviteeId    String
  relationId   String
  rewardAmount Int
  rewardType   String    @default("REFERRAL_BONUS")
  orderId      String?
  status       String    @default("PENDING")
  createdAt    DateTime  @default(now())
  processedAt  DateTime?
  
  inviter      User             @relation("InviterRewards", fields: [inviterId], references: [id], onDelete: Cascade)
  invitee      User             @relation("InviteeRewards", fields: [inviteeId], references: [id], onDelete: Cascade)
  relation     ReferralRelation @relation(fields: [relationId], references: [id], onDelete: Cascade)
  order        Order?           @relation(fields: [orderId], references: [id], onDelete: SetNull)
  
  @@index([inviterId])
  @@index([inviteeId])
  @@index([relationId])
  @@index([orderId])
  @@map("referral_rewards")
}

// User模型需要添加的关系
model User {
  // ... 现有字段 ...
  
  referralCodes      ReferralCode[]
  inviterRelations   ReferralRelation[] @relation("InviterRelations")
  inviteeRelation    ReferralRelation?  @relation("InviteeRelation")
  inviterRewards     ReferralReward[]   @relation("InviterRewards")
  inviteeRewards     ReferralReward[]   @relation("InviteeRewards")
}

// Order模型需要添加的关系
model Order {
  // ... 现有字段 ...
  
  referralRewards    ReferralReward[]
}
```

## Error Handling

### 错误类型定义
```typescript
enum ReferralErrorCode {
  INVALID_REFERRAL_CODE = 'INVALID_REFERRAL_CODE',
  EXPIRED_REFERRAL_CODE = 'EXPIRED_REFERRAL_CODE',
  SELF_REFERRAL_NOT_ALLOWED = 'SELF_REFERRAL_NOT_ALLOWED',
  ALREADY_REFERRED = 'ALREADY_REFERRED',
  REWARD_ALREADY_GRANTED = 'REWARD_ALREADY_GRANTED',
  INSUFFICIENT_PAYMENT_AMOUNT = 'INSUFFICIENT_PAYMENT_AMOUNT',
  REWARD_PROCESSING_FAILED = 'REWARD_PROCESSING_FAILED'
}

interface ReferralError {
  code: ReferralErrorCode;
  message: string;
  details?: any;
}
```

### 错误处理策略
1. **邀请码验证错误**: 前端实时验证，显示友好提示
2. **重复邀请错误**: 阻止重复建立邀请关系，记录异常日志
3. **奖励发放错误**: 使用事务确保数据一致性，失败时回滚
4. **支付回调错误**: 实现重试机制，记录详细错误日志
5. **并发处理错误**: 使用数据库锁机制防止重复奖励

## Testing Strategy

### 单元测试
1. **ReferralService 测试**
   - 邀请码生成和验证逻辑
   - 邀请关系创建和查询
   - 统计数据计算准确性

2. **RewardService 测试**
   - 奖励发放逻辑
   - 重复奖励检测
   - 余额更新准确性

3. **API 端点测试**
   - 请求参数验证
   - 认证和授权检查
   - 响应格式正确性

### 集成测试
1. **完整邀请流程测试**
   - 邀请码生成 → 新用户注册 → 首次充值 → 奖励发放
   - 异常情况处理（无效邀请码、重复邀请等）

2. **支付回调集成测试**
   - 模拟支付成功回调
   - 验证奖励发放时机
   - 测试并发支付场景

3. **前端组件集成测试**
   - 页面交互流程
   - 数据展示准确性
   - 错误状态处理

### 性能测试
1. **高并发邀请测试**: 模拟大量用户同时生成邀请码
2. **批量奖励发放测试**: 测试大量用户同时完成首次充值的处理能力
3. **数据库查询性能测试**: 验证统计查询和历史记录查询的性能

### 安全测试
1. **邀请码安全性测试**: 验证邀请码的唯一性和不可预测性
2. **权限验证测试**: 确保用户只能访问自己的邀请数据
3. **防刷测试**: 验证防止恶意刷邀请奖励的机制