// 日志保存API路由
import express from 'express';
import fs from 'fs';
import path from 'path';
import { authenticateToken } from '../middleware/auth.js';

const router = express.Router();

// 日志保存目录
const LOG_DIR = 'E:\\Data\\Own\\Entrepreneurship\\local-mianshijun\\docs\\development-plan\\log';

// 确保日志目录存在
const ensureLogDirectory = () => {
  if (!fs.existsSync(LOG_DIR)) {
    fs.mkdirSync(LOG_DIR, { recursive: true });
    console.log(`📁 Created log directory: ${LOG_DIR}`);
  }
};

// 生成时间戳文件名
const generateLogFileName = (type: 'frontend' | 'backend'): string => {
  const now = new Date();
  const timestamp = now.toISOString()
    .replace(/:/g, '-')
    .replace(/\./g, '-')
    .slice(0, 19); // YYYY-MM-DDTHH-MM-SS
  
  return `${type}-${timestamp}.txt`;
};

/**
 * 保存前端日志
 */
router.post('/save', authenticateToken, async (req, res) => {
  try {
    const { logs, type = 'frontend' } = req.body;
    const userId = req.user?.userId;
    
    if (!userId) {
      return res.status(401).json({ error: 'User not authenticated' });
    }
    
    if (!logs || !Array.isArray(logs)) {
      return res.status(400).json({ error: 'Invalid logs data' });
    }
    
    // 确保日志目录存在
    ensureLogDirectory();
    
    // 生成文件名
    const fileName = generateLogFileName(type);
    const filePath = path.join(LOG_DIR, fileName);
    
    // 准备日志内容
    const logHeader = [
      `=== ${type.toUpperCase()} LOG ===`,
      `Time: ${new Date().toISOString()}`,
      `User: ${userId}`,
      `Total Entries: ${logs.length}`,
      `===========================================\n`
    ].join('\n');
    
    // 格式化日志条目
    const formattedLogs = logs.map((log: any, index: number) => {
      const timestamp = new Date(log.timestamp).toISOString();
      return `[${index + 1}] ${timestamp} [${log.level?.toUpperCase() || 'LOG'}] ${log.message}`;
    }).join('\n');
    
    const logContent = logHeader + formattedLogs + '\n';
    
    // 写入文件
    fs.writeFileSync(filePath, logContent, 'utf8');
    
    console.log(`📝 Saved ${type} log: ${fileName} (${logs.length} entries)`);
    
    res.json({
      success: true,
      message: `${type} log saved successfully`,
      fileName,
      entriesCount: logs.length
    });
  } catch (error) {
    console.error('❌ Failed to save log:', error);
    res.status(500).json({ error: 'Failed to save log file' });
  }
});

/**
 * 保存后端日志（内部调用）
 */
export const saveBackendLog = (logs: any[]): string => {
  try {
    // 确保日志目录存在
    ensureLogDirectory();
    
    // 生成文件名
    const fileName = generateLogFileName('backend');
    const filePath = path.join(LOG_DIR, fileName);
    
    // 准备日志内容
    const logHeader = [
      `=== BACKEND LOG ===`,
      `Time: ${new Date().toISOString()}`,
      `Total Entries: ${logs.length}`,
      `===========================================\n`
    ].join('\n');
    
    // 格式化日志条目
    const formattedLogs = logs.map((log: any, index: number) => {
      const timestamp = new Date(log.timestamp || Date.now()).toISOString();
      const level = log.level?.toUpperCase() || 'LOG';
      const message = typeof log.message === 'object' ? JSON.stringify(log.message) : log.message;
      return `[${index + 1}] ${timestamp} [${level}] ${message}`;
    }).join('\n');
    
    const logContent = logHeader + formattedLogs + '\n';
    
    // 写入文件
    fs.writeFileSync(filePath, logContent, 'utf8');
    
    console.log(`📝 Saved backend log: ${fileName} (${logs.length} entries)`);
    return fileName;
  } catch (error) {
    console.error('❌ Failed to save backend log:', error);
    throw error;
  }
};

/**
 * 获取日志文件列表
 */
router.get('/list', authenticateToken, async (req, res) => {
  try {
    ensureLogDirectory();
    
    const files = fs.readdirSync(LOG_DIR)
      .filter(file => file.endsWith('.txt'))
      .map(file => {
        const filePath = path.join(LOG_DIR, file);
        const stats = fs.statSync(filePath);
        return {
          name: file,
          size: stats.size,
          created: stats.birthtime,
          modified: stats.mtime
        };
      })
      .sort((a, b) => b.created.getTime() - a.created.getTime()); // 按创建时间倒序
    
    res.json({
      success: true,
      files,
      totalFiles: files.length
    });
  } catch (error) {
    console.error('❌ Failed to list log files:', error);
    res.status(500).json({ error: 'Failed to list log files' });
  }
});

/**
 * 下载日志文件
 */
router.get('/download/:fileName', authenticateToken, async (req, res) => {
  try {
    const { fileName } = req.params;
    
    // 安全检查：只允许下载.txt文件
    if (!fileName.endsWith('.txt')) {
      return res.status(400).json({ error: 'Invalid file type' });
    }
    
    const filePath = path.join(LOG_DIR, fileName);
    
    // 检查文件是否存在
    if (!fs.existsSync(filePath)) {
      return res.status(404).json({ error: 'Log file not found' });
    }
    
    // 设置响应头
    res.setHeader('Content-Type', 'text/plain');
    res.setHeader('Content-Disposition', `attachment; filename="${fileName}"`);
    
    // 发送文件
    res.sendFile(filePath);
  } catch (error) {
    console.error('❌ Failed to download log file:', error);
    res.status(500).json({ error: 'Failed to download log file' });
  }
});

/**
 * 清理旧日志文件（保留最近30天）
 */
router.delete('/cleanup', authenticateToken, async (req, res) => {
  try {
    ensureLogDirectory();
    
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    
    const files = fs.readdirSync(LOG_DIR);
    let deletedCount = 0;
    
    files.forEach(file => {
      if (file.endsWith('.txt')) {
        const filePath = path.join(LOG_DIR, file);
        const stats = fs.statSync(filePath);
        
        if (stats.birthtime < thirtyDaysAgo) {
          fs.unlinkSync(filePath);
          deletedCount++;
          console.log(`🗑️ Deleted old log file: ${file}`);
        }
      }
    });
    
    res.json({
      success: true,
      message: `Cleaned up ${deletedCount} old log files`,
      deletedCount
    });
  } catch (error) {
    console.error('❌ Failed to cleanup log files:', error);
    res.status(500).json({ error: 'Failed to cleanup log files' });
  }
});

export default router;
