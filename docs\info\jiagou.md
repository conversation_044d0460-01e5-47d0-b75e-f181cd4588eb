[MODE: INNOVATE]

我可以更详细地展开每个模块的功能和数据流向，让架构图更加完善。

```mermaid
graph TB
    %% 用户界面层
    subgraph "用户界面层"
        UI_Home["首页界面 (index.html)"]
        UI_Login["账户登录/注册 (account.html)"]
        UI_Account["账户管理 (account.html)"]
        UI_Recharge["充值中心 (recharge.html)"]
        UI_Simulation["模拟面试 (simulation.html)"]
        UI_Formal["正式面试 (formal.html)"]
        UI_Interview["面试会话 (interview-session.html)"]
        UI_Record["面试记录与分析"]
        UI_Share["分享奖励 (share-rewards.html)"]
        UI_EnterCode["邀请码兑换 (enter-code.html)"]
    end

    %% 业务逻辑层
    subgraph "业务逻辑层"
        BL_Auth["用户认证服务"]
        BL_Auth_Register["注册服务"]
        BL_Auth_Login["登录服务"]
        BL_Auth_Profile["用户信息管理"]
        
        BL_Interview["面试管理服务"]
        BL_Interview_Create["面试会话创建"]
        BL_Interview_Message["面试消息处理"]
        BL_Interview_Evaluate["面试结果评估"]
        BL_Interview_Record["面试记录管理"]
        
        BL_Payment["支付服务"]
        BL_Payment_Order["订单创建"]
        BL_Payment_WxPay["微信支付"]
        BL_Payment_AliPay["支付宝支付"]
        BL_Payment_Verify["支付验证"]
        
        BL_Recognition["语音识别服务"]
        BL_Recognition_Stream["实时音频流处理"]
        BL_Recognition_Text["文本转换"]
        
        BL_AI["AI对话服务"]
        BL_AI_Prompt["面试官角色设定"]
        BL_AI_Chat["AI对话生成"]
        BL_AI_Analysis["回答分析与评分"]
        
        BL_Share["分享与推荐服务"]
        BL_Share_Code["邀请码管理"]
        BL_Share_Reward["奖励发放"]
        BL_Share_Stats["推广数据统计"]
        
        BL_ScreenShare["屏幕共享服务"]
    end

    %% 数据访问层
    subgraph "数据访问层"
        DA_User["用户数据访问"]
        DA_Interview["面试数据访问"]
        DA_Payment["支付数据访问"]
        DA_Promotion["推广数据访问"]
    end

    %% 数据存储层
    subgraph "数据存储层"
        DB_User["用户表(users)"]
        DB_UserAuth["用户认证表"]
        DB_UserWallet["用户钱包表"]
        
        DB_Interview["面试会话表(interview_sessions)"]
        DB_InterviewMsg["面试消息表(interview_messages)"]
        DB_InterviewResult["面试结果表"]
        
        DB_Payment["支付订单表"]
        DB_PaymentLog["支付日志表"]
        DB_Package["套餐配置表"]
        
        DB_Invitation["邀请关系表(user_invitations)"]
        DB_InviteReward["邀请奖励表(invite_rewards)"]
    end

    %% 外部服务
    subgraph "外部服务"
        EXT_DeepSeek["DeepSeek大模型API"]
        EXT_OpenAI["OpenAI大模型API"]
        EXT_BaiduASR["百度语音识别API"]
        EXT_WechatPay["微信支付API"]
        EXT_AliPay["支付宝API"]
    end

    %% 用户界面层连接
    UI_Home --> UI_Login
    UI_Home --> UI_Simulation
    UI_Home --> UI_Formal
    UI_Home --> UI_Account
    UI_Home --> UI_Recharge
    UI_Home --> UI_Share
    UI_Home --> UI_Record
    
    UI_Formal --> UI_Interview
    UI_Simulation --> UI_Interview
    UI_Share --> UI_EnterCode

    %% 用户界面到业务逻辑层连接
    UI_Login --> BL_Auth_Login
    UI_Login --> BL_Auth_Register
    
    UI_Account --> BL_Auth_Profile
    UI_Account --> BL_Share_Code
    
    UI_Recharge --> BL_Payment_Order
    UI_Recharge --> BL_Payment_WxPay
    UI_Recharge --> BL_Payment_AliPay
    
    UI_Simulation --> BL_Interview_Create
    UI_Simulation --> BL_AI_Chat
    
    UI_Formal --> BL_Interview_Create
    UI_Formal --> BL_Auth_Profile
    
    UI_Interview --> BL_Interview_Message
    UI_Interview --> BL_Recognition_Stream
    UI_Interview --> BL_AI_Chat
    UI_Interview --> BL_ScreenShare
    UI_Interview --> BL_Interview_Evaluate
    
    UI_Record --> BL_Interview_Record
    
    UI_Share --> BL_Share_Code
    UI_Share --> BL_Share_Stats
    
    UI_EnterCode --> BL_Share_Code

    %% 业务逻辑层内部连接
    BL_Auth_Login --> BL_Auth
    BL_Auth_Register --> BL_Auth
    BL_Auth_Profile --> BL_Auth
    
    BL_Interview_Create --> BL_Interview
    BL_Interview_Message --> BL_Interview
    BL_Interview_Evaluate --> BL_Interview
    BL_Interview_Record --> BL_Interview
    
    BL_Payment_Order --> BL_Payment
    BL_Payment_WxPay --> BL_Payment
    BL_Payment_AliPay --> BL_Payment
    BL_Payment_Verify --> BL_Payment
    
    BL_Recognition_Stream --> BL_Recognition
    BL_Recognition_Text --> BL_Recognition
    
    BL_AI_Prompt --> BL_AI
    BL_AI_Chat --> BL_AI
    BL_AI_Analysis --> BL_AI
    
    BL_Share_Code --> BL_Share
    BL_Share_Reward --> BL_Share
    BL_Share_Stats --> BL_Share

    %% 业务逻辑层到数据访问层连接
    BL_Auth --> DA_User
    BL_Interview --> DA_Interview
    BL_Payment --> DA_Payment
    BL_Share --> DA_Promotion
    
    %% 面试流程中的特殊连接
    BL_Interview_Message --> BL_AI_Chat
    BL_Recognition_Text --> BL_Interview_Message
    BL_AI_Chat --> BL_Interview_Message
    
    %% 数据访问层到数据存储层连接
    DA_User --> DB_User
    DA_User --> DB_UserAuth
    DA_User --> DB_UserWallet
    
    DA_Interview --> DB_Interview
    DA_Interview --> DB_InterviewMsg
    DA_Interview --> DB_InterviewResult
    
    DA_Payment --> DB_Payment
    DA_Payment --> DB_PaymentLog
    DA_Payment --> DB_Package
    
    DA_Promotion --> DB_Invitation
    DA_Promotion --> DB_InviteReward
    
    %% 业务逻辑层到外部服务连接
    BL_AI_Chat --> EXT_DeepSeek
    BL_AI_Chat --> EXT_OpenAI
    BL_Recognition --> EXT_BaiduASR
    BL_Payment_WxPay --> EXT_WechatPay
    BL_Payment_AliPay --> EXT_AliPay
    
    %% 数据表关系
    DB_User -.-> DB_UserWallet
    DB_User -.-> DB_Interview
    DB_User -.-> DB_Invitation
    DB_Interview -.-> DB_InterviewMsg
    DB_Interview -.-> DB_InterviewResult
    DB_Payment -.-> DB_PaymentLog
    DB_Invitation -.-> DB_InviteReward
```

## 模块功能详解

### 1. 用户界面层

#### 首页界面 (index.html)
- 展示用户仪表盘、面试卡片
- 提供各功能入口
- 展示用户的面试次数和账户状态

#### 账户登录/注册 (account.html)
- 用户注册表单（含邀请码字段）
- 用户登录表单
- 账号状态提示

#### 账户管理 (account.html)
- 个人信息查看与修改
- 密码修改
- 邀请码生成与查看

#### 充值中心 (recharge.html)
- 展示不同的套餐选项（基础套餐、进阶套餐、专业套餐）
- 支付方式选择（微信支付、支付宝）
- 支付状态反馈

#### 模拟面试 (simulation.html)
- 免费面试练习入口
- 简化版AI面试官对话
- 基础的问答交互界面

#### 正式面试 (formal.html)
- 岗位和行业选择
- 面试语言选择
- 消耗面试次数的正式面试启动

#### 面试会话 (interview-session.html)
- 实时面试对话界面
- 音频采集与显示
- 实时语音识别结果展示
- 面试官AI回复展示
- 面试计时功能
- 屏幕共享控制
- 结束面试与评分入口

#### 面试记录与分析
- 历史面试会话列表
- 面试记录查看
- 面试评分和反馈查看

#### 分享奖励 (share-rewards.html)
- 展示分享规则和奖励机制
- 个人邀请链接生成与复制
- 邀请统计数据展示
- 奖励提现功能

#### 邀请码兑换 (enter-code.html)
- 输入邀请码的界面
- 兑换结果反馈

### 2. 业务逻辑层

#### 用户认证服务
- **注册服务**：新用户注册、邀请关系记录
- **登录服务**：用户认证、会话管理
- **用户信息管理**：个人资料更新、密码修改

#### 面试管理服务
- **面试会话创建**：根据用户选择生成面试会话
- **面试消息处理**：记录并处理面试中的对话消息
- **面试结果评估**：生成面试评分和反馈
- **面试记录管理**：存储和检索面试历史

#### 支付服务
- **订单创建**：生成支付订单
- **微信支付**：接入微信支付API
- **支付宝支付**：接入支付宝API
- **支付验证**：验证支付结果并更新用户账户

#### 语音识别服务
- **实时音频流处理**：处理用户音频输入
- **文本转换**：调用百度语音识别API转为文本

#### AI对话服务
- **面试官角色设定**：根据面试类型设置AI角色
- **AI对话生成**：使用大模型生成面试官提问和回复
- **回答分析与评分**：分析用户回答并生成评分

#### 分享与推荐服务
- **邀请码管理**：生成和验证邀请码
- **奖励发放**：处理邀请奖励的计算和发放
- **推广数据统计**：统计邀请数据和奖励数据

#### 屏幕共享服务
- 处理用户屏幕和系统音频的采集
- 在面试过程中提供屏幕分享功能

### 3. 数据层

#### 用户数据
- **用户表(users)**：用户基本信息、面试次数额度、邀请码
- **用户认证表**：登录凭证、密码哈希
- **用户钱包表**：账户余额、交易记录

#### 面试数据
- **面试会话表(interview_sessions)**：面试基本信息、状态、时间
- **面试消息表(interview_messages)**：所有问答记录
- **面试结果表**：面试评分、反馈内容

#### 支付数据
- **支付订单表**：订单ID、金额、状态
- **支付日志表**：支付过程记录
- **套餐配置表**：套餐价格和面试次数配置

#### 推广数据
- **邀请关系表(user_invitations)**：邀请人和被邀请人关系
- **邀请奖励表(invite_rewards)**：奖励记录和状态

### 4. 外部服务

#### AI大模型服务
- **DeepSeek API**：提供AI面试官对话能力
- **OpenAI API**：备用AI对话服务提供商

#### 语音识别服务
- **百度语音识别API**：提供实时语音转文本能力

#### 支付服务
- **微信支付API**：处理微信支付
- **支付宝API**：处理支付宝支付

### 核心数据流与业务流程

1. **用户注册流程**
   - 用户填写注册信息（可选填邀请码）
   - 系统创建用户账号
   - 如有邀请码，记录邀请关系
   - 赠送初始免费面试次数

2. **充值流程**
   - 用户选择套餐和支付方式
   - 系统创建订单
   - 用户完成支付
   - 系统验证支付结果
   - 更新用户面试次数额度
   - 如用户是通过邀请注册，计算邀请人奖励

3. **面试流程**
   - 用户选择面试类型（模拟/正式）
   - 设置面试参数（岗位、语言等）
   - 系统创建面试会话
   - 实时语音采集并转文本
   - AI生成面试官对话
   - 记录全部问答内容
   - 面试结束后生成评分和反馈

4. **邀请奖励流程**
   - 用户获取专属邀请码
   - 被邀请人注册和充值
   - 系统计算邀请奖励
   - 奖励添加到邀请人账户
   - 邀请人可查看奖励记录和提现

