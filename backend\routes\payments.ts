import express, { Request, Response } from 'express';
import { authenticateToken, AuthenticatedRequest } from '../middleware/auth';
import prisma from '../lib/prisma';
import AlipayService from '../services/alipayService';

const router = express.Router();

/**
 * 支付宝异步通知回调
 * 处理支付成功/失败的通知
 */
router.post('/alipay-notify', async (req: Request, res: Response) => {
  try {
    console.log('📨 收到支付宝回调通知:', req.body);

    // 验证回调签名
    const isValidSign = AlipayService.verifyNotify(req.body);
    if (!isValidSign) {
      console.error('❌ 支付回调签名验证失败');
      return res.status(400).send('FAIL');
    }

    const {
      out_trade_no,    // 商户订单号（我们的订单ID）
      trade_no,        // 易支付订单号
      trade_status,    // 支付状态
      money,           // 支付金额
      name,            // 商品名称
      param            // 附加参数
    } = req.body;

    // 查找订单
    const order = await prisma.order.findUnique({
      where: { id: out_trade_no },
      include: { user: true }
    });

    if (!order) {
      console.error('❌ 订单不存在:', out_trade_no);
      return res.status(404).send('ORDER_NOT_FOUND');
    }

    // 验证金额是否匹配
    const orderAmount = parseFloat(order.amount.toString());
    const paidAmount = parseFloat(money);
    if (Math.abs(orderAmount - paidAmount) > 0.01) {
      console.error('❌ 支付金额不匹配:', { orderAmount, paidAmount });
      return res.status(400).send('AMOUNT_MISMATCH');
    }

    // 检查订单状态，避免重复处理
    if (order.status === 'COMPLETED') {
      console.log('✅ 订单已处理过，直接返回成功');
      return res.send('success');
    }

    // 处理支付结果
    if (trade_status === 'TRADE_SUCCESS') {
      // 支付成功，使用事务更新订单状态和用户余额
      await prisma.$transaction(async (tx) => {
        // 更新订单状态
        await tx.order.update({
          where: { id: order.id },
          data: {
            status: 'COMPLETED',
            transactionId: trade_no,
            updatedAt: new Date()
          }
        });

        // 解析用户ID（从param参数中获取）
        let userId = order.userId;
        if (param && param.includes('userId:')) {
          const paramUserId = param.split('userId:')[1];
          if (paramUserId) {
            userId = paramUserId;
          }
        }

        // 获取或创建用户余额记录
        let userBalance = await tx.userBalance.findUnique({
          where: { userId }
        });

        if (!userBalance) {
          userBalance = await tx.userBalance.create({
            data: {
              userId,
              mockInterviewCredits: 0,
              formalInterviewCredits: 0,
              mianshijunBalance: 0
            }
          });
        }

        // 根据商品类型增加相应的余额
        // 这里需要根据itemId或itemDescription来判断购买的是什么商品
        const itemDesc = order.itemDescription?.toLowerCase() || '';
        let updateData: any = {};

        if (itemDesc.includes('面巾') || itemDesc.includes('充值')) {
          // 面巾充值，根据金额计算面巾数量（假设1元=10面巾）
          const mianshijinAmount = Math.floor(orderAmount * 10);
          updateData.mianshijunBalance = {
            increment: mianshijinAmount
          };
          console.log(`💰 增加面巾余额: ${mianshijinAmount}`);
        } else if (itemDesc.includes('模拟面试') || itemDesc.includes('基础')) {
          // 模拟面试次数
          updateData.mockInterviewCredits = {
            increment: 1
          };
          console.log('🎯 增加模拟面试次数: 1');
        } else if (itemDesc.includes('正式面试') || itemDesc.includes('高级')) {
          // 正式面试次数
          updateData.formalInterviewCredits = {
            increment: 1
          };
          console.log('🎯 增加正式面试次数: 1');
        } else {
          // 默认按金额增加面巾
          const mianshijinAmount = Math.floor(orderAmount * 10);
          updateData.mianshijunBalance = {
            increment: mianshijinAmount
          };
          console.log(`💰 默认增加面巾余额: ${mianshijinAmount}`);
        }

        // 更新用户余额
        await tx.userBalance.update({
          where: { userId },
          data: updateData
        });

        console.log('✅ 支付成功处理完成:', {
          orderId: order.id,
          userId,
          amount: orderAmount,
          updateData
        });
      });

      console.log('🎉 支付成功，订单和余额已更新');
    } else {
      // 支付失败
      await prisma.order.update({
        where: { id: order.id },
        data: {
          status: 'FAILED',
          transactionId: trade_no,
          updatedAt: new Date()
        }
      });

      console.log('❌ 支付失败，订单状态已更新');
    }

    // 返回成功响应
    return res.send('success');

  } catch (error: any) {
    console.error('❌ 处理支付回调失败:', error);
    return res.status(500).send('ERROR');
  }
});

/**
 * 查询订单支付状态
 */
router.get('/status/:orderId', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { orderId } = req.params;
    const userId = req.user!.userId;

    // 查找订单
    const order = await prisma.order.findFirst({
      where: {
        id: orderId,
        userId: userId
      }
    });

    if (!order) {
      return res.status(404).json({
        success: false,
        message: '订单不存在'
      });
    }

    // 如果订单状态是PENDING，尝试查询支付宝订单状态
    if (order.status === 'PENDING') {
      try {
        const queryResult = await AlipayService.queryOrder(orderId);
        
        if (queryResult.code === 1 && queryResult.status === 1) {
          // 支付成功，更新订单状态
          await prisma.order.update({
            where: { id: orderId },
            data: { status: 'COMPLETED' }
          });
          
          return res.json({
            success: true,
            status: 'COMPLETED',
            message: '支付成功'
          });
        }
      } catch (error) {
        console.error('查询支付状态失败:', error);
        // 查询失败不影响返回当前状态
      }
    }

    // 返回当前订单状态
    return res.json({
      success: true,
      status: order.status,
      message: order.status === 'COMPLETED' ? '支付成功' : 
               order.status === 'FAILED' ? '支付失败' : '等待支付'
    });

  } catch (error: any) {
    console.error('查询支付状态失败:', error);
    return res.status(500).json({
      success: false,
      message: '查询支付状态失败'
    });
  }
});

export default router;
