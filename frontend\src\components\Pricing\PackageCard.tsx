// 文件路径: frontend/src/components/Pricing/PackageCard.tsx
// (File Path: frontend/src/components/Pricing/PackageCard.tsx)
import React from 'react';
import { motion } from 'framer-motion';
import { Check, CheckCircle } from 'lucide-react';

interface PackageProps {
  pkg: { // Changed from 'package' to 'pkg' to avoid conflict with reserved keyword
    id: string;
    title: string;
    description: string;
    price: number;
    features: string[];
    notes: string[];
    color: string;
    borderColor: string;
    popular: boolean;
    popularText?: string;
    buyButtonText?: string;
  };
  isSelected: boolean;
  onSelect: (id: string) => void;
  onPurchase: (pkg: any) => void;
}

const PackageCard: React.FC<PackageProps> = ({ pkg, isSelected, onSelect, onPurchase }) => {
  const handleCardClick = () => {
    onSelect(pkg.id);
  };

  const handlePurchaseClick = (e: React.MouseEvent) => {
    e.stopPropagation(); // 防止触发卡片选中事件
    if (isSelected) {
      onPurchase(pkg);
    }
  };

  return (
    <motion.div
      whileHover={{ y: -5 }}
      onClick={handleCardClick}
      className={`rounded-xl border ${isSelected ? 'border-blue-300' : pkg.borderColor} overflow-hidden ${pkg.color} relative h-full backdrop-blur-sm shadow-lg cursor-pointer transition-all ${isSelected ? 'ring-2 ring-blue-200' : ''}`}
    >
      {isSelected && pkg.popular && pkg.popularText && (
        <div className="absolute top-0 right-0 left-0">
          <div className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white p-2 relative">
            <div className="text-sm font-medium text-center">
              {pkg.popularText}
            </div>
            <CheckCircle className="absolute -bottom-3 right-3 h-6 w-6 text-white bg-blue-600 rounded-full p-1" />
          </div>
        </div>
      )}

      <div className={`p-6 ${isSelected && pkg.popular ? 'pt-12' : 'pt-6'}`}> {/* Increased padding */}
        <h3 className="text-xl font-bold text-gray-800 mb-2">{pkg.title}</h3> {/* Increased font size */}
        <p className="text-sm text-gray-600 mb-2 h-10">{pkg.description}</p> {/* Reduced margin from mb-5 to mb-2 */}

        <div className="space-y-2.5 mb-4"> {/* Reduced bottom margin from mb-5 to mb-4 */}
          {pkg.features.map((feature, index) => (
            <div key={index} className="flex items-start gap-2">
              <Check className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
              <span className="text-sm text-gray-700">{feature}</span>
            </div>
          ))}
        </div>

        <div className="space-y-1.5 mb-3"> {/* Reduced bottom margin from mb-5 to mb-3 */}
          {pkg.notes.map((note, index) => (
            <div key={index} className="text-xs text-gray-500 flex items-center gap-1.5"> {/* Increased gap */}
              <span className="h-1.5 w-1.5 rounded-full bg-gray-300" /> {/* Increased size */}
              <span>{note}</span>
            </div>
          ))}
        </div>

        <div className="text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-indigo-600 mb-4"> {/* Reduced margin from mb-6 to mb-4 */}
          ¥{pkg.price}
          {/* <span className="text-base text-gray-700">元</span> Removed 元 as ¥ is present */}
        </div>

        <button
          onClick={handlePurchaseClick}
          disabled={!isSelected}
          className={`w-full py-3 rounded-lg text-center text-sm font-medium transition-all ${
            isSelected
              ? 'bg-gradient-to-r from-blue-600 to-indigo-600 text-white hover:shadow-xl focus:ring-4 focus:ring-blue-300'
              : 'bg-gray-200 text-gray-700 hover:bg-gray-300 focus:ring-4 focus:ring-gray-200'
          }`}
        >
          {pkg.buyButtonText || '立即购买'}
        </button>
      </div>
    </motion.div>
  );
};

export default PackageCard;
