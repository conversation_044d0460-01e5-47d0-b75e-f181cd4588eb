@tailwind base;
@tailwind components;
@tailwind utilities;

/* 全局主题切换过渡动画 */
@layer base {
  * {
    @apply transition-colors duration-300 ease-in-out;
  }

  /* 为特定元素禁用过渡，避免影响性能 */
  *:where(
    .no-transition,
    [data-no-transition],
    .animate-spin,
    .animate-pulse,
    .animate-bounce
  ) {
    @apply transition-none;
  }
}

/* 自定义滚动条样式 */
@layer components {
  .custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: rgba(203, 213, 225, 0.5) transparent;
  }

  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background-color: rgba(203, 213, 225, 0.5);
    border-radius: 10px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background-color: rgba(148, 163, 184, 0.7);
  }

  /* 主题切换特效 */
  .theme-transition {
    @apply transition-all duration-500 ease-in-out;
  }

  /* 深色模式滚动条样式 */
  .dark .custom-scrollbar {
    scrollbar-color: rgba(75, 85, 99, 0.5) transparent;
  }

  .dark .custom-scrollbar::-webkit-scrollbar-thumb {
    background-color: rgba(75, 85, 99, 0.5);
  }

  .dark .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background-color: rgba(107, 114, 128, 0.7);
  }
}
