import crypto from 'crypto';
import axios from 'axios';

// 支付宝配置信息
const ALIPAY_CONFIG = {
  // 易支付接口地址
  GATEWAY_URL: 'https://z-pay.cn',
  // 商户ID
  PID: '2025073117331546',
  // 商户密钥
  PKEY: '4BkfWU02E3xrQgMlS27b4k49ltaZSU1z',
  // 支付方式
  PAYMENT_TYPE: 'alipay',
  // 签名类型
  SIGN_TYPE: 'MD5'
};

// 支付参数接口
interface PaymentParams {
  name: string;           // 商品名称
  money: string;          // 订单金额
  out_trade_no: string;   // 订单编号
  notify_url: string;     // 异步通知地址
  return_url: string;     // 跳转地址
  param?: string;         // 附加内容
}

// 支付响应接口
interface PaymentResponse {
  code: number | string;
  msg?: string;
  trade_no?: string;
  payurl?: string;
  qrcode?: string;
  img?: string;
}

// 订单查询响应接口
interface OrderQueryResponse {
  code: number;
  msg: string;
  trade_no?: string;
  out_trade_no?: string;
  type?: string;
  pid?: string;
  addtime?: string;
  endtime?: string;
  name?: string;
  money?: string;
  status?: number;
  param?: string;
  buyer?: string;
}

/**
 * 支付宝支付服务类
 */
export class AlipayService {
  /**
   * 参数排序并生成待签名字符串
   * @param params 参数对象
   * @returns 待签名字符串
   */
  private static getVerifyParams(params: Record<string, any>): string {
    const sPara: [string, any][] = [];
    
    for (const key in params) {
      // 跳过空值、sign和sign_type参数
      if (!params[key] || key === 'sign' || key === 'sign_type') {
        continue;
      }
      sPara.push([key, params[key]]);
    }
    
    // 按参数名ASCII码排序
    sPara.sort();
    
    // 拼接成URL键值对格式
    let prestr = '';
    for (let i = 0; i < sPara.length; i++) {
      const obj = sPara[i];
      if (i === sPara.length - 1) {
        prestr = prestr + obj[0] + '=' + obj[1];
      } else {
        prestr = prestr + obj[0] + '=' + obj[1] + '&';
      }
    }
    
    return prestr;
  }

  /**
   * 生成MD5签名
   * @param params 参数对象
   * @returns 签名字符串
   */
  private static generateSign(params: Record<string, any>): string {
    const prestr = this.getVerifyParams(params);
    const signStr = prestr + ALIPAY_CONFIG.PKEY;
    return crypto.createHash('md5').update(signStr).digest('hex');
  }

  /**
   * 验证签名
   * @param params 参数对象
   * @param sign 签名
   * @returns 验证结果
   */
  private static verifySign(params: Record<string, any>, sign: string): boolean {
    const expectedSign = this.generateSign(params);
    return expectedSign === sign;
  }

  /**
   * 创建支付订单（API接口方式）
   * @param paymentParams 支付参数
   * @returns 支付响应
   */
  public static async createPayment(paymentParams: PaymentParams): Promise<PaymentResponse> {
    try {
      // 构建请求参数
      const params: Record<string, any> = {
        pid: ALIPAY_CONFIG.PID,
        type: ALIPAY_CONFIG.PAYMENT_TYPE,
        out_trade_no: paymentParams.out_trade_no,
        notify_url: paymentParams.notify_url,
        name: paymentParams.name,
        money: paymentParams.money,
        clientip: '127.0.0.1', // 用户IP，这里暂时使用默认值
        device: 'pc',
        param: paymentParams.param || '',
        sign_type: ALIPAY_CONFIG.SIGN_TYPE
      };

      // 生成签名
      params.sign = this.generateSign(params);

      console.log('🔐 支付宝支付参数:', params);

      // 调用易支付API
      const response = await axios.post(`${ALIPAY_CONFIG.GATEWAY_URL}/mapi.php`, params, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        transformRequest: [(data) => {
          // 转换为form-data格式
          const formData = new URLSearchParams();
          for (const key in data) {
            formData.append(key, data[key]);
          }
          return formData.toString();
        }]
      });

      console.log('💰 支付宝API响应:', response.data);

      return response.data;
    } catch (error: any) {
      console.error('❌ 创建支付订单失败:', error);
      throw new Error(error.response?.data?.msg || '创建支付订单失败');
    }
  }

  /**
   * 生成支付跳转URL（页面跳转方式）
   * @param paymentParams 支付参数
   * @returns 支付跳转URL
   */
  public static generatePaymentUrl(paymentParams: PaymentParams): string {
    // 构建请求参数
    const params: Record<string, any> = {
      pid: ALIPAY_CONFIG.PID,
      type: ALIPAY_CONFIG.PAYMENT_TYPE,
      out_trade_no: paymentParams.out_trade_no,
      notify_url: paymentParams.notify_url,
      return_url: paymentParams.return_url,
      name: paymentParams.name,
      money: paymentParams.money,
      param: paymentParams.param || '',
      sign_type: ALIPAY_CONFIG.SIGN_TYPE
    };

    // 生成签名
    params.sign = this.generateSign(params);

    // 构建URL参数
    const urlParams = new URLSearchParams();
    for (const key in params) {
      urlParams.append(key, params[key]);
    }

    return `${ALIPAY_CONFIG.GATEWAY_URL}/submit.php?${urlParams.toString()}`;
  }

  /**
   * 查询订单状态
   * @param outTradeNo 商户订单号
   * @returns 订单查询结果
   */
  public static async queryOrder(outTradeNo: string): Promise<OrderQueryResponse> {
    try {
      const url = `${ALIPAY_CONFIG.GATEWAY_URL}/api.php?act=order&pid=${ALIPAY_CONFIG.PID}&key=${ALIPAY_CONFIG.PKEY}&out_trade_no=${outTradeNo}`;
      
      console.log('🔍 查询订单状态:', { outTradeNo, url });

      const response = await axios.get(url);
      
      console.log('📊 订单查询响应:', response.data);

      return response.data;
    } catch (error: any) {
      console.error('❌ 查询订单状态失败:', error);
      throw new Error(error.response?.data?.msg || '查询订单状态失败');
    }
  }

  /**
   * 验证支付回调
   * @param params 回调参数
   * @returns 验证结果
   */
  public static verifyNotify(params: Record<string, any>): boolean {
    const { sign, sign_type, ...otherParams } = params;
    
    if (sign_type !== ALIPAY_CONFIG.SIGN_TYPE) {
      console.error('❌ 签名类型不匹配:', sign_type);
      return false;
    }

    const isValid = this.verifySign(otherParams, sign);
    console.log('🔐 支付回调签名验证:', { isValid, params });
    
    return isValid;
  }
}

export default AlipayService;
