import { QueryClient } from '@tanstack/react-query';

// React Query 配置
export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      // SWR策略：5分钟内数据被认为是新鲜的
      staleTime: 5 * 60 * 1000, // 5分钟
      
      // 缓存时间：30分钟后从内存中移除
      gcTime: 30 * 60 * 1000, // 30分钟 (原 cacheTime)
      
      // 重试配置
      retry: (failureCount, error: any) => {
        // 对于认证错误，不重试
        if (error?.message?.includes('Unauthorized') || error?.status === 401) {
          return false;
        }
        // 最多重试2次
        return failureCount < 2;
      },
      
      // 重试延迟（指数退避）
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
      
      // 窗口重新获得焦点时重新获取数据
      refetchOnWindowFocus: true,
      
      // 网络重新连接时重新获取数据
      refetchOnReconnect: true,
      
      // 组件挂载时重新获取数据（如果数据过期）
      refetchOnMount: true,
    },
    mutations: {
      // 变更操作的重试配置
      retry: 1,
      retryDelay: 1000,
    },
  },
});

// 查询键工厂 - 统一管理查询键
export const queryKeys = {
  // 意向岗位相关
  positions: {
    all: ['positions'] as const,
    lists: () => [...queryKeys.positions.all, 'list'] as const,
    list: (filters?: Record<string, any>) => 
      [...queryKeys.positions.lists(), filters] as const,
    details: () => [...queryKeys.positions.all, 'detail'] as const,
    detail: (id: string) => [...queryKeys.positions.details(), id] as const,
  },
  
  // 用户相关
  user: {
    all: ['user'] as const,
    profile: () => [...queryKeys.user.all, 'profile'] as const,
    balance: () => [...queryKeys.user.all, 'balance'] as const,
  },
  
  // 简历相关
  resumes: {
    all: ['resumes'] as const,
    lists: () => [...queryKeys.resumes.all, 'list'] as const,
    list: (filters?: Record<string, any>) => 
      [...queryKeys.resumes.lists(), filters] as const,
  },
} as const;

// 缓存失效工具函数
export const invalidateQueries = {
  // 使所有意向岗位查询失效
  positions: () => queryClient.invalidateQueries({ 
    queryKey: queryKeys.positions.all 
  }),
  
  // 使特定岗位详情失效
  positionDetail: (id: string) => queryClient.invalidateQueries({ 
    queryKey: queryKeys.positions.detail(id) 
  }),
  
  // 使用户相关查询失效
  user: () => queryClient.invalidateQueries({ 
    queryKey: queryKeys.user.all 
  }),
  
  // 使简历相关查询失效
  resumes: () => queryClient.invalidateQueries({ 
    queryKey: queryKeys.resumes.all 
  }),
};

// 预取工具函数
export const prefetchQueries = {
  // 预取意向岗位列表
  positions: async () => {
    await queryClient.prefetchQuery({
      queryKey: queryKeys.positions.list(),
      queryFn: async () => {
        const { targetPositionService } = await import('../lib/api/apiService');
        return targetPositionService.getTargetPositions();
      },
      staleTime: 5 * 60 * 1000, // 5分钟
    });
  },
};

// 乐观更新工具函数
export const optimisticUpdates = {
  // 乐观添加岗位
  addPosition: (newPosition: any) => {
    queryClient.setQueryData(
      queryKeys.positions.list(),
      (oldData: any[] = []) => [...oldData, newPosition]
    );
  },
  
  // 乐观更新岗位
  updatePosition: (id: string, updates: any) => {
    queryClient.setQueryData(
      queryKeys.positions.list(),
      (oldData: any[] = []) => 
        oldData.map(position => 
          position.id === id ? { ...position, ...updates } : position
        )
    );
  },
  
  // 乐观删除岗位
  removePosition: (id: string) => {
    queryClient.setQueryData(
      queryKeys.positions.list(),
      (oldData: any[] = []) => oldData.filter(position => position.id !== id)
    );
  },
};

// 错误处理工具
export const handleQueryError = (error: any, context?: string) => {
  console.error(`Query error${context ? ` in ${context}` : ''}:`, error);
  
  // 根据错误类型进行不同处理
  if (error?.message?.includes('Unauthorized') || error?.status === 401) {
    // 认证错误 - 可能需要重新登录
    console.warn('Authentication error detected, user may need to re-login');
    // 这里可以触发登录状态检查或重定向到登录页
  } else if (error?.message?.includes('Network')) {
    // 网络错误
    console.warn('Network error detected, will retry automatically');
  }
  
  return error;
};

// 性能监控工具
export const queryPerformance = {
  // 记录查询性能
  logQueryPerformance: (queryKey: string, startTime: number) => {
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    console.log(`🔍 Query Performance [${queryKey}]: ${duration}ms`);
    
    // 如果查询时间过长，记录警告
    if (duration > 3000) {
      console.warn(`⚠️ Slow query detected [${queryKey}]: ${duration}ms`);
    }
  },
  
  // 获取缓存统计信息
  getCacheStats: () => {
    const cache = queryClient.getQueryCache();
    const queries = cache.getAll();
    
    return {
      totalQueries: queries.length,
      staleQueries: queries.filter(q => q.isStale()).length,
      fetchingQueries: queries.filter(q => q.isFetching()).length,
      errorQueries: queries.filter(q => q.state.status === 'error').length,
    };
  },
};

export default queryClient;
