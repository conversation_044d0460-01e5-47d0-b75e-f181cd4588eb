import express from 'express';
import cors from 'cors';
import jwt from 'jsonwebtoken';

const app = express();
const PORT = 3000;
const JWT_SECRET = 'f2e279e5d7a6b9c4e3f1g8h0i2j3k4l5m6n7o8p9q0r1s2t3u4v5w6x7y8z9a0';

// 模拟用户余额数据存储（内存中）
const userBalances = new Map();

// 中间件
app.use(cors());
app.use(express.json());

// 添加请求日志
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
  next();
});

// 模拟认证中间件
const mockAuth = (req, res, next) => {
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({ message: 'Unauthorized' });
  }
  
  try {
    const token = authHeader.substring(7);
    const decoded = jwt.verify(token, JWT_SECRET);
    req.userId = decoded.userId;
    next();
  } catch (error) {
    return res.status(401).json({ message: 'Invalid token' });
  }
};

// 测试路由
app.get('/', (req, res) => {
  res.json({ 
    message: 'Backend server is running', 
    timestamp: new Date().toISOString() 
  });
});

// 简历API
app.get('/api/resumes', mockAuth, (req, res) => {
  console.log('收到 GET /api/resumes 请求');
  res.json([]);
});

app.post('/api/resumes', mockAuth, (req, res) => {
  console.log('收到 POST /api/resumes 请求:', req.body);
  res.status(201).json({
    id: 'test-resume-id',
    fileName: req.body.fileName || 'test.pdf',
    filePath: req.body.filePath || '/uploads/test.pdf',
    ...req.body
  });
});

// 岗位API
app.get('/api/positions', mockAuth, (req, res) => {
  console.log('收到 GET /api/positions 请求');
  res.json([]);
});

app.post('/api/positions', mockAuth, (req, res) => {
  console.log('收到 POST /api/positions 请求:', req.body);
  res.status(201).json({
    id: 'test-position-id',
    positionName: req.body.positionName,
    companyName: req.body.companyName,
    positionRequirements: req.body.positionRequirements || '无特定要求',
    companyProfile: req.body.companyProfile || '无公司简介',
    status: req.body.status || '待处理',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    userId: req.userId
  });
});

app.put('/api/positions/:id', mockAuth, (req, res) => {
  console.log('收到 PUT /api/positions/:id 请求:', req.params.id, req.body);
  res.json({
    id: req.params.id,
    ...req.body,
    updatedAt: new Date().toISOString()
  });
});

app.delete('/api/positions/:id', mockAuth, (req, res) => {
  console.log('收到 DELETE /api/positions/:id 请求:', req.params.id);
  res.status(204).send();
});

// 用户API
app.get('/api/users/me', mockAuth, (req, res) => {
  console.log('收到 GET /api/users/me 请求');
  res.json({
    id: req.userId,
    email: '<EMAIL>',
    name: 'Test User'
  });
});

// 获取或初始化用户余额
const getUserBalance = (userId) => {
  if (!userBalances.has(userId)) {
    // 初始化用户余额
    userBalances.set(userId, {
      mockInterviewCredits: 4,
      formalInterviewCredits: 3,
      mianshijunBalance: 100,
      updatedAt: new Date().toISOString()
    });
  }
  return userBalances.get(userId);
};

// 更新用户余额
const updateUserBalance = (userId, updates) => {
  const currentBalance = getUserBalance(userId);
  const newBalance = {
    ...currentBalance,
    ...updates,
    updatedAt: new Date().toISOString()
  };
  userBalances.set(userId, newBalance);
  return newBalance;
};

// 用户余额API
app.get('/api/users/me/credits', mockAuth, (req, res) => {
  console.log('收到 GET /api/users/me/credits 请求');
  const balance = getUserBalance(req.userId);
  console.log('返回用户余额:', balance);

  res.json({
    success: true,
    credits: balance
  });
});

app.put('/api/users/me', mockAuth, (req, res) => {
  console.log('收到 PUT /api/users/me 请求:', req.body);
  const { action, nickname } = req.body;

  if (action === 'nickname' && nickname) {
    res.json({
      id: req.userId,
      email: '<EMAIL>',
      name: nickname
    });
  } else {
    res.status(400).json({ message: '无效的请求参数' });
  }
});

// 兑换码API
app.post('/api/redeem', mockAuth, (req, res) => {
  console.log('收到 POST /api/redeem 请求');
  console.log('请求头:', req.headers);
  console.log('请求体:', req.body);
  console.log('用户ID:', req.userId);

  const { code } = req.body;

  if (!code) {
    console.log('兑换码为空，返回400错误');
    return res.status(400).json({ message: '兑换码不能为空' });
  }

  console.log('兑换码:', code);

  // 解析兑换码类型和数值
  let benefitType, benefitValue;

  if (code.includes('POINTS') || code.includes('面巾')) {
    benefitType = 'POINTS';
    benefitValue = parseInt(code.match(/\d+/)?.[0] || '100');
  } else if (code.includes('MOCK') || code.includes('模拟')) {
    benefitType = 'MOCK_INTERVIEW';
    benefitValue = parseInt(code.match(/\d+/)?.[0] || '5');
  } else if (code.includes('FORMAL') || code.includes('正式')) {
    benefitType = 'FORMAL_INTERVIEW';
    benefitValue = parseInt(code.match(/\d+/)?.[0] || '3');
  } else {
    // 默认为面巾
    benefitType = 'POINTS';
    benefitValue = 100;
  }

  console.log('解析兑换码:', { benefitType, benefitValue });

  // 获取当前用户余额
  const currentBalance = getUserBalance(req.userId);
  console.log('当前余额:', currentBalance);

  // 根据兑换类型更新余额
  let updates = {};
  let newBalance;

  if (benefitType === 'POINTS') {
    updates.mianshijunBalance = currentBalance.mianshijunBalance + benefitValue;
    newBalance = updates.mianshijunBalance;
  } else if (benefitType === 'MOCK_INTERVIEW') {
    updates.mockInterviewCredits = currentBalance.mockInterviewCredits + benefitValue;
    newBalance = updates.mockInterviewCredits;
  } else if (benefitType === 'FORMAL_INTERVIEW') {
    updates.formalInterviewCredits = currentBalance.formalInterviewCredits + benefitValue;
    newBalance = updates.formalInterviewCredits;
  }

  // 更新用户余额
  const updatedBalance = updateUserBalance(req.userId, updates);
  console.log('更新后余额:', updatedBalance);

  // 返回兑换成功响应
  res.json({
    success: true,
    message: '兑换成功！',
    benefitType: benefitType,
    benefitValue: benefitValue,
    newBalance: newBalance
  });
});

// 订单API
app.get('/api/orders', mockAuth, (req, res) => {
  console.log('收到 GET /api/orders 请求');

  // 模拟订单数据
  const mockOrders = [
    {
      id: '1',
      orderNumber: 'MSJ20250105001',
      createTime: '2025-01-05 14:32:15',
      product: '基础礼包',
      price: '¥99 CNY',
      status: 'paid'
    },
    {
      id: '2',
      orderNumber: 'MSJ20250104002',
      createTime: '2025-01-04 10:15:30',
      product: '高级礼包',
      price: '¥199 CNY',
      status: 'unpaid'
    },
    {
      id: '3',
      orderNumber: 'MSJ20250103003',
      createTime: '2025-01-03 16:45:22',
      product: '尊享礼包',
      price: '¥399 CNY',
      status: 'paid'
    },
    {
      id: '4',
      orderNumber: 'MSJ20250102004',
      createTime: '2025-01-02 09:20:18',
      product: '面巾100',
      price: '¥19 CNY',
      status: 'cancelled'
    },
    {
      id: '5',
      orderNumber: 'MSJ20250101005',
      createTime: '2025-01-01 20:30:45',
      product: '基础礼包',
      price: '¥99 CNY',
      status: 'paid'
    }
  ];

  res.json({
    success: true,
    orders: mockOrders,
    total: mockOrders.length
  });
});

// 消耗记录API
app.get('/api/usage-records', mockAuth, (req, res) => {
  console.log('收到 GET /api/usage-records 请求');

  // 模拟消耗记录数据
  const mockUsageRecords = [
    {
      id: '1',
      date: '2025-01-05 14:32:15',
      type: 'formal_interview',
      amount: -1,
      reason: 'AI正式面试消耗'
    },
    {
      id: '2',
      date: '2025-01-05 10:15:30',
      type: 'credit_recharge',
      amount: 100,
      reason: '兑换码兑换'
    },
    {
      id: '3',
      date: '2025-01-04 16:45:22',
      type: 'mock_interview',
      amount: -1,
      reason: 'AI模拟面试消耗'
    },
    {
      id: '4',
      date: '2025-01-04 09:20:18',
      type: 'formal_interview',
      amount: 5,
      reason: '购买充值包'
    },
    {
      id: '5',
      date: '2025-01-03 20:30:45',
      type: 'mock_interview',
      amount: 3,
      reason: '兑换码兑换'
    },
    {
      id: '6',
      date: '2025-01-03 15:12:33',
      type: 'credit_recharge',
      amount: 200,
      reason: '购买充值包'
    },
    {
      id: '7',
      date: '2025-01-02 11:45:12',
      type: 'formal_interview',
      amount: -1,
      reason: 'AI正式面试消耗'
    },
    {
      id: '8',
      date: '2025-01-01 18:20:55',
      type: 'mock_interview',
      amount: -1,
      reason: 'AI模拟面试消耗'
    }
  ];

  res.json({
    success: true,
    records: mockUsageRecords,
    total: mockUsageRecords.length
  });
});

// 错误处理中间件
app.use((err, req, res, next) => {
  console.error('服务器错误:', err);
  res.status(500).json({ message: '服务器内部错误' });
});

// 错误处理
process.on('uncaughtException', (error) => {
  console.error('未捕获的异常:', error);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('未处理的Promise拒绝:', reason);
});

// 启动服务器
const server = app.listen(PORT, () => {
  console.log(`✅ Backend server running on http://localhost:${PORT}`);
  console.log(`健康检查地址: http://localhost:${PORT}/`);
  console.log(`API端点已启用: resumes, positions, users, redeem, orders, usage-records`);
});

server.on('error', (error) => {
  console.error('服务器错误:', error);
});

// 保持进程运行
process.stdin.resume();
