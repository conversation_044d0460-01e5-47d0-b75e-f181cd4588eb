# 前端组件实现示例

## 1. 验证码输入组件

### 1.1 VerificationCodeInput.tsx

```typescript
import React, { useState, useRef, useEffect } from 'react';

interface VerificationCodeInputProps {
  length?: number;
  value: string;
  onChange: (value: string) => void;
  disabled?: boolean;
  error?: string;
  autoFocus?: boolean;
}

const VerificationCodeInput: React.FC<VerificationCodeInputProps> = ({
  length = 6,
  value,
  onChange,
  disabled = false,
  error,
  autoFocus = false
}) => {
  const [focusedIndex, setFocusedIndex] = useState<number>(-1);
  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);

  useEffect(() => {
    if (autoFocus && inputRefs.current[0]) {
      inputRefs.current[0].focus();
    }
  }, [autoFocus]);

  const handleInputChange = (index: number, inputValue: string) => {
    // 只允许数字输入
    const numericValue = inputValue.replace(/[^0-9]/g, '');
    
    if (numericValue.length <= 1) {
      const newValue = value.split('');
      newValue[index] = numericValue;
      onChange(newValue.join(''));

      // 自动跳转到下一个输入框
      if (numericValue && index < length - 1) {
        inputRefs.current[index + 1]?.focus();
      }
    }
  };

  const handleKeyDown = (index: number, e: React.KeyboardEvent) => {
    if (e.key === 'Backspace' && !value[index] && index > 0) {
      // 退格键跳转到上一个输入框
      inputRefs.current[index - 1]?.focus();
    } else if (e.key === 'ArrowLeft' && index > 0) {
      inputRefs.current[index - 1]?.focus();
    } else if (e.key === 'ArrowRight' && index < length - 1) {
      inputRefs.current[index + 1]?.focus();
    }
  };

  const handlePaste = (e: React.ClipboardEvent) => {
    e.preventDefault();
    const pastedData = e.clipboardData.getData('text').replace(/[^0-9]/g, '');
    const newValue = pastedData.slice(0, length);
    onChange(newValue);

    // 聚焦到最后一个有值的输入框
    const nextIndex = Math.min(newValue.length, length - 1);
    setTimeout(() => {
      inputRefs.current[nextIndex]?.focus();
    }, 0);
  };

  return (
    <div className="space-y-2">
      <div className="flex gap-3 justify-center">
        {Array.from({ length }, (_, index) => (
          <input
            key={index}
            ref={(el) => (inputRefs.current[index] = el)}
            type="text"
            inputMode="numeric"
            maxLength={1}
            value={value[index] || ''}
            onChange={(e) => handleInputChange(index, e.target.value)}
            onKeyDown={(e) => handleKeyDown(index, e)}
            onFocus={() => setFocusedIndex(index)}
            onBlur={() => setFocusedIndex(-1)}
            onPaste={handlePaste}
            disabled={disabled}
            className={`
              w-12 h-12 text-center text-xl font-bold border-2 rounded-lg
              transition-all duration-200 outline-none
              ${error 
                ? 'border-red-500 bg-red-50' 
                : focusedIndex === index
                  ? 'border-blue-500 bg-blue-50 ring-2 ring-blue-200'
                  : value[index]
                    ? 'border-green-500 bg-green-50'
                    : 'border-gray-300 bg-white hover:border-gray-400'
              }
              ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-text'}
            `}
          />
        ))}
      </div>
      {error && (
        <p className="text-sm text-red-600 text-center">{error}</p>
      )}
    </div>
  );
};

export default VerificationCodeInput;
```

### 1.2 SendCodeButton.tsx

```typescript
import React, { useState, useEffect } from 'react';
import { Send, Clock, CheckCircle, AlertCircle } from 'lucide-react';
import { useToastContext } from '../../contexts/ToastContext';

interface SendCodeButtonProps {
  identifier: string;
  type: 'EMAIL' | 'SMS';
  disabled?: boolean;
  onSendSuccess?: () => void;
  onSendError?: (error: string) => void;
}

const SendCodeButton: React.FC<SendCodeButtonProps> = ({
  identifier,
  type,
  disabled = false,
  onSendSuccess,
  onSendError
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [countdown, setCountdown] = useState(0);
  const [lastSendTime, setLastSendTime] = useState<number | null>(null);
  const { showSuccess, showError, showWarning } = useToastContext();

  // 倒计时效果
  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (countdown > 0) {
      timer = setTimeout(() => setCountdown(countdown - 1), 1000);
    }
    return () => clearTimeout(timer);
  }, [countdown]);

  // 检查是否可以发送
  const canSend = !disabled && !isLoading && countdown === 0 && identifier.trim() !== '';

  const handleSendCode = async () => {
    if (!canSend) return;

    setIsLoading(true);

    try {
      const response = await fetch('/api/auth/send-verification-code', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          identifier,
          type,
          purpose: 'LOGIN'
        }),
      });

      const data = await response.json();

      if (data.success) {
        setCountdown(60); // 60秒倒计时
        setLastSendTime(Date.now());
        showSuccess(
          type === 'EMAIL' 
            ? '验证码已发送到您的邮箱，请查收' 
            : '验证码已发送到您的手机，请查收'
        );
        onSendSuccess?.();
      } else {
        const errorMessage = data.message || '验证码发送失败';
        showError(errorMessage);
        onSendError?.(errorMessage);
      }
    } catch (error) {
      const errorMessage = '网络错误，请稍后重试';
      showError(errorMessage);
      onSendError?.(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const getButtonText = () => {
    if (isLoading) return '发送中...';
    if (countdown > 0) return `${countdown}秒后重试`;
    return '获取验证码';
  };

  const getButtonIcon = () => {
    if (isLoading) return <Clock className="w-4 h-4 animate-spin" />;
    if (countdown > 0) return <CheckCircle className="w-4 h-4" />;
    return <Send className="w-4 h-4" />;
  };

  return (
    <button
      type="button"
      onClick={handleSendCode}
      disabled={!canSend}
      className={`
        flex items-center gap-2 px-4 py-2 rounded-lg font-medium text-sm
        transition-all duration-200 min-w-[120px] justify-center
        ${canSend
          ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white hover:from-blue-600 hover:to-blue-700 hover:shadow-lg'
          : countdown > 0
            ? 'bg-green-100 text-green-700 border border-green-300'
            : 'bg-gray-200 text-gray-500 cursor-not-allowed'
        }
      `}
    >
      {getButtonIcon()}
      {getButtonText()}
    </button>
  );
};

export default SendCodeButton;
```

## 2. 登录页面集成

### 2.1 修改StandaloneLoginPage.tsx

```typescript
// 在现有的StandaloneLoginPage.tsx中添加验证码登录功能

import VerificationCodeInput from '../components/ui/VerificationCodeInput';
import SendCodeButton from '../components/ui/SendCodeButton';

// 添加新的状态管理
const [loginMethod, setLoginMethod] = useState<'email-password' | 'email-code' | 'sms-code'>('email-password');
const [verificationCode, setVerificationCode] = useState('');
const [isCodeSent, setIsCodeSent] = useState(false);

// 验证码登录处理函数
const handleCodeLogin = async () => {
  if (!verificationCode || verificationCode.length !== 6) {
    setError('请输入6位验证码');
    return;
  }

  setIsLoading(true);
  setError('');

  try {
    const response = await fetch('/api/auth/login-with-code', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        identifier: loginMethod === 'email-code' ? email : phone,
        code: verificationCode,
        type: loginMethod === 'email-code' ? 'EMAIL' : 'SMS'
      }),
    });

    const data = await response.json();

    if (data.success) {
      // 保存token和用户信息
      useAuthStore.getState().login(data.data.token);
      setSuccessMessage('登录成功！正在跳转...');
      
      setTimeout(() => {
        navigate('/dashboard');
      }, 500);
    } else {
      setError(data.message || '登录失败，请重试');
    }
  } catch (error) {
    setError('网络错误，请稍后重试');
  } finally {
    setIsLoading(false);
  }
};

// 在JSX中添加登录方式选择
<div className="mb-6">
  <div className="flex rounded-xl bg-gray-100 p-1">
    <button
      type="button"
      onClick={() => setLoginMethod('email-password')}
      className={`flex-1 py-2 px-4 rounded-lg text-sm font-medium transition-all duration-200 ${
        loginMethod === 'email-password'
          ? 'bg-white text-gray-900 shadow-sm'
          : 'text-gray-600 hover:text-gray-900'
      }`}
    >
      <Lock size={16} className="inline mr-2" />
      密码登录
    </button>
    <button
      type="button"
      onClick={() => setLoginMethod('email-code')}
      className={`flex-1 py-2 px-4 rounded-lg text-sm font-medium transition-all duration-200 ${
        loginMethod === 'email-code'
          ? 'bg-white text-gray-900 shadow-sm'
          : 'text-gray-600 hover:text-gray-900'
      }`}
    >
      <Mail size={16} className="inline mr-2" />
      邮箱验证码
    </button>
    <button
      type="button"
      onClick={() => setLoginMethod('sms-code')}
      className={`flex-1 py-2 px-4 rounded-lg text-sm font-medium transition-all duration-200 ${
        loginMethod === 'sms-code'
          ? 'bg-white text-gray-900 shadow-sm'
          : 'text-gray-600 hover:text-gray-900'
      }`}
    >
      <Phone size={16} className="inline mr-2" />
      短信验证码
    </button>
  </div>
</div>

// 验证码登录表单
{loginMethod !== 'email-password' && (
  <>
    {/* 邮箱或手机号输入 */}
    <div className="mb-4">
      <div className="flex items-center border border-gray-300 rounded-xl bg-gray-50 p-3 focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-blue-500 transition-all duration-200 hover:border-gray-400">
        {loginMethod === 'email-code' ? (
          <Mail size={18} className="text-gray-400 mx-2" />
        ) : (
          <Phone size={18} className="text-gray-400 mx-2" />
        )}
        <input
          type={loginMethod === 'email-code' ? 'email' : 'tel'}
          placeholder={loginMethod === 'email-code' ? '请输入邮箱地址' : '请输入手机号'}
          className="bg-transparent border-none outline-none flex-grow text-gray-800 placeholder-gray-400"
          value={loginMethod === 'email-code' ? email : phone}
          onChange={(e) => loginMethod === 'email-code' ? setEmail(e.target.value) : setPhone(e.target.value)}
        />
        <SendCodeButton
          identifier={loginMethod === 'email-code' ? email : phone}
          type={loginMethod === 'email-code' ? 'EMAIL' : 'SMS'}
          disabled={loginMethod === 'email-code' ? !email : !phone}
          onSendSuccess={() => setIsCodeSent(true)}
        />
      </div>
    </div>

    {/* 验证码输入 */}
    {isCodeSent && (
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-3 text-center">
          请输入收到的验证码
        </label>
        <VerificationCodeInput
          value={verificationCode}
          onChange={setVerificationCode}
          error={error}
          autoFocus
        />
      </div>
    )}
  </>
)}
```

## 3. 表单验证规则

### 3.1 验证码登录验证规则

```typescript
// 在 frontend/src/lib/validations/auth.ts 中添加

// 邮箱验证码登录验证
export const emailCodeLoginSchema = z.object({
  email: z
    .string()
    .min(1, { message: '邮箱是必填项' })
    .email({ message: '请输入有效的邮箱地址' }),
  code: z
    .string()
    .min(6, { message: '请输入6位验证码' })
    .max(6, { message: '验证码为6位数字' })
    .regex(/^\d{6}$/, { message: '验证码只能包含数字' })
});

// 手机验证码登录验证
export const smsCodeLoginSchema = z.object({
  phoneNumber: z
    .string()
    .min(1, { message: '手机号是必填项' })
    .regex(/^1[3-9]\d{9}$/, { message: '请输入有效的手机号' }),
  code: z
    .string()
    .min(6, { message: '请输入6位验证码' })
    .max(6, { message: '验证码为6位数字' })
    .regex(/^\d{6}$/, { message: '验证码只能包含数字' })
});

// 提取类型
export type EmailCodeLoginFormValues = z.infer<typeof emailCodeLoginSchema>;
export type SmsCodeLoginFormValues = z.infer<typeof smsCodeLoginSchema>;
```

## 4. API服务函数

### 4.1 验证码相关API服务

```typescript
// 在 frontend/src/lib/api/auth.ts 中添加

// 发送验证码
export const sendVerificationCode = async (
  identifier: string,
  type: 'EMAIL' | 'SMS'
): Promise<{ success: boolean; message: string; expiresIn?: number }> => {
  try {
    const response = await fetch(`${API_URL}/api/auth/send-verification-code`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        identifier,
        type,
        purpose: 'LOGIN'
      }),
    });

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Send verification code error:', error);
    throw new Error('网络错误，请稍后重试');
  }
};

// 验证码登录
export const loginWithCode = async (
  identifier: string,
  code: string,
  type: 'EMAIL' | 'SMS'
) => {
  try {
    const response = await fetch(`${API_URL}/api/auth/login-with-code`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        identifier,
        code,
        type
      }),
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.message || '登录失败');
    }

    return data.data; // 返回 { token, user, expiresIn }
  } catch (error) {
    console.error('Login with code error:', error);
    throw error;
  }
};

// 查询验证码状态
export const getVerificationStatus = async (
  identifier: string,
  type: 'EMAIL' | 'SMS'
): Promise<{
  canSend: boolean;
  remainingTime: number;
  attempts: number;
  maxAttempts: number;
  isLocked: boolean;
  lockExpires?: number;
}> => {
  try {
    const response = await fetch(
      `${API_URL}/api/auth/verification-status?identifier=${encodeURIComponent(identifier)}&type=${type}`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );

    const data = await response.json();
    return data.data;
  } catch (error) {
    console.error('Get verification status error:', error);
    throw new Error('获取验证码状态失败');
  }
};
```

## 5. 错误处理和用户反馈

### 5.1 错误处理策略

```typescript
// 统一的错误处理函数
const handleVerificationError = (error: any, showError: (message: string) => void) => {
  if (error.response?.data?.error?.code) {
    switch (error.response.data.error.code) {
      case 'RATE_LIMITED':
        showError('发送过于频繁，请稍后再试');
        break;
      case 'INVALID_EMAIL':
        showError('请输入有效的邮箱地址');
        break;
      case 'INVALID_PHONE':
        showError('请输入有效的手机号');
        break;
      case 'USER_NOT_FOUND':
        showError('该账号尚未注册，请先注册');
        break;
      case 'INVALID_CODE':
        showError('验证码错误，请重新输入');
        break;
      case 'CODE_EXPIRED':
        showError('验证码已过期，请重新获取');
        break;
      case 'ACCOUNT_LOCKED':
        showError('验证失败次数过多，账户已被锁定');
        break;
      default:
        showError(error.response.data.message || '操作失败，请稍后重试');
    }
  } else {
    showError(error.message || '网络错误，请稍后重试');
  }
};
```

这些实现示例展示了验证码登录系统的核心前端组件，包括验证码输入、发送按钮、页面集成、表单验证和错误处理等关键功能。所有组件都严格遵循项目的UI设计风格，使用Tailwind CSS和现有的Toast通知系统。
