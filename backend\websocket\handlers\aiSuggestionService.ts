// AI建议服务
import axios from 'axios';
import { AuthenticatedWebSocket } from '../../types/websocket.js';
// import prisma from '../../lib/prisma.js'; // 暂时注释掉

export class AISuggestionService {
  private activeLLMRequests: Map<string, boolean> = new Map();
  private readonly DEEPSEEK_API_URL = 'https://api.deepseek.com/v1/chat/completions';

  constructor() {
    console.log('✅ AI Suggestion Service initialized');
  }

  /**
   * 生成AI建议
   */
  async generateSuggestion(userText: string, sessionId: string, ws: AuthenticatedWebSocket): Promise<void> {
    // 检查是否已有相同的LLM请求在处理
    const isAlreadyProcessing = this.activeLLMRequests.get(sessionId);
    console.log(`🔍 LLM Request Status Check for session ${sessionId}:`, {
      isAlreadyProcessing: isAlreadyProcessing,
      userText: userText.substring(0, 50) + '...',
      textLength: userText.length,
      activeLLMRequestsSize: this.activeLLMRequests.size,
      allActiveSessions: Array.from(this.activeLLMRequests.keys())
    });

    if (isAlreadyProcessing) {
      console.log(`⚠️ LLM request already in progress for session ${sessionId}, skipping duplicate request`);
      return;
    }

    // 标记开始处理
    this.activeLLMRequests.set(sessionId, true);
    console.log(`🤖 Starting LLM generation for session ${sessionId}:`, {
      userText: `"${userText}"`,
      textLength: userText.length,
      timestamp: new Date().toISOString(),
      activeLLMRequestsAfterSet: this.activeLLMRequests.size
    });

    try {
      await this.callDeepSeekAPI(userText, sessionId, ws);
    } catch (error) {
      console.error(`❌ AI Suggestion error for session ${sessionId}:`, error);
      this.sendErrorMessage(ws, 'Failed to get AI suggestion', error);
    } finally {
      // 确保在任何情况下都清理LLM请求状态
      const wasActive = this.activeLLMRequests.delete(sessionId);
      console.log(`🧹 LLM request state cleaned for session ${sessionId}:`, {
        wasActiveBeforeDelete: wasActive,
        activeLLMRequestsAfterCleanup: this.activeLLMRequests.size,
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * 调用DeepSeek API
   */
  private async callDeepSeekAPI(userText: string, sessionId: string, ws: AuthenticatedWebSocket): Promise<void> {
    const apiKey = process.env.DEEPSEEK_API_KEY;
    if (!apiKey) {
      throw new Error('DEEPSEEK_API_KEY not configured');
    }

    const messagesForLLM = [
      {
        role: 'system',
        content: this.getSystemPrompt()
      },
      { 
        role: 'user', 
        content: `面试官说："${userText}"。请回答。` 
      }
    ];

    try {
      console.log(`🤖 Calling DeepSeek API for session ${sessionId}:`, {
        userText: userText.substring(0, 50) + '...',
        textLength: userText.length,
        apiUrl: this.DEEPSEEK_API_URL,
        hasApiKey: !!apiKey,
        wsReadyState: ws.readyState
      });

      const response = await axios.post(
        this.DEEPSEEK_API_URL,
        {
          model: "deepseek-chat",
          messages: messagesForLLM,
          stream: true,
        },
        {
          headers: {
            'Authorization': `Bearer ${apiKey}`,
            'Content-Type': 'application/json',
            'Accept': 'text/event-stream'
          },
          responseType: 'stream',
          timeout: 30000 // 30秒超时
        }
      );

      console.log(`✅ DeepSeek API response received for session ${sessionId}:`, {
        status: response.status,
        headers: response.headers['content-type'],
        hasData: !!response.data
      });

      await this.processStreamResponse(response, sessionId, ws);
    } catch (error) {
      this.handleAPIError(error, sessionId, ws);
    }
  }

  /**
   * 处理流式响应
   */
  private async processStreamResponse(response: any, sessionId: string, ws: AuthenticatedWebSocket): Promise<void> {
    let accumulatedSuggestion = "";
    
    for await (const chunk of response.data) {
      const lines = chunk.toString('utf8').split('\n\n');
      
      for (const line of lines) {
        if (line.startsWith('data: ')) {
          const jsonData = line.substring(6);
          
          if (jsonData.trim() === '[DONE]') {
            // 流式响应结束
            const endMessage = JSON.stringify({ type: 'ai_suggestion_end' });

            console.log(`📤 Sending AI suggestion end to client:`, {
              sessionId: sessionId,
              accumulatedLength: accumulatedSuggestion.length,
              wsReadyState: ws.readyState,
              messageSize: endMessage.length
            });

            if (ws.readyState === 1) { // WebSocket.OPEN
              ws.send(endMessage);
              console.log(`✅ AI suggestion end sent successfully`);
            } else {
              console.error(`❌ WebSocket not ready for sending end message, state: ${ws.readyState}`);
            }
            
            // 保存AI建议到数据库（暂时注释掉）
            if (accumulatedSuggestion.trim()) {
              await this.saveAISuggestion(accumulatedSuggestion.trim(), sessionId);
            }
            
            console.log(`✅ LLM suggestion completed for session ${sessionId}:`, {
              accumulatedLength: accumulatedSuggestion.length,
              timestamp: new Date().toISOString()
            });
            return;
          }
          
          try {
            const parsed = JSON.parse(jsonData);
            if (parsed.choices && parsed.choices[0].delta && parsed.choices[0].delta.content) {
              const contentChunk = parsed.choices[0].delta.content;
              accumulatedSuggestion += contentChunk;
              
              // 发送流式内容给客户端
              const chunkMessage = JSON.stringify({
                type: 'ai_suggestion_chunk',
                text: contentChunk
              });

              console.log(`📤 Sending AI suggestion chunk to client:`, {
                sessionId: sessionId,
                chunkLength: contentChunk.length,
                chunkPreview: contentChunk.substring(0, 20) + '...',
                wsReadyState: ws.readyState,
                messageSize: chunkMessage.length
              });

              if (ws.readyState === 1) { // WebSocket.OPEN
                ws.send(chunkMessage);
                console.log(`✅ AI suggestion chunk sent successfully`);
              } else {
                console.error(`❌ WebSocket not ready for sending, state: ${ws.readyState}`);
              }
            }
          } catch (parseError) {
            console.error('Error parsing DeepSeek stream chunk:', parseError, jsonData);
          }
        }
      }
    }
  }

  /**
   * 处理API错误
   */
  private handleAPIError(error: any, sessionId: string, ws: AuthenticatedWebSocket): void {
    console.error(`❌ DeepSeek LLM Error for session ${sessionId}:`, {
      errorMessage: error.message,
      errorResponse: error.response ? error.response.data : 'No response data',
      errorStatus: error.response ? error.response.status : 'No status',
      timestamp: new Date().toISOString()
    });

    let errorMessage = 'Failed to get AI suggestion';
    
    if (axios.isAxiosError(error)) {
      if (error.code === 'ECONNABORTED') {
        errorMessage = 'AI suggestion request timeout';
      } else if (error.response?.status === 401) {
        errorMessage = 'AI service authentication failed';
      } else if (error.response?.status === 429) {
        errorMessage = 'AI service rate limit exceeded';
      } else if (error.response?.status >= 500) {
        errorMessage = 'AI service temporarily unavailable';
      }
    }

    this.sendErrorMessage(ws, errorMessage, error);
  }

  /**
   * 获取系统提示词
   */
  private getSystemPrompt(): string {
    return `你是一位参加面试的应聘者。请以自然、口语化的方式回答面试官的问题。

你的回答应该：
1. 使用关键词+口语化描述的方式，第一行是关键词，换行显示后，第二行才是口语化的描述
2. 包含适当的个人习惯表达用语（这个、可能是、我认为等）
3. 语气自然，避免过于正式或书面化
4. 回答简洁但有内容，控制在50字以内
5. 偶尔可以有轻微的语法不完整，模拟真实对话
6. 可以适当表达思考过程

严格禁止：
1. 使用过于完美的结构化回答
2. 使用过多专业术语或行业黑话
3. 回答过于详尽或啰嗦
4. 使用明显的模板化语言
5. 最后一句不需要总结整个对话
6. 不要有"综上"、"总的来说"这样的字眼
7. 绝对不要有重复啰嗦的回复
8. 不要重复使用相同的词汇或短语
9. 避免冗余表达和无意义的填充词
10. 如果面试官的问题不清楚，直接要求澄清，不要猜测

注意：如果面试官的话听起来不像正常的面试问题（比如包含无意义的重复词汇），请礼貌地要求对方重新表达问题。`;
  }

  /**
   * 保存AI建议到数据库（暂时注释掉）
   */
  private async saveAISuggestion(text: string, sessionId: string): Promise<void> {
    try {
      // 暂时注释掉数据库操作
      // await prisma.aISuggestion.create({
      //   data: {
      //     text: text,
      //     sessionId: sessionId,
      //   }
      // });
      console.log(`💾 AI suggestion saved for session ${sessionId} (database operation commented out)`);
    } catch (dbError) {
      console.error('Error saving AI suggestion to database:', dbError);
    }
  }

  /**
   * 发送错误消息
   */
  private sendErrorMessage(ws: AuthenticatedWebSocket, message: string, error: any): void {
    const errorMessage = {
      type: 'ai_suggestion_error',
      message,
      error: error?.message || 'Unknown error',
      timestamp: Date.now()
    };
    
    if (ws.readyState === 1) { // WebSocket.OPEN
      ws.send(JSON.stringify(errorMessage));
    }
  }

  /**
   * 获取活跃请求状态
   */
  getActiveRequestsStatus(): { [sessionId: string]: boolean } {
    const status: { [sessionId: string]: boolean } = {};
    this.activeLLMRequests.forEach((isActive, sessionId) => {
      status[sessionId] = isActive;
    });
    return status;
  }

  /**
   * 清理会话
   */
  cleanupSession(sessionId: string): void {
    const wasActive = this.activeLLMRequests.delete(sessionId);
    if (wasActive) {
      console.log(`🧹 Cleaned up AI suggestion session: ${sessionId}`);
    }
  }

  /**
   * 销毁服务
   */
  async destroy(): Promise<void> {
    console.log('🧹 Destroying AI Suggestion Service');
    this.activeLLMRequests.clear();
    console.log('✅ AI Suggestion Service destroyed');
  }
}
