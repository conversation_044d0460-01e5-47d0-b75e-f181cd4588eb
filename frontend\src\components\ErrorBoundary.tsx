import React, { Component, ErrorInfo, ReactNode } from 'react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    // 检查是否是浏览器扩展相关错误
    if (error.message && 
        (error.message.includes('message channel closed') ||
         error.message.includes('Extension context invalidated') ||
         error.message.includes('listener indicated an asynchronous response'))) {
      console.warn('浏览器扩展相关错误，已忽略:', error.message);
      // 不更新状态，让组件继续正常渲染
      return { hasError: false };
    }

    // 对于其他错误，更新状态以显示错误UI
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // 检查是否是浏览器扩展相关错误
    if (error.message && 
        (error.message.includes('message channel closed') ||
         error.message.includes('Extension context invalidated') ||
         error.message.includes('listener indicated an asynchronous response'))) {
      console.warn('浏览器扩展相关错误，已忽略:', error.message);
      return;
    }

    // 记录其他错误
    console.error('ErrorBoundary caught an error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      // 自定义错误UI
      return this.props.fallback || (
        <div className="flex items-center justify-center min-h-screen bg-gray-50">
          <div className="max-w-md mx-auto text-center p-6">
            <div className="text-red-500 text-6xl mb-4">⚠️</div>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">出现了一些问题</h1>
            <p className="text-gray-600 mb-4">
              应用遇到了意外错误，请刷新页面重试。
            </p>
            <button
              onClick={() => window.location.reload()}
              className="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg transition-colors"
            >
              刷新页面
            </button>
            {process.env.NODE_ENV === 'development' && this.state.error && (
              <details className="mt-4 text-left">
                <summary className="cursor-pointer text-sm text-gray-500">
                  错误详情 (开发模式)
                </summary>
                <pre className="mt-2 text-xs text-red-600 bg-red-50 p-2 rounded overflow-auto">
                  {this.state.error.stack}
                </pre>
              </details>
            )}
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
