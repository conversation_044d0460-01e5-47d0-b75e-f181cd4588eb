import { BrowserRouter, Routes, Route } from 'react-router-dom';
import StandaloneLoginPage from './pages/StandaloneLoginPage';
import Layout from './components/Layout';
import Dashboard from './components/Dashboard';
import ApiTest from './components/ApiTest';

const StandaloneApp = () => {
  return (
    <BrowserRouter>
      <Routes>
        <Route path="/ai-login" element={<StandaloneLoginPage />} />
        <Route path="/new-register" element={<StandaloneLoginPage initialMode="register" />} />
        <Route path="/api-test" element={<ApiTest />} />
        <Route path="/app" element={<Layout />}>
          <Route path="dashboard" element={<Dashboard />} />
        </Route>
        <Route path="/" element={<StandaloneLoginPage />} />
      </Routes>
    </BrowserRouter>
  );
};

export default StandaloneApp;
