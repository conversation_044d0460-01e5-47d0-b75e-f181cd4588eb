{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@new-mianshijun/common": "*", "@types/crypto-js": "^4.2.2", "crypto-js": "^4.2.0", "framer-motion": "^12.15.0", "jwt-decode": "^4.0.0", "lucide-react": "^0.344.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.56.3", "react-router-dom": "^7.6.0", "react-use-websocket": "^4.13.0", "zod": "^3.24.4", "zustand": "^4.5.7"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/jwt-decode": "^2.2.1", "@types/node": "^22.15.17", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}