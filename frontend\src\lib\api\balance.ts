import { fetchWithAuth } from './apiService';
import { UserBalance } from '../../stores/balanceStore';

// 获取用户余额
export const getUserBalance = async (): Promise<UserBalance> => {
  // fetchWithAuth 直接返回解析后的JSON数据，不是Response对象
  const responseData = await fetchWithAuth<{success: boolean, credits: any}>('/users/me/credits');

  // 检查响应格式
  if (!responseData.success || !responseData.credits) {
    throw new Error('余额数据格式错误');
  }

  const credits = responseData.credits;

  return {
    mockInterviewCredits: credits.mockInterviewCredits || 0,
    formalInterviewCredits: credits.formalInterviewCredits || 0,
    mianshijunBalance: credits.mianshijunBalance || 0,
    lastUpdated: Date.now()
  };
};

// 余额预检查（用于"立即体验"按钮点击时检查）
export const checkUserBalance = async (): Promise<{
  hasMockCredits: boolean;
  hasFormalCredits: boolean;
  mockInterviewCredits: number;
  formalInterviewCredits: number;
  mianshijunBalance: number;
}> => {
  const responseData = await fetchWithAuth<{
    success: boolean;
    data: {
      mockInterviewCredits: number;
      formalInterviewCredits: number;
      mianshijunBalance: number;
      hasMockCredits: boolean;
      hasFormalCredits: boolean;
    };
  }>('/users/balance-check');

  if (!responseData.success || !responseData.data) {
    throw new Error('余额检查失败');
  }

  return responseData.data;
};

// 轻量级余额检查（只返回余额和更新时间）
export const checkBalanceUpdate = async (): Promise<{
  balance: UserBalance;
  hasChanged: boolean;
}> => {
  try {
    // fetchWithAuth 直接返回解析后的JSON数据，修复响应格式
    const responseData = await fetchWithAuth<{
      success: boolean;
      data: {
        mockInterviewCredits: number;
        formalInterviewCredits: number;
        mianshijunBalance: number;
        hasMockCredits: boolean;
        hasFormalCredits: boolean;
        updatedAt: string;
      };
    }>('/users/balance-check');

    // 检查响应格式
    if (!responseData.success || !responseData.data) {
      throw new Error('余额检查响应格式错误');
    }

    return {
      balance: {
        mockInterviewCredits: responseData.data.mockInterviewCredits || 0,
        formalInterviewCredits: responseData.data.formalInterviewCredits || 0,
        mianshijunBalance: responseData.data.mianshijunBalance || 0,
        lastUpdated: Date.now()
      },
      hasChanged: false // 这个API不返回hasChanged，设为false
    };
  } catch (error) {
    console.error('检查余额更新失败:', error);
    // 如果轻量级接口不存在，回退到完整接口
    const balance = await getUserBalance();
    return { balance, hasChanged: true };
  }
};
