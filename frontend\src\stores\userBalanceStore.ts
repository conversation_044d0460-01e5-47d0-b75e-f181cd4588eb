import { create } from 'zustand';
import { getUserCredits } from '../lib/api/paymentService';

// 用户余额数据接口
export interface UserBalance {
  mockInterviewCredits: number;
  formalInterviewCredits: number;
  mianshijunBalance: number;
  updatedAt: string;
}

// Store状态接口
interface UserBalanceState {
  balance: UserBalance | null;
  isLoading: boolean;
  error: string | null;
  
  // 操作方法
  fetchBalance: () => Promise<void>;
  updateBalance: (newBalance: Partial<UserBalance>) => void;
  clearBalance: () => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
}

// 创建UserBalance store
export const useUserBalanceStore = create<UserBalanceState>((set, get) => ({
  balance: null,
  isLoading: false,
  error: null,

  // 获取用户余额
  fetchBalance: async () => {
    const { setLoading, setError } = get();

    try {
      setLoading(true);
      setError(null);

      const response = await getUserCredits();

      if (response.success && response.credits) {
        set({
          balance: response.credits,
          isLoading: false,
          error: null
        });
      } else {
        throw new Error('获取余额数据失败');
      }
    } catch (error: any) {
      console.error('获取用户余额失败:', error);
      setError(error.message || '获取用户余额失败');
      setLoading(false);
    }
  },

  // 更新余额（用于兑换成功后的本地更新）
  updateBalance: (newBalance: Partial<UserBalance>) => {
    const { balance } = get();
    if (balance) {
      set({
        balance: {
          ...balance,
          ...newBalance,
          updatedAt: new Date().toISOString()
        }
      });
    }
  },

  // 清空余额数据
  clearBalance: () => {
    set({
      balance: null,
      isLoading: false,
      error: null
    });
  },

  // 设置加载状态
  setLoading: (loading: boolean) => {
    set({ isLoading: loading });
  },

  // 设置错误信息
  setError: (error: string | null) => {
    set({ error, isLoading: false });
  }
}));

export default useUserBalanceStore;
