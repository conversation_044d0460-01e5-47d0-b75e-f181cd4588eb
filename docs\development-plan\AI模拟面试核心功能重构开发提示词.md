# AI模拟面试核心功能重构开发提示词文档

## 项目概述

### 背景
当前AI模拟面试系统存在以下问题：
1. 用户进入实时面试页面后，缺少明确的开始按钮
2. 系统过度依赖ASR（语音识别），用户体验复杂
3. 需要简化流程，专注于AI对话交互

### 目标
重构AI模拟面试核心功能，实现：
1. 在实时面试页面中央添加"开始模拟面试"按钮
2. 暂时移除ASR依赖，完全基于LLM对话
3. AI充当面试官角色，用户通过文本输入回答
4. 保持现有UI设计风格，不改动现有界面布局

## 技术架构分析

### 现有组件结构
```
MockInterviewSessionPage (实时面试页面)
├── Header (面试头部信息)
├── InterviewContent (面试内容区域)
│   ├── MessageBubble (消息气泡组件)
│   │   ├── 蓝色气泡 (type: 'interviewer') - AI面试官问题
│   │   └── 绿色气泡 (type: 'ai-suggestion') - 用户回答
└── ControlBar (控制栏)
```

### 现有状态管理
- `useMockSession()` - 面试会话状态
- `useMockMessages()` - 消息列表
- `useMockSessionActions()` - 会话操作方法

### 现有LLM服务
- `LLMService` - 位于 `backend/services/llmService.ts`
- `MockInterviewService` - 位于 `backend/websocket/handlers/mockInterviewService.ts`
- 支持动态问题生成和回答分析

## 开发任务详细说明

### 任务1：修改InterviewContent组件
**文件位置**: `frontend/src/components/interview/InterviewContent.tsx`

**需求**:
1. 在页面正中央添加"开始模拟面试"按钮
2. 按钮样式参考现有项目风格（灰色主题，圆角设计）
3. 按钮只在面试未开始时显示
4. 点击后隐藏按钮，开始面试流程

**按钮样式规范**:
```css
className="w-full bg-gray-800 text-white py-3 rounded-lg text-base font-semibold hover:bg-gray-700 transition-colors shadow-sm"
```

**状态管理**:
- 添加 `isInterviewStarted` 状态
- 初始值为 `false`
- 点击开始按钮后设置为 `true`

### 任务2：添加用户输入组件
**文件位置**: 在InterviewContent组件中新增输入区域

**需求**:
1. 在消息列表下方添加文本输入框
2. 输入框样式与项目整体风格保持一致
3. 添加发送按钮
4. 支持回车键发送
5. 只在面试开始后显示

**输入框样式规范**:
```css
输入框: "flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
发送按钮: "px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
```

### 任务3：修改消息气泡逻辑
**文件位置**: `frontend/src/components/interview/MessageBubble.tsx`

**需求**:
1. 确保AI问题显示为蓝色气泡 (type: 'interviewer')
2. 确保用户回答显示为绿色气泡 (type: 'ai-suggestion')
3. 移除语音相关的状态指示器
4. 保持现有的打字机效果

**气泡类型映射**:
- AI面试官问题 → `type: 'interviewer'` → 蓝色气泡
- 用户回答 → `type: 'ai-suggestion'` → 绿色气泡

### 任务4：集成LLM服务调用
**文件位置**: `frontend/src/pages/MockInterviewSessionPage.tsx`

**需求**:
1. 面试开始时，调用LLM生成第一个问题
2. 用户提交回答后，调用LLM生成下一个问题
3. 使用现有的WebSocket连接和MockInterviewService
4. 移除ASR相关的音频处理逻辑

**WebSocket消息格式**:
```typescript
// 开始面试消息
{
  type: 'start_mock_interview',
  sessionId: string,
  config: {
    companyName: string,
    positionName: string,
    interviewLanguage: 'chinese' | 'english',
    answerStyle: 'keywords_conversational' | 'conversational'
  }
}

// 用户回答消息
{
  type: 'mock_interview_answer',
  sessionId: string,
  questionId: string,
  answerText: string,
  timestamp: number
}
```

### 任务5：修改ControlBar组件
**文件位置**: `frontend/src/components/interview/ControlBar.tsx`

**需求**:
1. 移除录音相关的按钮和状态
2. 保留刷新和结束面试功能
3. 简化控制栏界面

**保留功能**:
- 刷新会话按钮
- 结束面试按钮
- 移除麦克风/录音按钮

## UI设计规范

### 颜色主题
- 主要按钮：`bg-gray-800 hover:bg-gray-700`
- 次要按钮：`bg-blue-600 hover:bg-blue-700`
- 输入框：`border-gray-300 focus:ring-blue-500`
- 蓝色气泡：`bg-blue-50 border-blue-100 text-blue-800`
- 绿色气泡：`bg-green-50 border-green-100 text-green-800`

### 布局原则
- 保持现有页面布局不变
- 新增元素使用现有的间距和圆角规范
- 响应式设计，适配不同屏幕尺寸
- 过渡动画使用 `transition-colors`

## 后端适配说明

### MockInterviewService修改
**文件位置**: `backend/websocket/handlers/mockInterviewService.ts`

**需要确认的功能**:
1. `startMockInterview` 方法是否支持纯文本模式
2. `handleUserAnswer` 方法是否正确处理文本输入
3. LLM问题生成是否正常工作

### WebSocket消息路由
**文件位置**: `backend/websocket/interviewWs.ts`

**需要确认**:
1. `mode=mock` 参数路由到MockInterviewService
2. 消息类型正确处理
3. 错误处理机制完善

## 开发注意事项

### 严格约束
1. **不能修改现有UI布局和样式**
2. **遇到冲突必须先咨询确认**
3. **不确定的地方必须先询问**
4. **只完善逻辑，不改动UI设计**

### 测试要点
1. 面试开始流程是否正常
2. AI问题生成是否及时
3. 用户输入是否正确发送
4. 消息气泡显示是否正确
5. 面试结束流程是否完整

### 性能考虑
1. LLM调用响应时间控制在3秒内
2. 消息渲染性能优化
3. WebSocket连接稳定性
4. 错误恢复机制

## 开发优先级

### 第一阶段（核心功能）
1. 添加开始面试按钮
2. 集成用户文本输入
3. 实现基本的问答流程

### 第二阶段（完善体验）
1. 优化消息显示效果
2. 完善错误处理
3. 添加加载状态提示

### 第三阶段（测试优化）
1. 端到端测试
2. 性能优化
3. 用户体验细节调整

## 技术风险评估

### 潜在风险
1. LLM服务调用延迟
2. WebSocket连接稳定性
3. 状态管理复杂度
4. 现有代码兼容性

### 风险缓解
1. 添加加载状态提示
2. 实现重连机制
3. 简化状态逻辑
4. 充分测试现有功能

---

**重要提醒**: 开发过程中如遇到任何与现有UI冲突或不确定的地方，必须立即停止并咨询确认，确保不破坏现有功能。
