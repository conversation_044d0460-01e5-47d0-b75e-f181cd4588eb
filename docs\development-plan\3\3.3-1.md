好的，我们来整理一份最终的、完整的开发清单，优先使用OpenAI Whisper，其次是科大讯飞（讯飞的集成将作为后续步骤，因为目前没有提供API密钥，但会在环境变量中预留位置），最后是百度ASR。AI回复方面首先集成DeepSeek。

这份清单将保持对您“vibe coding”风格的友好性，并包含所有必要的安全和操作细节。

------

最终开发清单：实时AI面试助手核心功能 (V3 - ASR优先级调整)

Final Development Checklist: Core Features for Real-time AI Interview Assistant (V3 - ASR Prioritization Adjusted)

------

**重要安全提示 (CRITICAL SECURITY REMINDER)**:

- API密钥安全 (API Key Security)

  : 下方清单会将您提供的API密钥放入 

  ```
  .env
  ```

   文件的示例中。

  请确保 `backend/.env` 文件已添加到项目根目录下的 `.gitignore` 文件中！并且永远不要将包含真实密钥的 `.env` 文件提交到任何代码版本控制系统（如GitHub）。在服务器部署时，应使用服务器的环境变量来配置这些密钥。

  - 中文：检查项目根目录 `local-mianshijun/.gitignore` 文件，确保其中包含 `backend/.env` 这一行。
  - English: Check the `local-mianshijun/.gitignore` file in your project root to ensure it contains the line `backend/.env`.

- 在开始之前，请确保您已经在 Cursor 中打开了您的项目文件夹 `local-mianshijun`。

- 清单中的文件路径是相对于您的项目根目录 (`local-mianshijun/`) 的。

- 当需要安装新的依赖包时，请在对应的 `frontend` 或 `backend` 目录下打开终端执行命令。

------

**阶段零：环境与API密钥准备 (Phase 0: Environment and API Key Preparation)**

1. ## 接口调用流程

   *注：* 若需配置IP白名单，请前往控制台。IP白名单规则请参照 [IP白名单](https://www.xfyun.cn/doc/asr/rtasr/API.html#白名单)。

   实时语音转写接口调用包括两个阶段：握手阶段和实时通信阶段。

   ### [#](https://www.xfyun.cn/doc/asr/rtasr/API.html#握手阶段)握手阶段

   接口地址

   ```text
       ws://rtasr.xfyun.cn/v1/ws?{请求参数}
       或
       wss://rtasr.xfyun.cn/v1/ws?{请求参数}
   ```

   参数格式

   ```text
       key1=value1&key2=value2…（key和value都需要进行urlencode）
   ```

   参数说明

   | 参数          | 类型   | 必须 | 说明                                                         | 示例                                                         |
   | :------------ | :----- | :--- | :----------------------------------------------------------- | :----------------------------------------------------------- |
   | appid         | string | 是   | 讯飞开放平台应用ID                                           | 595f23df                                                     |
   | ts            | string | 是   | 当前时间戳，从1970年1月1日0点0分0秒开始到现在的秒数          | 1512041814                                                   |
   | signa         | string | 是   | 加密数字签名（基于HMACSHA1算法）                             | IrrzsJeOFk1NGfJHW6SkHUoN9CU=                                 |
   | lang          | string | 否   | 实时语音转写语种，不传默认为中文                             | 语种类型：中文、中英混合识别：cn；英文：en；小语种及方言可到控制台-实时语音转写-方言/语种处添加，添加后会显示该方言/语种参数值。传参示例如："lang=en" 若未授权无法使用会报错10110 |
   | transType     | string | 否   | normal表示普通翻译，默认值normal；                           | 例如：transType="normal" 注意：需控制台开通翻译功能          |
   | transStrategy | int    | 否   | 策略1，转写的vad结果直接送去翻译； 策略2，返回中间过程中的结果； 策略3，按照结束性标点拆分转写结果请求翻译； 建议使用策略2 | 例如：transStrategy=2 注意：需控制台开通翻译功能             |
   | targetLang    | String | 否   | 目标翻译语种：控制把源语言转换成什么类型的语言； 请注意类似英文转成法语必须以中文为过渡语言，即英-中-法，暂不支持不含中文语种之间的直接转换； 中文：cn 英文：en 日语：ja 韩语：ko 俄语：ru 法语：fr 西班牙语：es 越南语：vi 广东话：cn_cantonese | 例如：targetLang="en" 如果使用中文实时翻译为英文传参示例如下： "&lang=cn&transType=normal&transStrategy=2&targetLang=en" 注意：需控制台开通翻译功能 |
   | punc          | string | 否   | 标点过滤控制，默认返回标点，punc=0会过滤结果中的标点         | 0                                                            |
   | pd            | string | 否   | 垂直领域个性化参数: 法院: court 教育: edu 金融: finance 医疗: medical 科技: tech 运营商: isp 政府: gov 电商: ecom 军事: mil 企业: com 生活: life 汽车: car | 设置示例：pd="edu" 参数pd为非必须设置，不设置参数默认为通用  |
   | vadMdn        | int    | 否   | 远近场切换，不传此参数或传1代表远场，传2代表近场             | 设置示例：vadMdn=2                                           |
   | roleType      | int    | 否   | 是否开角色分离，默认不开启，传2开启 (效果持续优化中)仅支持中文 | 设置示例：roleType=2                                         |
   | engLangType   | int    | 否   | 语言识别模式，默认为模式1中英文模式： 1：自动中英文模式 2：中文模式，可能包含少量英文 4：纯中文模式，不包含英文 | 设置示例：engLangType=4                                      |

   #### [#](https://www.xfyun.cn/doc/asr/rtasr/API.html#signa生成)signa生成

   1.获取baseString，baseString由appid和当前时间戳ts拼接而成，假如appid为595f23df，ts为1512041814，则baseString为

   > 595f23df1512041814

   2.对baseString进行MD5，假如baseString为上一步生成的595f23df1512041814，MD5之后则为

   > 0829d4012497c14a30e7e72aeebe565e

   3.以apiKey为key对MD5之后的baseString进行HmacSHA1加密，然后再对加密后的字符串进行base64编码。
   假如apiKey为d9f4aa7ea6d94faca62cd88a28fd5234，MD5之后的baseString为上一步生成的0829d4012497c14a30e7e72aeebe565e，
   则加密之后再进行base64编码得到的signa为

   > IrrzsJeOFk1NGfJHW6SkHUoN9CU=

   备注：

   - apiKey：接口密钥，在应用中添加实时语音转写服务时自动生成，调用方注意保管；
   - signa的生成公式：HmacSHA1(MD5(appid + ts), api_key)，具体的生成方法详见【[调用示例](https://www.xfyun.cn/doc/asr/rtasr/API.html#调用示例)】；

   #### [#](https://www.xfyun.cn/doc/asr/rtasr/API.html#请求示例)请求示例

   ```text
   	ws://rtasr.xfyun.cn/v1/ws?appid=595f23df&ts=1512041814&signa=IrrzsJeOFk1NGfJHW6SkHUoN9CU=&pd=edu
   ```

   #### [#](https://www.xfyun.cn/doc/asr/rtasr/API.html#返回值)返回值

   结果格式为json，字段说明如下：

   | 参数   | 类型   | 说明                                                         |
   | :----- | :----- | :----------------------------------------------------------- |
   | action | string | 结果标识，started:握手，result:结果，error:异常              |
   | code   | string | 结果码(具体见[错误码](https://www.xfyun.cn/doc/asr/rtasr/API.html#错误码)) |
   | data   | string | 结果数据                                                     |
   | desc   | string | 描述                                                         |
   | sid    | string | 会话ID                                                       |

   其中sid字段主要用于DEBUG追查问题，如果出现问题，可以提供sid帮助确认问题。

   > 成功

   ```json
   	{
   	    
   	    "action":"started",
   		"code":"0",
   		"data":"",
   		"desc":"success",
   		"sid":"rta0000000a@ch312c0e3f63609f0900"
   	}
   ```

   > 失败

   ```json
   	{
   	    "action":"error",
   		"code":"10110",
   		"data":"",
   		"desc":"invalid authorization|illegal signa",
   		"sid":"rta0000000b@ch312c0e3f65f09f0900"
   	}
   ```

   ### [#](https://www.xfyun.cn/doc/asr/rtasr/API.html#实时通信阶段)实时通信阶段

   握手成功后，进入实时通信阶段，此时客户端的主动操作有两种：上传数据和上传结束标识，被动操作有两种：接收转写结果和错误

   #### [#](https://www.xfyun.cn/doc/asr/rtasr/API.html#上传数据)上传数据

   在实时转写过程中，客户端不断构造binary message发送到服务端，内容是音频的二进制数据。此操作的频率影响到文字结果展现的实时性。

   注意：

   1.建议音频流每40ms发送1280字节，发送过快可能导致引擎出错； 2.音频发送间隔超时时间为15秒，超时服务端报错并主动断开连接。

   #### [#](https://www.xfyun.cn/doc/asr/rtasr/API.html#上传结束标志)上传结束标志

   音频数据上传完成后，客户端需发送一个特殊的binary message到服务端作为结束标识，内容是：

   ```json
    	{"end": true}
   ```

   #### [#](https://www.xfyun.cn/doc/asr/rtasr/API.html#接收转写结果)接收转写结果

   交互过程中，服务端不断返回 text message （转写结果） 到客户端。当所有结果发送完毕后，服务端断开连接，交互结束。

   结果示例：

   ```json
   	{
       	    "action":"result",
       	    "code":"0",
       		"data":"{\"cn\":{\"st\":{\"bg\":\"820\",\"ed\":\"0\",\"rt\":[{\"ws\":[{\"cw\":[{\"w\":\"啊\",\"wp\":\"n\"}],\"wb\":0,\"we\":0},{\"cw\":[{\"w\":\"喂\",\"wp\":\"n\"}],\"wb\":0,\"we\":0},{\"cw\":[{\"w\":\"！\",\"wp\":\"p\"}],\"wb\":0,\"we\":0},{\"cw\":[{\"w\":\"你好\",\"wp\":\"n\"}],\"wb\":0,\"we\":0},{\"cw\":[{\"w\":\"！\",\"wp\":\"p\"}],\"wb\":0,\"we\":0},{\"cw\":[{\"w\":\"我\",\"wp\":\"n\"}],\"wb\":0,\"we\":0},{\"cw\":[{\"w\":\"是\",\"wp\":\"n\"}],\"wb\":0,\"we\":0},{\"cw\":[{\"w\":\"上\",\"wp\":\"n\"}],\"wb\":0,\"we\":0}]}],\"type\":\"1\"}},\"seg_id\":5}\n",
       		"desc":"success",
       		"sid":"rta0000000e@ch312c0e3f6bcc9f0900"
   	}
   ```

   其中data为转写结果的json字符串

   ```json
   	data：
   		{
   		    "cn":{
   		        "st":{
   		            "bg":"820",
   		            "ed":"0",
   		            "rt":[{
   	                    "ws":[{
                               "cw":[{
                                   "w":"啊",
                                   "wp":"n"
                               }],
                               "wb":0,
                               "we":0
                           },{
                           	"cw":[{
                                   "w":"喂",
                                   "wp":"n"
                               }],
                               "wb":0,
                               "we":0
                           },{
                               "cw":[{
                                   "w":"！",
                                   "wp":"p"
                               }],
                               "wb":0,
                               "we":0
                           },{
                               "cw":[{
                                   "w":"你好",
                                   "wp":"n"
                               }],
                               "wb":0,
                               "we":0
                           },{
                               "cw":[{
                               	"w":"！",
   								"wp":"p"
                               }],
                               "wb":0,
                               "we":0
   						},{
                               "cw":[{
                                   "w":"我",
                                   "wp":"n"
                               }],
   	                        "wb":0,
   	                        "we":0
                       	},{
                           	"cw":[{
                                   "w":"是",
                                   "wp":"n"
                               }],
   	                        "wb":0,
   	                        "we":0
   	                    },{
   	                        "cw":[{
   	                                "w":"上",
   	                                "wp":"n"
   	                        }],
   	                        "wb":0,
   	                        "we":0
                       	}]
   	                }],
   		            "type":"1"
   		        }
   		    },
   		    "seg_id":5
   		}
   ```

   结果示例（开启翻译功能）：

   ```json
   {
     "action": "result",
     "code": "0",
     "data": "{\"biz\":\"trans\",\"dst\":\" the bright moonlight in front of the bed, suspected to be frost on the ground, looked up at the bright moon, bowed his head and thought of his hometown.\",\"isEnd\":false,\"segId\":12,\"src\":\"床前明月光，疑是地上霜，举头望明月，低头思故乡。\",\"type\":0,\"bg\":0,\"ed\":4770}",
     "desc": "success",
     "sid": "rta00004fda@dx1f1c148be1d9000100"
   }
   ```

   其中data为转写结果的json字符串（开启翻译功能）：

   ```json
   {
     "biz": "trans",
     "dst": " the bright moonlight in front of the bed, suspected to be frost on the ground, looked up at the bright moon, bowed his head and thought of his hometown.",
     "isEnd": false,
     "segId": 12,
     "src": "床前明月光，疑是地上霜，举头望明月，低头思故乡。",
     "type": 0,
     "bg": 0,
     "ed": 4770
   }
   ```

   转写结果data字段说明如下：

   | 字段   | 含义                                                         | 描述                                 |
   | :----- | :----------------------------------------------------------- | :----------------------------------- |
   | bg     | 句子在整段语音中的开始时间，单位毫秒(ms)                     | 中间结果的bg为准确值                 |
   | ed     | 句子在整段语音中的结束时间，单位毫秒(ms)                     | 中间结果的ed为0                      |
   | w      | 词识别结果                                                   |                                      |
   | wp     | 词标识                                                       | n-普通词；s-顺滑词（语气词）；p-标点 |
   | wb     | 词在本句中的开始时间，单位是帧，1帧=10ms 即词在整段语音中的开始时间为(bg+wb*10)ms | 中间结果的 wb 为 0                   |
   | we     | 词在本句中的结束时间，单位是帧，1帧=10ms 即词在整段语音中的结束时间为(bg+we*10)ms | 中间结果的 we 为 0                   |
   | type   | 结果类型标识                                                 | 0-最终结果；1-中间结果               |
   | seg_id | 转写结果序号                                                 | 从0开始                              |
   | biz    | 业务标识字段，开启翻译功能后值为 trans                       | 翻译功能标识                         |
   | src    | 送翻译的原始文本                                             | 音频对应的识别文本                   |
   | dst    | 目标语种翻译文本结果                                         | 与原始文本src对应                    |
   | isEnd  | 翻译结束标识                                                 | 如果为 true，标识翻译结果已推送完成  |
   | rl     | 1、分离的角色编号，需开启角色分离的功能才返回对应的分离角色编号。 2、角色编号从1开始计算。 3、该字段只有在角色分离功能打开时出现。该值只有角色切换时才会变化，其余时值为0。例如角色A开始说话rl=1，后面角色A说话rl都是0，等到角色B开始说话时，rl=2，角色B继续说话rl又变回0 。 | 取值正整数                           |

   #### [#](https://www.xfyun.cn/doc/asr/rtasr/API.html#接收错误信息)接收错误信息

   交互过程中，在服务端出现异常而中断服务时（如会话超时），会将异常信息以 text message 形式返回给客户端并关闭连接。

   ## [#](https://www.xfyun.cn/doc/asr/rtasr/API.html#白名单)白名单

   在调用该业务接口时

   - 若关闭IP白名单，接口认为IP不限，不会校验IP。
   - 若打开IP白名单，则服务端会检查调用方IP是否在讯飞开放平台配置的IP白名单中，对于没有配置到白名单中的IP发来的请求，服务端会拒绝服务。

   IP白名单规则

   - IP白名单，在 控制台-我的应用-相应服务的应用管理卡片上 编辑，保存后五分钟左右生效；
   - 不同Appid的不同服务都需要分别设置IP白名单；
   - IP白名单需设置为外网IP，请勿设置局域网IP。
   - 如果服务器返回结果如下所示(illegal client_ip)，则表示由于未配置IP白名单或配置有误，服务端拒绝服务。

   ```json
   {
   	"action": "error",
   	"code": "10105",
   	"data": "",
   	"desc": "illegal access|illegal client_ip: xx.xx.xx.xx",
   	"sid": "rta..."
   }
   ```

   ## [#](https://www.xfyun.cn/doc/asr/rtasr/API.html#错误码)错误码

   | 错误码 | 描述                    | 说明                             | 处理方式                                    |
   | :----- | :---------------------- | :------------------------------- | :------------------------------------------ |
   | 0      | success                 | 成功                             |                                             |
   | 10105  | illegal access          | 没有权限                         | 检查apiKey，ip，ts等授权参数是否正确        |
   | 10106  | invalid parameter       | 无效参数                         | 上传必要的参数， 检查参数格式以及编码       |
   | 10107  | illegal parameter       | 非法参数值                       | 检查参数值是否超过范围或不符合要求          |
   | 10110  | no license              | 无授权许可                       | 检查参数值是否超过范围或不符合要求          |
   | 10700  | engine error            | 引擎错误                         | 提供接口返回值，向服务提供商反馈            |
   | 10202  | websocket connect error | websocket连接错误                | 检查网络是否正常                            |
   | 10204  | websocket write error   | 服务端websocket写错误            | 检查网络是否正常，向服务提供商反馈          |
   | 10205  | websocket read error    | 服务端websocket读错误            | 检查网络是否正常，向服务提供商反馈          |
   | 16003  | basic component error   | 基础组件异常                     | 重试或向服务提供商反馈                      |
   | 10800  | over max connect limit  | 超过授权的连接数                 | 确认连接数是否超过授权的连接数              |
   | 37005  | no audio data           | 超过15秒没有向服务端发送音频数据 | 10700/37005检查是否超过15秒没有进行音频发送 |

2. **准备科大讯飞API密钥 (Prepare iFlyTek API Keys - For Future Integration)**

   - 中文：如果您计划后续集成科大讯飞，请提前注册[讯飞开放平台](https://www.xfyun.cn/)，创建应用并获取其 **APPID, APIKey, APISecret**。我们会在 `.env` 文件中为它们预留位置。

   - English: If you plan to integrate iFlyTek later, please register on the [iFlyTek Open Platform](https://www.xfyun.cn/), create an application, and obtain its **APPID, APIKey, and APISecret**. We will reserve placeholders for them in the `.env` file.

   - APPID

     67c6992f

     APIKey

     f1bc87e68bf18ee4417574a383a3f64d

     *SDK调用方式只需APPID。APIKey或APISecret适用于WebAPI调用方式。

3. **配置后端环境变量 (Configure Backend Environment Variables)**

   - 中文：在 `local-mianshijun/backend/` 目录下，找到或创建 `.env` 文件。

   - English: In the `local-mianshijun/backend/` directory, find or create the `.env` file.

   - 中文：打开 

     ```
     backend/.env
     ```

      文件，填入以下内容 (已使用您提供的密钥，真实部署时请再次确认来源与安全)：

     代码段

     ```
     DATABASE_URL="postgresql://YOUR_DB_USER:YOUR_DB_PASSWORD@YOUR_DB_HOST:YOUR_DB_PORT/YOUR_DB_NAME?schema=public"
     
     # OpenAI API (首选 ASR - Whisper，也可用于 LLM)
     OPENAI_API_KEY="********************************************************************************************************************************************************************"
     
     # DeepSeek LLM (首选 LLM)
     DEEPSEEK_API_KEY="***********************************"
     
     # 科大讯飞 ASR (备选2 - 请替换为您的实际密钥)
     IFLYTEK_APP_ID="YOUR_IFLYTEK_APP_ID"
     IFLYTEK_API_KEY="YOUR_IFLYTEK_API_KEY"
     IFLYTEK_API_SECRET="YOUR_IFLYTEK_API_SECRET"
     
     # 百度 ASR (备选3)
     BAIDU_APP_ID="6636522"
     BAIDU_ASR_API_KEY="EoiwilRVLVzBuJddijQ9JTZU"
     BAIDU_ASR_SECRET_KEY="YkRgIT1qDmpNx5qBLfHg63EGmU6kFsSI"
     
     # WebSocket 端口
     PORT=8080 
     
     # JWT 密钥 (请务必修改为一个强随机字符串!)
     JWT_SECRET="YOUR_SUPER_SECRET_JWT_KEY_PLEASE_CHANGE_ME" 
     ```

   - English: Open the 

     ```
     backend/.env
     ```

      file and add the following content (using your provided keys; for actual deployment, re-verify source and security):

     代码段

     ```
     DATABASE_URL="postgresql://YOUR_DB_USER:YOUR_DB_PASSWORD@YOUR_DB_HOST:YOUR_DB_PORT/YOUR_DB_NAME?schema=public"
     
     # OpenAI API (Primary ASR - Whisper, can also be used for LLM)
     OPENAI_API_KEY="********************************************************************************************************************************************************************"
     
     # DeepSeek LLM (Primary LLM)
     DEEPSEEK_API_KEY="***********************************"
     
     # iFlyTek ASR (Alternative 2 - Please replace with your actual keys)
     IFLYTEK_APP_ID="YOUR_IFLYTEK_APP_ID"
     IFLYTEK_API_KEY="YOUR_IFLYTEK_API_KEY"
     IFLYTEK_API_SECRET="YOUR_IFLYTEK_API_SECRET"
     
     # Baidu ASR (Alternative 3)
     BAIDU_APP_ID="6636522"
     BAIDU_ASR_API_KEY="EoiwilRVLVzBuJddijQ9JTZU"
     BAIDU_ASR_SECRET_KEY="YkRgIT1qDmpNx5qBLfHg63EGmU6kFsSI"
     
     # WebSocket Port
     PORT=8080
     
     # JWT Secret (Please change to a strong random string!)
     JWT_SECRET="YOUR_SUPER_SECRET_JWT_KEY_PLEASE_CHANGE_ME"
     ```

4. **安装项目依赖 (Install Project Dependencies)**

   - 中文：
     - 在 `local-mianshijun/` 根目录打开终端，运行 `npm install`。
     - 进入 `local-mianshijun/frontend/` 目录，运行 `npm install`。
     - 进入 `local-mianshijun/backend/` 目录，运行 `npm install`。
   - English:
     - Open a terminal in the `local-mianshijun/` root directory and run `npm install` .
     - Navigate to the `local-mianshijun/frontend/` directory and run `npm install` .
     - Navigate to the `local-mianshijun/backend/` directory and run `npm install` .

------

**阶段一：后端 - WebSocket 与 AI 服务集成 (Phase 1: Backend - WebSocket & AI Service Integration)**

- **主要文件 (Main File): `local-mianshijun/backend/websocket/interviewWs.ts`**

1. **安装必要的后端依赖 (Install Necessary Backend Dependencies)**

   - 中文：在 

     ```
     local-mianshijun/backend/
     ```

      目录下打开终端，安装以下包 (有些可能已存在):

     Bash

     ```
     npm install ws openai form-data axios 
     # npm install bce-sdk-js # 百度SDK，如果需要
     # 对于科大讯飞，可能需要其特定的SDK或WebSocket库，请查阅其文档
     ```

   - English: Open a terminal in the 

     ```
     local-mianshijun/backend/
     ```

      directory and install the following packages (some might already exist):

     Bash

     ```
     npm install ws openai form-data axios
     # npm install bce-sdk-js # Baidu SDK, if needed
     # For iFlyTek, you might need its specific SDK or WebSocket library; consult their documentation
     ```

2. **实现 ASR 服务调用逻辑 (Implement ASR Service Call Logic)**

   - **文件 (File): `local-mianshijun/backend/websocket/interviewWs.ts`**

   - 中文：

     - 在文件顶部引入必要的模块和初始化API客户端：

       TypeScript

       ```
       import OpenAI from 'openai';
       import FormData from 'form-data'; // Not strictly needed for OpenAI SDK v4 file uploads with Buffers
       import { Readable } from 'stream';
       import axios from 'axios'; // For DeepSeek and potentially other HTTP APIs
       import prisma from '../lib/prisma';
       
       const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });
       // const deepSeekApiKey = process.env.DEEPSEEK_API_KEY;
       // 其他ASR服务客户端初始化 (讯飞、百度) 可以根据需要添加
       ```

     - 创建一个主函数 

       ```
       transcribeAudio(audioBuffer: Buffer): Promise<string | null>
       ```

       ，它将尝试按优先级调用ASR服务。

       TypeScript

       ```
       async function transcribeAudio(audioBuffer: Buffer, preferredAsr: string = 'openai'): Promise<string | null> {
         // 优先 OpenAI Whisper
         if (preferredAsr === 'openai' || !preferredAsr) { // Default to openai or if specified
             try {
                 console.log('Attempting transcription with OpenAI Whisper...');
                 const transcription = await openai.audio.transcriptions.create({
                     file: { name: "audio.webm", arrayBuffer: () => audioBuffer },
                     model: "whisper-1",
                     language: "zh",
                     response_format: "text",
                 });
                 console.log('OpenAI Whisper Result:', transcription);
                 if (typeof transcription === 'string' && transcription.trim() !== '') return transcription.trim();
             } catch (error) {
                 console.error("OpenAI Whisper ASR Error:", error);
                 // 如果OpenAI失败，可以尝试下一个 (如果实现了讯飞)
             }
         }
       
         // 备选2: 科大讯飞 (需要您实现其SDK或API调用逻辑)
         // if (preferredAsr === 'iflytek' || (lastError && !result)) {
         //   try {
         //     console.log('Attempting transcription with iFlyTek...');
         //     // const iflytekResult = await callIflytekASR(audioBuffer); // 您需要实现此函数
         //     // if (iflytekResult) return iflytekResult;
         //   } catch (error) {
         //     console.error("iFlyTek ASR Error:", error);
         //   }
         // }
       
         // 备选3: 百度 ASR (需要您实现其SDK或API调用逻辑)
         // if (preferredAsr === 'baidu' || (lastError && !result)) {
         //   try {
         //     console.log('Attempting transcription with Baidu ASR...');
         //     // const baiduResult = await callBaiduASR(audioBuffer); // 您需要实现此函数
         //     // if (baiduResult) return baiduResult;
         //   } catch (error) {
         //     console.error("Baidu ASR Error:", error);
         //   }
         // }
       
         console.warn('All ASR services failed or returned empty.');
         return null; // 所有ASR服务都失败
       }
       ```

     - **注意**: 上述代码中，科大讯飞和百度的调用逻辑 (`callIflytekASR`, `callBaiduASR`) 需要您根据其官方文档自行实现。目前清单主要关注OpenAI Whisper的集成。

   - English:

     - Import necessary modules and initialize API clients at the top of the file:

       TypeScript

       ```
       import OpenAI from 'openai';
       import FormData from 'form-data'; // Not strictly needed for OpenAI SDK v4 file uploads with Buffers
       import { Readable } from 'stream';
       import axios from 'axios'; // For DeepSeek and potentially other HTTP APIs
       import prisma from '../lib/prisma';
       
       const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });
       // const deepSeekApiKey = process.env.DEEPSEEK_API_KEY;
       // Initialization for other ASR service clients (iFlyTek, Baidu) can be added as needed
       ```

     - Create a main function 

       ```
       transcribeAudio(audioBuffer: Buffer): Promise<string | null>
       ```

        that will try ASR services in order of priority.

       TypeScript

       ```
       async function transcribeAudio(audioBuffer: Buffer, preferredAsr: string = 'openai'): Promise<string | null> {
         // Primary: OpenAI Whisper
         if (preferredAsr === 'openai' || !preferredAsr) { // Default to openai or if specified
             try {
                 console.log('Attempting transcription with OpenAI Whisper...');
                 const transcription = await openai.audio.transcriptions.create({
                     file: { name: "audio.webm", arrayBuffer: () => audioBuffer },
                     model: "whisper-1",
                     language: "zh",
                     response_format: "text",
                 });
                 console.log('OpenAI Whisper Result:', transcription);
                 if (typeof transcription === 'string' && transcription.trim() !== '') return transcription.trim();
             } catch (error) {
                 console.error("OpenAI Whisper ASR Error:", error);
                 // If OpenAI fails, could try next (if iFlyTek is implemented)
             }
         }
       
         // Alternative 2: iFlyTek (You'll need to implement its SDK/API call logic)
         // if (preferredAsr === 'iflytek' || (lastError && !result)) {
         //   try {
         //     console.log('Attempting transcription with iFlyTek...');
         //     // const iflytekResult = await callIflytekASR(audioBuffer); // You need to implement this function
         //     // if (iflytekResult) return iflytekResult;
         //   } catch (error) {
         //     console.error("iFlyTek ASR Error:", error);
         //   }
         // }
       
         // Alternative 3: Baidu ASR (You'll need to implement its SDK/API call logic)
         // if (preferredAsr === 'baidu' || (lastError && !result)) {
         //   try {
         //     console.log('Attempting transcription with Baidu ASR...');
         //     // const baiduResult = await callBaiduASR(audioBuffer); // You need to implement this function
         //     // if (baiduResult) return baiduResult;
         //   } catch (error) {
         //     console.error("Baidu ASR Error:", error);
         //   }
         // }
       
         console.warn('All ASR services failed or returned empty.');
         return null; // All ASR services failed
       }
       ```

     - **Note**: In the code above, the call logic for iFlyTek (`callIflytekASR`) and Baidu (`callBaiduASR`) needs to be implemented by you based on their official documentation. This checklist primarily focuses on OpenAI Whisper integration.

3. **WebSocket 消息处理 (WebSocket Message Handling)**

   - **文件 (File): `local-mianshijun/backend/websocket/interviewWs.ts`**

   - 中文：在 

     ```
     ws.on('message', async (messageData) => { ... });
     ```

      中：

     - 接收前端发送的 `audio_chunk` (应为 `Buffer` 类型)。

     - 调用 `transcribeAudio(audioBuffer)` 函数。

     - 如果获得转录文本 (

       ```
       asrResult
       ```

       )：

       - 通过WebSocket发送回客户端: `ws.send(JSON.stringify({ type: 'transcription', text: asrResult, speaker: 'user' }));`
       - 调用DeepSeek LLM（下一步骤）。
       - 存入数据库 `Transcription` 表。

   - English: In 

     ```
     ws.on('message', async (messageData) => { ... });
     ```

     :

     - Receive `audio_chunk` from frontend (should be a `Buffer`).

     - Call the `transcribeAudio(audioBuffer)` function.

     - If transcription text (

       ```
       asrResult
       ```

       ) is obtained:

       - Send back to client via WebSocket: `ws.send(JSON.stringify({ type: 'transcription', text: asrResult, speaker: 'user' }));`
       - Call DeepSeek LLM (next step).
       - Save to `Transcription` table in the database.

4. **集成 DeepSeek LLM (Integrate DeepSeek LLM)**

   - **文件 (File): `local-mianshijun/backend/websocket/interviewWs.ts`**

   - 中文：

     - 当从ASR获得用户文本后，构造Prompt，调用DeepSeek API。

     - 强烈建议实现流式输出 (Streaming)

        以提升用户体验。

       TypeScript

       ```
       async function getDeepSeekSuggestion(userText: string, interviewContext: any /* sessionId, history etc. */): Promise<void> {
           // 构造 messagesForLLM，包含系统提示、面试角色、历史对话、当前用户发言
           const messagesForLLM = [
               { role: 'system', content: '你是专业的面试助手，请根据用户在模拟面试中的发言提供建议或追问点。' },
               // ... (添加更多上下文信息，如面试角色、历史对话等)
               { role: 'user', content: `用户说：“${userText}”。请给出反馈。` }
           ];
       
           try {
               const response = await axios.post(
                   'https://api.deepseek.com/v1/chat/completions',
                   {
                       model: "deepseek-chat", // 或 "deepseek-coder" 根据需要
                       messages: messagesForLLM,
                       stream: true, // 启用流式输出
                   },
                   {
                       headers: {
                           'Authorization': `Bearer ${process.env.DEEPSEEK_API_KEY}`,
                           'Content-Type': 'application/json',
                           'Accept': 'text/event-stream' // 确保接收SSE
                       },
                       responseType: 'stream' // 关键：让axios以流的形式处理响应
                   }
               );
       
               let accumulatedSuggestion = "";
               // 'ws' 是指向当前 WebSocket 连接的变量
               for await (const chunk of response.data) {
                   const lines = chunk.toString('utf8').split('\n\n');
                   for (const line of lines) {
                       if (line.startsWith('data: ')) {
                           const jsonData = line.substring(6);
                           if (jsonData.trim() === '[DONE]') {
                               ws.send(JSON.stringify({ type: 'ai_suggestion_end' }));
                               // 流结束，保存完整建议到数据库
                               if (accumulatedSuggestion.trim() && interviewContext.sessionId) {
                                   await prisma.aiSuggestion.create({
                                       data: {
                                           text: accumulatedSuggestion.trim(),
                                           interviewSession: { connect: { id: interviewContext.sessionId } },
                                       }
                                   });
                               }
                               return;
                           }
                           try {
                               const parsed = JSON.parse(jsonData);
                               if (parsed.choices && parsed.choices[0].delta && parsed.choices[0].delta.content) {
                                   const contentChunk = parsed.choices[0].delta.content;
                                   accumulatedSuggestion += contentChunk;
                                   ws.send(JSON.stringify({ type: 'ai_suggestion_chunk', text: contentChunk }));
                               }
                           } catch (e) {
                               console.error('Error parsing DeepSeek stream chunk:', e, jsonData);
                           }
                       }
                   }
               }
           } catch (error: any) {
               console.error("DeepSeek LLM Error:", error.response ? error.response.data : error.message);
               ws.send(JSON.stringify({ type: 'ai_suggestion_error', message: 'Failed to get AI suggestion.' }));
           }
       }
       ```

     - 在ASR成功后调用 `getDeepSeekSuggestion(asrResult, { sessionId: currentInterviewSessionId /* ...other context */ });`

   - English:

     - After getting user text from ASR, construct the Prompt and call DeepSeek API.

     - Highly recommend implementing Streaming Output

        for better user experience.

       TypeScript

       ```
       async function getDeepSeekSuggestion(userText: string, interviewContext: any /* sessionId, history etc. */): Promise<void> {
           // Construct messagesForLLM, including system prompt, interview role, history, current user utterance
           const messagesForLLM = [
               { role: 'system', content: 'You are a professional interview assistant. Provide suggestions or follow-up questions based on the user\'s statements in a mock interview.' },
               // ... (add more context like interview role, conversation history, etc.)
               { role: 'user', content: `The user said: "${userText}". Please provide feedback.` }
           ];
       
           try {
               const response = await axios.post(
                   'https://api.deepseek.com/v1/chat/completions',
                   {
                       model: "deepseek-chat", // or "deepseek-coder" as needed
                       messages: messagesForLLM,
                       stream: true, // Enable streaming output
                   },
                   {
                       headers: {
                           'Authorization': `Bearer ${process.env.DEEPSEEK_API_KEY}`,
                           'Content-Type': 'application/json',
                           'Accept': 'text/event-stream' // Ensure SSE is accepted
                       },
                       responseType: 'stream' // CRITICAL: tells axios to handle response as a stream
                   }
               );
       
               let accumulatedSuggestion = "";
               // 'ws' is the variable referencing the current WebSocket connection
               for await (const chunk of response.data) {
                   const lines = chunk.toString('utf8').split('\n\n');
                   for (const line of lines) {
                       if (line.startsWith('data: ')) {
                           const jsonData = line.substring(6);
                           if (jsonData.trim() === '[DONE]') {
                               ws.send(JSON.stringify({ type: 'ai_suggestion_end' }));
                               // Stream ended, save complete suggestion to DB
                               if (accumulatedSuggestion.trim() && interviewContext.sessionId) {
                                   await prisma.aiSuggestion.create({
                                       data: {
                                           text: accumulatedSuggestion.trim(),
                                           interviewSession: { connect: { id: interviewContext.sessionId } },
                                       }
                                   });
                               }
                               return;
                           }
                           try {
                               const parsed = JSON.parse(jsonData);
                               if (parsed.choices && parsed.choices[0].delta && parsed.choices[0].delta.content) {
                                   const contentChunk = parsed.choices[0].delta.content;
                                   accumulatedSuggestion += contentChunk;
                                   ws.send(JSON.stringify({ type: 'ai_suggestion_chunk', text: contentChunk }));
                               }
                           } catch (e) {
                               console.error('Error parsing DeepSeek stream chunk:', e, jsonData);
                           }
                       }
                   }
               }
           } catch (error: any) {
               console.error("DeepSeek LLM Error:", error.response ? error.response.data : error.message);
               ws.send(JSON.stringify({ type: 'ai_suggestion_error', message: 'Failed to get AI suggestion.' }));
           }
       }
       ```

     - Call `getDeepSeekSuggestion(asrResult, { sessionId: currentInterviewSessionId /* ...other context */ });` after successful ASR.

5. **管理 InterviewSession (Manage InterviewSession)**

   - (同V1清单，确保在连接建立或 `start_interview` 时创建/获取 `interviewSessionId`，并在后续操作中使用)
   - (Same as V1 checklist, ensure `interviewSessionId` is created/retrieved on connection or `start_interview`, and used in subsequent operations)

------

**阶段二：前端 - 音频采集与实时展示 (Phase 2: Frontend - Audio Capture & Real-time Display)**

- **主要文件 (Main Files): `local-mianshijun/frontend/src/pages/AIInterviewPage.tsx`, `local-mianshijun/frontend/src/hooks/useInterviewSession.ts`**

1. **配置 WebSocket URL (Configure WebSocket URL)**
   - (同V1清单)
   - (Same as V1 checklist)
2. **实现屏幕/系统音频捕获 (Implement Screen/System Audio Capture)**
   - **文件 (File): `local-mianshijun/frontend/src/hooks/useInterviewSession.ts`**
   - 中文：使用 `MediaRecorder`，`mimeType: 'audio/webm;codecs=opus'`，`timeslice` 建议 `1000` 到 `2000` 毫秒。
   - English: Use `MediaRecorder` with `mimeType: 'audio/webm;codecs=opus'`. A `timeslice` between `1000`ms and `2000`ms is recommended.
3. **发送音频数据到后端 (Send Audio Data to Backend)**
   - (同V2清单，确保直接发送 `Blob` 数据)
   - (Same as V2 checklist, ensure direct sending of `Blob` data)
4. **接收并展示转录和AI建议 (Receive and Display Transcriptions & AI Suggestions)**
   - **文件 (Files): `local-mianshijun/frontend/src/hooks/useInterviewSession.ts`, `local-mianshijun/frontend/src/pages/AIInterviewPage.tsx`**
   - 中文：务必处理后端流式LLM的 `ai_suggestion_chunk`, `ai_suggestion_end`, 和 `ai_suggestion_error` 消息，以动态更新UI。
   - English: Crucially handle `ai_suggestion_chunk`, `ai_suggestion_end`, and `ai_suggestion_error` messages from the backend for streaming LLM responses to dynamically update the UI.
5. **开始/结束面试逻辑 (Start/End Interview Logic)**
   - (同V1清单)
   - (Same as V1 checklist)

------

**阶段三：集成测试与本地运行 (Phase 3: Integration Testing & Local Run)**

1. **启动后端服务 (Start Backend Service)** (同V1清单)
2. **启动前端服务 (Start Frontend Service)** (同V1清单)
3. 端到端测试 (End-to-End Testing)
   - 中文：重点测试OpenAI Whisper的识别效果。测试DeepSeek LLM流式建议的显示。
   - English: Focus on testing OpenAI Whisper's recognition performance. Test the display of DeepSeek LLM's streamed suggestions.

------

**阶段四：Linux 服务器部署 (Phase 4: Linux Server Deployment)**

1. **部署后端 (Deploy Backend)**
   - (同V2清单，**强调在服务器上使用环境变量配置API密钥，而不是 `.env` 文件**)
   - (Same as V2 checklist, **emphasize using server environment variables for API keys, not the `.env` file**)
2. **部署前端 (Deploy Frontend)**
   - (同V1清单，注意生产WebSocket URL的配置)
   - (Same as V1 checklist, ensure production WebSocket URL is correctly configured)
3. **最终测试 (Final Testing)** (同V1清单)

------

这份V3清单优先了OpenAI Whisper，并为其他ASR服务预留了集成点。DeepSeek的流式输出是提升用户体验的关键。祝您开发顺利！