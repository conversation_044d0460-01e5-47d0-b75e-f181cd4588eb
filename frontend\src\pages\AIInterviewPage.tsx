import React, { useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import AIInterviewHeader, { type TabType } from '../components/interview/AIInterviewHeader';
import VideoPlayerSection from '../components/interview/VideoPlayerSection';
import InterviewConfigForm from '../components/interview/InterviewConfigForm';
import InterviewRecordsPage from '../components/interview/InterviewRecordsPage';
import useDocumentTitle from '../hooks/useDocumentTitle';

const AIInterviewPage: React.FC = () => {
  // 设置页面标题
  useDocumentTitle('AI正式面试');

  const [searchParams, setSearchParams] = useSearchParams();
  const [activeTab, setActiveTab] = useState<TabType>(
    (searchParams.get('tab') as TabType) || 'interview'
  );

  const handleTabChange = (tab: TabType) => {
    setActiveTab(tab);
    setSearchParams({ tab });
  };

  const renderContent = () => {
    if (activeTab === 'records') {
      return <InterviewRecordsPage />;
    }

    return (
      <div className="h-full flex p-4 gap-4">
        <VideoPlayerSection />
        <InterviewConfigForm />
      </div>
    );
  };

  return (
    <div className="h-full flex flex-col">
      <AIInterviewHeader activeTab={activeTab} onTabChange={handleTabChange} />
      <div className="flex-1 overflow-hidden">
        {renderContent()}
      </div>
    </div>
  );
};

export default AIInterviewPage;
