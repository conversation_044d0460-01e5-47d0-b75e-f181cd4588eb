import React, { useState, useEffect, useCallback } from 'react';
import { CheckCircle, AlertCircle, AlertTriangle, Info, X } from 'lucide-react';
import type { Toast as ToastType } from '../../types/toast';

interface ToastProps {
  toast: ToastType;
  onRemove: (id: string) => void;
}

const Toast: React.FC<ToastProps> = ({ toast, onRemove }) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isLeaving, setIsLeaving] = useState(false);

  useEffect(() => {
    // 进入动画
    const timer = setTimeout(() => setIsVisible(true), 10);
    return () => clearTimeout(timer);
  }, []);

  const handleClose = useCallback(() => {
    setIsLeaving(true);
    setTimeout(() => onRemove(toast.id), 300);
  }, [onRemove, toast.id]);

  // 自动关闭
  useEffect(() => {
    if (toast.autoClose && toast.duration && toast.duration > 0) {
      const timer = setTimeout(handleClose, toast.duration);
      return () => clearTimeout(timer);
    }
  }, [toast.autoClose, toast.duration, handleClose]);

  // 获取样式配置
  const getToastConfig = () => {
    switch (toast.type) {
      case 'success':
        return {
          bgColor: 'bg-green-50 dark:bg-green-900/20',
          borderColor: 'border-green-200 dark:border-green-700',
          textColor: 'text-green-800 dark:text-green-200',
          iconColor: 'text-green-500 dark:text-green-400',
          icon: CheckCircle
        };
      case 'error':
        return {
          bgColor: 'bg-red-50 dark:bg-red-900/20',
          borderColor: 'border-red-200 dark:border-red-700',
          textColor: 'text-red-800 dark:text-red-200',
          iconColor: 'text-red-500 dark:text-red-400',
          icon: AlertCircle
        };
      case 'warning':
        return {
          bgColor: 'bg-yellow-50 dark:bg-yellow-900/20',
          borderColor: 'border-yellow-200 dark:border-yellow-700',
          textColor: 'text-yellow-800 dark:text-yellow-200',
          iconColor: 'text-yellow-500 dark:text-yellow-400',
          icon: AlertTriangle
        };
      case 'info':
      default:
        return {
          bgColor: 'bg-blue-50 dark:bg-blue-900/20',
          borderColor: 'border-blue-200 dark:border-blue-700',
          textColor: 'text-blue-800 dark:text-blue-200',
          iconColor: 'text-blue-500 dark:text-blue-400',
          icon: Info
        };
    }
  };

  const config = getToastConfig();
  const Icon = config.icon;

  return (
    <div
      className={`
        relative flex items-start gap-3 p-4 rounded-lg border shadow-lg max-w-md w-full
        ${config.bgColor} ${config.borderColor}
        transform transition-all duration-300 ease-in-out
        ${isVisible && !isLeaving ? 'translate-x-0 opacity-100' : 'translate-x-full opacity-0'}
      `}
    >
      {/* 图标 */}
      <div className="flex-shrink-0">
        <Icon size={20} className={config.iconColor} />
      </div>

      {/* 内容 */}
      <div className="flex-1 min-w-0">
        {toast.title && (
          <h4 className={`font-medium ${config.textColor} mb-1`}>
            {toast.title}
          </h4>
        )}
        <p className={`text-sm ${config.textColor} leading-relaxed`}>
          {toast.message}
        </p>
      </div>

      {/* 关闭按钮 */}
      <button
        onClick={handleClose}
        className={`
          flex-shrink-0 p-1 rounded-md transition-colors
          ${config.textColor} hover:bg-black hover:bg-opacity-10 dark:hover:bg-white dark:hover:bg-opacity-10
        `}
      >
        <X size={16} />
      </button>

      {/* 进度条（如果有持续时间） */}
      {toast.autoClose && toast.duration && toast.duration > 0 && (
        <div className="absolute bottom-0 left-0 right-0 h-1 bg-black bg-opacity-10 dark:bg-white dark:bg-opacity-10 rounded-b-lg overflow-hidden">
          <div
            className={`h-full ${config.iconColor.replace('text-', 'bg-')}`}
            style={{
              width: '100%',
              animation: `toast-progress ${toast.duration}ms linear forwards`
            }}
          />
        </div>
      )}
    </div>
  );
};

export default Toast;
