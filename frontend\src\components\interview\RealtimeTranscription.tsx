import React, { useState, useEffect, useRef, useCallback } from 'react';
import { EnhancedTextDisplay, TextUpdate } from './EnhancedTextDisplay';
import VADStatusIndicator from './VADStatusIndicator';
import { VADStatus } from '../../hooks/useVAD';

export interface TranscriptionMessage {
  type: 'transcription';
  text: string;
  confidence: number;
  timestamp: number;
  service: string;
  isPartial: boolean;
  segmentId: string;
  processingTime?: number;
  metadata?: any;
}

export interface RealtimeTranscriptionProps {
  vadStatus: VADStatus;
  isListening: boolean;
  onToggleListening: () => void;
  className?: string;
  showAdvancedControls?: boolean;
  showDebugInfo?: boolean;
}

/**
 * 实时转录显示组件
 * 集成VAD状态、文本显示和控制功能
 */
export const RealtimeTranscription: React.FC<RealtimeTranscriptionProps> = ({
  vadStatus,
  isListening,
  onToggleListening,
  className = '',
  showAdvancedControls = false,
  showDebugInfo = false
}) => {
  const [transcriptionHistory, setTranscriptionHistory] = useState<TextUpdate[]>([]);
  const [currentText, setCurrentText] = useState('');
  const [isReceivingData, setIsReceivingData] = useState(false);
  const [lastUpdateTime, setLastUpdateTime] = useState<number>(0);
  const [statistics, setStatistics] = useState({
    totalMessages: 0,
    averageConfidence: 0,
    averageProcessingTime: 0,
    serviceDistribution: {} as Record<string, number>
  });

  const textDisplayRef = useRef<any>(null);
  const wsRef = useRef<WebSocket | null>(null);

  /**
   * 处理WebSocket消息
   */
  const handleWebSocketMessage = useCallback((event: MessageEvent) => {
    try {
      const message = JSON.parse(event.data);
      
      if (message.type === 'transcription') {
        const transcriptionMessage: TranscriptionMessage = message;
        
        console.log('📨 Received transcription:', {
          text: transcriptionMessage.text.substring(0, 50) + '...',
          confidence: transcriptionMessage.confidence,
          service: transcriptionMessage.service,
          isPartial: transcriptionMessage.isPartial
        });

        // 创建文本更新对象
        const textUpdate: TextUpdate = {
          id: transcriptionMessage.segmentId || `update-${Date.now()}`,
          text: transcriptionMessage.text,
          confidence: transcriptionMessage.confidence,
          timestamp: transcriptionMessage.timestamp || Date.now(),
          isPartial: transcriptionMessage.isPartial,
          service: transcriptionMessage.service,
          processingTime: transcriptionMessage.processingTime
        };

        // 更新显示
        if (textDisplayRef.current?.addTextUpdate) {
          textDisplayRef.current.addTextUpdate(textUpdate);
        }

        // 更新历史记录
        setTranscriptionHistory(prev => {
          const newHistory = [...prev, textUpdate];
          return newHistory.slice(-50); // 保持最近50条记录
        });

        // 更新统计信息
        updateStatistics(textUpdate);
        
        // 设置接收数据状态
        setIsReceivingData(true);
        setLastUpdateTime(Date.now());
        
        // 3秒后重置接收状态
        setTimeout(() => setIsReceivingData(false), 3000);
      }
    } catch (error) {
      console.error('Error parsing WebSocket message:', error);
    }
  }, []);

  /**
   * 更新统计信息
   */
  const updateStatistics = useCallback((update: TextUpdate) => {
    setStatistics(prev => {
      const newStats = { ...prev };
      
      newStats.totalMessages++;
      
      // 更新平均置信度
      newStats.averageConfidence = (
        (prev.averageConfidence * (prev.totalMessages - 1) + update.confidence) / 
        prev.totalMessages
      );
      
      // 更新平均处理时间
      if (update.processingTime) {
        newStats.averageProcessingTime = (
          (prev.averageProcessingTime * (prev.totalMessages - 1) + update.processingTime) / 
          prev.totalMessages
        );
      }
      
      // 更新服务分布
      const service = update.service || 'unknown';
      newStats.serviceDistribution = {
        ...prev.serviceDistribution,
        [service]: (prev.serviceDistribution[service] || 0) + 1
      };
      
      return newStats;
    });
  }, []);

  /**
   * 获取监听按钮样式
   */
  const getListeningButtonStyle = () => {
    if (!vadStatus.isInitialized) {
      return 'bg-gray-400 cursor-not-allowed';
    }
    if (isListening) {
      return 'bg-red-500 hover:bg-red-600 animate-pulse';
    }
    return 'bg-blue-500 hover:bg-blue-600';
  };

  /**
   * 获取监听按钮文本
   */
  const getListeningButtonText = () => {
    if (!vadStatus.isInitialized) return '初始化中...';
    if (isListening) return '停止录音';
    return '开始录音';
  };

  /**
   * 清空转录历史
   */
  const clearTranscriptionHistory = useCallback(() => {
    setTranscriptionHistory([]);
    setCurrentText('');
    setStatistics({
      totalMessages: 0,
      averageConfidence: 0,
      averageProcessingTime: 0,
      serviceDistribution: {}
    });
    
    if (textDisplayRef.current?.clearText) {
      textDisplayRef.current.clearText();
    }
  }, []);

  /**
   * 导出转录结果
   */
  const exportTranscription = useCallback(() => {
    const exportData = {
      timestamp: new Date().toISOString(),
      statistics,
      transcriptionHistory: transcriptionHistory.map(item => ({
        text: item.text,
        confidence: item.confidence,
        timestamp: new Date(item.timestamp).toISOString(),
        service: item.service,
        isPartial: item.isPartial
      })),
      finalText: currentText
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { 
      type: 'application/json' 
    });
    
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `transcription-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, [statistics, transcriptionHistory, currentText]);

  return (
    <div className={`realtime-transcription space-y-4 ${className}`}>
      {/* 标题和控制区域 */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-800">实时语音转录</h3>
        
        <div className="flex items-center gap-3">
          {/* 数据接收指示器 */}
          {isReceivingData && (
            <div className="flex items-center gap-1 text-xs text-green-600">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span>接收中</span>
            </div>
          )}
          
          {/* 主要控制按钮 */}
          <button
            onClick={onToggleListening}
            disabled={!vadStatus.isInitialized}
            className={`px-4 py-2 text-white rounded-lg font-medium transition-all duration-200 ${getListeningButtonStyle()}`}
          >
            {getListeningButtonText()}
          </button>
        </div>
      </div>

      {/* VAD状态指示器 */}
      <VADStatusIndicator 
        vadStatus={vadStatus}
        showDetails={showDebugInfo}
        showAnimation={true}
        className="w-full"
      />

      {/* 主要文本显示区域 */}
      <EnhancedTextDisplay
        ref={textDisplayRef}
        className="w-full"
        placeholder={
          !vadStatus.isInitialized ? "正在初始化语音识别系统..." :
          !isListening ? "点击开始录音按钮开始语音识别" :
          "正在监听，请开始说话..."
        }
        showConfidence={true}
        showMetadata={showDebugInfo}
        animationSpeed={30}
        maxDisplayLength={2000}
        onTextUpdate={setCurrentText}
      />

      {/* 高级控制面板 */}
      {showAdvancedControls && (
        <div className="bg-gray-50 rounded-lg p-4 space-y-3">
          <div className="flex items-center justify-between">
            <h4 className="text-sm font-medium text-gray-700">高级控制</h4>
            <div className="flex gap-2">
              <button
                onClick={clearTranscriptionHistory}
                className="px-3 py-1 text-xs text-gray-600 border border-gray-300 rounded hover:bg-white transition-colors"
              >
                清空历史
              </button>
              <button
                onClick={exportTranscription}
                disabled={transcriptionHistory.length === 0}
                className="px-3 py-1 text-xs text-blue-600 border border-blue-300 rounded hover:bg-blue-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                导出结果
              </button>
            </div>
          </div>

          {/* 统计信息 */}
          {statistics.totalMessages > 0 && (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3 text-xs">
              <div className="bg-white rounded p-2">
                <div className="text-gray-500">总消息数</div>
                <div className="font-semibold text-lg">{statistics.totalMessages}</div>
              </div>
              <div className="bg-white rounded p-2">
                <div className="text-gray-500">平均置信度</div>
                <div className="font-semibold text-lg">
                  {Math.round(statistics.averageConfidence * 100)}%
                </div>
              </div>
              <div className="bg-white rounded p-2">
                <div className="text-gray-500">平均处理时间</div>
                <div className="font-semibold text-lg">
                  {Math.round(statistics.averageProcessingTime)}ms
                </div>
              </div>
              <div className="bg-white rounded p-2">
                <div className="text-gray-500">最后更新</div>
                <div className="font-semibold text-lg">
                  {lastUpdateTime > 0 ? `${Math.round((Date.now() - lastUpdateTime) / 1000)}s前` : '-'}
                </div>
              </div>
            </div>
          )}

          {/* 服务分布 */}
          {Object.keys(statistics.serviceDistribution).length > 0 && (
            <div className="bg-white rounded p-2">
              <div className="text-xs text-gray-500 mb-2">服务使用分布</div>
              <div className="flex flex-wrap gap-2">
                {Object.entries(statistics.serviceDistribution).map(([service, count]) => (
                  <span 
                    key={service}
                    className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs"
                  >
                    {service}: {count}
                  </span>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {/* 调试信息 */}
      {showDebugInfo && transcriptionHistory.length > 0 && (
        <details className="bg-gray-50 rounded-lg p-4">
          <summary className="text-sm font-medium text-gray-700 cursor-pointer">
            调试信息 (最近10条)
          </summary>
          <div className="mt-2 space-y-2 max-h-40 overflow-y-auto">
            {transcriptionHistory.slice(-10).map((item, index) => (
              <div key={item.id} className="bg-white rounded p-2 text-xs">
                <div className="flex justify-between items-start mb-1">
                  <span className="font-medium">#{transcriptionHistory.length - 10 + index + 1}</span>
                  <span className={`px-1 rounded ${
                    item.confidence >= 0.8 ? 'bg-green-100 text-green-800' :
                    item.confidence >= 0.6 ? 'bg-yellow-100 text-yellow-800' :
                    'bg-red-100 text-red-800'
                  }`}>
                    {Math.round(item.confidence * 100)}%
                  </span>
                </div>
                <div className="text-gray-600 mb-1">
                  {item.text.substring(0, 100)}...
                </div>
                <div className="flex justify-between text-gray-500">
                  <span>{item.service} | {item.isPartial ? '部分' : '最终'}</span>
                  <span>{new Date(item.timestamp).toLocaleTimeString()}</span>
                </div>
              </div>
            ))}
          </div>
        </details>
      )}
    </div>
  );
};

export default RealtimeTranscription;
