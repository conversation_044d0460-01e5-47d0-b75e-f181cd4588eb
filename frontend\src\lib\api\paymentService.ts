// 导入 fetchWithAuth 函数来进行API调用
import useAuthStore from '../../stores/authStore';

// API 基础URL
const API_BASE_URL = process.env.NODE_ENV === 'production'
  ? 'https://mianshijun.xyz/api'
  : '/api';

// 认证API调用函数
async function fetchWithAuth<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
  const token = useAuthStore.getState().token;

  const headers: Record<string, string> = {
    ...options.headers as Record<string, string>,
  };

  if (token) {
    headers['Authorization'] = `Bearer ${token}`;
  }

  if (options.body && typeof options.body === 'object') {
    headers['Content-Type'] = 'application/json';
    options.body = JSON.stringify(options.body);
  }

  const response = await fetch(`${API_BASE_URL}${endpoint}`, {
    ...options,
    headers,
  });

  if (!response.ok) {
    let errorData;
    try {
      errorData = await response.json();
    } catch {
      errorData = { message: response.statusText };
    }
    throw new Error(errorData?.message || `API请求失败，状态码: ${response.status}`);
  }

  if (response.status === 204) {
    return null as T;
  }

  return response.json();
}

// 订单创建请求接口
export interface CreateOrderRequest {
  itemId: string;
  amount: number;
  paymentMethod: 'ALIPAY' | 'WECHATPAY';
  itemDescription?: string;
}

// 订单响应接口
export interface OrderResponse {
  success: boolean;
  orderId: string;
  message: string;
  order: {
    id: string;
    amount: number;
    status: string;
    paymentMethod: string;
    itemDescription: string;
    createdAt: string;
  };
}

// 用户余额响应接口
export interface UserCreditsResponse {
  success: boolean;
  credits: {
    mockInterviewCredits: number;
    formalInterviewCredits: number;
    mianshijunBalance: number;
    updatedAt: string;
  };
}

// 订单列表响应接口
export interface OrderListResponse {
  success: boolean;
  orders: Array<{
    id: string;
    amount: number;
    status: string;
    paymentMethod: string;
    itemDescription: string;
    createdAt: string;
    updatedAt: string;
  }>;
}

// 单个订单详情响应接口
export interface OrderDetailResponse {
  success: boolean;
  order: {
    id: string;
    amount: number;
    status: string;
    paymentMethod: string;
    itemId: string;
    itemDescription: string;
    transactionId: string | null;
    createdAt: string;
    updatedAt: string;
  };
}

/**
 * 创建订单
 * @param orderDetails 订单详情
 * @returns 创建的订单信息
 */
export const createOrder = async (orderDetails: CreateOrderRequest): Promise<OrderResponse> => {
  try {
    const response = await fetchWithAuth<OrderResponse>('/orders/create', {
      method: 'POST',
      body: orderDetails
    });
    return response;
  } catch (error: any) {
    console.error('创建订单失败:', error);
    throw new Error(error.message || '创建订单失败，请稍后再试');
  }
};

/**
 * 获取用户余额信息
 * @returns 用户余额信息
 */
export const getUserCredits = async (): Promise<UserCreditsResponse> => {
  try {
    const response = await fetchWithAuth<UserCreditsResponse>('/users/me/credits', {
      method: 'GET'
    });
    return response;
  } catch (error: any) {
    console.error('获取用户余额失败:', error);
    throw new Error(error.message || '获取用户余额失败，请稍后再试');
  }
};

/**
 * 获取用户订单列表
 * @returns 订单列表
 */
export const getUserOrders = async (): Promise<OrderListResponse> => {
  try {
    const response = await fetchWithAuth<OrderListResponse>('/orders', {
      method: 'GET'
    });
    return response;
  } catch (error: any) {
    console.error('获取订单列表失败:', error);
    throw new Error(error.message || '获取订单列表失败，请稍后再试');
  }
};

/**
 * 获取订单详情
 * @param orderId 订单ID
 * @returns 订单详情
 */
export const getOrderDetail = async (orderId: string): Promise<OrderDetailResponse> => {
  try {
    const response = await fetchWithAuth<OrderDetailResponse>(`/orders/${orderId}`, {
      method: 'GET'
    });
    return response;
  } catch (error: any) {
    console.error('获取订单详情失败:', error);
    throw new Error(error.message || '获取订单详情失败，请稍后再试');
  }
};

/**
 * 模拟支付状态检查（实际项目中应该调用真实的支付接口）
 * @param orderId 订单ID
 * @returns 支付状态
 */
export const checkPaymentStatus = async (orderId: string): Promise<{ status: 'pending' | 'success' | 'failed' }> => {
  try {
    // 这里应该调用真实的支付宝或微信支付状态查询接口
    // 目前模拟返回状态
    const response = await getOrderDetail(orderId);
    
    // 根据订单状态映射支付状态
    const statusMap: { [key: string]: 'pending' | 'success' | 'failed' } = {
      'PENDING': 'pending',
      'COMPLETED': 'success',
      'FAILED': 'failed',
      'CANCELLED': 'failed'
    };
    
    return {
      status: statusMap[response.order.status] || 'pending'
    };
  } catch (error: any) {
    console.error('检查支付状态失败:', error);
    throw new Error(error.response?.data?.message || '检查支付状态失败，请稍后再试');
  }
};

/**
 * 生成支付二维码URL（模拟）
 * @param orderId 订单ID
 * @param paymentMethod 支付方式
 * @returns 二维码URL
 */
export const generatePaymentQR = async (orderId: string, paymentMethod: 'ALIPAY' | 'WECHATPAY'): Promise<string> => {
  try {
    // 实际项目中这里应该调用支付宝或微信的API生成真实的支付二维码
    // 目前返回模拟的二维码数据
    const qrData = `${paymentMethod}_PAYMENT_${orderId}_${Date.now()}`;
    return `data:image/svg+xml;base64,${btoa(`<svg xmlns="http://www.w3.org/2000/svg" width="200" height="200"><rect width="200" height="200" fill="white"/><text x="100" y="100" text-anchor="middle" font-size="12">${qrData}</text></svg>`)}`;
  } catch (error: any) {
    console.error('生成支付二维码失败:', error);
    throw new Error('生成支付二维码失败，请稍后再试');
  }
};

// 导出所有支付相关的API函数
export const paymentService = {
  createOrder,
  getUserCredits,
  getUserOrders,
  getOrderDetail,
  checkPaymentStatus,
  generatePaymentQR
};
