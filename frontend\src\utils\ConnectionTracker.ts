/**
 * 全局连接追踪器 - 诊断连接创建源头
 * 用于追踪所有WebSocket连接的创建、使用和销毁
 */

export interface ConnectionInfo {
  id: string;
  type: 'websocket' | 'asr' | 'audio';
  source: string; // 创建来源
  stack: string; // 调用栈
  timestamp: number;
  status: 'creating' | 'connected' | 'disconnected' | 'error';
  config?: any;
  lastActivity?: number;
  metadata?: Record<string, any>;
}

export interface ConnectionReport {
  totalConnections: number;
  activeConnections: number;
  connectionsByType: Record<string, number>;
  connectionsBySource: Record<string, number>;
  duplicateConnections: ConnectionInfo[];
  suspiciousConnections: ConnectionInfo[];
  timeline: ConnectionInfo[];
}

class ConnectionTracker {
  private static instance: ConnectionTracker;
  private connections: Map<string, ConnectionInfo> = new Map();
  private isEnabled: boolean = true;

  private constructor() {
    // 在开发环境启用详细追踪
    this.isEnabled = process.env.NODE_ENV === 'development';
    
    if (this.isEnabled) {
      console.log('🔍 ConnectionTracker: Initialized (Development Mode)');
      
      // 定期生成报告
      setInterval(() => {
        this.generatePeriodicReport();
      }, 30000); // 每30秒
    }
  }

  public static getInstance(): ConnectionTracker {
    if (!ConnectionTracker.instance) {
      ConnectionTracker.instance = new ConnectionTracker();
    }
    return ConnectionTracker.instance;
  }

  /**
   * 追踪连接创建
   */
  public trackConnection(
    id: string,
    type: 'websocket' | 'asr' | 'audio',
    source: string,
    config?: any
  ): void {
    if (!this.isEnabled) return;

    const stack = this.captureStack();
    const connectionInfo: ConnectionInfo = {
      id,
      type,
      source,
      stack,
      timestamp: Date.now(),
      status: 'creating',
      config,
      metadata: {
        userAgent: navigator.userAgent,
        url: window.location.href,
        sessionStorage: !!sessionStorage.getItem('interview_session')
      }
    };

    this.connections.set(id, connectionInfo);
    
    console.log(`🔍 ConnectionTracker: New ${type} connection tracked`, {
      id,
      source,
      total: this.connections.size
    });

    // 检查是否有重复连接
    this.checkForDuplicates(connectionInfo);
  }

  /**
   * 更新连接状态
   */
  public updateConnectionStatus(
    id: string,
    status: ConnectionInfo['status'],
    metadata?: Record<string, any>
  ): void {
    if (!this.isEnabled) return;

    const connection = this.connections.get(id);
    if (connection) {
      connection.status = status;
      connection.lastActivity = Date.now();
      if (metadata) {
        connection.metadata = { ...connection.metadata, ...metadata };
      }
      
      console.log(`🔍 ConnectionTracker: Connection ${id} status updated to ${status}`);
    }
  }

  /**
   * 移除连接追踪
   */
  public removeConnection(id: string): void {
    if (!this.isEnabled) return;

    const connection = this.connections.get(id);
    if (connection) {
      connection.status = 'disconnected';
      console.log(`🔍 ConnectionTracker: Connection ${id} removed`);
      
      // 保留一段时间用于分析，然后删除
      setTimeout(() => {
        this.connections.delete(id);
      }, 60000); // 1分钟后删除
    }
  }

  /**
   * 生成连接报告
   */
  public generateReport(): ConnectionReport {
    const connections = Array.from(this.connections.values());
    const activeConnections = connections.filter(c => c.status === 'connected');
    
    const connectionsByType: Record<string, number> = {};
    const connectionsBySource: Record<string, number> = {};
    
    connections.forEach(conn => {
      connectionsByType[conn.type] = (connectionsByType[conn.type] || 0) + 1;
      connectionsBySource[conn.source] = (connectionsBySource[conn.source] || 0) + 1;
    });

    // 检测重复连接
    const duplicateConnections = this.findDuplicateConnections();
    
    // 检测可疑连接
    const suspiciousConnections = this.findSuspiciousConnections();

    return {
      totalConnections: connections.length,
      activeConnections: activeConnections.length,
      connectionsByType,
      connectionsBySource,
      duplicateConnections,
      suspiciousConnections,
      timeline: connections.sort((a, b) => a.timestamp - b.timestamp)
    };
  }

  /**
   * 获取所有连接信息
   */
  public getAllConnections(): ConnectionInfo[] {
    return Array.from(this.connections.values());
  }

  /**
   * 检查重复连接
   */
  private checkForDuplicates(newConnection: ConnectionInfo): void {
    const duplicates = Array.from(this.connections.values()).filter(conn => 
      conn.id !== newConnection.id &&
      conn.type === newConnection.type &&
      conn.source === newConnection.source &&
      conn.status !== 'disconnected'
    );

    if (duplicates.length > 0) {
      console.warn(`⚠️ ConnectionTracker: Potential duplicate connection detected!`, {
        new: newConnection,
        existing: duplicates
      });
    }
  }

  /**
   * 查找重复连接
   */
  private findDuplicateConnections(): ConnectionInfo[] {
    const connections = Array.from(this.connections.values());
    const duplicates: ConnectionInfo[] = [];
    
    const seen = new Set<string>();
    connections.forEach(conn => {
      const key = `${conn.type}_${conn.source}`;
      if (seen.has(key) && conn.status !== 'disconnected') {
        duplicates.push(conn);
      }
      seen.add(key);
    });
    
    return duplicates;
  }

  /**
   * 查找可疑连接
   */
  private findSuspiciousConnections(): ConnectionInfo[] {
    const connections = Array.from(this.connections.values());
    const now = Date.now();
    
    return connections.filter(conn => {
      // 连接时间过长
      const isOld = now - conn.timestamp > 5 * 60 * 1000; // 5分钟
      
      // 长时间无活动
      const isInactive = conn.lastActivity && (now - conn.lastActivity) > 2 * 60 * 1000; // 2分钟
      
      // 状态异常
      const hasAbnormalStatus = conn.status === 'creating' && (now - conn.timestamp) > 30000; // 30秒还在创建中
      
      return isOld || isInactive || hasAbnormalStatus;
    });
  }

  /**
   * 捕获调用栈
   */
  private captureStack(): string {
    try {
      throw new Error();
    } catch (e) {
      return (e as Error).stack?.split('\n').slice(2, 8).join('\n') || 'No stack available';
    }
  }

  /**
   * 定期报告
   */
  private generatePeriodicReport(): void {
    const report = this.generateReport();
    
    if (report.totalConnections > 0) {
      console.group('🔍 ConnectionTracker: Periodic Report');
      console.log('📊 Summary:', {
        total: report.totalConnections,
        active: report.activeConnections,
        byType: report.connectionsByType,
        bySource: report.connectionsBySource
      });
      
      if (report.duplicateConnections.length > 0) {
        console.warn('⚠️ Duplicate connections detected:', report.duplicateConnections);
      }
      
      if (report.suspiciousConnections.length > 0) {
        console.warn('🚨 Suspicious connections detected:', report.suspiciousConnections);
      }
      
      console.groupEnd();
    }
  }

  /**
   * 导出报告到控制台
   */
  public exportReport(): void {
    const report = this.generateReport();
    console.group('🔍 ConnectionTracker: Full Report');
    console.log('📊 Report Data:', report);
    console.log('📋 Timeline:', report.timeline);
    console.groupEnd();
  }

  /**
   * 清理所有追踪数据
   */
  public clearAll(): void {
    this.connections.clear();
    console.log('🔍 ConnectionTracker: All tracking data cleared');
  }
}

// 导出单例实例
export const connectionTracker = ConnectionTracker.getInstance();

// 全局调试接口
if (typeof window !== 'undefined') {
  (window as any).connectionTracker = connectionTracker;
}
