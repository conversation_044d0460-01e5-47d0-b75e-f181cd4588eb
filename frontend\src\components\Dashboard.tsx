import React from 'react';
import type { Position } from '@new-mianshijun/common';
import ResumeSection from './ResumeSection';
import PositionSection from './PositionSection';
import InterviewCard from './InterviewCard';
import useAuthStore from '../stores/authStore';
import useDocumentTitle from '../hooks/useDocumentTitle';

const Dashboard: React.FC = () => {
  // 设置页面标题
  useDocumentTitle('首页');

  const positions: Position[] = [];
  const maxPositions = 10;
  const { user } = useAuthStore();

  // 获取用户名称，优先显示name，如果没有则显示email的用户名部分
  const userName = user?.name || (user?.email ? user.email.split('@')[0] : '用户');

  return (
    <main className="h-full p-4">
      <div className="h-full grid grid-rows-[auto_auto_1fr_1fr] gap-4">
        {/* 用户欢迎信息 */}
        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700 transition-colors">
          <h2 className="text-xl font-semibold text-gray-800 dark:text-white">
            欢迎回来，{userName}！
          </h2>
          <p className="text-gray-600 dark:text-gray-300 mt-1">
            今天是 {new Date().toLocaleDateString('zh-CN', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })}，祝您面试顺利！
          </p>
        </div>

        <ResumeSection />

        <PositionSection
          positions={positions}
          maxPositions={maxPositions}
        />

        <div className="grid grid-cols-2 gap-4">
          <InterviewCard
            type="simulation"
            title="AI 模拟面试"
            subtitle="练习模式"
            description="通过 AI 模拟真实面试场景，获得即时反馈和建议，帮助你提升面试技巧和自信心。"
          />

          <InterviewCard
            type="formal"
            title="AI 正式面试"
            subtitle="专业模式"
            description="更专业的面试模拟体验，深入评估你的专业能力和表现。获得详细的分析报告和改进建议。"
          />
        </div>
      </div>
    </main>
  );
};

export default Dashboard;