好的，很乐意帮助你设计兑换码兑换功能的逻辑部分。下面是一份详细的操作清单，我会尽量描述清楚每一步，并提供相应的代码片段。考虑到你是“vibe coding”并且不熟悉技术细节，这份清单会侧重于直接的操作步骤。

请按照以下步骤操作：

------

**重要提示 (Important Notes):**

- **备份代码 (Backup Your Code):** 在进行任何修改之前，请务必备份你的项目文件夹，以防万一。
- **文件路径 (File Paths):** 我会提供文件路径。请确保你在项目 `basicprotein/local-mianshijun/` 下的正确位置找到并修改这些文件。
- **Prisma Studio:** 这款工具可以让你方便地查看和管理数据库。在修改数据库结构后，你可以用它来手动添加一些兑换码进行测试。
- **重启服务 (Restart Services):** 修改后端代码后，通常需要重启后端服务。修改前端代码后，前端开发服务器通常会自动刷新，但有时也需要手动刷新或重启。

------

**操作清单 (Action Checklist)**

第 1 部分：后端设置 (Backend Setup)

这部分包括修改数据库、创建API接口等。

步骤 1.1：更新用户数据模型 (Update User Data Model)

我们需要在用户数据中添加一个字段来存储他们的积分（或其他因兑换码获得的好处）。这里以积分为例。

1. 中文： 打开文件 backend/prisma/schema.prisma。

   English: Open the file backend/prisma/schema.prisma.

2. 中文： 找到 model User 的定义，在其中添加 points Int @default(0) 字段。

   English: Find the model User definition and add the points Int @default(0) field.

   修改后的 `User`模型部分应该看起来像这样 (The modified `User` model section should look like this):

   代码段

   ```
   model User {
     id             String            @id @default(uuid())
     email          String            @unique
     password       String
     name           String?
     avatarUrl      String?
     points         Int               @default(0) // <--- 新增这一行 (Add this line)
     createdAt      DateTime          @default(now())
     updatedAt      DateTime          @updatedAt
     resumes        Resume[]
     positions      Position[]
     interviews     Interview[]
     feedbacks      Feedback[]
     orders         Order[]
     interviewSessions InterviewSession[]
     // Keep other relations and fields as they are
     redeemedCodes  RedemptionCode[] // <--- 新增这一行，用于反向关联 (Add this line for reverse relation)
   }
   ```

步骤 1.2：创建兑换码数据模型 (Create Redemption Code Data Model)

我们需要一个新的数据表来存储兑换码及其状态。

1. 中文： 在 backend/prisma/schema.prisma 文件的末尾（或其他合适的位置），添加以下 RedemptionCode 模型定义。

   English: At the end of the backend/prisma/schema.prisma file (or another suitable place), add the following RedemptionCode model definition.

   代码段

   ```
   model RedemptionCode {
     id          String    @id @default(uuid())
     code        String    @unique // 兑换码本身 (The code itself)
     isUsed      Boolean   @default(false) // 是否已被使用 (Whether it has been used)
     createdAt   DateTime  @default(now())
     updatedAt   DateTime  @updatedAt
     expiresAt   DateTime? // 可选的过期时间 (Optional expiration date)
   
     // 描述兑换码的类型和值 (Describes the type and value of the code)
     // 例如: type="POINTS", value=100 表示奖励100积分
     // Example: type="POINTS", value=100 means 100 points reward
     benefitType String    @default("POINTS")
     benefitValue Int      @default(0)
   
     userId      String?   // 哪个用户使用了这个码 (Which user used this code)
     user        User?     @relation(fields: [userId], references: [id]) // 关联到User模型 (Relation to User model)
   
     @@index([userId]) // 为userId创建索引，如果需要经常查询用户兑换的码 (Index for userId if you query user's redeemed codes often)
   }
   ```

   - **中文：** 同时，确保你在 `User` 模型中添加了 `redeemedCodes RedemptionCode[]` (如步骤1.1所示) 来建立反向关联。 **English:** Also, ensure you've added `redeemedCodes RedemptionCode[]` in the `User` model (as shown in Step 1.1) for the reverse relation.

步骤 1.3：生成并运行数据库迁移 (Generate and Run Database Migration)

这些更改需要应用到你的数据库。

1. **中文：** 打开你的终端 (命令行工具)。 **English:** Open your terminal (command line tool).

2. **中文：** 导航到 `backend` 文件夹：`cd path/to/your/project/basicprotein/local-mianshijun/backend`。 **English:** Navigate to the `backend` folder: `cd path/to/your/project/basicprotein/local-mianshijun/backend`.

3. 中文：

    运行以下命令来生成迁移文件。系统会提示你为迁移命名，例如输入 

   ```
   add_redemption_codes
   ```

   。

   English:

    Run the following command to generate a migration file. You'll be prompted for a migration name, e.g., enter 

   ```
   add_redemption_codes
   ```

   .

   Bash

   ```
   npx prisma migrate dev --name add_redemption_codes
   ```

   - **中文：** 这条命令会自动格式化你的 `schema.prisma` 文件，生成一个新的 SQL 迁移文件，并将其应用到你的数据库。它还会重新生成 Prisma Client。 **English:** This command will automatically format your `schema.prisma` file, generate a new SQL migration file, and apply it to your database. It also regenerates the Prisma Client.
   - **中文：** 如果你只想生成迁移文件而不立即应用，可以使用 `npx prisma migrate diff` 和 `npx prisma db push` (或 `npx prisma migrate deploy` 用于生产环境) 等命令，但对于开发环境 `migrate dev` 通常是最简单的。 **English:** If you only want to generate migration files without applying them immediately, you can use commands like `npx prisma migrate diff` and `npx prisma db push` (or `npx prisma migrate deploy` for production), but `migrate dev` is usually the simplest for development.

**步骤 1.4：创建兑换码API路由和逻辑 (Create Redemption Code API Route and Logic)**

1. 中文： 在 backend 文件夹下创建一个新的文件夹，命名为 redeem。

   English: Create a new folder named redeem inside the backend folder.

2. 中文： 在 backend/redeem 文件夹中创建一个新文件，命名为 index.ts。

   English: Create a new file named index.ts inside the backend/redeem folder.

3. 中文： 将以下代码粘贴到 backend/redeem/index.ts 中：

   English: Paste the following code into backend/redeem/index.ts:

   TypeScript

   ```
   import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
   import { PrismaClient, RedemptionCode, User } from '@prisma/client'; // PrismaClient 和生成的类型 (PrismaClient and generated types)
   
   // 假设 prisma 实例是从 lib 文件夹导入的 (Assuming prisma instance is imported from lib)
   import prisma from '../lib/prisma';
   
   interface RedeemRequestBody {
     code: string;
   }
   
   export default async function redeemRoutes(server: FastifyInstance) {
     server.post(
       '/',
       {
         preHandler: [server.authenticate], // 确保用户已登录 (Ensure user is authenticated)
         schema: { // 可选：用于请求体验证的Schema (Optional: Schema for request validation)
           body: {
             type: 'object',
             required: ['code'],
             properties: {
               code: { type: 'string', minLength: 1 },
             },
           },
         },
       },
       async (request: FastifyRequest<{ Body: RedeemRequestBody }>, reply: FastifyReply) => {
         const userId = request.user?.id; // 从 authenticate 装饰器获取用户ID (Get user ID from authenticate decorator)
         const { code } = request.body;
   
         if (!userId) {
           return reply.status(401).send({ message: '用户未认证 (User not authenticated)' });
         }
   
         if (!code || code.trim() === '') {
           return reply.status(400).send({ message: '兑换码不能为空 (Redemption code cannot be empty)' });
         }
   
         try {
           // 使用事务确保原子性 (Use transaction for atomicity)
           const result = await prisma.$transaction(async (tx) => {
             const redemptionCode = await tx.redemptionCode.findUnique({
               where: { code: code.trim() },
             });
   
             if (!redemptionCode) {
               // 注意：为了安全，不要明确提示“无效”，而是用通用消息 (For security, don't explicitly say "invalid", use a generic message)
               // 但这里为了测试和开发方便，我们先用明确消息 (But for testing and development ease, we'll use a clear message for now)
               reply.status(404).send({ message: '兑换码无效或不存在 (Invalid or non-existent code)' });
               return null; // 中断事务 (Abort transaction)
             }
   
             if (redemptionCode.isUsed) {
               reply.status(400).send({ message: '此兑换码已被使用 (This code has already been used)' });
               return null;
             }
   
             if (redemptionCode.expiresAt && new Date() > new Date(redemptionCode.expiresAt)) {
               reply.status(400).send({ message: '此兑换码已过期 (This code has expired)' });
               return null;
             }
   
             // 标记兑换码为已使用 (Mark code as used)
             const updatedCode = await tx.redemptionCode.update({
               where: { id: redemptionCode.id },
               data: {
                 isUsed: true,
                 userId: userId,
               },
             });
   
             let updatedUser: User;
             // 根据兑换码类型应用权益 (Apply benefit based on code type)
             if (updatedCode.benefitType === 'POINTS') {
               updatedUser = await tx.user.update({
                 where: { id: userId },
                 data: {
                   points: {
                     increment: updatedCode.benefitValue, // 增加积分 (Increment points)
                   },
                 },
               });
             } else {
               // 在这里处理其他类型的权益，例如解锁功能、延长订阅等
               // (Handle other benefit types here, e.g., unlocking features, extending subscriptions)
               // 为了简单起见，如果不是 POINTS，我们暂时什么也不做，但记录下来
               // (For simplicity, if not POINTS, we do nothing for now but log it)
               console.warn(`Unhandled benefit type: ${updatedCode.benefitType} for code ${updatedCode.code}`);
               updatedUser = await tx.user.findUniqueOrThrow({ where: { id: userId } }); // 至少返回当前用户 (At least return current user)
             }
   
             // 确保 reply.send 只在事务成功后调用 (Ensure reply.send is only called after transaction success)
             // 如果中途 reply.send 被调用，则事务会回滚，但响应已发送，这是不好的
             // 正确做法是事务内只返回数据，事务外发送响应
             // (Correct way is to return data within transaction, and send response outside if transaction commits)
   
             return { updatedCode, updatedUser };
           });
   
           if (!result) {
               // 如果 result 为 null，说明事务中途 reply.send 已经被调用并中断
               // (If result is null, it means reply.send was called mid-transaction and aborted it)
               // 此时不需要再发送响应 (No need to send response again)
               return;
           }
   
           return reply.send({
             message: '兑换成功！(Redemption successful!)',
             benefitType: result.updatedCode.benefitType,
             benefitValue: result.updatedCode.benefitValue,
             newPoints: result.updatedUser.points, // 返回新的总积分 (Return new total points)
           });
   
         } catch (error) {
           console.error('兑换处理失败 (Redemption processing failed):', error);
           // 检查错误是否是因为 reply.send 已经被调用
           if (reply.sent) {
               return;
           }
           // 如果是 Prisma 事务错误或其他内部错误
           // (If it's a Prisma transaction error or other internal error)
           return reply.status(500).send({ message: '兑换失败，请稍后再试 (Redemption failed, please try again later)' });
         }
       }
     );
   }
   ```

**步骤 1.5：在主服务中注册API路由 (Register API Route in Main Server)**

1. 中文： 打开 backend/server.ts。

   English: Open backend/server.ts.

2. 中文： 导入并注册新的兑换码路由。

   English: Import and register the new redeem routes.

   在文件顶部添加导入 (Add import at the top of the file):

   TypeScript

   ```
   // ... 其他导入 (other imports)
   import redeemRoutes from './redeem'; // <--- 新增这一行 (Add this line)
   // ...
   ```

   在注册其他路由的地方添加 (Add where other routes are registered):

   TypeScript

   ```
   // ...
   server.register(userRoutes, { prefix: '/api/users' });
   server.register(interviewRoutes, { prefix: '/api/interviews' });
   // ... (其他已有的路由 Other existing routes)
   server.register(redeemRoutes, { prefix: '/api/redeem' }); // <--- 新增这一行 (Add this line)
   // ...
   ```

3. 中文： 重启你的后端服务以使更改生效。

   English: Restart your backend server for the changes to take effect. (通常在 backend 目录下运行类似 npm run dev 或 pnpm dev 的命令 - Usually run a command like npm run dev or pnpm dev in the backend directory).

------

第 2 部分：前端集成 (Frontend Integration)

这部分包括创建调用API的函数，并在你的兑换码页面中使用它。

**步骤 2.1：创建前端API服务函数 (Create Frontend API Service Function)**

1. 中文： 你可以在 frontend/src/lib/api/ 文件夹下创建一个新文件 redeem.ts，或者将函数添加到现有的服务文件中（例如 userService.ts 或 apiService.ts，但不推荐直接修改 apiService.ts）。这里我们创建一个新文件。

   English: You can create a new file redeem.ts in the frontend/src/lib/api/ folder, or add the function to an existing service file (e.g., userService.ts or apiService.ts, though directly modifying apiService.ts is not recommended). Here we create a new file.

2. 中文： 创建文件 frontend/src/lib/api/redeem.ts。

   English: Create the file frontend/src/lib/api/redeem.ts.

3. 中文： 将以下代码粘贴到 frontend/src/lib/api/redeem.ts 中：

   English: Paste the following code into frontend/src/lib/api/redeem.ts:

   TypeScript

   ```
   import apiService from './apiService'; // 假设你的 axios 实例在这里配置 (Assuming your axios instance is configured here)
   
   interface RedeemResponse {
     message: string;
     benefitType: string;
     benefitValue: number;
     newPoints?: number; // 根据后端返回的数据结构 (Based on backend response structure)
   }
   
   interface RedeemErrorResponse {
     message: string;
     // 可以添加其他错误相关的字段 (Can add other error-related fields)
   }
   
   export const redeemCodeApi = async (code: string): Promise<RedeemResponse> => {
     try {
       const response = await apiService.post<RedeemResponse>('/redeem', { code });
       return response.data;
     } catch (error: any) {
       // apiService 应该已经处理了基本的错误结构，这里可以进一步定制
       // (apiService should already handle basic error structure, further customization can be done here)
       const errorData: RedeemErrorResponse = error.response?.data || { message: '网络错误或未知错误 (Network or unknown error)' };
       throw errorData; // 抛出错误，让调用方处理 (Throw error for the caller to handle)
     }
   };
   ```

步骤 2.2：在兑换页面中实现逻辑 (Implement Logic in Redemption Page)

由于你说前端UI已经完成，你需要找到对应的UI文件。假设这个文件是 frontend/src/pages/RedeemCodePage.tsx (如果不是，请替换为你的实际文件名)。

1. 中文： 打开你的兑换码页面的前端组件文件 (例如 frontend/src/pages/RedeemCodePage.tsx)。

   English: Open your frontend component file for the redemption code page (e.g., frontend/src/pages/RedeemCodePage.tsx). If it's not this path, please use your actual file path.

2. 中文： 修改代码以包含处理兑换码的逻辑。

   English: Modify the code to include logic for handling the redemption code.

   TypeScript

   ```
   import React, { useState } from 'react';
   import { redeemCodeApi } from '../lib/api/redeem'; // 导入我们刚创建的API函数 (Import the API function we just created)
   import { useAuthStore } from '../stores/authStore'; // 用于获取用户信息或更新 (For getting/updating user info)
   import { useUserStore } from '../stores/userStore'; // 假设你用它来管理用户状态，如积分 (Assuming you use this for user state like points)
   
   // 假设你的UI组件已经有输入框和按钮 (Assuming your UI component already has an input field and a button)
   // 例如 (For example):
   // <input type="text" value={code} onChange={(e) => setCode(e.target.value)} />
   // <button onClick={handleRedeem} disabled={isLoading}>兑换 (Redeem)</button>
   // {message && <p>{message}</p>}
   
   const RedeemCodePage: React.FC = () => {
     const [code, setCode] = useState('');
     const [isLoading, setIsLoading] = useState(false);
     const [message, setMessage] = useState<string | null>(null); // 用于显示成功或错误消息 (For success or error messages)
   
     // 如果你需要更新Zustand store中的用户积分 (If you need to update user points in Zustand store)
     const updateUserPoints = useUserStore((state) => state.updateUserPoints); // 假设 userStore 中有这个action (Assuming this action exists in userStore)
     // 或者，如果用户信息在 authStore (Or if user info is in authStore)
     // const { user, setUser } = useAuthStore();
   
   
     const handleRedeem = async () => {
       if (!code.trim()) {
         setMessage('请输入兑换码 (Please enter a redemption code)');
         return;
       }
   
       setIsLoading(true);
       setMessage(null);
   
       try {
         const response = await redeemCodeApi(code.trim());
         setMessage(`兑换成功！获得 ${response.benefitValue} ${response.benefitType === 'POINTS' ? '积分' : response.benefitType}。(Successfully redeemed! Received ${response.benefitValue} ${response.benefitType === 'POINTS' ? 'points' : response.benefitType}.)`);
   
         // 更新用户状态 (Update user state)
         if (response.benefitType === 'POINTS' && response.newPoints !== undefined) {
            // 假设 userStore 有一个 updateUserPoints 方法来更新用户数据
            // (Assuming userStore has an updateUserPoints method to update user data)
            // 或者直接更新 authStore 里的用户信息
            // (Or directly update user info in authStore)
            // updateUserPoints(response.newPoints); 
            console.log('New total points:', response.newPoints);
   
            // 示例: 如果你的 userStore 存储整个用户对象并有一个 setUser 方法
            // (Example: If your userStore stores the whole user object and has a setUser method)
            const currentUser = useUserStore.getState().user;
            if (currentUser) {
                useUserStore.getState().setUser({ ...currentUser, points: response.newPoints });
            }
         }
         setCode(''); // 清空输入框 (Clear input field)
       } catch (error: any) {
         setMessage(error.message || '兑换失败，请检查兑换码或稍后再试。(Redemption failed. Please check the code or try again later.)');
         console.error('Redemption error:', error);
       } finally {
         setIsLoading(false);
       }
     };
   
     return (
       <div>
         <h1>兑换码 (Redeem Code)</h1>
         <input
           type="text"
           value={code}
           onChange={(e) => setCode(e.target.value)}
           placeholder="输入你的兑换码 (Enter your redemption code)"
           disabled={isLoading}
         />
         <button onClick={handleRedeem} disabled={isLoading || !code.trim()}>
           {isLoading ? '兑换中... (Redeeming...)' : '兑换 (Redeem)'}
         </button>
         {message && <p style={{ color: message.includes('成功') || message.includes('Successfully') ? 'green' : 'red' }}>{message}</p>}
   
         {/* 你现有的其他UI元素 (Your existing other UI elements) */}
       </div>
     );
   };
   
   export default RedeemCodePage;
   ```

   - **中文：** 你需要将上述代码中的 `useUserStore` 和 `updateUserPoints` (或者你用来管理用户状态的等效部分) 替换成你项目中实际的状态管理逻辑。如果你的用户积分直接显示在页面上，确保在兑换成功后更新这些显示。 **English:** You'll need to replace `useUserStore` and `updateUserPoints` (or the equivalent parts you use for managing user state) in the code above with the actual state management logic in your project. If user points are displayed directly on the page, ensure they are updated after successful redemption.

------

**第 3 部分：如何生成兑换码 (How to Generate Redemption Codes - Basic)**

目前我们没有创建专门的管理员界面来生成兑换码。对于初学者，最简单的方法是直接在数据库中添加。

1. **中文：** 打开终端，导航到 `backend` 文件夹。 **English:** Open your terminal, navigate to the `backend` folder.

2. **中文：** 运行 `npx prisma studio`。 **English:** Run `npx prisma studio`.

3. **中文：** 这会在浏览器中打开 Prisma Studio，一个数据库管理界面。 **English:** This will open Prisma Studio in your browser, a database management interface.

4. **中文：** 在 Prisma Studio 中，找到 `RedemptionCode` 模型（表）。 **English:** In Prisma Studio, find the `RedemptionCode` model (table).

5. 中文：

    点击 "Add record" 或类似按钮来添加新的兑换码。

   English:

    Click "Add record" or a similar button to add new redemption codes.

   - **`code`**: 输入你想设置的兑换码，例如 `WELCOME100`。 **`code`**: Enter the code you want to set, e.g., `WELCOME100`.
   - **`isUsed`**: 保持 `false` (未勾选)。 **`isUsed`**: Keep it `false` (unchecked).
   - **`benefitType`**: 例如输入 `POINTS`。 **`benefitType`**: E.g., enter `POINTS`.
   - **`benefitValue`**: 例如输入 `100` (如果 `benefitType` 是 `POINTS`，就表示100积分)。 **`benefitValue`**: E.g., enter `100` (if `benefitType` is `POINTS`, this means 100 points).
   - **`expiresAt`**: 可选，如果你想设置过期时间。 **`expiresAt`**: Optional, if you want to set an expiration date.
   - 其他字段可以暂时留空或使用默认值。 Other fields can be left blank or use default values for now.

6. **中文：** 保存记录。现在你就可以在前端页面尝试使用这个兑换码了。 **English:** Save the record. Now you can try using this code on your frontend page.

------

请一步步仔细操作。如果你在某个步骤遇到问题，可以随时告诉我具体是哪一步以及遇到的错误信息，我会尽力提供帮助。祝你编码顺利！