// 🔥 连接池管理器 - 简化版本
import { EventEmitter } from 'events';

interface ConnectionInfo {
  id: string;
  userId: string;
  sessionId: string;
  gatewayId: string;
  createdAt: number;
  lastActivity: number;
  status: 'active' | 'idle' | 'terminated';
}

/**
 * 🔥 连接池管理器 - 作为会话状态的单一真实来源
 */
export class ConnectionPool extends EventEmitter {
  private static instance: ConnectionPool;
  private connections: Map<string, ConnectionInfo> = new Map();
  private userConnections: Map<string, Set<string>> = new Map(); // userId -> connectionIds
  
  // 🔥 性能监控
  private stats = {
    totalConnections: 0,
    activeConnections: 0,
    terminatedConnections: 0,
    lastCleanup: 0,
    cleanupCount: 0
  };

  static getInstance(): ConnectionPool {
    if (!ConnectionPool.instance) {
      ConnectionPool.instance = new ConnectionPool();
    }
    return ConnectionPool.instance;
  }

  /**
   * 🔥 注册新连接
   */
  registerConnection(connectionId: string, userId: string, sessionId: string, gatewayId: string): void {
    const now = Date.now();
    
    // 检查用户是否已有连接
    const existingConnections = this.getUserConnections(userId);
    if (existingConnections.length > 0) {
      console.log(`🔄 ConnectionPool: User ${userId} has ${existingConnections.length} existing connections, terminating...`);
      
      // 终止现有连接
      existingConnections.forEach(connId => {
        this.terminateConnection(connId, 'New connection from same user');
      });
    }

    // 创建新连接记录
    const connectionInfo: ConnectionInfo = {
      id: connectionId,
      userId,
      sessionId,
      gatewayId,
      createdAt: now,
      lastActivity: now,
      status: 'active'
    };

    this.connections.set(connectionId, connectionInfo);
    
    // 更新用户连接映射
    if (!this.userConnections.has(userId)) {
      this.userConnections.set(userId, new Set());
    }
    this.userConnections.get(userId)!.add(connectionId);

    // 更新统计
    this.stats.totalConnections++;
    this.stats.activeConnections++;

    console.log(`📊 ConnectionPool: Connection registered`, {
      connectionId,
      userId,
      sessionId,
      gatewayId,
      totalConnections: this.stats.totalConnections,
      activeConnections: this.stats.activeConnections
    });

    // 发射连接注册事件
    this.emit('connection.registered', { connectionId, userId, sessionId, gatewayId });
  }

  /**
   * 🔥 终止连接
   */
  terminateConnection(connectionId: string, reason: string): void {
    const connection = this.connections.get(connectionId);
    if (!connection) {
      console.warn(`⚠️ ConnectionPool: Connection ${connectionId} not found for termination`);
      return;
    }

    // 更新连接状态
    connection.status = 'terminated';
    connection.lastActivity = Date.now();

    // 从用户连接映射中移除
    const userConnections = this.userConnections.get(connection.userId);
    if (userConnections) {
      userConnections.delete(connectionId);
      if (userConnections.size === 0) {
        this.userConnections.delete(connection.userId);
      }
    }

    // 更新统计
    this.stats.activeConnections--;
    this.stats.terminatedConnections++;

    console.log(`🔌 ConnectionPool: Connection terminated`, {
      connectionId,
      userId: connection.userId,
      reason,
      activeConnections: this.stats.activeConnections
    });

    // 发射连接终止事件
    this.emit('connection.terminated', { connectionId, userId: connection.userId, reason });
  }

  /**
   * 🔥 更新连接活动时间
   */
  updateActivity(connectionId: string): void {
    const connection = this.connections.get(connectionId);
    if (connection && connection.status === 'active') {
      connection.lastActivity = Date.now();
    }
  }

  /**
   * 🔥 获取用户的所有连接
   */
  getUserConnections(userId: string): string[] {
    const userConnections = this.userConnections.get(userId);
    return userConnections ? Array.from(userConnections) : [];
  }

  /**
   * 🔥 获取连接信息
   */
  getConnection(connectionId: string): ConnectionInfo | null {
    return this.connections.get(connectionId) || null;
  }

  /**
   * 🔥 清理过期连接
   */
  cleanupExpiredConnections(maxIdleTime: number = 30 * 60 * 1000): number {
    const now = Date.now();
    const expiredConnections: string[] = [];

    for (const [connectionId, connection] of this.connections.entries()) {
      if (connection.status === 'active') {
        const idleTime = now - connection.lastActivity;
        if (idleTime > maxIdleTime) {
          expiredConnections.push(connectionId);
        }
      }
    }

    // 终止过期连接
    expiredConnections.forEach(connectionId => {
      this.terminateConnection(connectionId, 'Connection expired due to inactivity');
    });

    // 清理已终止的连接记录（保留一段时间用于调试）
    const cleanupThreshold = now - (24 * 60 * 60 * 1000); // 24小时前
    let cleanedCount = 0;

    for (const [connectionId, connection] of this.connections.entries()) {
      if (connection.status === 'terminated' && connection.lastActivity < cleanupThreshold) {
        this.connections.delete(connectionId);
        cleanedCount++;
      }
    }

    this.stats.lastCleanup = now;
    this.stats.cleanupCount++;

    if (expiredConnections.length > 0 || cleanedCount > 0) {
      console.log(`🧹 ConnectionPool: Cleanup completed`, {
        expiredConnections: expiredConnections.length,
        cleanedRecords: cleanedCount,
        activeConnections: this.stats.activeConnections
      });
    }

    return expiredConnections.length + cleanedCount;
  }

  /**
   * 🔥 获取性能统计
   */
  getStats(): typeof this.stats & { timestamp: number } {
    return {
      ...this.stats,
      timestamp: Date.now()
    };
  }

  /**
   * 🔥 输出连接池状态
   */
  logStatus(): void {
    const stats = this.getStats();
    const activeConnections = Array.from(this.connections.values())
      .filter(conn => conn.status === 'active')
      .map(conn => ({
        id: conn.id,
        userId: conn.userId,
        sessionId: conn.sessionId,
        uptime: Date.now() - conn.createdAt,
        idleTime: Date.now() - conn.lastActivity
      }));

    console.log('📊 ConnectionPool Status:', {
      ...stats,
      activeConnectionsDetail: activeConnections
    });
  }
}
