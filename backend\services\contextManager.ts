import { ContextInfo } from '../types/asrTypes';

export interface ContextWindow {
  id: string;
  sessionId: string;
  content: string;
  timestamp: number;
  confidence: number;
  metadata?: {
    speaker?: string;
    topic?: string;
    sentiment?: string;
    keywords?: string[];
  };
}

export interface ContextConfig {
  maxWindowSize: number;        // 最大上下文窗口大小
  maxHistorySize: number;       // 最大历史记录大小
  contextTimeWindow: number;    // 上下文时间窗口(ms)
  relevanceThreshold: number;   // 相关性阈值
  keywordExtractionEnabled: boolean;
  topicDetectionEnabled: boolean;
}

export const DEFAULT_CONTEXT_CONFIG: ContextConfig = {
  maxWindowSize: 10,
  maxHistorySize: 100,
  contextTimeWindow: 300000,    // 5分钟
  relevanceThreshold: 0.6,
  keywordExtractionEnabled: true,
  topicDetectionEnabled: true
};

/**
 * 上下文管理器
 * 管理语音识别的上下文信息，保持语义连续性
 */
export class ContextManager {
  private contextWindows: Map<string, ContextWindow[]> = new Map();
  private globalHistory: ContextWindow[] = [];
  private config: ContextConfig;
  private topicKeywords: Map<string, string[]> = new Map();

  constructor(config: Partial<ContextConfig> = {}) {
    this.config = { ...DEFAULT_CONTEXT_CONFIG, ...config };
    this.initializeTopicKeywords();
    console.log('ContextManager initialized with config:', this.config);
  }

  /**
   * 初始化主题关键词
   */
  private initializeTopicKeywords(): void {
    this.topicKeywords.set('技术', ['编程', '代码', '算法', '数据库', '框架', '开发', '技术', '系统', '架构']);
    this.topicKeywords.set('工作', ['工作', '项目', '团队', '管理', '经验', '职责', '任务', '目标', '计划']);
    this.topicKeywords.set('教育', ['学习', '教育', '培训', '课程', '知识', '技能', '专业', '学校', '大学']);
    this.topicKeywords.set('个人', ['个人', '兴趣', '爱好', '性格', '优势', '缺点', '成长', '发展', '目标']);
  }

  /**
   * 添加上下文信息
   */
  addToContext(sessionId: string, content: string, confidence: number = 1.0): string {
    const contextWindow: ContextWindow = {
      id: `ctx-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      sessionId,
      content: content.trim(),
      timestamp: Date.now(),
      confidence,
      metadata: {}
    };

    // 提取元数据
    if (this.config.keywordExtractionEnabled) {
      contextWindow.metadata!.keywords = this.extractKeywords(content);
    }

    if (this.config.topicDetectionEnabled) {
      contextWindow.metadata!.topic = this.detectTopic(content);
    }

    // 添加到会话上下文
    if (!this.contextWindows.has(sessionId)) {
      this.contextWindows.set(sessionId, []);
    }

    const sessionContext = this.contextWindows.get(sessionId)!;
    sessionContext.push(contextWindow);

    // 保持窗口大小限制
    if (sessionContext.length > this.config.maxWindowSize) {
      sessionContext.shift();
    }

    // 添加到全局历史
    this.globalHistory.push(contextWindow);
    if (this.globalHistory.length > this.config.maxHistorySize) {
      this.globalHistory.shift();
    }

    console.log(`Added context for session ${sessionId}:`, {
      id: contextWindow.id,
      content: content.substring(0, 50) + '...',
      confidence,
      topic: contextWindow.metadata?.topic,
      keywords: contextWindow.metadata?.keywords?.slice(0, 3)
    });

    return contextWindow.id;
  }

  /**
   * 获取会话上下文
   */
  getSessionContext(sessionId: string): ContextWindow[] {
    return this.contextWindows.get(sessionId) || [];
  }

  /**
   * 获取相关上下文
   */
  getRelevantContext(sessionId: string, query: string, maxResults: number = 5): ContextWindow[] {
    const sessionContext = this.getSessionContext(sessionId);
    const now = Date.now();
    
    // 过滤时间窗口内的上下文
    const recentContext = sessionContext.filter(ctx => 
      now - ctx.timestamp <= this.config.contextTimeWindow
    );

    // 计算相关性并排序
    const scoredContext = recentContext.map(ctx => ({
      context: ctx,
      relevance: this.calculateRelevance(query, ctx)
    }));

    scoredContext.sort((a, b) => b.relevance - a.relevance);

    // 返回相关性高于阈值的结果
    return scoredContext
      .filter(item => item.relevance >= this.config.relevanceThreshold)
      .slice(0, maxResults)
      .map(item => item.context);
  }

  /**
   * 计算相关性
   */
  private calculateRelevance(query: string, context: ContextWindow): number {
    const queryWords = this.tokenize(query.toLowerCase());
    const contextWords = this.tokenize(context.content.toLowerCase());
    
    // 计算词汇重叠度
    const intersection = queryWords.filter(word => contextWords.includes(word));
    const union = [...new Set([...queryWords, ...contextWords])];
    const jaccardSimilarity = intersection.length / union.length;

    // 考虑关键词匹配
    let keywordBonus = 0;
    if (context.metadata?.keywords) {
      const queryKeywords = this.extractKeywords(query);
      const matchingKeywords = queryKeywords.filter(keyword => 
        context.metadata!.keywords!.includes(keyword)
      );
      keywordBonus = matchingKeywords.length * 0.2;
    }

    // 考虑时间衰减
    const timeDecay = this.calculateTimeDecay(context.timestamp);

    // 综合相关性分数
    const relevance = (jaccardSimilarity + keywordBonus) * timeDecay * context.confidence;

    return Math.min(relevance, 1.0);
  }

  /**
   * 计算时间衰减
   */
  private calculateTimeDecay(timestamp: number): number {
    const now = Date.now();
    const age = now - timestamp;
    const maxAge = this.config.contextTimeWindow;
    
    // 线性衰减
    return Math.max(0, 1 - (age / maxAge));
  }

  /**
   * 提取关键词
   */
  private extractKeywords(text: string): string[] {
    const words = this.tokenize(text.toLowerCase());
    
    // 简单的关键词提取：过滤停用词，选择长度大于2的词
    const stopWords = new Set(['的', '了', '在', '是', '我', '你', '他', '她', '它', '们', '这', '那', '有', '和', '与', '或', '但', '而', '就', '都', '要', '会', '能', '可以', '应该', '可能']);
    
    const keywords = words
      .filter(word => word.length > 2 && !stopWords.has(word))
      .filter((word, index, arr) => arr.indexOf(word) === index) // 去重
      .slice(0, 10); // 最多10个关键词

    return keywords;
  }

  /**
   * 检测主题
   */
  private detectTopic(text: string): string {
    const words = this.tokenize(text.toLowerCase());
    const topicScores: Map<string, number> = new Map();

    // 计算每个主题的匹配分数
    for (const [topic, keywords] of this.topicKeywords.entries()) {
      let score = 0;
      for (const keyword of keywords) {
        if (words.includes(keyword)) {
          score++;
        }
      }
      if (score > 0) {
        topicScores.set(topic, score);
      }
    }

    // 返回得分最高的主题
    if (topicScores.size === 0) {
      return '通用';
    }

    let bestTopic = '通用';
    let bestScore = 0;
    for (const [topic, score] of topicScores.entries()) {
      if (score > bestScore) {
        bestScore = score;
        bestTopic = topic;
      }
    }

    return bestTopic;
  }

  /**
   * 分词（简单实现）
   */
  private tokenize(text: string): string[] {
    // 简单的分词：按空格和标点符号分割
    return text
      .replace(/[^\w\u4e00-\u9fa5]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 0);
  }

  /**
   * 构建上下文信息
   */
  buildContextInfo(sessionId: string, currentQuery?: string): ContextInfo {
    const sessionContext = this.getSessionContext(sessionId);
    const recentResults = sessionContext
      .slice(-5) // 最近5个结果
      .map(ctx => ctx.content);

    let currentTopic: string | undefined;
    if (currentQuery) {
      currentTopic = this.detectTopic(currentQuery);
    } else if (sessionContext.length > 0) {
      currentTopic = sessionContext[sessionContext.length - 1].metadata?.topic;
    }

    // 分析语音特征（简化实现）
    const speakerInfo = this.analyzeSpeakerInfo(sessionContext);
    const environmentInfo = this.analyzeEnvironmentInfo(sessionContext);

    return {
      previousResults: recentResults,
      currentTopic,
      speakerInfo,
      environmentInfo
    };
  }

  /**
   * 分析说话者信息
   */
  private analyzeSpeakerInfo(contexts: ContextWindow[]): ContextInfo['speakerInfo'] {
    if (contexts.length === 0) return undefined;

    // 简单的语言检测
    const hasChineseChars = contexts.some(ctx => /[\u4e00-\u9fa5]/.test(ctx.content));
    const hasEnglishChars = contexts.some(ctx => /[a-zA-Z]/.test(ctx.content));

    let language = 'unknown';
    if (hasChineseChars && !hasEnglishChars) {
      language = 'zh-CN';
    } else if (hasEnglishChars && !hasChineseChars) {
      language = 'en-US';
    } else if (hasChineseChars && hasEnglishChars) {
      language = 'mixed';
    }

    // 计算平均语速（基于文本长度和时间间隔的粗略估计）
    let speakingRate: number | undefined;
    if (contexts.length >= 2) {
      const totalChars = contexts.reduce((sum, ctx) => sum + ctx.content.length, 0);
      const timeSpan = contexts[contexts.length - 1].timestamp - contexts[0].timestamp;
      speakingRate = timeSpan > 0 ? (totalChars / timeSpan) * 1000 : undefined; // 字符/秒
    }

    return {
      language,
      speakingRate
    };
  }

  /**
   * 分析环境信息
   */
  private analyzeEnvironmentInfo(contexts: ContextWindow[]): ContextInfo['environmentInfo'] {
    if (contexts.length === 0) return undefined;

    // 基于置信度估算音频质量
    const avgConfidence = contexts.reduce((sum, ctx) => sum + ctx.confidence, 0) / contexts.length;
    const audioQuality = avgConfidence;

    // 简单的噪音水平估计（基于置信度变化）
    let noiseLevel = 0;
    if (contexts.length >= 2) {
      const confidenceVariance = this.calculateVariance(contexts.map(ctx => ctx.confidence));
      noiseLevel = Math.min(confidenceVariance * 2, 1); // 归一化到0-1
    }

    return {
      noiseLevel,
      audioQuality
    };
  }

  /**
   * 计算方差
   */
  private calculateVariance(values: number[]): number {
    if (values.length === 0) return 0;
    
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
    
    return variance;
  }

  /**
   * 获取上下文统计信息
   */
  getContextStats(): {
    totalSessions: number;
    totalContexts: number;
    averageContextsPerSession: number;
    topTopics: Array<{ topic: string; count: number }>;
    averageConfidence: number;
  } {
    const totalSessions = this.contextWindows.size;
    const totalContexts = this.globalHistory.length;
    const averageContextsPerSession = totalSessions > 0 ? totalContexts / totalSessions : 0;

    // 统计主题分布
    const topicCounts: Map<string, number> = new Map();
    this.globalHistory.forEach(ctx => {
      const topic = ctx.metadata?.topic || '未知';
      topicCounts.set(topic, (topicCounts.get(topic) || 0) + 1);
    });

    const topTopics = Array.from(topicCounts.entries())
      .map(([topic, count]) => ({ topic, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 5);

    const averageConfidence = this.globalHistory.length > 0 ?
      this.globalHistory.reduce((sum, ctx) => sum + ctx.confidence, 0) / this.globalHistory.length : 0;

    return {
      totalSessions,
      totalContexts,
      averageContextsPerSession,
      topTopics,
      averageConfidence
    };
  }

  /**
   * 清理过期上下文
   */
  cleanupExpiredContext(): void {
    const now = Date.now();
    const cutoffTime = now - this.config.contextTimeWindow;

    // 清理会话上下文
    for (const [sessionId, contexts] of this.contextWindows.entries()) {
      const validContexts = contexts.filter(ctx => ctx.timestamp > cutoffTime);
      if (validContexts.length === 0) {
        this.contextWindows.delete(sessionId);
      } else {
        this.contextWindows.set(sessionId, validContexts);
      }
    }

    // 清理全局历史
    const beforeCount = this.globalHistory.length;
    this.globalHistory = this.globalHistory.filter(ctx => ctx.timestamp > cutoffTime);
    const afterCount = this.globalHistory.length;

    if (beforeCount !== afterCount) {
      console.log(`Cleaned up ${beforeCount - afterCount} expired contexts`);
    }
  }

  /**
   * 重置上下文
   */
  reset(sessionId?: string): void {
    if (sessionId) {
      this.contextWindows.delete(sessionId);
      console.log(`Reset context for session: ${sessionId}`);
    } else {
      this.contextWindows.clear();
      this.globalHistory = [];
      console.log('Reset all contexts');
    }
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<ContextConfig>): void {
    this.config = { ...this.config, ...newConfig };
    console.log('ContextManager config updated:', this.config);
  }

  /**
   * 添加自定义主题关键词
   */
  addTopicKeywords(topic: string, keywords: string[]): void {
    this.topicKeywords.set(topic, keywords);
    console.log(`Added topic keywords for "${topic}":`, keywords);
  }
}
