// 前端日志劫持并通过 WebSocket 推送到后端
// 使用方式：在 WebSocket 连接建立后调用 setupClientLogger(ws)

/* eslint-disable @typescript-eslint/no-explicit-any */

type Level = 'log' | 'info' | 'warn' | 'error';

// 缓冲区，在 WebSocket 连接建立之前暂存日志
const buffer: Array<{ level: Level; message: string }> = [];

// 当前 WebSocket 引用
let wsRef: WebSocket | null = null;

// 发送单条日志
function send(level: Level, message: string) {
  if (wsRef && wsRef.readyState === WebSocket.OPEN) {
    try {
      wsRef.send(
        JSON.stringify({
          type: 'client_log',
          level,
          message,
        })
      );
    } catch {
      // 网络错误时重新放回缓冲区
      buffer.push({ level, message });
    }
  } else {
    buffer.push({ level, message });
  }
}

// 刷新缓冲区
function flush() {
  if (!wsRef || wsRef.readyState !== WebSocket.OPEN) return;
  buffer.splice(0).forEach(({ level, message }) => {
    // winston 后端不支持 .log 方法，转成 info
    const mappedLevel = (level === 'log' ? 'info' : level) as Level;
    send(mappedLevel, message);
  });
}

// 全局覆盖 console 方法（只执行一次）
if (!(window as any).__clientLoggerInitialized) {
  const levels: Level[] = ['log', 'info', 'warn', 'error'];
  levels.forEach((level) => {
    const original = console[level] as (...args: any[]) => void;
    const override = (...args: any[]) => {
      // 保持原行为
      original.apply(console, args);

      // 拼接消息字符串
      const message = args
        .map((arg) => {
          if (typeof arg === 'object') {
            try {
              return JSON.stringify(arg);
            } catch {
              return String(arg);
            }
          }
          return String(arg);
        })
        .join(' ');

      // winston 后端不支持 .log 方法，转成 info
      const mappedLevel = (level === 'log' ? 'info' : level) as Level;
      send(mappedLevel, message);
    };
    (console as any)[level] = override;
  });

  // onbeforeunload 时尝试 flush
  window.addEventListener('beforeunload', () => {
    // 尝试使用 sendBeacon 发送剩余日志
    if (buffer.length > 0) {
      try {
        const blob = new Blob([
          buffer
            .map((b) => `${b.level.toUpperCase()}: ${b.message}`)
            .join('\n')
        ], { type: 'text/plain' });
        navigator.sendBeacon('/api/client-log-beacon', blob);
      } catch {
        // ignore
      }
    }
  });

  (window as any).__clientLoggerInitialized = true;
}

// 供业务代码在 WebSocket 建立后调用，设置并 flush
export function attachClientLoggerWebSocket(ws: WebSocket) {
  wsRef = ws;
  if (ws.readyState === WebSocket.OPEN) {
    flush();
  } else {
    ws.addEventListener('open', flush);
  }
} 