// ASR服务管理器 - 简化版单连接模式
import { DashScopeASRProvider } from './dashscopeProvider';
import { OpenAIWhisperProvider } from './openaiWhisperProvider';
import { backendConnectionTracker } from '../../../utils/ConnectionTracker';

export interface ASRConnection {
  id: string;
  provider: 'dashscope' | 'whisper';
  instance: DashScopeASRProvider | OpenAIWhisperProvider;
  status: 'idle' | 'active' | 'error';
  lastUsed: number;
  sessionId?: string;
}

/**
 * ASR服务管理器 - 简化版单连接模式
 * 负责按需创建DashScope和Whisper服务连接
 */
export class ASRServicePool {
  constructor() {
    // 简化构造函数，不需要配置连接池
  }

  /**
   * 简化初始化 - 移除连接池，改为单连接模式
   */
  public async initialize(): Promise<void> {
    console.log('🔧 ASRServicePool: Simplified initialization (single connection mode)');

    try {
      // 不进行任何预初始化，连接将在需要时创建
      console.log('✅ ASRServicePool: Ready (single connection mode)');
    } catch (error) {
      console.error('❌ ASRServicePool: Initialization failed:', error);
      throw error;
    }
  }

  /**
   * 获取ASR连接 - 简化为单连接模式
   */
  public async getConnection(sessionId: string, preferredProvider?: 'dashscope' | 'whisper'): Promise<ASRConnection> {
    console.log(`🔧 ASRServicePool: Creating single connection for session ${sessionId}`);

    try {
      const provider = preferredProvider || 'dashscope';

      // 直接创建新连接，不使用连接池
      const connection = await this.createDirectConnection(sessionId, provider);

      console.log(`✅ ASRServicePool: Created direct ${provider} connection for session ${sessionId}`);
      return connection;
    } catch (error) {
      console.error(`❌ ASRServicePool: Failed to create connection for ${sessionId}:`, error);
      throw error;
    }
  }

  /**
   * 释放连接
   */
  public releaseConnection(connectionId: string): void {
    console.log(`🔄 ASRServicePool: Releasing connection ${connectionId}`);
    
    const connection = this.findConnection(connectionId);
    if (connection) {
      connection.sessionId = undefined;
      connection.status = 'idle';
      connection.lastUsed = Date.now();
      console.log(`✅ ASRServicePool: Released connection ${connectionId}`);
    } else {
      console.warn(`⚠️ ASRServicePool: Connection ${connectionId} not found`);
    }
  }

  /**
   * 清理资源
   */
  public async cleanup(): Promise<void> {
    console.log('🧹 ASRServicePool: Cleaning up resources');
    
    // 停止健康检查
    if (this.healthCheckTimer) {
      clearInterval(this.healthCheckTimer);
      this.healthCheckTimer = null;
    }
    
    // 清理所有连接
    const cleanupTasks = [
      ...this.dashscopePool.map(conn => this.cleanupConnection(conn)),
      ...this.whisperPool.map(conn => this.cleanupConnection(conn))
    ];
    
    await Promise.allSettled(cleanupTasks);
    
    this.dashscopePool = [];
    this.whisperPool = [];
    
    console.log('✅ ASRServicePool: Cleanup completed');
  }

  // 私有方法（简化版）
  /**
   * 直接创建连接 - 简化版，不使用连接池
   */
  private async createDirectConnection(sessionId: string, provider: 'dashscope' | 'whisper'): Promise<ASRConnection> {
    const id = `${provider}_${sessionId}_${Date.now()}`;

    // 🔍 追踪连接创建
    backendConnectionTracker.trackConnection(
      id,
      provider as 'dashscope' | 'whisper',
      'ASRServicePool.createDirectConnection',
      { sessionId, provider, timestamp: Date.now() }
    );

    try {
      let connection: ASRConnection;

      if (provider === 'dashscope') {
        connection = await this.createDashScopeConnection(id);
      } else {
        connection = await this.createWhisperConnection(id);
      }

      connection.sessionId = sessionId;
      backendConnectionTracker.updateConnectionStatus(id, 'connected');
      return connection;
    } catch (error) {
      backendConnectionTracker.updateConnectionStatus(id, 'error', { error: error.toString() });
      throw error;
    }
  }

  private async createDashScopeConnection(id: string): Promise<ASRConnection> {
    const provider = new DashScopeASRProvider();
    await provider.initialize();
    
    return {
      id,
      provider: 'dashscope',
      instance: provider,
      status: 'idle',
      lastUsed: Date.now()
    };
  }

  private async createWhisperConnection(id: string): Promise<ASRConnection> {
    const provider = new OpenAIWhisperProvider();
    await provider.initialize();
    
    return {
      id,
      provider: 'whisper',
      instance: provider,
      status: 'idle',
      lastUsed: Date.now()
    };
  }



  /**
   * 简化的连接清理 - 单连接模式不需要复杂的清理逻辑
   */
  private async cleanupConnection(connection: ASRConnection): Promise<void> {
    try {
      if (connection.instance && typeof connection.instance.cleanup === 'function') {
        await connection.instance.cleanup();
      }
      console.log(`🧹 ASRServicePool: Connection ${connection.id} cleaned up`);
    } catch (error) {
      console.error(`❌ ASRServicePool: Failed to cleanup connection ${connection.id}:`, error);
    }
  }
}
