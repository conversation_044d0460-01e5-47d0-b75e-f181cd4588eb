// WebSocket资源管理器
import { InterviewConfig } from './InterviewSessionManager';
import useAuthStore from '../stores/authStore';
import { connectionTracker } from '../utils/ConnectionTracker';

export interface WebSocketConnection {
  ws: WebSocket;
  sessionId: string;
  status: 'connecting' | 'connected' | 'disconnected' | 'error';
  lastActivity: number;
  retryCount: number;
}

export interface WebSocketPool {
  [sessionId: string]: WebSocketConnection;
}

/**
 * WebSocket资源管理器（重构版）
 * 负责WebSocket连接的智能管理，移除过度预连接，实现按需连接
 */
export class WebSocketManager {
  private connectionPool: WebSocketPool = {};
  private maxRetries = 3;
  private retryDelay = 1000;
  private connectionReuse = true; // 启用连接复用
  private systemCheckCache: { [key: string]: any } = {}; // 系统检查缓存
  private systemCheckCacheExpiry = 5 * 60 * 1000; // 5分钟缓存

  /**
   * 简化初始化 - 移除预热逻辑，改为空操作
   */
  public async warmUp(): Promise<void> {
    console.log('🔧 WebSocketManager: Warm-up disabled (using on-demand connections)');

    // 不进行任何预热操作，连接将在需要时创建
    console.log('✅ WebSocketManager: Ready (no warm-up needed)');
  }

  /**
   * 创建会话 - 智能分配WebSocket连接（重构版）
   */
  public async createSession(sessionId: string, config: InterviewConfig, mode: 'live' | 'mock' = 'live'): Promise<void> {
    console.log(`🔧 WebSocketManager: Creating smart session ${sessionId}`);

    try {
      // 检查是否已有可复用的连接
      const existingConnection = this.connectionPool[sessionId];
      if (existingConnection && existingConnection.ws.readyState === WebSocket.OPEN) {
        console.log('♻️ WebSocketManager: Reusing existing session connection');
        existingConnection.lastActivity = Date.now();
        return;
      }

      // 创建智能连接（使用缓存的系统检查）
      const ws = await this.createSmartConnection(sessionId, mode);

      // 配置会话特定的WebSocket
      await this.configureSessionWebSocket(ws, sessionId, config);

      // 添加到连接池
      this.connectionPool[sessionId] = {
        ws,
        sessionId,
        status: 'connected',
        lastActivity: Date.now(),
        retryCount: 0
      };

      console.log(`✅ WebSocketManager: Smart session ${sessionId} created successfully`);
    } catch (error) {
      console.error(`❌ WebSocketManager: Failed to create session ${sessionId}:`, error);
      throw error;
    }
  }

  /**
   * 激活会话 - 开始实际的面试会话
   */
  public async activateSession(sessionId: string): Promise<void> {
    console.log(`⚡ WebSocketManager: Activating session ${sessionId}`);
    
    const connection = this.connectionPool[sessionId];
    if (!connection) {
      throw new Error(`Session ${sessionId} not found in pool`);
    }
    
    try {
      // 发送激活消息
      const activationMessage = {
        type: 'activate_session',
        sessionId,
        timestamp: Date.now()
      };
      
      connection.ws.send(JSON.stringify(activationMessage));
      connection.lastActivity = Date.now();
      
      console.log(`✅ WebSocketManager: Session ${sessionId} activated`);
    } catch (error) {
      console.error(`❌ WebSocketManager: Failed to activate session ${sessionId}:`, error);
      throw error;
    }
  }

  /**
   * 获取会话连接
   */
  public getSessionConnection(sessionId: string): WebSocketConnection | undefined {
    return this.connectionPool[sessionId];
  }

  /**
   * 发送消息
   */
  public sendMessage(sessionId: string, message: any): void {
    const connection = this.connectionPool[sessionId];
    if (!connection || connection.status !== 'connected') {
      throw new Error(`Session ${sessionId} is not connected`);
    }
    
    try {
      connection.ws.send(JSON.stringify(message));
      connection.lastActivity = Date.now();
    } catch (error) {
      console.error(`❌ WebSocketManager: Failed to send message to ${sessionId}:`, error);
      this.handleConnectionError(sessionId, error);
      throw error;
    }
  }

  /**
   * 清理资源（重构版）
   */
  public async cleanup(): Promise<void> {
    console.log('🧹 WebSocketManager: Cleaning up resources');

    // 关闭所有会话连接
    Object.values(this.connectionPool).forEach(connection => {
      if (connection.ws.readyState === WebSocket.OPEN) {
        connection.ws.close();
      }
    });

    // 清理连接池和缓存
    this.connectionPool = {};
    this.systemCheckCache = {};

    console.log('✅ WebSocketManager: Cleanup completed');
  }

  // 私有方法

  /**
   * 创建智能连接（使用缓存的系统检查）
   */
  private async createSmartConnection(sessionId: string, mode: 'live' | 'mock' = 'live'): Promise<WebSocket> {
    console.log(`🧠 WebSocketManager: Creating smart connection for ${sessionId}`);

    const wsUrl = this.buildWebSocketUrl(sessionId, mode);

    // 🔍 追踪连接创建
    connectionTracker.trackConnection(
      sessionId,
      'websocket',
      'WebSocketManager.createSmartConnection',
      { url: wsUrl, timestamp: Date.now() }
    );

    const ws = new WebSocket(wsUrl);

    try {
      await this.waitForConnection(ws, sessionId);
      console.log(`✅ WebSocketManager: Smart connection established for ${sessionId}`);
      connectionTracker.updateConnectionStatus(sessionId, 'connected');
      return ws;
    } catch (error) {
      console.error(`❌ WebSocketManager: Smart connection failed for ${sessionId}:`, error);
      connectionTracker.updateConnectionStatus(sessionId, 'error', { error: error.toString() });
      throw error;
    }
  }

  /**
   * 缓存系统检查结果
   */
  private async cacheSystemChecks(): Promise<void> {
    const cacheKey = 'system_checks';
    const now = Date.now();

    // 检查缓存是否有效
    if (this.systemCheckCache[cacheKey] &&
        (now - this.systemCheckCache[cacheKey].timestamp) < this.systemCheckCacheExpiry) {
      console.log('📋 WebSocketManager: System checks already cached');
      return;
    }

    console.log('📋 WebSocketManager: Caching system checks...');

    // 这里可以添加实际的系统检查逻辑
    // 目前只是模拟缓存
    this.systemCheckCache[cacheKey] = {
      timestamp: now,
      checks: {
        ffmpeg: 'available',
        asr: 'ready',
        network: 'connected'
      }
    };

    console.log('✅ WebSocketManager: System checks cached successfully');
  }

  private async createNewConnection(sessionId: string): Promise<WebSocket> {
    const wsUrl = this.buildWebSocketUrl(sessionId);
    const ws = new WebSocket(wsUrl);
    
    await this.waitForConnection(ws, sessionId);
    return ws;
  }

  private async configureSessionWebSocket(ws: WebSocket, sessionId: string, config: InterviewConfig): Promise<void> {
    // 设置消息处理器
    ws.onmessage = (event) => {
      this.handleMessage(sessionId, event);
    };
    
    // 设置错误处理器
    ws.onerror = (error) => {
      this.handleConnectionError(sessionId, error);
    };
    
    // 设置关闭处理器
    ws.onclose = (event) => {
      this.handleConnectionClose(sessionId, event);
    };
    
    // 发送初始化配置
    const initMessage = {
      type: 'initialize_session',
      sessionId,
      config: {
        interviewType: config.interviewType,
        language: config.interviewLanguage,
        answerStyle: config.answerStyle
      },
      timestamp: Date.now()
    };
    
    ws.send(JSON.stringify(initMessage));
  }

  private buildWebSocketUrl(sessionId: string, mode: 'live' | 'mock' = 'live'): string {
    // 正确获取token - 从AuthStore获取而不是直接从localStorage
    const token = useAuthStore.getState().token;
    const baseUrl = process.env.NODE_ENV === 'production'
      ? 'wss://mianshijun.xyz'
      : 'ws://localhost:3000';

    console.log('🔐 WebSocketManager: Building URL with token:', token ? `present (${token.substring(0, 20)}...)` : 'missing');
    console.log('🔐 WebSocketManager: Full URL:', `${baseUrl}/api/ws/interview/${sessionId}?token=${token ? token.substring(0, 20) + '...' : 'null'}&mode=${mode}`);
    return `${baseUrl}/api/ws/interview/${sessionId}?token=${token}&mode=${mode}`;
  }

  private waitForConnection(ws: WebSocket, sessionId: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('WebSocket connection timeout'));
      }, 10000);

      ws.onopen = () => {
        clearTimeout(timeout);
        console.log(`🔗 WebSocketManager: Connection opened for ${sessionId}, triggering connection change event`);

        // 触发连接状态变化事件
        window.dispatchEvent(new CustomEvent('websocket-connection-change', {
          detail: { sessionId, connected: true }
        }));

        resolve();
      };

      ws.onerror = (error) => {
        clearTimeout(timeout);
        reject(error);
      };
    });
  }

  private handleMessage(sessionId: string, event: MessageEvent): void {
    try {
      const data = JSON.parse(event.data);
      console.log(`📨 WebSocketManager: Message received for ${sessionId}:`, data);
      
      // 更新连接活动时间
      const connection = this.connectionPool[sessionId];
      if (connection) {
        connection.lastActivity = Date.now();
      }
      
      // 触发全局事件，让其他组件处理消息
      window.dispatchEvent(new CustomEvent('websocket-message', {
        detail: { sessionId, data }
      }));
    } catch (error) {
      console.error(`❌ WebSocketManager: Failed to parse message for ${sessionId}:`, error);
    }
  }

  private handleConnectionError(sessionId: string, error: any): void {
    console.error(`❌ WebSocketManager: Connection error for ${sessionId}:`, error);
    
    const connection = this.connectionPool[sessionId];
    if (connection) {
      connection.status = 'error';
      connection.retryCount++;
      
      // 尝试重连
      if (connection.retryCount <= this.maxRetries) {
        setTimeout(() => {
          this.retryConnection(sessionId);
        }, this.retryDelay * connection.retryCount);
      }
    }
  }

  private handleConnectionClose(sessionId: string, event: CloseEvent): void {
    console.log(`🔌 WebSocketManager: Connection closed for ${sessionId}:`, event);
    
    const connection = this.connectionPool[sessionId];
    if (connection) {
      connection.status = 'disconnected';
    }
  }

  private async retryConnection(sessionId: string): Promise<void> {
    console.log(`🔄 WebSocketManager: Retrying connection for ${sessionId}`);

    try {
      const connection = this.connectionPool[sessionId];
      if (!connection) return;

      // 关闭旧连接
      if (connection.ws.readyState === WebSocket.OPEN) {
        connection.ws.close();
      }

      // 创建新的智能连接
      const newWs = await this.createSmartConnection(sessionId);
      connection.ws = newWs;
      connection.status = 'connected';
      connection.lastActivity = Date.now();

      console.log(`✅ WebSocketManager: Retry successful for ${sessionId}`);
    } catch (error) {
      console.error(`❌ WebSocketManager: Retry failed for ${sessionId}:`, error);
    }
  }

  /**
   * 简化的连接健康检查（仅清理死连接）
   */
  private cleanupDeadConnections(): void {
    Object.entries(this.connectionPool).forEach(([sessionId, connection]) => {
      if (connection.ws.readyState !== WebSocket.OPEN) {
        console.log(`🔌 WebSocketManager: Removing dead connection ${sessionId}`);
        delete this.connectionPool[sessionId];
      }
    });
  }
}
