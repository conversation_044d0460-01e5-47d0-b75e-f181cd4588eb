// Toast相关类型定义

export type ToastType = 'success' | 'error' | 'info' | 'warning';

export interface Toast {
  id: string;              // 唯一标识符
  type: ToastType;         // Toast类型
  title?: string;          // 可选标题
  message: string;         // 消息内容
  duration?: number;       // 显示时长(毫秒)
  autoClose?: boolean;     // 是否自动关闭
  createdAt: number;       // 创建时间戳
}

export interface ToastOptions {
  type?: ToastType;        // Toast类型
  title?: string;          // 可选标题
  duration?: number;       // 显示时长
  autoClose?: boolean;     // 是否自动关闭
}

export interface UseToastReturn {
  toasts: Toast[];                                           // Toast列表
  showToast: (message: string, options?: ToastOptions) => string;  // 显示Toast
  showSuccess: (message: string, duration?: number) => string;     // 显示成功Toast
  showError: (message: string, duration?: number) => string;       // 显示错误Toast
  showInfo: (message: string, duration?: number) => string;        // 显示信息Toast
  showWarning: (message: string, duration?: number) => string;     // 显示警告Toast
  removeToast: (id: string) => void;                        // 移除Toast
  clearAllToasts: () => void;                               // 清除所有Toast
}
