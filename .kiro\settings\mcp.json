{"mcpServers": {"filesystem": {"command": "cmd", "args": ["/c", "npx", "-y", "@modelcontextprotocol/server-filesystem", "E:\\Data"], "disabled": false, "autoApprove": ["create_directory"]}, "github": {"command": "cmd", "args": ["/c", "npx", "-y", "@modelcontextprotocol/server-github"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "*****************************************"}, "disabled": false, "autoApprove": []}, "playwright": {"command": "cmd", "args": ["/c", "npx", "-y", "@executeautomation/playwright-mcp-server"], "disabled": false, "autoApprove": []}, "sequential-thinking": {"command": "cmd", "args": ["/c", "npx", "-y", "@modelcontextprotocol/server-sequential-thinking"], "disabled": false, "autoApprove": []}, "context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"], "disabled": false, "autoApprove": []}, "寸止": {"command": "E:\\Software\\cunzhi\\寸止.exe", "args": [], "disabled": false, "autoApprove": ["zhi", "ji"]}, "postgresql": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-postgres", "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&connect_timeout=60&pool_timeout=60&application_name=mianshijun_local&keepalives=1&keepalives_idle=30&keepalives_interval=10&target_session_attrs=read-write"], "env": {}, "disabled": false, "autoApprove": []}}}