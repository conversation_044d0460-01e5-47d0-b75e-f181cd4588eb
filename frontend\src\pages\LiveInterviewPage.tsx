// frontend/src/pages/LiveInterviewPage.tsx
import React, { useEffect, useRef } from 'react';
import Header from '../components/interview/Header';
import InterviewContent from '../components/interview/InterviewContent';
import ControlBar from '../components/interview/ControlBar';
import { useInterviewSession } from '../hooks/useInterviewSession';
import { useNavigate } from 'react-router-dom';
import useAuthStore from '../stores/authStore';
import useInterviewStore from '../stores/interviewStore';
import { useToast } from '../hooks/useToast';
import ToastContainer from '../components/ui/ToastContainer';
import useDocumentTitle from '../hooks/useDocumentTitle';

const LiveInterviewPage: React.FC = () => {
  // 设置页面标题
  useDocumentTitle('AI实时面试');

  const navigate = useNavigate();
  const { isAuthenticated } = useAuthStore();
  const { config } = useInterviewStore();
  const { toasts, removeToast, showError, showInfo, showSuccess } = useToast();

  // 使用ref来跟踪是否已经显示过通知，避免重复显示
  const hasShownNotification = useRef(false);

  const {
    session,
    formattedTime,
    isListening,
    toggleListening,
    refreshSession,
    endInterview,
    vadStatus
  } = useInterviewSession({
    mode: 'live',
    showError,
    showSuccess,
    showInfo
  });

  // 如果未登录，重定向到登录页面
  React.useEffect(() => {
    if (!isAuthenticated) {
      navigate('/login');
    }
  }, [isAuthenticated, navigate]);

  // 检查屏幕共享状态并监控其有效性
  useEffect(() => {
    // 避免重复显示通知
    if (hasShownNotification.current) {
      return;
    }

    // 检查屏幕共享状态
    if (config.screenShareStatus !== 'sharing' || !config.sharedStream) {
      showError('屏幕共享已断开，面试功能可能受到影响', 5000);
      console.warn('LiveInterviewPage: Screen sharing is not active', {
        status: config.screenShareStatus,
        hasStream: !!config.sharedStream
      });
      hasShownNotification.current = true;
      return;
    }

    // 验证屏幕共享流是否仍然有效
    const stream = config.sharedStream;
    const videoTracks = stream.getVideoTracks();
    const audioTracks = stream.getAudioTracks();

    if (videoTracks.length === 0) {
      showError('屏幕共享视频轨道丢失', 5000);
      console.warn('LiveInterviewPage: No video tracks in shared stream');
      hasShownNotification.current = true;
      return;
    }

    // 检查视频轨道状态
    const activeVideoTrack = videoTracks.find(track => track.readyState === 'live');
    if (!activeVideoTrack) {
      showError('屏幕共享视频轨道已停止', 5000);
      console.warn('LiveInterviewPage: Video track is not live');
      hasShownNotification.current = true;
      return;
    }

    // 监听轨道结束事件
    const handleTrackEnded = () => {
      showInfo('屏幕共享已结束', 5000);
      console.log('LiveInterviewPage: Screen share track ended');
    };

    videoTracks.forEach(track => {
      track.addEventListener('ended', handleTrackEnded);
    });

    showSuccess('屏幕共享状态正常，面试功能已就绪', 5000);
    console.log('LiveInterviewPage: Screen sharing is active and valid', {
      videoTracks: videoTracks.length,
      audioTracks: audioTracks.length,
      videoTrackState: activeVideoTrack.readyState
    });

    hasShownNotification.current = true;

    // 清理函数
    return () => {
      videoTracks.forEach(track => {
        track.removeEventListener('ended', handleTrackEnded);
      });
    };
    // 移除showError, showInfo, showSuccess从依赖数组，避免重复触发
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [config.screenShareStatus, config.sharedStream]);

  if (!isAuthenticated) {
    return null; // 等待重定向，不显示任何内容
  }

  return (
    <>
      {/* Toast容器 */}
      <ToastContainer toasts={toasts} onRemoveToast={removeToast} />

      <div className="h-screen w-screen overflow-hidden bg-gray-100 flex flex-col">
        <div className="flex flex-col h-full bg-white">
          <Header
            companyName={session.companyName}
            positionName={session.positionName}
            elapsedTime={formattedTime}
            onEndInterview={endInterview}
          />

          <InterviewContent
            messages={session.messages}
            isListening={isListening}
            mode="live"
          />

          <ControlBar
            isListening={isListening}
            onToggleListening={toggleListening}
            onRefresh={refreshSession}
          />
        </div>
      </div>
    </>
  );
};

export default LiveInterviewPage;
