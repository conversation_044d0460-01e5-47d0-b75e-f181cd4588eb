// 统一面试会话Hook - 使用权威单例连接架构
import { useState, useEffect, useCallback, useRef } from 'react';
import { useWebSocket } from '../providers/WebSocketProvider';
import type { InterviewConfig } from '../managers/UnifiedWebSocketManager';
import useAuthStore from '../stores/authStore';

interface UnifiedInterviewSessionState {
  isConnected: boolean;
  isRecording: boolean;
  transcription: string;
  aiSuggestion: string;
  sessionId: string | null;
  error: string | null;
}

interface UnifiedInterviewSessionActions {
  startInterview: (config: InterviewConfig) => Promise<void>;
  stopInterview: () => Promise<void>;
  startRecording: () => void;
  stopRecording: () => void;
  sendMessage: (message: any) => void;
}

/**
 * 统一面试会话Hook
 * 使用权威单例WebSocket连接架构
 * 替换原有的分散WebSocket创建逻辑
 */
export const useUnifiedInterviewSession = (): UnifiedInterviewSessionState & UnifiedInterviewSessionActions => {
  // 获取统一WebSocket管理器
  const webSocketManager = useWebSocket();
  const { token, isAuthenticated } = useAuthStore();

  // 状态管理
  const [state, setState] = useState<UnifiedInterviewSessionState>({
    isConnected: false,
    isRecording: false,
    transcription: '',
    aiSuggestion: '',
    sessionId: null,
    error: null
  });

  // 音频相关引用
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);

  /**
   * 开始面试会话
   */
  const startInterview = useCallback(async (config: InterviewConfig) => {
    try {
      console.log('🚀 UnifiedInterviewSession: Starting interview with config:', config);
      
      if (!isAuthenticated || !token) {
        throw new Error('User not authenticated');
      }

      // 生成会话ID
      const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      // 使用统一WebSocket管理器连接
      await webSocketManager.connect(sessionId, config);
      
      setState(prev => ({
        ...prev,
        sessionId,
        isConnected: true,
        error: null
      }));

      console.log('✅ UnifiedInterviewSession: Interview started successfully');
      
    } catch (error) {
      console.error('❌ UnifiedInterviewSession: Failed to start interview:', error);
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to start interview',
        isConnected: false
      }));
    }
  }, [webSocketManager, isAuthenticated, token]);

  /**
   * 停止面试会话
   */
  const stopInterview = useCallback(async () => {
    try {
      console.log('🛑 UnifiedInterviewSession: Stopping interview');
      
      // 停止录音
      if (state.isRecording) {
        stopRecording();
      }
      
      // 断开WebSocket连接
      await webSocketManager.disconnect();
      
      setState(prev => ({
        ...prev,
        isConnected: false,
        isRecording: false,
        sessionId: null,
        error: null
      }));

      console.log('✅ UnifiedInterviewSession: Interview stopped successfully');
      
    } catch (error) {
      console.error('❌ UnifiedInterviewSession: Failed to stop interview:', error);
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to stop interview'
      }));
    }
  }, [webSocketManager, state.isRecording]);

  /**
   * 开始录音
   */
  const startRecording = useCallback(() => {
    try {
      console.log('🎤 UnifiedInterviewSession: Starting recording');
      
      navigator.mediaDevices.getUserMedia({ audio: true })
        .then(stream => {
          const mediaRecorder = new MediaRecorder(stream);
          mediaRecorderRef.current = mediaRecorder;
          audioChunksRef.current = [];

          mediaRecorder.ondataavailable = (event) => {
            if (event.data.size > 0) {
              audioChunksRef.current.push(event.data);
              
              // 发送音频数据到WebSocket
              webSocketManager.sendMessage({
                type: 'audio_chunk',
                data: event.data,
                timestamp: Date.now()
              });
            }
          };

          mediaRecorder.start(1000); // 每秒发送一次数据
          
          setState(prev => ({
            ...prev,
            isRecording: true,
            error: null
          }));

          console.log('✅ UnifiedInterviewSession: Recording started');
        })
        .catch(error => {
          console.error('❌ UnifiedInterviewSession: Failed to start recording:', error);
          setState(prev => ({
            ...prev,
            error: 'Failed to access microphone'
          }));
        });
        
    } catch (error) {
      console.error('❌ UnifiedInterviewSession: Recording error:', error);
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Recording failed'
      }));
    }
  }, [webSocketManager]);

  /**
   * 停止录音
   */
  const stopRecording = useCallback(() => {
    try {
      console.log('🛑 UnifiedInterviewSession: Stopping recording');
      
      if (mediaRecorderRef.current && state.isRecording) {
        mediaRecorderRef.current.stop();
        mediaRecorderRef.current.stream.getTracks().forEach(track => track.stop());
        mediaRecorderRef.current = null;
      }
      
      setState(prev => ({
        ...prev,
        isRecording: false
      }));

      console.log('✅ UnifiedInterviewSession: Recording stopped');
      
    } catch (error) {
      console.error('❌ UnifiedInterviewSession: Failed to stop recording:', error);
    }
  }, [state.isRecording]);

  /**
   * 发送消息
   */
  const sendMessage = useCallback((message: any) => {
    try {
      webSocketManager.sendMessage(message);
    } catch (error) {
      console.error('❌ UnifiedInterviewSession: Failed to send message:', error);
      setState(prev => ({
        ...prev,
        error: 'Failed to send message'
      }));
    }
  }, [webSocketManager]);

  // 监听WebSocket事件
  useEffect(() => {
    const handleMessage = (event: CustomEvent) => {
      const data = event.detail;
      
      switch (data.type) {
        case 'transcription':
          setState(prev => ({
            ...prev,
            transcription: data.text || ''
          }));
          break;
          
        case 'ai_suggestion':
          setState(prev => ({
            ...prev,
            aiSuggestion: data.suggestion || ''
          }));
          break;
          
        case 'error':
          setState(prev => ({
            ...prev,
            error: data.message || 'Unknown error'
          }));
          break;
          
        default:
          console.log('🔍 UnifiedInterviewSession: Received message:', data);
      }
    };

    const handleConnectionChange = (event: CustomEvent) => {
      const { connected } = event.detail;
      setState(prev => ({
        ...prev,
        isConnected: connected
      }));
    };

    // 添加事件监听器
    window.addEventListener('websocket-message', handleMessage as EventListener);
    window.addEventListener('websocket-connection-change', handleConnectionChange as EventListener);

    // 清理函数
    return () => {
      window.removeEventListener('websocket-message', handleMessage as EventListener);
      window.removeEventListener('websocket-connection-change', handleConnectionChange as EventListener);
    };
  }, []);

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      if (state.isConnected) {
        stopInterview();
      }
    };
  }, []);

  return {
    // 状态
    ...state,
    
    // 操作
    startInterview,
    stopInterview,
    startRecording,
    stopRecording,
    sendMessage
  };
};
