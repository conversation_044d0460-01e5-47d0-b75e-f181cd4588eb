# ASR识别结果发送修复任务

## 任务描述
修复ASR识别结果无法发送到前端的问题

## 问题分析
通过分析后端日志发现：
- ✅ ASR识别正常工作（iFlyTek成功识别出文字）
- ✅ 音频段接收正常
- ❌ **识别结果没有发送到前端WebSocket客户端**

## 根本原因
1. **broadcastASRResult函数使用了未定义的sessionWebSocketMap**
2. **增量识别结果没有触发emitResultReady事件**
3. **普通识别结果也没有触发emitResultReady事件**

## 修复内容

### 修复1: 修复broadcastASRResult函数
**文件**: `backend/websocket/interviewWs.ts`
**问题**: 使用了未定义的`sessionWebSocketMap`
**修复**: 改为使用正确的`sessionClients`

```javascript
// 修复前
const ws = sessionWebSocketMap.get(sessionId);

// 修复后  
const clients = sessionClients.get(sessionId);
```

### 修复2: 增量识别结果发送
**文件**: `backend/services/streamingASRManager.ts`
**问题**: `performIncrementalRecognition`没有调用`emitResultReady`
**修复**: 添加事件触发

```javascript
// 添加
this.emitResultReady(session.sessionId, result, true);
```

### 修复3: 普通识别结果发送
**文件**: `backend/services/streamingASRManager.ts`
**问题**: `performRecognition`没有调用`emitResultReady`
**修复**: 添加事件触发

```javascript
// 添加
this.emitResultReady(session.sessionId, bestResult, true);
```

### 修复4: 增强调试日志
**文件**: `backend/services/streamingASRManager.ts`
**修复**: 在`emitResultReady`中添加详细日志

## 当前执行步骤: "10. 修复后端idle状态问题"

## 任务进度
- [x] 1. 分析问题根因
- [x] 2. 修复broadcastASRResult函数
- [x] 3. 修复识别结果事件触发
- [x] 4. 添加调试日志
- [x] 5. 修复前端音频处理稳定性
- [x] 6. 添加音频段验证调试信息
- [x] 7. 修复音频段验证逻辑
- [x] 8. 修复音频处理器频繁重建
- [x] 9. 彻底修复processAudio稳定性
- [x] 10. 修复后端idle状态问题
- [ ] 11. 测试修复效果
- [ ] 12. 检查前端VAD检测逻辑
- [ ] 13. 检查后端ASR识别流程
- [ ] 14. 验证端到端功能

## 阶段1修复详情 - 前端音频处理稳定性
**文件**: `frontend/src/hooks/useInterviewSession.ts`
**问题**: useEffect依赖项过多导致音频上下文频繁重建
**修复**:
1. 分离音频上下文创建和音频处理逻辑
2. 音频上下文创建只依赖关键项：`[interviewConfig.sharedStream, readyState]`
3. 音频处理逻辑独立useEffect，依赖：`[isListening, vadStatus.isInitialized, processAudio, sendWebSocketMessage]`
4. 避免频繁清理和重建AudioContext、ScriptProcessorNode

## 阶段2修复详情 - 音频段验证调试信息
**文件**: `frontend/src/utils/dynamicSegmenter.ts`
**问题**: 所有音频段都被标记为"Invalid segment"，无法确定原因
**修复**:
1. 在`isValidSegment`函数中添加详细的验证日志
2. 记录每个验证条件的检查结果
3. 显示当前配置阈值和实际值的对比

## 阶段3修复详情 - 音频段验证逻辑
**文件**: `frontend/src/utils/dynamicSegmenter.ts`
**问题**: 置信度阈值过高（0.6），实际音频段置信度约0.33
**修复**:
1. 临时将confidenceThreshold从0.6降低到0.3
2. 允许更多音频段通过验证并发送到后端
3. 后续可根据实际效果调整合适的阈值

## 阶段4修复详情 - 音频处理器频繁重建
**文件**: `frontend/src/hooks/useVAD.ts` 和 `frontend/src/hooks/useInterviewSession.ts`
**问题**: processAudio函数循环依赖导致音频处理器频繁重建
**修复**:
1. **useVAD.ts**: 移除processAudio对status.isInitialized的依赖，改用ref直接检查
2. **useInterviewSession.ts**: 使用useRef存储状态，避免闭包问题
3. 音频处理器useEffect只依赖稳定的processAudio函数
4. 使用ref检查isListening和vadInitialized状态，避免频繁重建

## 阶段9修复详情 - 彻底修复processAudio稳定性
**文件**: `frontend/src/hooks/useInterviewSession.ts`
**问题**: onSegmentReady回调函数不稳定，导致processAudio频繁重建
**根本原因**: useVAD调用时使用了内联函数作为onSegmentReady回调
**修复**:
1. 重新组织代码结构：先定义handleAudioSegment，再定义onSegmentReady
2. 使用useCallback包装onSegmentReady回调，确保引用稳定
3. useVAD使用稳定的onSegmentReady引用，避免processAudio重建
4. 彻底解决回调函数依赖链不稳定的问题

## 阶段10修复详情 - 修复后端idle状态问题
**文件**: `backend/services/streamingASRManager.ts`
**问题**: 后端StreamingASRManager一直处于idle状态，不进行ASR识别
**根本原因**: 后端置信度阈值过高（0.6-0.7），前端音频段置信度只有0.33
**修复**:
1. **handleIdleState**: 将置信度阈值从0.6降低到0.3
2. **handleOutputtingState**: 将置信度阈值从0.7降低到0.3
3. **shouldStartRecognition**: 将置信度阈值从0.7降低到0.3
4. **处理silence段**: 修改逻辑也处理silence类型的音频段，因为可能包含低音量语音
5. 确保后端能够正确处理前端发送的音频段并启动ASR识别

## 预期结果
修复后应该能看到：
1. 🚀 Emitting result_ready event 日志
2. 🎯 ASR result ready for session 日志  
3. 📤 Sent ASR result to session 日志
4. 前端收到transcription消息

## 测试方法
1. 重启后端服务应用修复
2. 前端发送音频段
3. 检查后端日志是否有新的发送日志
4. 检查前端是否收到识别结果
