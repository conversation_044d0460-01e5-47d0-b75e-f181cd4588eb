import { create } from 'zustand';
import { persist, PersistOptions } from 'zustand/middleware';
import { jwtDecode } from 'jwt-decode';

// JWT令牌解码后的结构
interface DecodedToken {
  userId: string;
  email: string;
  name?: string;
  exp: number; // 过期时间
}

// 认证状态存储
interface AuthState {
  token: string | null;
  isAuthenticated: boolean;
  user: {
    id: string;
    email: string;
    name?: string;
  } | null;

  // 操作方法
  login: (token: string) => void;
  logout: () => void;
  initializeAuth: () => void;
}

// 创建认证状态存储
type AuthPersist = (
  config: (
    set: (state: Partial<AuthState>) => void,
    get: () => AuthState,
    api: any
  ) => AuthState,
  options: PersistOptions<AuthState, Pick<AuthState, 'token'>>
) => any;

const useAuthStore = create<AuthState>()(
  (persist as AuthPersist)(
    (set, get) => ({
      token: null,
      isAuthenticated: false,
      user: null,

      // 登录：设置令牌并解码用户信息
      login: (token: string) => {
        try {
          console.log('AuthStore: Starting login process with token:', token.substring(0, 20) + '...');
          const decoded = jwtDecode<DecodedToken>(token);
          console.log('AuthStore: Token decoded successfully:', decoded);

          // 检查令牌是否过期
          const currentTime = Date.now() / 1000;
          if (decoded.exp < currentTime) {
            console.error('AuthStore: Token has expired');
            set({ token: null, isAuthenticated: false, user: null });
            return;
          }

          const newState = {
            token,
            isAuthenticated: true,
            user: {
              id: decoded.userId,
              email: decoded.email,
              name: decoded.name
            }
          };

          console.log('AuthStore: Setting new auth state:', newState);
          set(newState);
          console.log('AuthStore: Login completed successfully');
        } catch (error) {
          console.error('AuthStore: Failed to decode token:', error);
          set({ token: null, isAuthenticated: false, user: null });
        }
      },

      // 登出：清除认证状态
      logout: () => {
        set({ token: null, isAuthenticated: false, user: null });
      },

      // 初始化：从存储中恢复并验证令牌
      initializeAuth: () => {
        const { token } = get();

        if (!token) {
          set({ isAuthenticated: false, user: null });
          return;
        }

        try {
          const decoded = jwtDecode<DecodedToken>(token);

          // 检查令牌是否过期
          const currentTime = Date.now() / 1000;
          if (decoded.exp < currentTime) {
            console.error('Token has expired during initialization');
            set({ token: null, isAuthenticated: false, user: null });
            return;
          }

          set({
            isAuthenticated: true,
            user: {
              id: decoded.userId,
              email: decoded.email,
              name: decoded.name
            }
          });
        } catch (error) {
          console.error('Failed to initialize auth:', error);
          set({ token: null, isAuthenticated: false, user: null });
        }
      }
    }),
    {
      name: 'auth-storage', // 本地存储的键名
      partialize: (state: AuthState) => ({ token: state.token }), // 只持久化令牌
    }
  )
);

export default useAuthStore;
