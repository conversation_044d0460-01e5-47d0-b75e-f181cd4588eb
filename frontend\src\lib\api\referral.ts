// 邀请相关API接口

import { fetchWithAuth } from './apiService';

// 邀请码接口
export interface ReferralCode {
  code: string;
  inviteLink: string;
  createdAt: string;
  isActive: boolean;
  hasInviter: boolean; // 是否已有邀请人
  inviterCode?: string; // 邀请人的邀请码
}

// 邀请统计接口
export interface ReferralStats {
  totalInvited: number;
  successfulReferrals: number;
  totalRewards: number;
  pendingRewards: number;
}

// 邀请记录接口
export interface ReferralRecord {
  id: string;
  invitedUserEmail: string;
  registeredAt: string;
  firstPaymentAt?: string;
  rewardAmount?: number;
  status: 'registered' | 'paid' | 'rewarded';
}

// 分页结果接口
export interface PaginatedReferralRecords {
  records: ReferralRecord[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    hasMore: boolean;
  };
}

// API响应基础接口
interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
}

// 邀请历史查询参数
export interface ReferralHistoryParams {
  page?: number;
  limit?: number;
  status?: 'all' | 'registered' | 'paid' | 'rewarded';
}

/**
 * 邀请相关API
 */
export const referralApi = {
  /**
   * 获取或生成用户邀请码
   */
  async getReferralCode(): Promise<ApiResponse<ReferralCode>> {
    try {
      return await fetchWithAuth<ApiResponse<ReferralCode>>('/referral/code', { method: 'GET' });
    } catch (error) {
      console.error('获取邀请码失败:', error);
      throw error;
    }
  },

  /**
   * 验证邀请码有效性（公开接口，无需认证）
   */
  async validateReferralCode(code: string): Promise<ApiResponse<{ valid: boolean }>> {
    try {
      const response = await fetch('/api/referral/code/validate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ code })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('验证邀请码失败:', error);
      throw error;
    }
  },

  /**
   * 设置邀请人邀请码（注册后补填）
   */
  async setInviterCode(inviterCode: string): Promise<ApiResponse> {
    try {
      return await fetchWithAuth<ApiResponse>('/referral/code/set-inviter', {
        method: 'POST',
        body: { inviterCode }
      });
    } catch (error) {
      console.error('设置邀请人邀请码失败:', error);
      throw error;
    }
  },

  /**
   * 获取邀请统计数据
   */
  async getStats(): Promise<ApiResponse<ReferralStats>> {
    try {
      return await fetchWithAuth<ApiResponse<ReferralStats>>('/referral/stats', { method: 'GET' });
    } catch (error) {
      console.error('获取邀请统计失败:', error);
      throw error;
    }
  },

  /**
   * 获取邀请历史记录
   */
  async getHistory(params: ReferralHistoryParams = {}): Promise<ApiResponse<PaginatedReferralRecords>> {
    try {
      const searchParams = new URLSearchParams();

      if (params.page) searchParams.append('page', params.page.toString());
      if (params.limit) searchParams.append('limit', params.limit.toString());
      if (params.status) searchParams.append('status', params.status);

      const url = `/referral/history${searchParams.toString() ? `?${searchParams.toString()}` : ''}`;

      return await fetchWithAuth<ApiResponse<PaginatedReferralRecords>>(url, { method: 'GET' });
    } catch (error) {
      console.error('获取邀请历史失败:', error);
      throw error;
    }
  },

  /**
   * 处理邀请奖励（内部接口）
   */
  async processReward(params: { orderId?: string; codeUsageId?: string }): Promise<ApiResponse<{
    rewardProcessed: boolean;
    rewardAmount?: number;
  }>> {
    try {
      const response = await fetch(`${API_BASE_URL}/api/referral/reward`, {
        method: 'POST',
        headers: createAuthHeaders(),
        body: JSON.stringify(params)
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('处理邀请奖励失败:', error);
      throw error;
    }
  }
};
