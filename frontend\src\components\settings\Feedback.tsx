import React, { useState } from 'react';
import { MessageSquare, Send, CheckCircle, AlertCircle, Bug, Lightbulb, HelpCircle } from 'lucide-react';
import useAuthStore from '../../stores/authStore';

const Feedback: React.FC = () => {
  const { token } = useAuthStore();
  
  const [feedbackForm, setFeedbackForm] = useState({
    type: '',
    content: '',
    contact: '',
  });
  
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitMessage, setSubmitMessage] = useState<{
    type: 'success' | 'error' | null;
    text: string;
  }>({ type: null, text: '' });

  const feedbackTypes = [
    {
      id: 'bug',
      name: 'Bug报告',
      description: '报告系统错误或异常行为',
      icon: Bug,
      color: 'text-red-600 bg-red-50 border-red-200',
    },
    {
      id: 'feature',
      name: '功能建议',
      description: '建议新功能或改进现有功能',
      icon: Lightbulb,
      color: 'text-yellow-600 bg-yellow-50 border-yellow-200',
    },
    {
      id: 'question',
      name: '使用问题',
      description: '关于产品使用的疑问',
      icon: HelpCircle,
      color: 'text-blue-600 bg-blue-50 border-blue-200',
    },
    {
      id: 'other',
      name: '其他',
      description: '其他类型的反馈',
      icon: MessageSquare,
      color: 'text-gray-600 bg-gray-50 border-gray-200',
    },
  ];

  // 处理表单变化
  const handleFormChange = (field: keyof typeof feedbackForm, value: string) => {
    setFeedbackForm(prev => ({ ...prev, [field]: value }));
    setSubmitMessage({ type: null, text: '' }); // 清除消息
  };

  // 提交反馈
  const submitFeedback = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!token) {
      setSubmitMessage({ type: 'error', text: '请先登录' });
      return;
    }

    // 表单验证
    if (!feedbackForm.content.trim()) {
      setSubmitMessage({ type: 'error', text: '请填写反馈内容' });
      return;
    }

    if (feedbackForm.content.length > 2000) {
      setSubmitMessage({ type: 'error', text: '反馈内容不能超过2000个字符' });
      return;
    }

    setIsSubmitting(true);

    try {
      const response = await fetch('/api/feedback', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: feedbackForm.type || null,
          content: feedbackForm.content.trim(),
          contact: feedbackForm.contact.trim() || null,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || '提交反馈失败');
      }

      setSubmitMessage({ type: 'success', text: '感谢您的反馈！我们会认真考虑您的建议。' });
      setFeedbackForm({ type: '', content: '', contact: '' });
    } catch (error) {
      console.error('提交反馈失败:', error);
      setSubmitMessage({ 
        type: 'error', 
        text: error instanceof Error ? error.message : '提交反馈失败，请稍后重试' 
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-2 flex items-center gap-2">
          <MessageSquare className="w-5 h-5 text-blue-600" />
          意见反馈
        </h3>
        <p className="text-gray-600 mb-6">
          您的反馈对我们非常重要，帮助我们不断改进产品体验。
        </p>

        <form onSubmit={submitFeedback} className="space-y-6">
          {/* 反馈类型选择 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">
              反馈类型（可选）
            </label>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
              {feedbackTypes.map((type) => {
                const Icon = type.icon;
                const isSelected = feedbackForm.type === type.id;

                return (
                  <div
                    key={type.id}
                    className={`p-3 border-2 rounded-lg cursor-pointer transition-all ${
                      isSelected
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => handleFormChange('type', isSelected ? '' : type.id)}
                  >
                    <div className="flex items-center gap-3">
                      <div className={`p-2 rounded-lg border ${type.color}`}>
                        <Icon className="w-4 h-4" />
                      </div>
                      <div>
                        <div className="font-medium text-gray-900">{type.name}</div>
                        <div className="text-xs text-gray-600">{type.description}</div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* 反馈内容 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              反馈内容 <span className="text-red-500">*</span>
            </label>
            <textarea
              value={feedbackForm.content}
              onChange={(e) => handleFormChange('content', e.target.value)}
              rows={6}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
              placeholder="请详细描述您遇到的问题、建议或意见..."
              maxLength={2000}
            />
            <div className="flex justify-between items-center mt-2">
              <p className="text-xs text-gray-500">
                请尽可能详细地描述，这有助于我们更好地理解和解决问题
              </p>
              <span className={`text-xs ${
                feedbackForm.content.length > 1800 ? 'text-red-500' : 'text-gray-400'
              }`}>
                {feedbackForm.content.length}/2000
              </span>
            </div>
          </div>

          {/* 联系方式 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              联系方式（可选）
            </label>
            <input
              type="text"
              value={feedbackForm.contact}
              onChange={(e) => handleFormChange('contact', e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="如需回复，请留下您的邮箱或微信号"
              maxLength={200}
            />
            <p className="text-xs text-gray-500 mt-1">
              如果您希望我们就您的反馈与您联系，请留下联系方式
            </p>
          </div>

          {/* 消息提示 */}
          {submitMessage.text && (
            <div className={`p-4 rounded-lg flex items-center gap-3 ${
              submitMessage.type === 'success' 
                ? 'bg-green-50 text-green-700 border border-green-200' 
                : 'bg-red-50 text-red-700 border border-red-200'
            }`}>
              {submitMessage.type === 'success' ? (
                <CheckCircle className="w-5 h-5 flex-shrink-0" />
              ) : (
                <AlertCircle className="w-5 h-5 flex-shrink-0" />
              )}
              <span>{submitMessage.text}</span>
            </div>
          )}

          {/* 提交按钮 */}
          <button
            type="submit"
            disabled={isSubmitting || !feedbackForm.content.trim()}
            className="w-full flex items-center justify-center gap-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-medium"
          >
            <Send className="w-4 h-4" />
            {isSubmitting ? '提交中...' : '提交反馈'}
          </button>
        </form>
      </div>

      {/* 其他联系方式 */}
      <div className="bg-gray-50 rounded-lg p-4">
        <h4 className="font-medium text-gray-900 mb-2">其他联系方式</h4>
        <p className="text-sm text-gray-600">
          如果您需要紧急支持或有其他问题，也可以通过以下方式联系我们：
        </p>
        <ul className="text-sm text-gray-600 mt-2 space-y-1">
          <li>• 邮箱：<EMAIL></li>
          <li>• 工作时间：周一至周五 9:00-18:00</li>
        </ul>
      </div>
    </div>
  );
};

export default Feedback;
