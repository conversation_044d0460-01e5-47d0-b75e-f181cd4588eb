import { EventEmitter } from 'events';
import { sessionConnectionManager } from '../websocket/managers/SessionConnectionManager.js';

// 🔥 监控指标接口
export interface SystemMetrics {
  timestamp: number;
  
  // 连接指标
  connections: {
    total: number;
    active: number;
    idle: number;
    error: number;
    averageLifetime: number;
  };
  
  // 性能指标
  performance: {
    memoryUsage: NodeJS.MemoryUsage;
    cpuUsage: number;
    uptime: number;
    eventLoopDelay: number;
  };
  
  // DashScope指标
  dashscope: {
    totalRequests: number;
    successfulRequests: number;
    failedRequests: number;
    averageResponseTime: number;
    connectionErrors: number;
    heartbeatFailures: number;
  };
  
  // 音频处理指标
  audio: {
    totalAudioProcessed: number;
    averageAudioSize: number;
    processingErrors: number;
    streamingSessions: number;
  };
}

// 🔥 告警级别
export enum AlertLevel {
  INFO = 'info',
  WARNING = 'warning',
  ERROR = 'error',
  CRITICAL = 'critical'
}

// 🔥 告警接口
export interface Alert {
  id: string;
  level: AlertLevel;
  title: string;
  message: string;
  timestamp: number;
  source: string;
  metadata?: any;
}

// 🔥 监控配置
export interface MonitoringConfig {
  metricsInterval: number;        // 指标收集间隔（毫秒）
  alertThresholds: {
    maxConnections: number;       // 最大连接数阈值
    maxMemoryUsage: number;       // 最大内存使用阈值（MB）
    maxCpuUsage: number;          // 最大CPU使用阈值（%）
    maxErrorRate: number;         // 最大错误率阈值（%）
    maxResponseTime: number;      // 最大响应时间阈值（毫秒）
  };
  retentionPeriod: number;        // 数据保留期（毫秒）
}

// 🔥 系统监控器
export class SystemMonitor extends EventEmitter {
  private config: MonitoringConfig;
  private metrics: SystemMetrics[] = [];
  private alerts: Alert[] = [];
  private metricsTimer: NodeJS.Timeout | null = null;
  private isRunning: boolean = false;
  
  // 性能计数器
  private counters = {
    dashscopeRequests: 0,
    dashscopeSuccesses: 0,
    dashscopeFailures: 0,
    dashscopeResponseTimes: [] as number[],
    audioProcessed: 0,
    audioSizes: [] as number[],
    audioErrors: 0
  };

  constructor(config?: Partial<MonitoringConfig>) {
    super();
    
    // 默认配置
    this.config = {
      metricsInterval: 30000,     // 30秒
      alertThresholds: {
        maxConnections: 80,       // 80个连接
        maxMemoryUsage: 1024,     // 1GB内存
        maxCpuUsage: 80,          // 80% CPU
        maxErrorRate: 10,         // 10% 错误率
        maxResponseTime: 5000     // 5秒响应时间
      },
      retentionPeriod: 24 * 60 * 60 * 1000, // 24小时
      ...config
    };
  }

  /**
   * 🔥 启动监控
   */
  start(): void {
    if (this.isRunning) {
      console.warn('⚠️ SystemMonitor is already running');
      return;
    }

    console.log('🔍 Starting SystemMonitor...');
    this.isRunning = true;
    
    // 启动指标收集
    this.metricsTimer = setInterval(() => {
      this.collectMetrics();
    }, this.config.metricsInterval);

    // 设置优雅关闭
    this.setupGracefulShutdown();
    
    console.log('✅ SystemMonitor started successfully');
  }

  /**
   * 🔥 停止监控
   */
  stop(): void {
    if (!this.isRunning) {
      return;
    }

    console.log('🛑 Stopping SystemMonitor...');
    this.isRunning = false;

    if (this.metricsTimer) {
      clearInterval(this.metricsTimer);
      this.metricsTimer = null;
    }

    console.log('✅ SystemMonitor stopped');
  }

  /**
   * 🔥 收集系统指标
   */
  private collectMetrics(): void {
    try {
      const connectionStats = sessionConnectionManager.getConnectionStats();
      const memoryUsage = process.memoryUsage();
      const cpuUsage = process.cpuUsage();
      
      const metrics: SystemMetrics = {
        timestamp: Date.now(),
        
        connections: {
          total: connectionStats.totalConnections,
          active: connectionStats.connectionsByState.streaming || 0,
          idle: connectionStats.connectionsByState.connected || 0,
          error: connectionStats.connectionsByState.error || 0,
          averageLifetime: 0 // TODO: 计算平均连接生命周期
        },
        
        performance: {
          memoryUsage,
          cpuUsage: this.calculateCpuUsage(cpuUsage),
          uptime: process.uptime(),
          eventLoopDelay: 0 // TODO: 测量事件循环延迟
        },
        
        dashscope: {
          totalRequests: this.counters.dashscopeRequests,
          successfulRequests: this.counters.dashscopeSuccesses,
          failedRequests: this.counters.dashscopeFailures,
          averageResponseTime: this.calculateAverage(this.counters.dashscopeResponseTimes),
          connectionErrors: 0, // TODO: 从连接管理器获取
          heartbeatFailures: 0 // TODO: 从连接管理器获取
        },
        
        audio: {
          totalAudioProcessed: this.counters.audioProcessed,
          averageAudioSize: this.calculateAverage(this.counters.audioSizes),
          processingErrors: this.counters.audioErrors,
          streamingSessions: connectionStats.connectionsByState.streaming || 0
        }
      };

      this.metrics.push(metrics);
      this.checkAlerts(metrics);
      this.cleanupOldData();

      // 发出指标事件
      this.emit('metrics', metrics);

      console.log('📊 System metrics collected:', {
        connections: metrics.connections.total,
        memoryMB: Math.round(metrics.performance.memoryUsage.heapUsed / 1024 / 1024),
        cpuUsage: metrics.performance.cpuUsage.toFixed(1) + '%'
      });

    } catch (error) {
      console.error('❌ Error collecting metrics:', error);
      this.createAlert(AlertLevel.ERROR, 'Metrics Collection Failed', 
        `Failed to collect system metrics: ${error}`, 'SystemMonitor');
    }
  }

  /**
   * 🔥 检查告警条件
   */
  private checkAlerts(metrics: SystemMetrics): void {
    const { alertThresholds } = this.config;

    // 检查连接数
    if (metrics.connections.total > alertThresholds.maxConnections) {
      this.createAlert(AlertLevel.WARNING, 'High Connection Count',
        `Connection count (${metrics.connections.total}) exceeds threshold (${alertThresholds.maxConnections})`,
        'ConnectionMonitor', { connectionCount: metrics.connections.total });
    }

    // 检查内存使用
    const memoryUsageMB = metrics.performance.memoryUsage.heapUsed / 1024 / 1024;
    if (memoryUsageMB > alertThresholds.maxMemoryUsage) {
      this.createAlert(AlertLevel.WARNING, 'High Memory Usage',
        `Memory usage (${memoryUsageMB.toFixed(1)}MB) exceeds threshold (${alertThresholds.maxMemoryUsage}MB)`,
        'MemoryMonitor', { memoryUsageMB });
    }

    // 检查CPU使用
    if (metrics.performance.cpuUsage > alertThresholds.maxCpuUsage) {
      this.createAlert(AlertLevel.WARNING, 'High CPU Usage',
        `CPU usage (${metrics.performance.cpuUsage.toFixed(1)}%) exceeds threshold (${alertThresholds.maxCpuUsage}%)`,
        'CpuMonitor', { cpuUsage: metrics.performance.cpuUsage });
    }

    // 检查错误率
    const errorRate = metrics.dashscope.totalRequests > 0 
      ? (metrics.dashscope.failedRequests / metrics.dashscope.totalRequests) * 100 
      : 0;
    if (errorRate > alertThresholds.maxErrorRate) {
      this.createAlert(AlertLevel.ERROR, 'High Error Rate',
        `DashScope error rate (${errorRate.toFixed(1)}%) exceeds threshold (${alertThresholds.maxErrorRate}%)`,
        'DashScopeMonitor', { errorRate });
    }

    // 检查响应时间
    if (metrics.dashscope.averageResponseTime > alertThresholds.maxResponseTime) {
      this.createAlert(AlertLevel.WARNING, 'High Response Time',
        `Average response time (${metrics.dashscope.averageResponseTime}ms) exceeds threshold (${alertThresholds.maxResponseTime}ms)`,
        'DashScopeMonitor', { responseTime: metrics.dashscope.averageResponseTime });
    }
  }

  /**
   * 🔥 创建告警
   */
  private createAlert(level: AlertLevel, title: string, message: string, source: string, metadata?: any): void {
    const alert: Alert = {
      id: `alert-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      level,
      title,
      message,
      timestamp: Date.now(),
      source,
      metadata
    };

    this.alerts.push(alert);
    this.emit('alert', alert);

    console.log(`🚨 [${level.toUpperCase()}] ${title}: ${message}`);
  }

  /**
   * 🔥 记录DashScope请求
   */
  recordDashScopeRequest(success: boolean, responseTime: number): void {
    this.counters.dashscopeRequests++;
    if (success) {
      this.counters.dashscopeSuccesses++;
    } else {
      this.counters.dashscopeFailures++;
    }
    this.counters.dashscopeResponseTimes.push(responseTime);
    
    // 限制响应时间数组大小
    if (this.counters.dashscopeResponseTimes.length > 1000) {
      this.counters.dashscopeResponseTimes = this.counters.dashscopeResponseTimes.slice(-500);
    }
  }

  /**
   * 🔥 记录音频处理
   */
  recordAudioProcessing(audioSize: number, success: boolean): void {
    this.counters.audioProcessed++;
    this.counters.audioSizes.push(audioSize);
    
    if (!success) {
      this.counters.audioErrors++;
    }
    
    // 限制音频大小数组大小
    if (this.counters.audioSizes.length > 1000) {
      this.counters.audioSizes = this.counters.audioSizes.slice(-500);
    }
  }

  /**
   * 🔥 获取最新指标
   */
  getLatestMetrics(): SystemMetrics | null {
    return this.metrics.length > 0 ? this.metrics[this.metrics.length - 1] : null;
  }

  /**
   * 🔥 获取告警历史
   */
  getAlerts(level?: AlertLevel): Alert[] {
    if (level) {
      return this.alerts.filter(alert => alert.level === level);
    }
    return [...this.alerts];
  }

  /**
   * 🔥 计算CPU使用率
   */
  private calculateCpuUsage(cpuUsage: NodeJS.CpuUsage): number {
    // 简化的CPU使用率计算
    return (cpuUsage.user + cpuUsage.system) / 1000000; // 转换为毫秒
  }

  /**
   * 🔥 计算平均值
   */
  private calculateAverage(values: number[]): number {
    if (values.length === 0) return 0;
    return values.reduce((sum, value) => sum + value, 0) / values.length;
  }

  /**
   * 🔥 清理旧数据
   */
  private cleanupOldData(): void {
    const cutoffTime = Date.now() - this.config.retentionPeriod;
    
    // 清理旧指标
    this.metrics = this.metrics.filter(metric => metric.timestamp > cutoffTime);
    
    // 清理旧告警
    this.alerts = this.alerts.filter(alert => alert.timestamp > cutoffTime);
  }

  /**
   * 🔥 设置优雅关闭
   */
  private setupGracefulShutdown(): void {
    const shutdown = () => {
      this.stop();
    };

    process.on('SIGTERM', shutdown);
    process.on('SIGINT', shutdown);
  }
}

// 🔥 全局监控实例
export const systemMonitor = new SystemMonitor();
