import { EventEmitter } from 'events';
import { DashScopeProvider } from '../providers/asr/dashscopeProvider';
import { ASRProvider } from '../../types/asr';

// 🔥 会话连接状态
export enum SessionConnectionState {
  IDLE = 'idle',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  STREAMING = 'streaming',
  DISCONNECTING = 'disconnecting',
  DISCONNECTED = 'disconnected',
  ERROR = 'error'
}

// 🔥 会话连接信息
export interface SessionConnection {
  sessionId: string;
  userId: string;
  provider: DashScopeProvider;
  state: SessionConnectionState;
  createdAt: number;
  lastActiveAt: number;
  errorCount: number;
  totalAudioSent: number;
  connectionAttempts: number;
}

// 🔥 会话连接配置
export interface SessionConnectionConfig {
  maxIdleTime: number;        // 最大空闲时间（毫秒）
  maxErrorCount: number;      // 最大错误次数
  cleanupInterval: number;    // 清理间隔（毫秒）
  maxConnections: number;     // 最大连接数
  connectionTimeout: number;  // 连接超时时间
}

// 🔥 会话级连接管理器 - 解决连接复用冲突问题
export class SessionConnectionManager extends EventEmitter {
  private connections: Map<string, SessionConnection> = new Map();
  private config: SessionConnectionConfig;
  private cleanupTimer: NodeJS.Timeout | null = null;
  private isShuttingDown: boolean = false;

  constructor(config?: Partial<SessionConnectionConfig>) {
    super();
    
    // 🔥 默认配置（基于官方文档优化）
    this.config = {
      maxIdleTime: 300000,      // 5分钟空闲超时
      maxErrorCount: 3,         // 最多3次错误
      cleanupInterval: 60000,   // 1分钟清理一次
      maxConnections: 100,      // 最多100个并发连接
      connectionTimeout: 30000, // 30秒连接超时
      ...config
    };

    this.startCleanupTimer();
    this.setupGracefulShutdown();
  }

  /**
   * 🔥 为会话创建独立的DashScope连接
   */
  async createSessionConnection(sessionId: string, userId: string): Promise<SessionConnection> {
    console.log('🔗 Creating dedicated connection for session:', { sessionId, userId });

    // 检查连接数限制
    if (this.connections.size >= this.config.maxConnections) {
      throw new Error(`Maximum connections limit reached: ${this.config.maxConnections}`);
    }

    // 检查是否已存在连接
    if (this.connections.has(sessionId)) {
      const existing = this.connections.get(sessionId)!;
      console.log('⚠️ Session connection already exists:', { sessionId, state: existing.state });
      return existing;
    }

    try {
      // 🔥 创建专用的DashScope Provider实例
      const provider = new DashScopeProvider();
      
      // 创建会话连接记录
      const connection: SessionConnection = {
        sessionId,
        userId,
        provider,
        state: SessionConnectionState.IDLE,
        createdAt: Date.now(),
        lastActiveAt: Date.now(),
        errorCount: 0,
        totalAudioSent: 0,
        connectionAttempts: 0
      };

      // 设置连接事件监听
      this.setupConnectionEventListeners(connection);

      // 存储连接
      this.connections.set(sessionId, connection);

      console.log('✅ Session connection created successfully:', {
        sessionId,
        userId,
        totalConnections: this.connections.size
      });

      // 发出连接创建事件
      this.emit('connectionCreated', connection);

      return connection;
    } catch (error) {
      console.error('❌ Failed to create session connection:', { sessionId, userId, error });
      throw error;
    }
  }

  /**
   * 🔥 获取会话连接
   */
  getSessionConnection(sessionId: string): SessionConnection | null {
    const connection = this.connections.get(sessionId);
    if (connection) {
      connection.lastActiveAt = Date.now();
    }
    return connection || null;
  }

  /**
   * 🔥 关闭会话连接
   */
  async closeSessionConnection(sessionId: string, reason: string = 'Session ended'): Promise<void> {
    const connection = this.connections.get(sessionId);
    if (!connection) {
      console.warn('⚠️ Attempted to close non-existent session connection:', sessionId);
      return;
    }

    console.log('🔌 Closing session connection:', { sessionId, reason, state: connection.state });

    try {
      connection.state = SessionConnectionState.DISCONNECTING;

      // 停止流式处理
      if (connection.provider.isStreaming) {
        await connection.provider.stopStreaming();
      }

      // 断开连接
      await connection.provider.disconnect();

      connection.state = SessionConnectionState.DISCONNECTED;

      // 移除连接
      this.connections.delete(sessionId);

      console.log('✅ Session connection closed successfully:', {
        sessionId,
        reason,
        totalConnections: this.connections.size
      });

      // 发出连接关闭事件
      this.emit('connectionClosed', { sessionId, reason });

    } catch (error) {
      console.error('❌ Error closing session connection:', { sessionId, error });
      connection.state = SessionConnectionState.ERROR;
      connection.errorCount++;
    }
  }

  /**
   * 🔥 设置连接事件监听器
   */
  private setupConnectionEventListeners(connection: SessionConnection): void {
    const { provider, sessionId } = connection;

    // 连接状态变化
    provider.on('connected', () => {
      connection.state = SessionConnectionState.CONNECTED;
      connection.lastActiveAt = Date.now();
      console.log('🔗 Session connection established:', sessionId);
    });

    provider.on('disconnected', () => {
      connection.state = SessionConnectionState.DISCONNECTED;
      console.log('🔌 Session connection lost:', sessionId);
    });

    // 流式处理状态
    provider.on('streamingStarted', () => {
      connection.state = SessionConnectionState.STREAMING;
      connection.lastActiveAt = Date.now();
    });

    provider.on('streamingStopped', () => {
      connection.state = SessionConnectionState.CONNECTED;
      connection.lastActiveAt = Date.now();
    });

    // 错误处理
    provider.on('error', (error: any) => {
      connection.state = SessionConnectionState.ERROR;
      connection.errorCount++;
      connection.lastActiveAt = Date.now();
      
      console.error('❌ Session connection error:', { sessionId, error, errorCount: connection.errorCount });

      // 如果错误次数过多，自动关闭连接
      if (connection.errorCount >= this.config.maxErrorCount) {
        console.warn('⚠️ Too many errors, closing session connection:', sessionId);
        this.closeSessionConnection(sessionId, 'Too many errors');
      }
    });

    // 音频数据统计
    provider.on('audioSent', (size: number) => {
      connection.totalAudioSent += size;
      connection.lastActiveAt = Date.now();
    });
  }

  /**
   * 🔥 清理空闲和错误连接
   */
  private cleanupConnections(): void {
    if (this.isShuttingDown) return;

    const now = Date.now();
    const connectionsToClose: string[] = [];

    for (const [sessionId, connection] of this.connections) {
      const idleTime = now - connection.lastActiveAt;
      
      // 检查空闲超时
      if (idleTime > this.config.maxIdleTime) {
        console.log('🧹 Cleaning up idle connection:', { sessionId, idleTime });
        connectionsToClose.push(sessionId);
        continue;
      }

      // 检查错误连接
      if (connection.state === SessionConnectionState.ERROR && 
          connection.errorCount >= this.config.maxErrorCount) {
        console.log('🧹 Cleaning up error connection:', { sessionId, errorCount: connection.errorCount });
        connectionsToClose.push(sessionId);
        continue;
      }
    }

    // 关闭需要清理的连接
    for (const sessionId of connectionsToClose) {
      this.closeSessionConnection(sessionId, 'Cleanup');
    }

    if (connectionsToClose.length > 0) {
      console.log('🧹 Cleanup completed:', {
        closedConnections: connectionsToClose.length,
        remainingConnections: this.connections.size
      });
    }
  }

  /**
   * 🔥 启动清理定时器
   */
  private startCleanupTimer(): void {
    this.cleanupTimer = setInterval(() => {
      this.cleanupConnections();
    }, this.config.cleanupInterval);
  }

  /**
   * 🔥 设置优雅关闭
   */
  private setupGracefulShutdown(): void {
    const shutdown = async () => {
      console.log('🛑 SessionConnectionManager shutting down...');
      this.isShuttingDown = true;

      if (this.cleanupTimer) {
        clearInterval(this.cleanupTimer);
        this.cleanupTimer = null;
      }

      // 关闭所有连接
      const closePromises = Array.from(this.connections.keys()).map(sessionId =>
        this.closeSessionConnection(sessionId, 'Server shutdown')
      );

      await Promise.all(closePromises);
      console.log('✅ SessionConnectionManager shutdown completed');
    };

    process.on('SIGTERM', shutdown);
    process.on('SIGINT', shutdown);
  }

  /**
   * 🔥 获取连接统计信息
   */
  getConnectionStats() {
    const stats = {
      totalConnections: this.connections.size,
      connectionsByState: {} as Record<SessionConnectionState, number>,
      totalAudioSent: 0,
      averageErrorCount: 0
    };

    // 统计各状态连接数
    for (const state of Object.values(SessionConnectionState)) {
      stats.connectionsByState[state] = 0;
    }

    let totalErrors = 0;
    for (const connection of this.connections.values()) {
      stats.connectionsByState[connection.state]++;
      stats.totalAudioSent += connection.totalAudioSent;
      totalErrors += connection.errorCount;
    }

    stats.averageErrorCount = this.connections.size > 0 ? totalErrors / this.connections.size : 0;

    return stats;
  }
}

// 🔥 全局会话连接管理器实例
export const sessionConnectionManager = new SessionConnectionManager();
