import React, { useState, useEffect } from 'react';

interface SendCodeButtonProps {
  onClick: () => Promise<void>;
  disabled?: boolean;
  cooldownSeconds?: number;
  className?: string;
  children?: React.ReactNode;
}

const SendCodeButton: React.FC<SendCodeButtonProps> = ({
  onClick,
  disabled = false,
  cooldownSeconds = 60,
  className = '',
  children = '发送验证码'
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [countdown, setCountdown] = useState(0);

  // 倒计时效果
  useEffect(() => {
    let timer: NodeJS.Timeout;
    
    if (countdown > 0) {
      timer = setTimeout(() => {
        setCountdown(countdown - 1);
      }, 1000);
    }

    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [countdown]);

  // 处理点击事件
  const handleClick = async () => {
    if (disabled || isLoading || countdown > 0) return;

    setIsLoading(true);
    
    try {
      await onClick();
      // 发送成功后开始倒计时
      setCountdown(cooldownSeconds);
    } catch (error) {
      console.error('发送验证码失败:', error);
      // 发送失败不启动倒计时
    } finally {
      setIsLoading(false);
    }
  };

  // 按钮状态判断
  const isButtonDisabled = disabled || isLoading || countdown > 0;

  // 按钮文本
  const getButtonText = () => {
    if (isLoading) {
      return (
        <span className="flex items-center gap-2">
          <svg className="animate-spin h-4 w-4" viewBox="0 0 24 24">
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
              fill="none"
            />
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            />
          </svg>
          发送中...
        </span>
      );
    }
    
    if (countdown > 0) {
      return `重新发送 (${countdown}s)`;
    }
    
    return children;
  };

  return (
    <button
      type="button"
      onClick={handleClick}
      disabled={isButtonDisabled}
      className={`
        px-4 py-2 text-sm font-medium rounded-md transition-all duration-200
        ${isButtonDisabled
          ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
          : 'bg-blue-600 text-white hover:bg-blue-700 active:bg-blue-800 shadow-sm hover:shadow-md'
        }
        ${className}
      `}
    >
      {getButtonText()}
    </button>
  );
};

export default SendCodeButton;
