import React, { useState, useEffect } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { ChevronDown, Lock, Mail, Phone, Send, Key, Loader, Gift } from 'lucide-react';
import { z } from 'zod';
import { registerSchema, loginSchema } from '../lib/validations/auth';
import useAuthStore from '../stores/authStore';
import { registerUser, loginUser } from '../lib/api/auth';
import useDocumentTitle from '../hooks/useDocumentTitle';
import VerificationCodeInput from '../components/VerificationCodeInput';
import SendCodeButton from '../components/SendCodeButton';
import { sendVerificationCode, loginWithCode, isValidEmail, isValidPhone, isValidCode } from '../lib/api/verification';
import { useToastContext } from '../contexts/ToastContext';
import { referralApi } from '../lib/api/referral';

interface StandaloneLoginPageProps {
  initialMode?: 'login' | 'register';
}

const StandaloneLoginPage: React.FC<StandaloneLoginPageProps> = ({ initialMode = 'login' }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { showError, showSuccess } = useToastContext();
  const [activeTab, setActiveTab] = useState<'phone' | 'email'>('email'); // 默认邮箱登录
  // 注释掉密码登录功能，只保留验证码登录
  // const [emailLoginType, setEmailLoginType] = useState<'code' | 'password'>('code');
  // const [phoneLoginType, setPhoneLoginType] = useState<'code' | 'password'>('code');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [email, setEmail] = useState('');
  const [code, setCode] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  // 使用统一的错误处理，不再需要单独的密码错误状态
  const [countryCode] = useState('+86');
  const [agreementChecked, setAgreementChecked] = useState(false);
  const [secondsLeft, setSecondsLeft] = useState(0);
  // 移除注册/登录区分，统一为验证码登录
  const showRegister = false; // 始终为登录模式
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [showCodeInput, setShowCodeInput] = useState(false); // 控制验证码输入框显示

  // 邀请码相关状态
  const [inviteCode, setInviteCode] = useState('');
  const [isValidatingInvite, setIsValidatingInvite] = useState(false);
  const [inviteCodeValid, setInviteCodeValid] = useState<boolean | null>(null);
  const [showInviteInput, setShowInviteInput] = useState(false);

  // 设置页面标题
  useDocumentTitle('登录');

  // 处理URL参数中的邀请码
  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    const inviteParam = searchParams.get('invite');
    if (inviteParam) {
      setInviteCode(inviteParam);
      setShowInviteInput(true);
      // 自动验证邀请码
      validateInviteCode(inviteParam);
    }
  }, [location.search]);

  // 邀请码验证函数
  const validateInviteCode = async (code: string) => {
    if (!code.trim()) {
      setInviteCodeValid(null);
      return;
    }

    setIsValidatingInvite(true);
    try {
      const response = await referralApi.validateReferralCode(code.trim());
      setInviteCodeValid(response.success && response.data?.valid === true);
    } catch (error) {
      console.error('验证邀请码失败:', error);
      setInviteCodeValid(false);
    } finally {
      setIsValidatingInvite(false);
    }
  };

  // 监听输入，控制验证码输入框显示
  useEffect(() => {
    if (activeTab === 'email' && email && isValidEmail(email)) {
      setShowCodeInput(true);
    } else if (activeTab === 'phone' && phoneNumber && isValidPhone(phoneNumber)) {
      setShowCodeInput(true);
    } else {
      setShowCodeInput(false);
      setCode(''); // 清空验证码
    }
  }, [email, phoneNumber, activeTab]);

  const handleSendCode = async () => {
    if (secondsLeft > 0) return;

    // 检查是否同意服务协议
    if (!agreementChecked) {
      showError('请先阅读并同意服务协议');
      return;
    }

    // 手机验证码维护提示
    if (activeTab === 'phone') {
      showError('功能维护中，请使用邮箱账号登录');
      return;
    }

    // 验证输入
    const identifier = activeTab === 'email' ? email : phoneNumber;
    if (!identifier) {
      showError(activeTab === 'email' ? '请输入邮箱地址' : '请输入手机号');
      return;
    }

    // 验证格式
    if (activeTab === 'email' && !isValidEmail(identifier)) {
      showError('请输入有效的邮箱地址');
      return;
    }

    if (activeTab === 'phone' && !isValidPhone(identifier)) {
      showError('请输入有效的手机号');
      return;
    }

    try {
      setError(null);
      const type = activeTab === 'email' ? 'EMAIL' : 'SMS';
      const response = await sendVerificationCode(identifier, type);

      if (response.success) {
        setSuccessMessage(`验证码已发送到您的${activeTab === 'email' ? '邮箱' : '手机'}`);

        // 开始倒计时
        setSecondsLeft(60);
        const timer = setInterval(() => {
          setSecondsLeft((prev) => {
            if (prev <= 1) {
              clearInterval(timer);
              return 0;
            }
            return prev - 1;
          });
        }, 1000);
      }
    } catch (error: any) {
      setError(error.message || '发送验证码失败，请稍后重试');
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    console.log('handleSubmit called');
    console.log('isLoading:', isLoading);
    console.log('showRegister:', showRegister);
    console.log('activeTab:', activeTab);
    console.log('email:', email);
    console.log('password:', password);
    console.log('agreementChecked:', agreementChecked);

    if (isLoading) {
      console.log('Returning early due to isLoading');
      return;
    }

    // 表单验证 - 只验证验证码登录
    try {
      // 验证码登录只需要验证邮箱和验证码
      if (activeTab === 'email') {
        if (!email || !isValidEmail(email)) {
          throw new Error('请输入有效的邮箱地址');
        }
        if (!code || !isValidCode(code)) {
          throw new Error('请输入6位数字验证码');
        }
      } else if (activeTab === 'phone') {
        if (!phoneNumber || !isValidPhone(phoneNumber)) {
          throw new Error('请输入有效的手机号');
        }
        if (!code || !isValidCode(code)) {
          throw new Error('请输入6位数字验证码');
        }
      }
    } catch (error) {
      if (error instanceof z.ZodError) {
        const errorMessages = error.errors.map(err => `${err.path}: ${err.message}`).join(', ');
        setError(errorMessages);
        return;
      }
    }

    // 检查是否同意用户协议
    if (!agreementChecked) {
      setError('请阅读并同意用户协议和隐私政策');
      return;
    }

    setIsLoading(true);
    setError(null);
    setSuccessMessage(null);

    try {
      // 统一的验证码登录逻辑（自动注册）
      if (activeTab === 'phone') {
        // 手机验证码登录（维护中）
        showError('功能维护中，请使用邮箱账号登录');
        setIsLoading(false);
        return;
      } else if (activeTab === 'email') {
        // 邮箱验证码登录（未注册用户自动注册）
        if (!isValidCode(code)) {
          showError('请输入6位数字验证码');
          setIsLoading(false);
          return;
        }

        const response = await loginWithCode(email, code, 'EMAIL', inviteCode.trim() || undefined);

        if (response.success && response.data) {
          console.log('Login with code successful, updating auth state...');

          // 更新全局状态
          useAuthStore.getState().login(response.data.token);

          setSuccessMessage('登录成功！正在跳转...');

          // 等待状态更新后再跳转
          setTimeout(() => {
            console.log('Navigating to dashboard...');
            navigate('/dashboard');
          }, 500);
        }
      }
    } catch (err) {
      console.error('操作失败:', err);
      if (err instanceof Error) {
        setError(err.message);
      } else {
        setError('操作失败，请稍后再试');
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex flex-col md:flex-row">
      {/* 左侧产品介绍 */}
      <div className="bg-gradient-to-br from-blue-600 via-blue-500 to-cyan-200 md:w-1/2 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 relative overflow-hidden">
        {/* 现代感装饰元素 */}
        <div className="absolute top-0 left-0 w-full h-full">
          {/* 主要装饰 - 右上角橙粉渐变 */}
          <div className="absolute top-0 right-0 w-96 h-96 bg-gradient-to-bl from-orange-300 via-pink-300 to-rose-400 rounded-full mix-blend-multiply filter blur-3xl opacity-60 animate-blob"></div>

          {/* 次要装饰 - 左下角紫青渐变 */}
          <div className="absolute bottom-0 left-0 w-80 h-80 bg-gradient-to-tr from-purple-300 via-violet-300 to-cyan-300 rounded-full mix-blend-multiply filter blur-3xl opacity-50 animate-blob animation-delay-4000"></div>

          {/* 中心点缀 - 浮动的小装饰 */}
          <div className="absolute top-1/3 left-1/3 w-32 h-32 bg-gradient-to-r from-pink-200 to-orange-200 rounded-full mix-blend-multiply filter blur-xl opacity-40 animate-blob animation-delay-2000"></div>
        </div>
        <div className="flex flex-col justify-center items-center h-full p-8 lg:p-12 max-w-xl mx-auto text-center relative z-10">
          <div className="animate-fade-in">
            <h2 className="text-3xl lg:text-5xl font-bold text-white mb-4">智能 <span className="bg-gradient-to-r from-orange-200 via-pink-200 to-cyan-200 bg-clip-text text-transparent font-bold">AI</span> 面试助手</h2>
            <h2 className="text-2xl lg:text-3xl font-medium text-white mb-8">提升面试成功率，加速职业进阶</h2>
            <div className="flex justify-between text-center mb-12 max-w-md">
              <div className="flex-1">
                <p className="text-2xl font-mono font-bold text-white">95%</p>
                <p className="text-sm text-blue-100">用户满意度</p>
              </div>
              <div className="flex-1">
                <p className="text-2xl font-mono font-bold text-white">3倍</p>
                <p className="text-sm text-blue-100">面试通过率</p>
              </div>
              <div className="flex-1">
                <p className="text-2xl font-mono font-bold text-white">30%</p>
                <p className="text-sm text-blue-100">平均调薪提升</p>
              </div>
            </div>
          </div>

          <div className="relative mt-8">
            <div className="relative">
              <p className="text-sm text-blue-50 mb-4 font-medium">面试君采用先进的AI技术，实时分析面试问题并提供专业建议。不再因为紧张而失误，让你在面试中展现最佳状态。</p>
              <p className="text-sm text-blue-50 mb-4 font-medium">我们的用户已经覆盖了互联网、金融、教育等多个行业领域。无论你是应届生还是资深人士，面试君都能为你量身定制面试方案。</p>
              <p className="text-sm text-blue-50 mb-4 font-medium">加入面试君，你将获得专属面试指导、技能评估和沟通技巧提升。让我们一起探索更多职业可能性，实现你的职场梦想。</p>
            </div>
          </div>
        </div>
      </div>

      {/* 右侧登录表单 */}
      <div className="md:w-1/2 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 bg-slate-50">
        <div className="w-full max-w-md bg-white p-8 rounded-2xl shadow-xl border border-gray-100">
          <div className="mb-8">
            <Link to="/" className="inline-block">
              <h1 className="text-5xl font-extrabold text-gray-800">面试<span className="bg-gradient-to-r from-blue-600 to-cyan-500 bg-clip-text text-transparent">君</span></h1>
            </Link>
          </div>

          <h1 className="text-3xl font-bold mb-8 text-gray-800">
            {showRegister ? '注册账户' : '欢迎回来'}
          </h1>

          <div className="mb-8">
            <div className="flex border-b border-gray-200">
              <button
                className={`pb-3 px-6 text-sm font-medium transition-all duration-200 relative ${activeTab === 'phone' ? 'text-blue-600 font-semibold' : 'text-gray-500 hover:text-gray-700'}`}
                onClick={() => setActiveTab('phone')}
              >
                手机{showRegister ? '注册' : '登录'}
                {activeTab === 'phone' && (
                  <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-blue-600 to-cyan-500"></div>
                )}
              </button>
              <button
                className={`pb-3 px-6 text-sm font-medium transition-all duration-200 relative ${activeTab === 'email' ? 'text-blue-600 font-semibold' : 'text-gray-500 hover:text-gray-700'}`}
                onClick={() => setActiveTab('email')}
              >
                邮箱{showRegister ? '注册' : '登录'}
                {activeTab === 'email' && (
                  <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-blue-600 to-cyan-500"></div>
                )}
              </button>
            </div>
          </div>

          <form onSubmit={handleSubmit}>
            {activeTab === 'phone' ? (
              <>
                <div className="mb-6">
                  <div className="flex items-center border border-gray-300 rounded-xl bg-gray-50 p-3 focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-blue-500 transition-all duration-200 hover:border-gray-400">
                    <div className="flex items-center border-r pr-2">
                      <span className="text-gray-600 text-sm">{countryCode}</span>
                      <ChevronDown size={16} className="text-gray-400 ml-1" />
                    </div>
                    <Phone size={18} className="text-gray-400 mx-2" />
                    <input
                      type="tel"
                      name="phone"
                      id="phone"
                      autoComplete="tel"
                      autoSave="phone"
                      autoCapitalize="none"
                      autoCorrect="off"
                      spellCheck="false"
                      placeholder="请输入您的手机号"
                      className="bg-transparent border-none outline-none flex-grow text-gray-800 placeholder-gray-400"
                      value={phoneNumber}
                      onChange={(e) => setPhoneNumber(e.target.value)}
                    />
                  </div>
                </div>

                {/* 注释掉密码登录选项，只保留验证码登录 */}
                {/* 手机号只支持验证码登录 */}
              </>
            ) : (
              <>
                <div className="mb-6">
                  <div className="flex items-center border border-gray-300 rounded-xl bg-gray-50 p-3 focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-blue-500 transition-all duration-200 hover:border-gray-400">
                    <Mail size={18} className="text-gray-400 mx-2" />
                    <input
                      type="email"
                      name="email"
                      id="email"
                      autoComplete="email"
                      autoSave="email"
                      autoCapitalize="none"
                      autoCorrect="off"
                      spellCheck="false"
                      placeholder="请输入您的邮箱"
                      className="bg-transparent border-none outline-none flex-grow text-gray-800 placeholder-gray-400"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                    />
                  </div>
                </div>

                {/* 邀请码输入框 - 仅在邮箱模式下显示 */}
                <div className="mb-4">
                  <div className="flex items-center justify-between mb-2">
                    <label className="text-sm text-gray-600">邀请码（可选）</label>
                    <button
                      type="button"
                      onClick={() => setShowInviteInput(!showInviteInput)}
                      className="text-xs text-blue-600 hover:text-blue-700 transition-colors"
                    >
                      {showInviteInput ? '隐藏' : '有邀请码？'}
                    </button>
                  </div>

                  {showInviteInput && (
                    <div className={`transition-all duration-300 ease-in-out ${
                      showInviteInput ? 'opacity-100 max-h-20' : 'opacity-0 max-h-0 overflow-hidden'
                    }`}>
                      <div className="flex items-center border border-gray-300 rounded-xl bg-gray-50 p-3 focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-blue-500 transition-all duration-200 hover:border-gray-400">
                        <Gift size={18} className="text-gray-400 mx-2" />
                        <input
                          type="text"
                          placeholder="请输入8位邀请码"
                          className="bg-transparent border-none outline-none flex-grow text-gray-800 placeholder-gray-400 uppercase"
                          value={inviteCode}
                          onChange={(e) => {
                            const value = e.target.value.toUpperCase();
                            setInviteCode(value);
                            if (value.length === 8) {
                              validateInviteCode(value);
                            } else {
                              setInviteCodeValid(null);
                            }
                          }}
                          maxLength={8}
                        />
                        {isValidatingInvite && (
                          <Loader className="w-4 h-4 text-blue-500 animate-spin mx-2" />
                        )}
                        {inviteCodeValid === true && (
                          <div className="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center mx-2">
                            <span className="text-white text-xs">✓</span>
                          </div>
                        )}
                        {inviteCodeValid === false && (
                          <div className="w-4 h-4 bg-red-500 rounded-full flex items-center justify-center mx-2">
                            <span className="text-white text-xs">✗</span>
                          </div>
                        )}
                      </div>
                      {inviteCodeValid === false && (
                        <p className="text-xs text-red-500 mt-1 ml-2">邀请码无效或已过期</p>
                      )}
                      {inviteCodeValid === true && (
                        <p className="text-xs text-green-600 mt-1 ml-2">邀请码有效！注册后邀请者将获得奖励</p>
                      )}
                    </div>
                  )}
                </div>

                {/* 注释掉密码登录选项，只保留验证码登录 */}
                {/* 邮箱只支持验证码登录 */}
              </>
            )}

            {showRegister ? (
              <>
                <div className="mb-6">
                  <div className="flex items-center border border-gray-300 rounded-xl bg-gray-50 p-3 focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-blue-500 transition-all duration-200 hover:border-gray-400">
                    <Lock size={18} className="text-gray-400 mx-2" />
                    <input
                      type="password"
                      placeholder="设置密码"
                      className="bg-transparent border-none outline-none flex-grow text-gray-800 placeholder-gray-400"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                    />
                  </div>
                </div>
                <div className="mb-6">
                  <div className="flex items-center border border-gray-300 rounded-xl bg-gray-50 p-3 focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-blue-500 transition-all duration-200 hover:border-gray-400">
                    <Lock size={18} className="text-gray-400 mx-2" />
                    <input
                      type="password"
                      placeholder="确认密码"
                      className="bg-transparent border-none outline-none flex-grow text-gray-800 placeholder-gray-400"
                      value={confirmPassword}
                      onChange={(e) => setConfirmPassword(e.target.value)}
                    />
                  </div>
                  {/* 密码错误通过统一的错误消息处理 */}
                </div>
              </>
            ) : null}

            {/* 验证码输入框 - 只在邮箱输入有效时显示，带动画效果 */}
            {!showRegister && showCodeInput && (
              <div className={`mb-6 space-y-4 transition-all duration-500 ease-in-out transform ${
                showCodeInput ? 'opacity-100 translate-y-0 max-h-96' : 'opacity-0 -translate-y-4 max-h-0 overflow-hidden'
              }`}>
                <div className="text-center">
                  <p className="text-sm text-gray-600 mb-4">
                    请输入发送到您{activeTab === 'email' ? '邮箱' : '手机'}的6位验证码
                  </p>
                  <VerificationCodeInput
                    value={code}
                    onChange={setCode}
                    error={!!error && error.includes('验证码')}
                    disabled={isLoading}
                  />
                </div>

                {/* 服务协议勾选框已移至验证码输入框外面 */}

                <div className="flex justify-center">
                  <SendCodeButton
                    onClick={handleSendCode}
                    disabled={isLoading || !agreementChecked || !showCodeInput}
                    cooldownSeconds={60}
                  >
                    {secondsLeft > 0 ? `重新发送 (${secondsLeft}s)` : '获取验证码'}
                  </SendCodeButton>
                </div>
              </div>
            )}

            {/* 服务协议勾选框 - 一直显示 */}
            <div className="mb-6 flex items-start justify-center">
              <input
                type="checkbox"
                id="agreement"
                className="mr-3 h-5 w-5 text-blue-600 border-gray-300 rounded focus:ring-blue-500 focus:ring-2 mt-0.5"
                checked={agreementChecked}
                onChange={(e) => setAgreementChecked(e.target.checked)}
              />
              <label htmlFor="agreement" className="text-sm text-gray-600 leading-relaxed">
                我已阅读并同意 <a href="/service-agreement.html" target="_blank" className="text-blue-600 hover:text-blue-700 font-medium">服务协议</a> 和 <a href="/privacy-policy.html" target="_blank" className="text-blue-600 hover:text-blue-700 font-medium">隐私政策</a>
              </label>
            </div>

            {error && (
              <div className="mb-6 p-4 bg-red-50 border border-red-200 text-red-700 rounded-xl">
                {error}
              </div>
            )}

            {successMessage && (
              <div className="mb-6 p-4 bg-green-50 border border-green-200 text-green-700 rounded-xl">
                {successMessage}
              </div>
            )}

            <button
              type="submit"
              className="w-full bg-gradient-to-r from-blue-600 via-blue-500 to-cyan-500 hover:from-blue-700 hover:via-blue-600 hover:to-cyan-600 text-white font-semibold py-4 px-6 rounded-xl transition-all duration-300 flex items-center justify-center shadow-lg hover:shadow-xl transform hover:scale-[1.02] disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none disabled:shadow-lg"
              disabled={isLoading || !agreementChecked || !showCodeInput || !code}
            >
              {isLoading ? (
                <>
                  <Loader size={18} className="animate-spin mr-2" />
                  正在登录...
                </>
              ) : (
                '登录'
              )}
            </button>

            {/* 移除注册/登录切换，统一为验证码登录 */}
          </form>
        </div>
      </div>
    </div>
  );
};

export default StandaloneLoginPage;
