// FFmpeg音频处理器
import { spawn, ChildProcessWithoutNullStreams } from 'child_process';
import { existsSync } from 'fs';
import { Writable } from 'stream';
import { FFmpegProcessInfo } from '../../../types/audio.js';

export class FFmpegProcessor {
  private ffmpegInstances: Map<string, FFmpegProcessInfo> = new Map();

  /**
   * 获取FFmpeg路径
   */
  private getFFmpegPath(): string | null {
    const possiblePaths = [
      // 手动下载的预编译版本
      'E:\\Software\\FFMPEG\\ffmpeg-master-latest-win64-gpl\\bin\\ffmpeg.exe',
      'E:\\Software\\FFMPEG\\ffmpeg-release-essentials\\bin\\ffmpeg.exe',
      // 原有路径
      'E:\\Software\\FFMPEG\\ffmpeg-7.1.1.tar\\ffmpeg-7.1.1\\bin\\ffmpeg.exe',
      'E:\\Software\\FFMPEG\\bin\\ffmpeg.exe',
      // Chocolatey安装路径
      'C:\\ProgramData\\chocolatey\\bin\\ffmpeg.exe',
      // 系统PATH
      'ffmpeg.exe',
      'ffmpeg'
    ];

    for (const path of possiblePaths) {
      if (existsSync(path) || path === 'ffmpeg' || path === 'ffmpeg.exe') {
        console.log(`✅ Found FFmpeg at: ${path}`);
        return path;
      }
    }

    console.log('❌ FFmpeg not found in any expected location');
    return null;
  }

  /**
   * 测试FFmpeg是否正常工作
   */
  async testFFmpeg(): Promise<boolean> {
    const ffmpegPath = this.getFFmpegPath();
    if (!ffmpegPath) {
      console.log('❌ FFmpeg not available for testing');
      return false;
    }

    return new Promise((resolve) => {
      console.log('🧪 Testing FFmpeg functionality...');
      const ffmpeg = spawn(ffmpegPath, ['-version']);

      let output = '';
      ffmpeg.stdout.on('data', (data) => {
        output += data.toString();
      });

      ffmpeg.on('close', (code) => {
        if (code === 0 && output.includes('ffmpeg version')) {
          console.log('✅ FFmpeg test successful');
          resolve(true);
        } else {
          console.log('❌ FFmpeg test failed');
          resolve(false);
        }
      });

      ffmpeg.on('error', (error) => {
        console.log(`❌ FFmpeg test error: ${error.message}`);
        resolve(false);
      });
    });
  }

  /**
   * 转换音频为PCM格式
   */
  async convertToPCM(audioBuffer: Buffer, format: string): Promise<Buffer> {
    return new Promise((resolve, reject) => {
      console.log(`🔄 Converting ${format} to PCM format...`);

      const ffmpegPath = this.getFFmpegPath();
      if (!ffmpegPath) {
        reject(new Error('FFmpeg not found. Please install FFmpeg or check the path.'));
        return;
      }

      // 根据格式选择不同的FFmpeg参数
      let inputFormat = format;
      let additionalParams: string[] = [];

      switch (format.toLowerCase()) {
        case 'ogg':
          inputFormat = 'ogg';
          break;
        case 'mp4':
          inputFormat = 'mp4';
          break;
        case 'webm':
          inputFormat = 'webm';
          break;
        case 'wav':
          inputFormat = 'wav';
          break;
        case 'mp3':
          inputFormat = 'mp3';
          break;
        case 'unknown':
          // 对于未知格式，尝试让FFmpeg自动检测
          console.log(`⚠️ Unknown format detected, trying auto-detection`);
          inputFormat = 'webm'; // 默认尝试webm，因为前端通常发送webm
          break;
        default:
          inputFormat = format;
          break;
      }

      // 构建FFmpeg命令参数
      const ffmpegArgs = [
        '-fflags', '+genpts',
        '-avoid_negative_ts', 'make_zero',
        '-analyzeduration', '0',
        '-probesize', '32',
        '-f', inputFormat,
        '-i', 'pipe:0',
        '-vn',
        '-acodec', 'pcm_s16le',
        '-ac', '1',
        '-ar', '16000',
        '-f', 's16le',
        '-loglevel', 'error',
        '-y',
        'pipe:1'
      ];

      console.log(`🔧 FFmpeg command: ${ffmpegPath} ${ffmpegArgs.join(' ')}`);
      const ffmpeg = spawn(ffmpegPath, ffmpegArgs);

      let outputBuffer = Buffer.alloc(0);
      let errorOutput = '';

      ffmpeg.stdout.on('data', (chunk) => {
        outputBuffer = Buffer.concat([outputBuffer, chunk]);
      });

      ffmpeg.stderr.on('data', (chunk) => {
        errorOutput += chunk.toString();
      });

      ffmpeg.on('close', (code) => {
        if (code === 0 && outputBuffer.length > 0) {
          console.log(`✅ ${format} to PCM conversion successful, output size: ${outputBuffer.length} bytes`);
          resolve(outputBuffer);
        } else {
          console.error(`❌ FFmpeg ${format} conversion failed with code: ${code}`);
          console.error(`❌ FFmpeg error output: ${errorOutput}`);
          reject(new Error(`${format} conversion failed: ${errorOutput || 'Unknown error'}`));
        }
      });

      ffmpeg.on('error', (error) => {
        console.error(`FFmpeg spawn error for ${format}:`, error);
        reject(error);
      });

      // 写入输入数据并关闭stdin
      ffmpeg.stdin.write(audioBuffer);
      ffmpeg.stdin.end();
    });
  }

  /**
   * 获取或创建流式FFmpeg实例
   */
  getOrCreateStreamingInstance(
    sessionId: string,
    onPcmData: (pcmChunk: Buffer) => void,
    onError: (error: Error) => void
  ): Writable | null {
    if (this.ffmpegInstances.has(sessionId)) {
      return this.ffmpegInstances.get(sessionId)!.stdin;
    }

    const ffmpegPath = this.getFFmpegPath();
    if (!ffmpegPath) {
      onError(new Error('FFmpeg not found'));
      return null;
    }

    console.log(`[${sessionId}] Starting streaming FFmpeg process: ${ffmpegPath}`);
    const ffmpeg = spawn(ffmpegPath, [
      '-fflags', '+genpts',
      '-avoid_negative_ts', 'make_zero',
      '-analyzeduration', '0',
      '-probesize', '32',
      '-f', 'webm',
      '-i', 'pipe:0',
      '-vn',
      '-acodec', 'pcm_s16le',
      '-ac', '1',
      '-ar', '16000',
      '-f', 's16le',
      '-loglevel', 'error',
      '-y',
      'pipe:1'
    ], { stdio: ['pipe', 'pipe', 'pipe'] });

    ffmpeg.stdout.on('data', (chunk: Buffer) => {
      console.log(`[${sessionId}] FFmpeg PCM data length: ${chunk.length}`);
      onPcmData(chunk);
    });

    ffmpeg.stderr.on('data', (data: Buffer) => {
      console.error(`[${sessionId}] FFmpeg stderr: ${data.toString()}`);
    });

    ffmpeg.on('error', (error) => {
      console.error(`[${sessionId}] FFmpeg process error:`, error);
      onError(error);
      this.ffmpegInstances.delete(sessionId);
    });

    ffmpeg.on('close', (code) => {
      console.log(`[${sessionId}] FFmpeg process exited with code ${code}`);
      this.ffmpegInstances.delete(sessionId);
    });

    const processInfo: FFmpegProcessInfo = {
      process: ffmpeg,
      stdin: ffmpeg.stdin,
      onPcmData,
      onError
    };

    this.ffmpegInstances.set(sessionId, processInfo);
    return ffmpeg.stdin;
  }

  /**
   * 结束音频流
   */
  endAudioStream(sessionId: string): void {
    const instanceInfo = this.ffmpegInstances.get(sessionId);
    if (instanceInfo) {
      console.log(`[${sessionId}] Ending FFmpeg stdin stream`);
      instanceInfo.stdin.end();
    }
  }

  /**
   * 清理会话的FFmpeg实例
   */
  cleanupSession(sessionId: string): void {
    const instanceInfo = this.ffmpegInstances.get(sessionId);
    if (instanceInfo) {
      console.log(`[${sessionId}] Forcefully killing FFmpeg process`);
      instanceInfo.process.kill();
      this.ffmpegInstances.delete(sessionId);
    }
  }

  /**
   * 清理所有FFmpeg实例
   */
  cleanup(): void {
    console.log('Cleaning up all FFmpeg instances');
    this.ffmpegInstances.forEach((instanceInfo, sessionId) => {
      console.log(`Killing FFmpeg process for session ${sessionId}`);
      instanceInfo.process.kill();
    });
    this.ffmpegInstances.clear();
  }
}
