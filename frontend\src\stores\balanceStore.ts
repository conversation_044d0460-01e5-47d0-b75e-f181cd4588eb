import { create } from 'zustand';
import { persist } from 'zustand/middleware';

// 用户余额接口
export interface UserBalance {
  mockInterviewCredits: number;
  formalInterviewCredits: number;
  mianshijunBalance: number;
  lastUpdated: number; // 最后更新时间戳
}

// 余额状态管理接口
interface BalanceState {
  balance: UserBalance | null;
  isLoading: boolean;
  lastFetchTime: number;
  
  // 操作方法
  setBalance: (balance: UserBalance) => void;
  updateBalance: (updates: Partial<UserBalance>) => void;
  clearBalance: () => void;
  setLoading: (loading: boolean) => void;
  
  // 缓存相关方法
  isBalanceStale: () => boolean;
  shouldRefreshBalance: () => boolean;
}

// 缓存配置
const CACHE_DURATION = 5 * 60 * 1000; // 5分钟缓存时间
const STALE_THRESHOLD = 10 * 60 * 1000; // 10分钟后认为数据过期

export const useBalanceStore = create<BalanceState>()(
  persist(
    (set, get) => ({
      balance: null,
      isLoading: false,
      lastFetchTime: 0,

      setBalance: (balance: UserBalance) => {
        set({
          balance: {
            ...balance,
            lastUpdated: Date.now()
          },
          lastFetchTime: Date.now(),
          isLoading: false
        });
      },

      updateBalance: (updates: Partial<UserBalance>) => {
        const currentBalance = get().balance;
        if (currentBalance) {
          set({
            balance: {
              ...currentBalance,
              ...updates,
              lastUpdated: Date.now()
            },
            lastFetchTime: Date.now()
          });
        }
      },

      clearBalance: () => {
        set({
          balance: null,
          isLoading: false,
          lastFetchTime: 0
        });
      },

      setLoading: (loading: boolean) => {
        set({ isLoading: loading });
      },

      // 检查缓存是否过期
      isBalanceStale: () => {
        const { lastFetchTime } = get();
        return Date.now() - lastFetchTime > STALE_THRESHOLD;
      },

      // 检查是否需要刷新余额
      shouldRefreshBalance: () => {
        const { lastFetchTime, balance } = get();
        return !balance || Date.now() - lastFetchTime > CACHE_DURATION;
      }
    }),
    {
      name: 'balance-storage',
      // 只持久化余额数据，不持久化加载状态
      partialize: (state) => ({
        balance: state.balance,
        lastFetchTime: state.lastFetchTime
      })
    }
  )
);
