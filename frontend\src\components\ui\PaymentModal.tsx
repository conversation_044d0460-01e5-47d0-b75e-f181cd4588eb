import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, CreditCard, Smartphone, AlertCircle } from 'lucide-react';

interface PaymentModalProps {
  isOpen: boolean;
  onClose: () => void;
  onPaymentMethodSelect: (method: 'ALIPAY' | 'WECHATPAY') => void;
  packageInfo: {
    title: string;
    price: number;
    description?: string;
  };
}

const PaymentModal: React.FC<PaymentModalProps> = ({
  isOpen,
  onClose,
  onPaymentMethodSelect,
  packageInfo
}) => {
  const [selectedMethod, setSelectedMethod] = useState<'ALIPAY' | 'WECHATPAY' | null>(null);
  const [showWechatWarning, setShowWechatWarning] = useState(false);

  const handleMethodSelect = (method: 'ALIPAY' | 'WECHATPAY') => {
    setSelectedMethod(method);
    if (method === 'WECHATPAY') {
      setShowWechatWarning(true);
    } else {
      setShowWechatWarning(false);
    }
  };

  const handleConfirmPayment = () => {
    if (selectedMethod === 'WECHATPAY') {
      // 显示微信维护提示，不进行支付
      return;
    }
    if (selectedMethod) {
      onPaymentMethodSelect(selectedMethod);
    }
  };

  const handleClose = () => {
    setSelectedMethod(null);
    setShowWechatWarning(false);
    onClose();
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          {/* 背景遮罩 */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 bg-black bg-opacity-50 backdrop-blur-sm"
            onClick={handleClose}
          />
          
          {/* 弹窗内容 */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.9, y: 20 }}
            transition={{ duration: 0.3 }}
            className="relative bg-white bg-opacity-95 backdrop-blur-md rounded-xl shadow-2xl border border-gray-200 w-full max-w-md mx-4"
          >
            {/* 关闭按钮 */}
            <button
              onClick={handleClose}
              className="absolute top-4 right-4 p-2 text-gray-400 hover:text-gray-600 transition-colors"
            >
              <X className="h-5 w-5" />
            </button>

            {/* 弹窗头部 */}
            <div className="p-6 pb-4">
              <h2 className="text-xl font-bold text-gray-800 mb-2">选择支付方式</h2>
              <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4 border border-blue-100">
                <h3 className="font-semibold text-gray-800">{packageInfo.title}</h3>
                <p className="text-sm text-gray-600 mt-1">{packageInfo.description}</p>
                <div className="text-2xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-indigo-600 mt-2">
                  ¥{packageInfo.price}
                </div>
              </div>
            </div>

            {/* 支付方式选择 */}
            <div className="px-6 pb-4">
              <div className="space-y-3">
                {/* 支付宝选项 */}
                <div
                  onClick={() => handleMethodSelect('ALIPAY')}
                  className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${
                    selectedMethod === 'ALIPAY'
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300 bg-gray-50'
                  }`}
                >
                  <div className="flex items-center gap-3">
                    <div className="flex-shrink-0">
                      <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                        <CreditCard className="h-5 w-5 text-white" />
                      </div>
                    </div>
                    <div className="flex-1">
                      <h4 className="font-semibold text-gray-800">支付宝</h4>
                      <p className="text-sm text-gray-600">安全便捷的在线支付</p>
                    </div>
                    <div className={`w-4 h-4 rounded-full border-2 ${
                      selectedMethod === 'ALIPAY'
                        ? 'border-blue-500 bg-blue-500'
                        : 'border-gray-300'
                    }`}>
                      {selectedMethod === 'ALIPAY' && (
                        <div className="w-full h-full rounded-full bg-white scale-50"></div>
                      )}
                    </div>
                  </div>
                </div>

                {/* 微信支付选项 */}
                <div
                  onClick={() => handleMethodSelect('WECHATPAY')}
                  className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${
                    selectedMethod === 'WECHATPAY'
                      ? 'border-green-500 bg-green-50'
                      : 'border-gray-200 hover:border-gray-300 bg-gray-50'
                  }`}
                >
                  <div className="flex items-center gap-3">
                    <div className="flex-shrink-0">
                      <div className="w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center">
                        <Smartphone className="h-5 w-5 text-white" />
                      </div>
                    </div>
                    <div className="flex-1">
                      <h4 className="font-semibold text-gray-800">微信支付</h4>
                      <p className="text-sm text-gray-600">微信安全支付</p>
                    </div>
                    <div className={`w-4 h-4 rounded-full border-2 ${
                      selectedMethod === 'WECHATPAY'
                        ? 'border-green-500 bg-green-500'
                        : 'border-gray-300'
                    }`}>
                      {selectedMethod === 'WECHATPAY' && (
                        <div className="w-full h-full rounded-full bg-white scale-50"></div>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* 微信维护提示 */}
              {showWechatWarning && (
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="mt-3 p-3 bg-orange-50 border border-orange-200 rounded-lg flex items-start gap-2"
                >
                  <AlertCircle className="h-5 w-5 text-orange-500 flex-shrink-0 mt-0.5" />
                  <div>
                    <p className="text-sm font-medium text-orange-800">微信支付暂不可用</p>
                    <p className="text-xs text-orange-600 mt-1">系统维护中，请选择支付宝支付，敬请谅解</p>
                  </div>
                </motion.div>
              )}
            </div>

            {/* 底部按钮 */}
            <div className="p-6 pt-2 flex gap-3">
              <button
                onClick={handleClose}
                className="flex-1 py-3 px-4 border border-gray-300 text-gray-700 rounded-lg font-medium hover:bg-gray-50 transition-colors"
              >
                取消
              </button>
              <button
                onClick={handleConfirmPayment}
                disabled={!selectedMethod || selectedMethod === 'WECHATPAY'}
                className={`flex-1 py-3 px-4 rounded-lg font-medium transition-all ${
                  selectedMethod && selectedMethod !== 'WECHATPAY'
                    ? 'bg-gradient-to-r from-blue-600 to-indigo-600 text-white hover:shadow-lg'
                    : 'bg-gray-200 text-gray-400 cursor-not-allowed'
                }`}
              >
                立即支付
              </button>
            </div>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  );
};

export default PaymentModal;
