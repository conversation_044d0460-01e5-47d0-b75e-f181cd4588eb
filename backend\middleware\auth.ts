// 认证中间件
import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

// 扩展Request接口以包含用户信息
export interface AuthenticatedRequest extends Request {
  user?: {
    userId: string;
    email: string;
    name?: string;
  };
}

/**
 * JWT认证中间件 - 统一版本
 * 用于验证用户身份并将用户信息附加到请求对象
 */
export const authenticateToken = (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  const authHeader = req.headers.authorization;
  const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

  console.log('🔐 Auth middleware - Request URL:', req.url);
  console.log('🔐 Auth middleware - Auth header present:', !!authHeader);
  console.log('🔐 Auth middleware - Token present:', !!token);

  if (!token) {
    console.log('❌ Auth middleware - No token provided');
    return res.status(401).json({
      success: false,
      message: '请先登录',
      error: {
        code: 'NO_TOKEN'
      }
    });
  }

  try {
    console.log('🔐 Auth middleware - Verifying token with JWT_SECRET:', JWT_SECRET.substring(0, 10) + '...');
    const decoded = jwt.verify(token, JWT_SECRET) as any;
    console.log('✅ Auth middleware - Token decoded successfully:', {
      userId: decoded.userId,
      email: decoded.email,
      exp: decoded.exp ? new Date(decoded.exp * 1000).toISOString() : 'undefined'
    });

    req.user = {
      userId: decoded.userId,
      email: decoded.email,
      name: decoded.name
    };
    next();
  } catch (error: any) {
    console.log('❌ Auth middleware - Token verification failed:', error.message);
    console.log('❌ Auth middleware - Error type:', error.name);

    let errorCode = 'INVALID_TOKEN';
    let errorMessage = '登录已过期，请重新登录';

    if (error.name === 'TokenExpiredError') {
      errorCode = 'TOKEN_EXPIRED';
      errorMessage = '登录已过期，请重新登录';
    } else if (error.name === 'JsonWebTokenError') {
      errorCode = 'MALFORMED_TOKEN';
      errorMessage = '无效的登录凭证';
    }

    return res.status(401).json({
      success: false,
      message: errorMessage,
      error: {
        code: errorCode,
        details: error.message
      }
    });
  }
};

/**
 * 可选认证中间件
 * 如果有token则验证并附加用户信息，没有则继续
 */
export const optionalAuthenticateToken = (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  const authHeader = req.headers.authorization;
  const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

  if (!token) {
    console.log('🔐 Optional Auth - No token, continuing as guest');
    return next();
  }

  try {
    const decoded = jwt.verify(token, JWT_SECRET) as any;
    req.user = {
      userId: decoded.userId,
      email: decoded.email,
      name: decoded.name
    };
    console.log('✅ Optional Auth - User authenticated:', req.user.userId);
    next();
  } catch (error) {
    console.log('⚠️ Optional Auth - Invalid token, continuing as guest');
    next();
  }
};

/**
 * 验证Token并返回用户ID（用于Vercel函数）
 */
export const verifyToken = (authHeader: string | undefined): string | null => {
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }

  const token = authHeader.substring(7);
  try {
    const decoded = jwt.verify(token, JWT_SECRET) as { userId: string };
    return decoded.userId;
  } catch (error) {
    return null;
  }
};

/**
 * 管理员权限检查中间件
 */
export const isAdmin = (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  // 这里可以添加管理员权限检查逻辑
  // 目前暂时允许所有认证用户访问
  next();
};
