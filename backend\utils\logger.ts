import fs from 'fs';
import path from 'path';
import winston from 'winston';

// 日志目录（请确保与需求一致）
const LOG_DIR = 'E:/Data/Own/Entrepreneurship/local-mianshijun/docs/development-plan/log';

// 如果目录不存在则自动创建
fs.mkdirSync(LOG_DIR, { recursive: true });

// 生成文件名，例如 20250612-153045.txt
const timestamp = new Date()
  .toISOString()
  .replace(/[:T]/g, '-') // 替换冒号与T
  .split('.')[0];

const logFile = path.join(LOG_DIR, `backend-${timestamp}.txt`);

// 前端日志文件（与后端区分）
const clientLogFile = path.join(LOG_DIR, `frontend-${timestamp}.txt`);

// 创建winston logger
export const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss.SSS' }),
    winston.format.printf(({ level, message, timestamp }) => `[${timestamp}] ${level.toUpperCase()}: ${message}`)
  ),
  transports: [
    new winston.transports.Console(),
    new winston.transports.File({ filename: logFile })
  ]
});

// 专用前端logger：只写文件
export const clientLogger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss.SSS' }),
    winston.format.printf(({ level, message, timestamp }) => `[${timestamp}] ${level.toUpperCase()}: ${message}`)
  ),
  transports: [
    new winston.transports.File({ filename: clientLogFile })
  ]
});

const levelMap: Record<string, keyof typeof logger> = {
  log: 'info',
  info: 'info',
  warn: 'warn',
  error: 'error'
};

(Object.keys(levelMap) as Array<keyof typeof levelMap>).forEach((method) => {
  // @ts-ignore 覆盖全局console方法
  console[method] = (...args: any[]) => {
    const msg = args.map(arg => (typeof arg === 'object' ? JSON.stringify(arg) : String(arg))).join(' ');
    logger[levelMap[method]](msg);
  };
}); 