import { useState, useEffect } from 'react';
import useDocumentTitle from '../hooks/useDocumentTitle';

const ApiTest = () => {
  // 设置页面标题
  useDocumentTitle('API测试');

  const [backendStatus, setBackendStatus] = useState<string>('正在检查后端连接...');
  const [registerStatus, setRegisterStatus] = useState<string>('');
  const [loginStatus, setLoginStatus] = useState<string>('');
  const [email, setEmail] = useState<string>('');
  const [password, setPassword] = useState<string>('');
  const [name, setName] = useState<string>('');

  // 登录表单
  const [loginEmail, setLoginEmail] = useState<string>('');
  const [loginPassword, setLoginPassword] = useState<string>('');

  // 测试后端健康检查
  useEffect(() => {
    const checkBackendStatus = async () => {
      try {
        const response = await fetch('http://localhost:3000/');
        const data = await response.json();
        setBackendStatus(`后端连接成功: ${data.message}`);
      } catch (error) {
        setBackendStatus(`后端连接失败: ${error instanceof Error ? error.message : '未知错误'}`);
      }
    };

    checkBackendStatus();
  }, []);

  // 测试注册功能
  const handleRegister = async (e: React.FormEvent) => {
    e.preventDefault();
    setRegisterStatus('正在注册...');

    try {
      const response = await fetch('http://localhost:3000/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password, name }),
      });

      const data = await response.json();

      if (response.ok) {
        setRegisterStatus(`注册成功: ${data.message}`);
      } else {
        setRegisterStatus(`注册失败: ${data.message}`);
      }
    } catch (error) {
      setRegisterStatus(`注册请求错误: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  };

  // 测试登录功能
  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoginStatus('正在登录...');

    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email: loginEmail, password: loginPassword }),
      });

      const data = await response.json();

      if (response.ok) {
        setLoginStatus(`登录成功: ${data.message}`);
      } else {
        setLoginStatus(`登录失败: ${data.message}`);
      }
    } catch (error) {
      setLoginStatus(`登录请求错误: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  };

  return (
    <div className="p-6 max-w-md mx-auto bg-white rounded-xl shadow-md">
      <h2 className="text-xl font-bold mb-4">API 连接测试</h2>

      <div className="mb-6 p-4 border rounded">
        <h3 className="font-semibold mb-2">后端健康检查</h3>
        <p className={`${backendStatus.includes('成功') ? 'text-green-600' : 'text-red-600'}`}>
          {backendStatus}
        </p>
      </div>

      <div className="mb-6 p-4 border rounded">
        <h3 className="font-semibold mb-2">注册功能测试</h3>
        <form onSubmit={handleRegister} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">邮箱</label>
            <input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">密码</label>
            <input
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">姓名 (可选)</label>
            <input
              type="text"
              value={name}
              onChange={(e) => setName(e.target.value)}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm"
            />
          </div>
          <button
            type="submit"
            className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
          >
            测试注册
          </button>
        </form>
        {registerStatus && (
          <p className={`mt-4 ${registerStatus.includes('成功') ? 'text-green-600' : 'text-red-600'}`}>
            {registerStatus}
          </p>
        )}
      </div>

      <div className="p-4 border rounded">
        <h3 className="font-semibold mb-2">登录功能测试</h3>
        <form onSubmit={handleLogin} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">邮箱</label>
            <input
              type="email"
              value={loginEmail}
              onChange={(e) => setLoginEmail(e.target.value)}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">密码</label>
            <input
              type="password"
              value={loginPassword}
              onChange={(e) => setLoginPassword(e.target.value)}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm"
              required
            />
          </div>
          <button
            type="submit"
            className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
          >
            测试登录
          </button>
        </form>
        {loginStatus && (
          <p className={`mt-4 ${loginStatus.includes('成功') ? 'text-green-600' : 'text-red-600'}`}>
            {loginStatus}
          </p>
        )}
      </div>
    </div>
  );
};

export default ApiTest;
