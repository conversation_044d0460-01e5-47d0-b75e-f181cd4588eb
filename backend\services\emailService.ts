import nodemailer from 'nodemailer';
import { createTransport } from 'nodemailer';

export class EmailService {
  private transporter: nodemailer.Transporter;

  constructor() {
    this.transporter = createTransport({
      host: process.env.SMTP_HOST || 'smtpdm.aliyun.com',
      port: parseInt(process.env.SMTP_PORT || '25'),
      secure: false, // 阿里云邮件推送使用25端口，不使用SSL
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS
      }
    });
  }

  async sendVerificationCode(email: string, code: string): Promise<void> {
    const mailOptions = {
      from: process.env.SMTP_FROM || '面试君 <<EMAIL>>',
      to: email,
      subject: '【面试君】登录验证码',
      html: this.generateEmailTemplate(code)
    };

    try {
      const result = await this.transporter.sendMail(mailOptions);
      console.log('Email sent successfully:', result.messageId);
    } catch (error) {
      console.error('Email sending failed:', error);
      throw new Error('邮件发送失败');
    }
  }

  private generateEmailTemplate(code: string): string {
    return `
      <!DOCTYPE html>
      <html lang="zh-CN">
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>面试君登录验证码</title>
        <style>
          * { margin: 0; padding: 0; box-sizing: border-box; }
          body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #374151;
            background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
            padding: 20px;
          }
          .email-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
          }
          .header {
            background: linear-gradient(135deg, #2563eb 0%, #3b82f6 25%, #06b6d4 100%);
            color: white;
            padding: 40px 30px;
            text-align: center;
            position: relative;
            overflow: hidden;
          }
          .header::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: float 6s ease-in-out infinite;
          }
          @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
          }
          .logo {
            font-size: 36px;
            font-weight: 800;
            margin-bottom: 8px;
            position: relative;
            z-index: 1;
          }
          .logo-accent {
            background: linear-gradient(45deg, #fbbf24, #f59e0b, #06b6d4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
          }
          .tagline {
            font-size: 16px;
            opacity: 0.9;
            font-weight: 500;
            position: relative;
            z-index: 1;
          }
          .content {
            padding: 40px 30px;
            background: white;
          }
          .greeting {
            font-size: 24px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 16px;
          }
          .description {
            color: #6b7280;
            font-size: 16px;
            margin-bottom: 32px;
            line-height: 1.7;
          }
          .code-section {
            text-align: center;
            margin: 32px 0;
          }
          .code-label {
            font-size: 14px;
            color: #6b7280;
            margin-bottom: 16px;
            font-weight: 500;
          }
          .code-box {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border: 2px solid #e2e8f0;
            border-radius: 16px;
            padding: 24px;
            margin: 20px 0;
            position: relative;
            overflow: hidden;
          }
          .code-box::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #2563eb, #3b82f6, #06b6d4);
          }
          .code {
            font-size: 36px;
            font-weight: 800;
            color: #2563eb;
            letter-spacing: 8px;
            font-family: 'Courier New', monospace;
            text-shadow: 0 2px 4px rgba(37, 99, 235, 0.1);
          }
          .security-notice {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            border: 1px solid #f59e0b;
            border-radius: 12px;
            padding: 20px;
            margin: 32px 0;
            position: relative;
          }
          .security-notice::before {
            content: '🔒';
            position: absolute;
            top: -8px;
            left: 20px;
            background: #fbbf24;
            padding: 8px;
            border-radius: 50%;
            font-size: 16px;
          }
          .security-title {
            color: #92400e;
            font-weight: 700;
            margin-bottom: 12px;
            margin-left: 20px;
            font-size: 16px;
          }
          .security-list {
            color: #92400e;
            font-size: 14px;
            line-height: 1.8;
          }
          .security-list li {
            margin-bottom: 4px;
            list-style: none;
            position: relative;
            padding-left: 20px;
          }
          .security-list li::before {
            content: '•';
            color: #f59e0b;
            font-weight: bold;
            position: absolute;
            left: 0;
          }
          .help-section {
            background: #f8fafc;
            border-radius: 12px;
            padding: 20px;
            margin-top: 32px;
            text-align: center;
          }
          .help-text {
            color: #6b7280;
            font-size: 14px;
            margin-bottom: 12px;
          }
          .contact-link {
            color: #2563eb;
            text-decoration: none;
            font-weight: 600;
            font-size: 14px;
          }
          .footer {
            padding: 30px;
            text-align: center;
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border-top: 1px solid #e2e8f0;
          }
          .footer-text {
            color: #9ca3af;
            font-size: 12px;
            margin-bottom: 8px;
          }
          .copyright {
            color: #6b7280;
            font-size: 12px;
            font-weight: 500;
          }
          @media (max-width: 600px) {
            .email-container { margin: 10px; border-radius: 16px; }
            .header { padding: 30px 20px; }
            .content { padding: 30px 20px; }
            .logo { font-size: 28px; }
            .code { font-size: 28px; letter-spacing: 4px; }
          }
        </style>
      </head>
      <body>
        <div class="email-container">
          <div class="header">
            <div class="logo">面试<span class="logo-accent">君</span></div>
            <div class="tagline">智能AI面试助手，提升面试成功率</div>
          </div>

          <div class="content">
            <div class="greeting">🎯 登录验证码</div>
            <div class="description">
              您好！感谢使用面试君。您正在进行邮箱验证码登录，请使用以下验证码完成登录：
            </div>

            <div class="code-section">
              <div class="code-label">您的验证码</div>
              <div class="code-box">
                <div class="code">${code}</div>
              </div>
            </div>

            <div class="security-notice">
              <div class="security-title">安全提醒</div>
              <ul class="security-list">
                <li>验证码有效期为 <strong>5分钟</strong>，请及时使用</li>
                <li>请勿将验证码分享给任何人</li>
                <li>如非本人操作，请立即忽略此邮件</li>
                <li>面试君不会主动索要您的验证码</li>
              </ul>
            </div>

            <div class="help-section">
              <div class="help-text">遇到问题？我们随时为您提供帮助</div>
              <a href="mailto:<EMAIL>" class="contact-link">联系客服团队 →</a>
            </div>
          </div>

          <div class="footer">
            <div class="footer-text">此邮件由面试君系统自动发送，请勿直接回复</div>
            <div class="copyright">© 2025 面试君 - 让每一次面试都成为成功的开始</div>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  // 测试邮件连接
  async testConnection(): Promise<boolean> {
    try {
      await this.transporter.verify();
      console.log('Email service connection verified');
      return true;
    } catch (error) {
      console.error('Email service connection failed:', error);
      return false;
    }
  }
}

export default EmailService;
