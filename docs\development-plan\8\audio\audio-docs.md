实时语音识别可以将音频流实时转换为文本，实现“边说边出文字”的效果。它适用于对麦克风语音进行实时识别，以及对本地音频文件进行实时转录。

## **应用场景**

- **会议**：为会议、演讲、培训、庭审等提供实时记录。
- **直播：**为直播带货、赛事直播等提供实时字幕。
- **客服**：实时记录通话内容，协助提升服务品质。
- **游戏**：让玩家无需停下手头操作即可语音输入或阅读聊天内容。
- **社交聊天**：使用社交App或输入法时，语音自动转文本。
- **人机交互：**转换语音对话为文字，优化人机交互体验。

## **支持的模型**

**Paraformer**



**Gummy**



## **模型选型建议**

- **语种支持**：

  多语种混合场景下，推荐使用Gummy，能够带来更高的识别准确率。另外，Gummy对非常用词的识别准确率更高。

  - 对于**中文（普通话）、粤语、英语、日语、韩语，**可以选择Gummy或Paraformer模型。
  - 对于**德语、法语、俄语、意大利语、西班牙语**，请选择Gummy模型。
  - 对于**中文（方言）**，请选择Paraformer模型。

- **噪音环境下**：推荐使用Paraformer。

- **情感识别和语气词过滤**：如果需要情感识别和语气词过滤能力，请选择Paraformer语音识别模型。

**点击查看模型功能特性对比**



## **在线体验**

目前，仅以下实时语音识别模型支持在线体验：

- paraformer-realtime-v2：在**[语音识别](https://bailian.console.aliyun.com/?tab=model#/efm/model_experience_center/voice)**页面点击**更多模型**，选择“**Paraformer实时语音识别-v2**”
- paraformer-realtime-8k-v2：在**[语音识别](https://bailian.console.aliyun.com/?tab=model#/efm/model_experience_center/voice)**页面直接选择“**Paraformer实时语音识别-8k-v2**”
- paraformer-realtime-v1：在**[语音识别](https://bailian.console.aliyun.com/?tab=model#/efm/model_experience_center/voice)**页面点击**更多模型**，选择“**Paraformer实时语音识别-v1**”
- gummy-realtime-v1：在**[语音识别](https://bailian.console.aliyun.com/?tab=model#/efm/model_experience_center/voice)**页面点击**更多模型**，选择“**实时语音识别及翻译V1.0**”
- gummy-chat-v1：在**[语音识别](https://bailian.console.aliyun.com/?tab=model#/efm/model_experience_center/voice)**页面直接选择“**一句话识别及翻译V1.0**”

paraformer-realtime-8k-v1暂不支持在线体验，如需使用请通过[API](https://help.aliyun.com/zh/model-studio/real-time-speech-recognition?mode=pure#dfc47fd581dex)接入。

## **快速开始**

下面是调用API的示例代码。更多常用场景的代码示例，请参见[GitHub](https://github.com/aliyun/alibabacloud-bailian-speech-demo)。

您需要已[获取API Key](https://help.aliyun.com/zh/model-studio/get-api-key)并[配置API Key到环境变量](https://help.aliyun.com/zh/model-studio/configure-api-key-through-environment-variables)。如果通过SDK调用，还需要[安装DashScope SDK](https://help.aliyun.com/zh/model-studio/install-sdk/)。

Gummy

Paraformer

**实时语音识别**：适用于会议演讲、视频直播等长时间不间断识别的场景。

**一句话识别**：对停顿更加敏感，支持对一分钟内的短语音进行精准识别，适用于对话聊天、指令控制、语音输入法、语音搜索等短时语音交互场景。

实时语音识别

一句话识别

实时语音识别支持对长时间的语音数据流（无论是从外部设备如麦克风获取的音频流，还是从本地文件读取的音频流）进行识别并流式返回结果。

识别传入麦克风的语音

识别本地文件

Java

Python

 

```routeros
# For prerequisites running the following sample, visit https://help.aliyun.com/document_detail/xxxxx.html

import pyaudio
import dashscope
from dashscope.audio.asr import *


# 若没有将API Key配置到环境变量中，需将your-api-key替换为自己的API Key
# dashscope.api_key = "your-api-key"

mic = None
stream = None

class Callback(TranslationRecognizerCallback):
    def on_open(self) -> None:
        global mic
        global stream
        print("TranslationRecognizerCallback open.")
        mic = pyaudio.PyAudio()
        stream = mic.open(
            format=pyaudio.paInt16, channels=1, rate=16000, input=True
        )

    def on_close(self) -> None:
        global mic
        global stream
        print("TranslationRecognizerCallback close.")
        stream.stop_stream()
        stream.close()
        mic.terminate()
        stream = None
        mic = None

    def on_event(
        self,
        request_id,
        transcription_result: TranscriptionResult,
        translation_result: TranslationResult,
        usage,
    ) -> None:
        print("request id: ", request_id)
        print("usage: ", usage)
        if translation_result is not None:
            print(
                "translation_languages: ",
                translation_result.get_language_list(),
            )
            english_translation = translation_result.get_translation("en")
            print("sentence id: ", english_translation.sentence_id)
            print("translate to english: ", english_translation.text)
            if english_translation.stash is not None:
                print(
                    "translate to english stash: ",
                    translation_result.get_translation("en").stash.text,
                )
        if transcription_result is not None:
            print("sentence id: ", transcription_result.sentence_id)
            print("transcription: ", transcription_result.text)
            if transcription_result.stash is not None:
                print("transcription stash: ", transcription_result.stash.text)


callback = Callback()


translator = TranslationRecognizerRealtime(
    model="gummy-realtime-v1",
    format="pcm",
    sample_rate=16000,
    transcription_enabled=True,
    translation_enabled=True,
    translation_target_languages=["en"],
    callback=callback,
)
translator.start()
print("请您通过麦克风讲话体验实时语音识别和翻译功能")
while True:
    if stream:
        data = stream.read(3200, exception_on_overflow=False)
        translator.send_audio_frame(data)
    else:
        break

translator.stop()
```

## **输入文件限制**

对本地音频文件进行识别时：

- **输入文件的方式**：将本地文件路径作为参数传递。

- **文件数量**：单次调用最多输入1个文件。

- **文件大小**：无限制。

- **音频时长**：

  - Paraformer：无限制
  - Gummy实时语音识别：无限制
  - Gummy一句话识别：一分钟以内

- **文件格式**：支持pcm、pcm编码的wav、mp3、ogg封装的opus、ogg封装的speex、aac、amr这几种格式。推荐pcm和wav。

  > 由于音频文件格式及其变种众多，因此不能保证所有格式均能够被正确识别。请通过测试验证您所提供的文件能够获得正常的语音识别结果。

- **音频采样位数：**16bit。

- **采样率**：因[模型](https://help.aliyun.com/zh/model-studio/real-time-speech-recognition?mode=pure#d5dfb21a4d07t)而异。

  > 采样率是指每秒对声音信号的采样次数。更高的采样率提供更多信息，有助于提高语音识别的准确率，但过高的采样率可能引入更多无关信息，反而影响识别效果。应根据实际采样率选择合适的模型。例如，8000Hz的语音数据应直接使用支持8000Hz的模型，无需转换为16000Hz。

## **API参考**

- [实时语音识别-Paraformer API参考](https://help.aliyun.com/zh/model-studio/paraformer-real-time-speech-recognition-api/)
- [实时语音识别-Gummy API参考](https://help.aliyun.com/zh/model-studio/real-time-speech-recognition-and-translation-api-details/)
- [一句话识别-Gummy API参考](https://help.aliyun.com/zh/model-studio/sentence-recognition-and-translation-api-details/)

## **模型应用上架及备案**

参见[通义大模型应用上架及合规备案](https://help.aliyun.com/zh/model-studio/compliance-and-launch-filing-guide-for-ai-apps-powered-by-the-tongyi-model)。

## 常见问题

### **1. 可能影响识别准确率的因素有哪些？**

1. **声音质量**：设备、环境等可能影响语音的清晰度，从而影响识别准确率。高质量的音频输入是提高识别准确性的前提。
2. **说话人特征**：不同人的声音特质（如音调、语速、口音、方言）差异很大，这些个体差异会对语音识别系统构成挑战，尤其是对于未充分训练过的特定口音或方言。
3. **语言和词汇**：语音识别模型通常针对特定的语言进行训练。当处理多语言混合、专业术语、俚语或网络用语时，识别难度会增加。若模型支持热词功能，可通过热词调整识别结果。
4. **上下文理解**：缺乏对对话上下文的理解可能会导致误解，尤其是在含义模糊或依赖于上下文的情境中。

### **2. 模型限流规则是怎样的？**

**Gummy：**

|      |      |
| ---- | ---- |
|      |      |

| **模型名称**      | **提交作业接口RPS限制** |
| ----------------- | ----------------------- |
| gummy-realtime-v1 | 10                      |
| gummy-chat-v1     |                         |

**Paraformer**：

|      |      |
| ---- | ---- |
|      |      |

| **模型名称**              | **提交作业接口RPS限制** |
| ------------------------- | ----------------------- |
| paraformer-realtime-v2    | 20                      |
| paraformer-realtime-v1    |                         |
| paraformer-realtime-8k-v2 |                         |
| paraformer-realtime-8k-v1 |                         |

### **3. 实时语音识别和一句话识别的区别是什么？**

- **一句话识别**：对停顿更加敏感，支持对一分钟内的短语音进行精准识别，适用于对话聊天、指令控制、语音输入法、语音搜索等短时语音交互场景。
- **实时语音识别**：适用于会议演讲、视频直播等长时间不间断识别的场景。



# Paraformer实时语音识别WebSocket API

更新时间：2025-06-07 02:17:34

[产品详情](https://www.aliyun.com/product/bailian)

[我的收藏](https://help.aliyun.com/my_favorites.html)

本文介绍如何通过WebSocket连接访问实时语音识别服务。

DashScope SDK目前仅支持Java和Python。若想使用其他编程语言开发Paraformer实时语音识别应用程序，可以通过WebSocket连接与服务进行通信。

WebSocket是一种支持全双工通信的网络协议。客户端和服务器通过一次握手建立持久连接，双方可以互相主动推送数据，因此在实时性和效率方面具有显著优势。

对于常用编程语言，有许多现成的WebSocket库和示例可供参考，例如：

- Go：`gorilla/websocket`
- PHP：`Ratchet`
- Node.js：`ws`

建议您先了解WebSocket的基本原理和技术细节，再参照本文进行开发。

## **前提条件**

已开通服务并[获取API Key](https://help.aliyun.com/zh/model-studio/get-api-key)。请[配置API Key到环境变量](https://help.aliyun.com/zh/model-studio/developer-reference/configure-api-key-through-environment-variables)，而非硬编码在代码中，防范因代码泄露导致的安全风险。

**说明**

当您需要为第三方应用或用户提供临时访问权限，或者希望严格控制敏感数据访问、删除等高风险操作时，建议使用[临时鉴权Token](https://help.aliyun.com/zh/model-studio/obtain-temporary-authentication-token)。

与长期有效的 API Key 相比，临时鉴权 Token 具备时效性短（60秒）、安全性高的特点，适用于临时调用场景，能有效降低API Key泄露的风险。

使用方式：在代码中，将原本用于鉴权的 API Key 替换为获取到的临时鉴权 Token 即可。

## **约束**

**接口调用方式限制**：不支持前端直接调用API，需通过后端中转。

## **模型列表**

|      |      |
| ---- | ---- |
|      |      |

| **模型名**                | **模型简介**                                                 |
| ------------------------- | ------------------------------------------------------------ |
| paraformer-realtime-v2    | **推荐使用**。Paraformer最新多语种实时语音识别模型。适用场景：直播、会议等实时语音处理场景支持的采样率：任意支持的语种：中文（包含中文普通话和各种方言）、英文、日语、韩语、德语、法语、俄语**支持的中文方言（单击查看详情）**核心能力：支持标点符号预测支持逆文本正则化（ITN）特色功能：指定语种：通过`language_hints`参数能够指定待识别语种，提升识别效果支持[定制热词](https://help.aliyun.com/zh/model-studio/custom-hot-words) |
| paraformer-realtime-8k-v2 | **推荐使用**。Paraformer最新8k中文实时语音识别模型，模型结构升级，具有更快的推理速度和更好的识别效果。适用场景：电话客服、语音信箱等 8kHz 音频的实时识别，需快速推理与高准确率的场景（如实时字幕生成）等支持的采样率：8kHz支持的语种：中文核心能力：支持标点符号预测支持逆文本正则化（ITN）特色功能：支持[定制热词](https://help.aliyun.com/zh/model-studio/custom-hot-words)支持情感识别情感识别仅在语义断句关闭时生效。语义断句默认为关闭状态，可通过[run-task指令](https://help.aliyun.com/zh/model-studio/websocket-for-paraformer-real-time-service?spm=a2c4g.11186623.0.0.712b2c4dZyRXWT#12d8a57443dmz)的`semantic_punctuation_enabled`参数控制。句子情感通过解析[result-generated事件](https://help.aliyun.com/zh/model-studio/websocket-for-paraformer-real-time-service?spm=a2c4g.11186623.0.0.712b2c4dZyRXWT#e9420a4d7bock)获取，具体步骤如下：获取`payload.output.sentence.sentence_end`的值，只有为true时才能进行下一步。通过`payload.output.sentence.emo_tag`和`payload.output.sentence.emo_confidence`字段分别获取当前句子的情感和情感置信度。 |
| paraformer-realtime-v1    | Paraformer中文实时语音识别模型。适用场景：视频直播、会议等实时场景支持的采样率：16kHz支持的语种：中文核心能力：支持标点符号预测支持逆文本正则化（ITN）特色功能：支持定制热词（参见[Paraformer语音识别热词定制与管理](https://help.aliyun.com/zh/model-studio/developer-reference/paraformer-asr-phrase-manager)） |
| paraformer-realtime-8k-v1 | Paraformer中文实时语音识别模型。适用场景：8kHz电话客服等场景支持的采样率：8kHz支持的语种：中文核心能力：支持标点符号预测支持逆文本正则化（ITN）特色功能：支持定制热词（参见[Paraformer语音识别热词定制与管理](https://help.aliyun.com/zh/model-studio/developer-reference/paraformer-asr-phrase-manager)） |

## **交互流程**

![image](https://help-static-aliyun-doc.aliyuncs.com/assets/img/zh-CN/3583329471/CAEQURiBgMCczta5pxkiIGY0N2Q2YjIwZTM1MTQyNTY4ZmFkY2MwN2JmOTllODFl4709861_20241015153444.149.svg)

客户端发送给服务端的消息有两种：JSON格式的[指令](https://help.aliyun.com/zh/model-studio/websocket-for-paraformer-real-time-service?spm=a2c4g.11186623.0.0.712b2c4dZyRXWT#271eb7a50ft6r)和[二进制音频](https://help.aliyun.com/zh/model-studio/websocket-for-paraformer-real-time-service?spm=a2c4g.11186623.0.0.712b2c4dZyRXWT#e14da5dfe9npt)（须为单声道音频）；服务端返回给客户端的消息称作[事件](https://help.aliyun.com/zh/model-studio/websocket-for-paraformer-real-time-service?spm=a2c4g.11186623.0.0.712b2c4dZyRXWT#a989eb7099wjv)。

按时间顺序，客户端与服务端的交互流程如下：

1. 建立连接：客户端与服务端建立WebSocket连接。
2. 开启任务：
   - 客户端发送[run-task指令](https://help.aliyun.com/zh/model-studio/websocket-for-paraformer-real-time-service?spm=a2c4g.11186623.0.0.712b2c4dZyRXWT#12d8a57443dmz)以开启任务。
   - 客户端收到服务端返回的[task-started事件](https://help.aliyun.com/zh/model-studio/websocket-for-paraformer-real-time-service?spm=a2c4g.11186623.0.0.712b2c4dZyRXWT#2942cede42z9e)，标志着任务已成功开启，可以进行后续步骤。
3. 发送音频流：
   - 客户端开始发送[二进制音频](https://help.aliyun.com/zh/model-studio/websocket-for-paraformer-real-time-service?spm=a2c4g.11186623.0.0.712b2c4dZyRXWT#e14da5dfe9npt)，并同时接收服务端持续返回的[result-generated事件](https://help.aliyun.com/zh/model-studio/websocket-for-paraformer-real-time-service?spm=a2c4g.11186623.0.0.712b2c4dZyRXWT#e9420a4d7bock)，该事件包含语音识别结果。
4. 通知服务端结束任务：
   - 客户端发送[finish-task指令](https://help.aliyun.com/zh/model-studio/websocket-for-paraformer-real-time-service?spm=a2c4g.11186623.0.0.712b2c4dZyRXWT#2e967d2d349es)通知服务端结束任务，并继续接收服务端返回的[result-generated事件](https://help.aliyun.com/zh/model-studio/websocket-for-paraformer-real-time-service?spm=a2c4g.11186623.0.0.712b2c4dZyRXWT#e9420a4d7bock)。
5. 任务结束：
   - 客户端收到服务端返回的[task-finished事件](https://help.aliyun.com/zh/model-studio/websocket-for-paraformer-real-time-service?spm=a2c4g.11186623.0.0.712b2c4dZyRXWT#f11a341732xug)，标志着任务结束。
6. 关闭连接：客户端关闭WebSocket连接。

## **URL**

WebSocket URL固定如下：

 

```http
wss://dashscope.aliyuncs.com/api-ws/v1/inference
```

## **Headers**

请求头中需添加如下信息：

 

```json
{
    "Authorization": "bearer <your_dashscope_api_key>", // 将<your_dashscope_api_key>替换成您自己的API Key
    "user-agent": "your_platform_info", //可选
    "X-DashScope-WorkSpace": workspace, // 可选
    "X-DashScope-DataInspection": "enable"
}
```

## **指令（客户端→服务端）**

指令是客户端发送给服务端的消息，为JSON格式，以Text Frame方式发送，用于控制任务的起止和标识任务边界。

**说明**

客户端发送给服务端的二进制音频（须为单声道音频）不包含在任何指令中，需单独发送。

发送指令需严格遵循以下时序，否则可能导致任务失败：

1. **发送**[**run-task指令**](https://help.aliyun.com/zh/model-studio/websocket-for-paraformer-real-time-service?spm=a2c4g.11186623.0.0.712b2c4dZyRXWT#12d8a57443dmz)
   - 用于启动语音识别任务。
   - 返回的 `task_id` 需在后续发送[finish-task指令](https://help.aliyun.com/zh/model-studio/websocket-for-paraformer-real-time-service?spm=a2c4g.11186623.0.0.712b2c4dZyRXWT#2e967d2d349es)时使用，必须保持一致。
2. **发送**[**二进制音频**](https://help.aliyun.com/zh/model-studio/websocket-for-paraformer-real-time-service?spm=a2c4g.11186623.0.0.712b2c4dZyRXWT#e14da5dfe9npt)**（单声道）**
   - 用于发送待识别音频。
   - 必须在接收到服务端返回的[task-started事件](https://help.aliyun.com/zh/model-studio/websocket-for-paraformer-real-time-service?spm=a2c4g.11186623.0.0.712b2c4dZyRXWT#2942cede42z9e)后发送音频。
3. **发送**[**finish-task指令**](https://help.aliyun.com/zh/model-studio/websocket-for-paraformer-real-time-service?spm=a2c4g.11186623.0.0.712b2c4dZyRXWT#2e967d2d349es)
   - 用于结束语音识别任务。
   - 在音频发送完毕后发送此指令。

### **1. run-task指令：开启任务**

该指令用于开启语音识别任务。`task_id`在后续发送[finish-task指令](https://help.aliyun.com/zh/model-studio/websocket-for-paraformer-real-time-service?spm=a2c4g.11186623.0.0.712b2c4dZyRXWT#2e967d2d349es)时也需要使用，必须保持一致。

**重要**

**发送时机：**WebSocket连接建立后。

**示例：**

 

```json
{
    "header": {
        "action": "run-task",
        "task_id": "2bf83b9a-baeb-4fda-8d9a-xxxxxxxxxxxx", // 随机uuid
        "streaming": "duplex"
    },
    "payload": {
        "task_group": "audio",
        "task": "asr",
        "function": "recognition",
        "model": "paraformer-realtime-v2",
        "parameters": {
            "format": "pcm", // 音频格式
            "sample_rate": 16000, // 采样率
            "vocabulary_id": "vocab-xxx-24ee19fa8cfb4d52902170a0xxxxxxxx", // paraformer-realtime-v2支持的热词ID
            "disfluency_removal_enabled": false, // 过滤语气词
            "language_hints": [
                "en"
            ] // 指定语言，仅支持paraformer-realtime-v2模型
        },
        "resources": [ //不使用热词功能时，不要传递resources参数
            {
                "resource_id": "xxxxxxxxxxxx", // paraformer-realtime-v1支持的热词ID
                "resource_type": "asr_phrase"
            }
        ],
        "input": {}
    }
}
```

`**header**`**参数说明：**

|      |      |      |      |
| ---- | ---- | ---- | ---- |
|      |      |      |      |

| **参数**         | **类型** | **是否必选** | **说明**                                                     |
| ---------------- | -------- | ------------ | ------------------------------------------------------------ |
| header.action    | string   | 是           | 指令类型。当前指令中，固定为"run-task"。                     |
| header.task_id   | string   | 是           | 当次任务ID。为32位通用唯一识别码（UUID），由32个随机生成的字母和数字组成。可以带横线（如 `"2bf83b9a-baeb-4fda-8d9a-xxxxxxxxxxxx"`）或不带横线（如 `"2bf83b9abaeb4fda8d9axxxxxxxxxxxx"`）。大多数编程语言都内置了生成UUID的API，例如Python： `import uuid def generateTaskId(self):    # 生成随机UUID    return uuid.uuid4().hex`在后续发送[finish-task指令](https://help.aliyun.com/zh/model-studio/websocket-for-paraformer-real-time-service?spm=a2c4g.11186623.0.0.712b2c4dZyRXWT#2e967d2d349es)时，用到的task_id需要和发送run-task指令时使用的task_id保持一致。 |
| header.streaming | string   | 是           | 固定字符串："duplex"                                         |

`**payload**`**参数说明：**

|      |      |      |      |
| ---- | ---- | ---- | ---- |
|      |      |      |      |

| **参数**                                                     | **类型**      | **是否必选** | **说明**                                                     |
| ------------------------------------------------------------ | ------------- | ------------ | ------------------------------------------------------------ |
| payload.task_group                                           | string        | 是           | 固定字符串："audio"。                                        |
| payload.task                                                 | string        | 是           | 固定字符串："asr"。                                          |
| payload.function                                             | string        | 是           | 固定字符串："recognition"。                                  |
| payload.model                                                | string        | 是           | 模型名称，支持的模型请参见[模型列表](https://help.aliyun.com/zh/model-studio/websocket-for-paraformer-real-time-service?spm=a2c4g.11186623.0.0.712b2c4dZyRXWT#dbdbfe151dv19)。 |
| payload.input                                                | object        | 是           | 固定格式：{}。                                               |
| **payload.parameters**                                       |               |              |                                                              |
| format                                                       | string        | 是           | 设置待识别音频格式。支持的音频格式：pcm、wav、mp3、opus、speex、aac、amr。**重要**对于opus和speex格式的音频，需要ogg封装；对于wav格式的音频，需要pcm编码。 |
| sample_rate                                                  | integer       | 是           | 设置待识别音频采样率（单位Hz）。因模型而异：paraformer-realtime-v2支持任意采样率。paraformer-realtime-v1仅支持16000Hz采样。paraformer-realtime-8k-v2仅支持8000Hz采样率。paraformer-realtime-8k-v1仅支持8000Hz采样率。 |
| vocabulary_id                                                | string        | 否           | 设置热词ID，若未设置则不生效。v2及更高版本模型设置热词ID时使用该字段。在本次语音识别中，将应用与该热词ID对应的热词信息。具体使用方法请参见[定制热词](https://help.aliyun.com/zh/model-studio/custom-hot-words)。 |
| disfluency_removal_enabled                                   | boolean       | 否           | 设置是否过滤语气词：true：过滤语气词false（默认）：不过滤语气词 |
| language_hints                                               | array[string] | 否           | 设置待识别语言代码。如果无法提前确定语种，可不设置，模型会自动识别语种。目前支持的语言代码：zh: 中文en: 英文ja: 日语yue: 粤语ko: 韩语de：德语fr：法语ru：俄语该参数仅对支持多语言的模型生效（参见[模型列表](https://help.aliyun.com/zh/model-studio/websocket-for-paraformer-real-time-service?spm=a2c4g.11186623.0.0.712b2c4dZyRXWT#dbdbfe151dv19)）。 |
| semantic_punctuation_enabled                                 | boolean       | 否           | 设置是否开启语义断句，默认关闭。true：开启语义断句，关闭VAD（Voice Activity Detection，语音活动检测）断句。false（默认）：开启VAD（Voice Activity Detection，语音活动检测）断句，关闭语义断句。语义断句准确性更高，适合会议转写场景；VAD（Voice Activity Detection，语音活动检测）断句延迟较低，适合交互场景。通过调整`semantic_punctuation_enabled`参数，可以灵活切换语音识别的断句方式以适应不同场景需求。该参数仅在模型为v2及更高版本时生效。 |
| max_sentence_silence                                         | integer       | 否           | 设置VAD（Voice Activity Detection，语音活动检测）断句的静音时长阈值（单位为ms）。当一段语音后的静音时长超过该阈值时，系统会判定该句子已结束。参数范围为200ms至6000ms，默认值为800ms。该参数仅在`semantic_punctuation_enabled`参数为false（VAD断句）且模型为v2及更高版本时生效。 |
| multi_threshold_mode_enabled                                 | boolean       | 否           | 该开关打开时（true）可以防止VAD断句切割过长。默认关闭。该参数仅在`semantic_punctuation_enabled`参数为false（VAD断句）且模型为v2及更高版本时生效。 |
| punctuation_prediction_enabled                               | boolean       | 否           | 设置是否在识别结果中自动添加标点：true（默认）：是false：否该参数仅在模型为v2及更高版本时生效。 |
| heartbeat                                                    | boolean       | 否           | 当需要与服务端保持长连接时，可通过该开关进行控制：true：在持续发送静音音频的情况下，可保持与服务端的连接不中断。false（默认）：即使持续发送静音音频，连接也将在60秒后因超时而断开。静音音频指的是在音频文件或数据流中没有声音信号的内容。静音音频可以通过多种方法生成，例如使用音频编辑软件如Audacity或Adobe Audition，或者通过命令行工具如FFmpeg。该参数仅在模型为v2及更高版本时生效。 |
| inverse_text_normalization_enabled                           | boolean       | 否           | 设置是否开启ITN（Inverse Text Normalization，逆文本正则化）。默认开启（true）。开启后，中文数字将转换为阿拉伯数字。该参数仅在模型为v2及更高版本时生效。 |
| **payload.resources（内容为列表，不使用热词功能时，不要传递该参数）** |               |              |                                                              |
| resource_id                                                  | string        | 否           | 热词ID，此次语音识别中生效此热词ID对应的热词信息。默认不启用。需和`resource_type`参数配合使用。注：`resource_id`对应SDK中的`phrase_id`字段，`phrase_id`为v1版本模型热词方案，不支持v2及后续系列模型。支持该方式热词的模型列表请参考[Paraformer语音识别热词定制与管理](https://help.aliyun.com/zh/model-studio/developer-reference/paraformer-asr-phrase-manager)。 |
| resource_type                                                | string        | 否           | 固定字符串“`asr_phrase`”，需和`resource_id`参数配合使用。    |

### **2. finish-task指令：结束任务**

该指令用于结束语音识别任务。音频发送完毕后，客户端可以发送此指令以结束任务。

**重要**

**发送时机：**音频发送完成后。

**示例：**

 

```json
{
    "header": {
        "action": "finish-task",
        "task_id": "2bf83b9a-baeb-4fda-8d9a-xxxxxxxxxxxx",
        "streaming": "duplex"
    },
    "payload": {
        "input": {}
    }
}
```

`**header**`**参数说明：**

|      |      |      |      |
| ---- | ---- | ---- | ---- |
|      |      |      |      |

| **参数**         | **类型** | **是否必选** | **说明**                                                     |
| ---------------- | -------- | ------------ | ------------------------------------------------------------ |
| header.action    | string   | 是           | 指令类型。当前指令中，固定为"finish-task"。                  |
| header.task_id   | string   | 是           | 当次任务ID。需要和发送[run-task指令](https://help.aliyun.com/zh/model-studio/websocket-for-paraformer-real-time-service?spm=a2c4g.11186623.0.0.712b2c4dZyRXWT#12d8a57443dmz)时使用的task_id保持一致。 |
| header.streaming | string   | 是           | 固定字符串："duplex"                                         |

`**payload**`**参数说明：**

|      |      |      |      |
| ---- | ---- | ---- | ---- |
|      |      |      |      |

| **参数**      | **类型** | **是否必选** | **说明**       |
| ------------- | -------- | ------------ | -------------- |
| payload.input | object   | 是           | 固定格式：{}。 |

## **二进制音频（客户端→服务端）**

客户端需在收到[task-started事件](https://help.aliyun.com/zh/model-studio/websocket-for-paraformer-real-time-service?spm=a2c4g.11186623.0.0.712b2c4dZyRXWT#2942cede42z9e)后，再发送待识别的音频流。

可以发送实时音频流（比如从话筒中实时获取到的）或者录音文件音频流，音频应是单声道。

音频通过WebSocket的二进制通道上传。建议每次发送100ms的音频，并间隔100ms。

## **事件（服务端→客户端）**

事件是服务端返回给客户端的消息，为JSON格式，代表不同的处理阶段。

### **1. task-started事件：任务已开启**

当监听到服务端返回的`task-started`事件时，标志着任务已成功开启。只有在接收到该事件后，才能向服务器发送待识别音频或[finish-task指令](https://help.aliyun.com/zh/model-studio/websocket-for-paraformer-real-time-service?spm=a2c4g.11186623.0.0.712b2c4dZyRXWT#2e967d2d349es)；否则，任务将执行失败。

`task-started`事件的`payload`没有内容。

**示例：**

 

```json
{
    "header": {
        "task_id": "2bf83b9a-baeb-4fda-8d9a-xxxxxxxxxxxx",
        "event": "task-started",
        "attributes": {}
    },
    "payload": {}
}
```

`**header**`**参数说明：**

|      |      |      |
| ---- | ---- | ---- |
|      |      |      |

| **参数**       | **类型** | **说明**                                     |
| -------------- | -------- | -------------------------------------------- |
| header.event   | string   | 事件类型。当前事件中，固定为"task-started"。 |
| header.task_id | string   | 客户端生成的task_id                          |

### **2. result-generated事件：语音识别结果**

客户端发送待识别音频和[finish-task指令](https://help.aliyun.com/zh/model-studio/websocket-for-paraformer-real-time-service?spm=a2c4g.11186623.0.0.712b2c4dZyRXWT#2e967d2d349es)的同时，服务端持续返回`result-generated`事件，该事件包含语音识别的结果。

可以通过`result-generated`事件中的`payload.sentence.endTime`是否为空来判断该结果是中间结果还是最终结果。

**示例：**

 

```json
{
  "header": {
    "task_id": "2bf83b9a-baeb-4fda-8d9a-xxxxxxxxxxxx",
    "event": "result-generated",
    "attributes": {}
  },
  "payload": {
    "output": {
      "sentence": {
        "begin_time": 170,
        "end_time": null,
        "text": "好，我们的一个",
        "words": [
          {
            "begin_time": 170,
            "end_time": 295,
            "text": "好",
            "punctuation": "，"
          },
          {
            "begin_time": 295,
            "end_time": 503,
            "text": "我们",
            "punctuation": ""
          },
          {
            "begin_time": 503,
            "end_time": 711,
            "text": "的一",
            "punctuation": ""
          },
          {
            "begin_time": 711,
            "end_time": 920,
            "text": "个",
            "punctuation": ""
          }
        ]
      }
    },
    "usage": null
  }
}
```

`**header**`**参数说明：**

|      |      |      |
| ---- | ---- | ---- |
|      |      |      |

| **参数**       | **类型** | **说明**                                         |
| -------------- | -------- | ------------------------------------------------ |
| header.event   | string   | 事件类型。当前事件中，固定为"result-generated"。 |
| header.task_id | string   | 客户端生成的task_id。                            |

`**payload**`**参数说明：**

|      |      |      |
| ---- | ---- | ---- |
|      |      |      |

| **参数** | **类型** | **说明**                                    |
| -------- | -------- | ------------------------------------------- |
| output   | object   | output.sentence为识别结果，详细内容见下文。 |
| usage    | object   | 固定为null。                                |

`payload.output.sentence`格式如下：

|      |      |      |
| ---- | ---- | ---- |
|      |      |      |

| **参数**       | **类型**        | **说明**                                                     |
| -------------- | --------------- | ------------------------------------------------------------ |
| begin_time     | integer         | 句子开始时间，单位为ms。                                     |
| end_time       | integer \| null | 句子结束时间，如果为中间识别结果则为null，单位为ms。         |
| text           | string          | 识别文本。                                                   |
| words          | array           | 字时间戳信息。                                               |
| heartbeat      | boolean \| null | 若该值为true，可跳过识别结果的处理。                         |
| sentence_end   | boolean         | 判断给定句子是否已结束。                                     |
| emo_tag        | string          | 当前句子的情感：positive：正面情感，如开心、满意negative：负面情感，如愤怒、沉闷neutral：无明显情感仅paraformer-realtime-8k-v2模型支持情感识别。情感识别仅在语义断句关闭时生效。语义断句默认为关闭状态，可通过[run-task指令](https://help.aliyun.com/zh/model-studio/websocket-for-paraformer-real-time-service?spm=a2c4g.11186623.0.0.712b2c4dZyRXWT#12d8a57443dmz)的`semantic_punctuation_enabled`参数控制。句子情感通过解析result-gnerated事件获取，具体步骤如下：获取`payload.output.sentence.sentence_end`的值，只有为true时才能进行下一步。通过`payload.output.sentence.emo_tag`字段获取当前句子的情感。 |
| emo_confidence | number          | 当前句子识别情感的置信度，取值范围：[0.0,1.0]。值越大表示置信度越高。仅paraformer-realtime-8k-v2模型支持情感识别。情感识别仅在语义断句关闭时生效。语义断句默认为关闭状态，可通过[run-task指令](https://help.aliyun.com/zh/model-studio/websocket-for-paraformer-real-time-service?spm=a2c4g.11186623.0.0.712b2c4dZyRXWT#12d8a57443dmz)的`semantic_punctuation_enabled`参数控制。句子情感置信度通过解析result-gnerated事件获取，具体步骤如下：获取`payload.output.sentence.sentence_end`的值，只有为true时才能进行下一步。通过`payload.output.sentence.emo_confidence`字段获取当前句子的情感置信度。 |

`payload.output.sentence.words`为字时间戳列表，其中每一个word格式如下：

|      |      |      |
| ---- | ---- | ---- |
|      |      |      |

| **参数**    | **类型** | **说明**               |
| ----------- | -------- | ---------------------- |
| begin_time  | integer  | 字开始时间，单位为ms。 |
| end_time    | integer  | 字结束时间，单位为ms。 |
| text        | string   | 字。                   |
| punctuation | string   | 标点。                 |

### **3. task-finished事件：任务已结束**

当监听到服务端返回的`task-finished`事件时，说明任务已结束。此时可以关闭WebSocket连接并结束程序。

**示例：**

 

```json
{
    "header": {
        "task_id": "2bf83b9a-baeb-4fda-8d9a-xxxxxxxxxxxx",
        "event": "task-finished",
        "attributes": {}
    },
    "payload": {
        "output": {},
        "usage": null
    }
}
```

`**header**`**参数说明：**

|      |      |      |
| ---- | ---- | ---- |
|      |      |      |

| **参数**       | **类型** | **说明**                                      |
| -------------- | -------- | --------------------------------------------- |
| header.event   | string   | 事件类型。当前事件中，固定为"task-finished"。 |
| header.task_id | string   | 客户端生成的task_id。                         |

### **4. task-failed事件：任务失败**

如果接收到`task-failed`事件，表示任务失败。此时需要关闭WebSocket连接并处理错误。通过分析报错信息，如果是由于编程问题导致的任务失败，您可以调整代码进行修正。

**示例：**

 

```json
{
    "header": {
        "task_id": "2bf83b9a-baeb-4fda-8d9a-xxxxxxxxxxxx",
        "event": "task-failed",
        "error_code": "CLIENT_ERROR",
        "error_message": "request timeout after 23 seconds.",
        "attributes": {}
    },
    "payload": {}
}
```

`**header**`**参数说明：**

|      |      |      |
| ---- | ---- | ---- |
|      |      |      |

| **参数**             | **类型** | **说明**                                    |
| -------------------- | -------- | ------------------------------------------- |
| header.event         | string   | 事件类型。当前事件中，固定为"task-failed"。 |
| header.task_id       | string   | 客户端生成的task_id。                       |
| header.error_code    | string   | 报错类型描述。                              |
| header.error_message | string   | 具体报错原因。                              |

## **关于建连开销和连接复用**

WebSocket服务支持连接复用以提升资源的利用效率，避免建立连接开销。

服务端收到客户端发送的[run-task指令](https://help.aliyun.com/zh/model-studio/websocket-for-paraformer-real-time-service?spm=a2c4g.11186623.0.0.712b2c4dZyRXWT#12d8a57443dmz)后，将启动一个新的任务，客户端发送[finish-task指令](https://help.aliyun.com/zh/model-studio/websocket-for-paraformer-real-time-service?spm=a2c4g.11186623.0.0.712b2c4dZyRXWT#2e967d2d349es)后，服务端在任务完成时返回[task-finished事件](https://help.aliyun.com/zh/model-studio/websocket-for-paraformer-real-time-service?spm=a2c4g.11186623.0.0.712b2c4dZyRXWT#f11a341732xug)以结束该任务。结束任务后WebSocket连接可以被复用，客户端重新发送[run-task指令](https://help.aliyun.com/zh/model-studio/websocket-for-paraformer-real-time-service?spm=a2c4g.11186623.0.0.712b2c4dZyRXWT#12d8a57443dmz)即可开启下一个任务。

**重要**

1. 在复用连接中的不同任务需要使用不同 task_id。
2. 如果在任务执行过程中发生失败，服务将依然返回[task-failed事件](https://help.aliyun.com/zh/model-studio/websocket-for-paraformer-real-time-service?spm=a2c4g.11186623.0.0.712b2c4dZyRXWT#ea4609132a8u7)，并关闭该连接。此时这个连接无法继续复用。
3. 如果在任务结束后60秒没有新的任务，连接会超时自动断开。

## **示例代码**

示例代码仅提供最基础的服务调通实现，实际业务场景的相关代码需您自行开发。

在编写WebSocket客户端代码时，为了同时发送和接收消息，通常采用异步编程。您可以按照以下步骤来编写程序：

1. **建立WebSocket连接**

   调用WebSocket库函数（具体实现方式因编程语言或库函数而异），传入[Headers](https://help.aliyun.com/zh/model-studio/websocket-for-paraformer-real-time-service?spm=a2c4g.11186623.0.0.712b2c4dZyRXWT#f5b7af17168sz)和[URL](https://help.aliyun.com/zh/model-studio/websocket-for-paraformer-real-time-service?spm=a2c4g.11186623.0.0.712b2c4dZyRXWT#f8ab6424cbcdk)建立WebSocket连接。

2. **监听服务端消息**

   通过 WebSocket 库提供的回调函数（观察者模式），您可以监听服务端返回的消息。具体实现方式因编程语言不同而有所差异。

   服务端返回的消息分为两类：二进制音频流和事件。

   **监听**[**事件**](https://help.aliyun.com/zh/model-studio/websocket-for-paraformer-real-time-service?spm=a2c4g.11186623.0.0.712b2c4dZyRXWT#a989eb7099wjv)**：**

   - **task-started：**当接收到[task-started事件](https://help.aliyun.com/zh/model-studio/websocket-for-paraformer-real-time-service?spm=a2c4g.11186623.0.0.712b2c4dZyRXWT#2942cede42z9e)时，表示任务已成功开启。只有在此事件触发后，才能向服务端发送二进制音频或[finish-task指令](https://help.aliyun.com/zh/model-studio/websocket-for-paraformer-real-time-service?spm=a2c4g.11186623.0.0.712b2c4dZyRXWT#2e967d2d349es)；否则任务会失败。
   - **result-generated：**客户端发送二进制音频时，服务端可能会持续返回[result-generated事件](https://help.aliyun.com/zh/model-studio/websocket-for-paraformer-real-time-service?spm=a2c4g.11186623.0.0.712b2c4dZyRXWT#e9420a4d7bock)，该事件包含语音识别结果。
   - **task-finished：**接收到[task-finished事件](https://help.aliyun.com/zh/model-studio/websocket-for-paraformer-real-time-service?spm=a2c4g.11186623.0.0.712b2c4dZyRXWT#f11a341732xug)时，表示任务已完成。此时可以关闭 WebSocket 连接并结束程序。
   - **task-failed：**如果接收到[task-failed事件](https://help.aliyun.com/zh/model-studio/websocket-for-paraformer-real-time-service?spm=a2c4g.11186623.0.0.712b2c4dZyRXWT#ea4609132a8u7)，表示任务失败。需要关闭 WebSocket 连接，并根据报错信息调整代码进行修正。

3. **向服务端发送消息（请务必注意时序）**

   在不同于监听服务端消息的线程（如主线程，具体实现因编程语言而异）中，向服务端发送[指令](https://help.aliyun.com/zh/model-studio/websocket-for-paraformer-real-time-service?spm=a2c4g.11186623.0.0.712b2c4dZyRXWT#271eb7a50ft6r)和二进制音频。

   发送指令需严格遵循以下时序，否则可能导致任务失败：

   1. **发送**[**run-task指令**](https://help.aliyun.com/zh/model-studio/websocket-for-paraformer-real-time-service?spm=a2c4g.11186623.0.0.712b2c4dZyRXWT#12d8a57443dmz)
      - 用于启动语音识别任务。
      - 返回的 `task_id` 需在后续发送[finish-task指令](https://help.aliyun.com/zh/model-studio/websocket-for-paraformer-real-time-service?spm=a2c4g.11186623.0.0.712b2c4dZyRXWT#2e967d2d349es)时使用，必须保持一致。
   2. **发送**[**二进制音频**](https://help.aliyun.com/zh/model-studio/websocket-for-paraformer-real-time-service?spm=a2c4g.11186623.0.0.712b2c4dZyRXWT#e14da5dfe9npt)**（单声道）**
      - 用于发送待识别音频。
      - 必须在接收到服务端返回的[task-started事件](https://help.aliyun.com/zh/model-studio/websocket-for-paraformer-real-time-service?spm=a2c4g.11186623.0.0.712b2c4dZyRXWT#2942cede42z9e)后发送音频。
   3. **发送**[**finish-task指令**](https://help.aliyun.com/zh/model-studio/websocket-for-paraformer-real-time-service?spm=a2c4g.11186623.0.0.712b2c4dZyRXWT#2e967d2d349es)
      - 用于结束语音识别任务。
      - 在音频发送完毕后发送此指令。

4. **关闭WebSocket连接**

   在程序正常结束、运行中出现异常或接收到[task-finished事件](https://help.aliyun.com/zh/model-studio/websocket-for-paraformer-real-time-service?spm=a2c4g.11186623.0.0.712b2c4dZyRXWT#f11a341732xug)、[task-failed事件](https://help.aliyun.com/zh/model-studio/websocket-for-paraformer-real-time-service?spm=a2c4g.11186623.0.0.712b2c4dZyRXWT#ea4609132a8u7)时，关闭WebSocket连接。通常通过调用工具库中的`close`函数来实现。

点击查看完整示例

需安装相关依赖：

 

```sh
npm install ws
npm install uuid
```

示例代码如下：

 

```nodejs
const fs = require('fs');
const WebSocket = require('ws');
const { v4: uuidv4 } = require('uuid'); // 用于生成UUID

// 若没有将API Key配置到环境变量，可将下行替换为：apiKey = 'your_api_key'。不建议在生产环境中直接将API Key硬编码到代码中，以减少API Key泄露风险。
const apiKey = process.env.DASHSCOPE_API_KEY;
const url = 'wss://dashscope.aliyuncs.com/api-ws/v1/inference/'; // WebSocket服务器地址
const audioFile = 'asr_example.wav'; // 替换为您的音频文件路径

// 生成32位随机ID
const TASK_ID = uuidv4().replace(/-/g, '').slice(0, 32);

// 创建WebSocket客户端
const ws = new WebSocket(url, {
  headers: {
    Authorization: `bearer ${apiKey}`,
    'X-DashScope-DataInspection': 'enable'
  }
});

let taskStarted = false; // 标记任务是否已启动

// 连接打开时发送run-task指令
ws.on('open', () => {
  console.log('连接到服务器');
  sendRunTask();
});

// 接收消息处理
ws.on('message', (data) => {
  const message = JSON.parse(data);
  switch (message.header.event) {
    case 'task-started':
      console.log('任务开始');
      taskStarted = true;
      sendAudioStream();
      break;
    case 'result-generated':
      console.log('识别结果：', message.payload.output.sentence.text);
      break;
    case 'task-finished':
      console.log('任务完成');
      ws.close();
      break;
    case 'task-failed':
      console.error('任务失败：', message.header.error_message);
      ws.close();
      break;
    default:
      console.log('未知事件：', message.header.event);
  }
});

// 如果没有收到task-started事件，关闭连接
ws.on('close', () => {
  if (!taskStarted) {
    console.error('任务未启动，关闭连接');
  }
});

// 发送run-task指令
function sendRunTask() {
  const runTaskMessage = {
    header: {
      action: 'run-task',
      task_id: TASK_ID,
      streaming: 'duplex'
    },
    payload: {
      task_group: 'audio',
      task: 'asr',
      function: 'recognition',
      model: 'paraformer-realtime-v2',
      parameters: {
        sample_rate: 16000,
        format: 'wav'
      },
      input: {}
    }
  };
  ws.send(JSON.stringify(runTaskMessage));
}

// 发送音频流
function sendAudioStream() {
  const audioStream = fs.createReadStream(audioFile);
  let chunkCount = 0;

  function sendNextChunk() {
    const chunk = audioStream.read();
    if (chunk) {
      ws.send(chunk);
      chunkCount++;
      setTimeout(sendNextChunk, 100); // 每100ms发送一次
    }
  }

  audioStream.on('readable', () => {
    sendNextChunk();
  });

  audioStream.on('end', () => {
    console.log('音频流结束');
    sendFinishTask();
  });

  audioStream.on('error', (err) => {
    console.error('读取音频文件错误：', err);
    ws.close();
  });
}

// 发送finish-task指令
function sendFinishTask() {
  const finishTaskMessage = {
    header: {
      action: 'finish-task',
      task_id: TASK_ID,
      streaming: 'duplex'
    },
    payload: {
      input: {}
    }
  };
  ws.send(JSON.stringify(finishTaskMessage));
}

// 错误处理
ws.on('error', (error) => {
  console.error('WebSocket错误：', error);
});
```

## **错误码**

在使用API过程中，如果调用失败并返回错误信息，请参见[错误信息](https://help.aliyun.com/zh/model-studio/error-code)进行解决。

## **常见问题**

### **功能特性**

#### **Q：在长时间静默的情况下，如何保持与服务端长连接？**

将请求参数`heartbeat`设置为true，并持续向服务端发送静音音频。

静音音频指的是在音频文件或数据流中没有声音信号的内容。静音音频可以通过多种方法生成，例如使用音频编辑软件如Audacity或Adobe Audition，或者通过命令行工具如FFmpeg。

#### **Q：如何识别本地文件（录音文件）？**

将本地文件转成二进制音频流，通过WebSocket的二进制通道上传二进制音频流进行识别（通常为WebSocket库的send方法）。代码片段如下所示，完整示例请参见[示例代码](https://help.aliyun.com/zh/model-studio/websocket-for-paraformer-real-time-service?spm=a2c4g.11186623.0.0.712b2c4dZyRXWT#f2e763e39dw6i)。

点击查看代码片段

```Node.js
// 发送音频流
function sendAudioStream() {
  const audioStream = fs.createReadStream(audioFile);
  let chunkCount = 0;

  function sendNextChunk() {
    const chunk = audioStream.read();
    if (chunk) {
      ws.send(chunk);
      chunkCount++;
      setTimeout(sendNextChunk, 100); // 每100ms发送一次
    }
  }

  audioStream.on('readable', () => {
    sendNextChunk();
  });

  audioStream.on('end', () => {
    console.log('音频流结束');
    sendFinishTask();
  });

  audioStream.on('error', (err) => {
    console.error('读取音频文件错误：', err);
    ws.close();
  });
}
```





# 高并发场景下实时语音识别的性能优化

更新时间：2025-03-28 16:09:06

[产品详情](https://www.aliyun.com/product/bailian)

[我的收藏](https://help.aliyun.com/my_favorites.html)

本文介绍在高并发场景下，如何通过DashScope Java SDK，高效调用Paraformer实时语音识别服务。

Paraformer实时语音识别内部通过WebSocket协议实现，在高并发场景下，WebSocket连接可能会被不断地创建，从而带来较大的资源消耗。

在使用DashScope Java SDK时，您可以根据服务器的实际情况，通过给连接池、对象池设定合理的大小来降低运行开销。

## **前提条件**

- 已开通服务并[获取API Key](https://help.aliyun.com/zh/model-studio/get-api-key)。请[配置API Key到环境变量](https://help.aliyun.com/zh/model-studio/configure-api-key-through-environment-variables)，而非硬编码在代码中，防范因代码泄露导致的安全风险。
- [安装最新版DashScope SDK](https://help.aliyun.com/zh/model-studio/install-sdk/)。

## 推荐配置

连接池和对象池不是越多越好，过少或过多都会导致程序运行变慢。建议您根据自己服务器的实际规格进行配置。

我们在服务器上只运行Paraformer实时语音识别服务的情况下，进行测试后得到了如下推荐配置供您参考：

|      |      |      |      |
| ---- | ---- | ---- | ---- |
|      |      |      |      |

| **常见机器配置（阿里云）** | **单机最大并发数** | **对象池大小** | **连接池大小** |
| -------------------------- | ------------------ | -------------- | -------------- |
| 4核8GiB                    | 100                | 500            | 2000           |
| 8核16GiB                   | 200                | 500            | 2000           |
| 16核32GiB                  | 400                | 500            | 2000           |

其中单机并发数指的是同一时刻正在运行的Paraformer实时语音识别任务数，也可以理解为工作线程数。

## **可配置参数**

### 连接池

DashScope Java SDK使用了OkHttp3提供的连接池复用WebSocket连接，降低不断创建WebSocket连接的耗时和资源开销。

连接池为SDK默认开启的优化项，您需要根据使用场景配置连接池的大小。

请您在运行Java服务前通过环境变量的方式在机器中提前按需配置好连接池的相关参数。连接池配置参数如下：

| DASHSCOPE_CONNECTION_POOL_SIZE            | 配置连接池大小。推荐配置为您的峰值并发数的2倍以上。默认值为32。 |
| ----------------------------------------- | ------------------------------------------------------------ |
| DASHSCOPE_MAXIMUM_ASYNC_REQUESTS          | 配置最大异步请求数。推荐配置为和连接池大小一致。默认值为32。更多信息参见[参考文档](https://square.github.io/okhttp/3.x/okhttp/okhttp3/Dispatcher.html#setMaxRequests-int-)。 |
| DASHSCOPE_MAXIMUM_ASYNC_REQUESTS_PER_HOST | 配置单host最大异步请求数。推荐配置为和连接池大小一致。默认值为32。更多信息参见[参考文档](https://square.github.io/okhttp/3.x/okhttp/okhttp3/Dispatcher.html#setMaxRequests-int-)。 |

### **对象池**

推荐使用对象池的方式复用`Recognition`对象，可以进一步降低反复创建、销毁对象带来的内存和时间开销。

请您在运行Java服务前通过环境变量或代码的方式在机器中提前按需配置好对象池的大小。对象池配置参数如下：

| RECOGNITION_OBJECTPOOL_SIZE | 对象池大小。推荐配置为您的峰值并发数的1.5~2倍。对象池大小需要小于等于连接池大小，不然会出现对象等待连接的情况造成调用阻塞。 |
| --------------------------- | ------------------------------------------------------------ |
|                             |                                                              |

关于如何配置环境变量，可参考[配置API Key到环境变量](https://help.aliyun.com/zh/model-studio/configure-api-key-through-environment-variables)。

## **示例代码**

下面为使用资源池的示例代码。其中，对象池为全局单例对象。

- 每个主账号默认每秒可提交20个Paraformer实时语音识别任务。

  如需开通更高QPS请[联系我们](https://smartservice.console.aliyun.com/service/create-ticket)。

- 在运行示例前，请提前下载好asr_example.wav示例音频，或替换为本地音频文件。

  [asr_example.wav](https://help-static-aliyun-doc.aliyuncs.com/file-manage-files/zh-CN/20241025/obqscj/asr_example.wav)

- 您需要在项目中引入DashScope和org.apache.commons.pool2相关的包，DashScope要求版本号>=2.16.9。

  

  以Maven和Gradle为例，配置如下：

  Maven

  Gradle

  1. 打开您的Maven项目的`pom.xml`文件。
  2. 在`<dependencies>`标签内添加以下依赖信息。

   

  ```xml
  <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>dashscope-sdk-java</artifactId>
      <!-- 请将 'the-latest-version' 替换为2.16.9及以上版本，可在如下链接查询相关版本号：https://mvnrepository.com/artifact/com.alibaba/dashscope-sdk-java -->
      <version>the-latest-version</version>
  </dependency>
  
  <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-pool2</artifactId>
      <!-- 请将 'the-latest-version' 替换为最新版本，可在如下链接查询相关版本号：https://mvnrepository.com/artifact/org.apache.commons/commons-pool2 -->
      <version>the-latest-version</version>
  </dependency>
  ```

  1. 保存`pom.xml`文件。
  2. 使用Maven命令（如`mvn clean install`或`mvn compile`）来更新项目依赖

 

```java
package org.example;

import com.alibaba.dashscope.audio.asr.recognition.Recognition;
import com.alibaba.dashscope.audio.asr.recognition.RecognitionParam;
import com.alibaba.dashscope.audio.asr.recognition.RecognitionResult;
import com.alibaba.dashscope.common.ResultCallback;
import com.alibaba.dashscope.exception.NoApiKeyException;
import com.alibaba.dashscope.utils.ApiKey;
import org.apache.commons.pool2.BasePooledObjectFactory;
import org.apache.commons.pool2.PooledObject;
import org.apache.commons.pool2.impl.DefaultPooledObject;
import org.apache.commons.pool2.impl.GenericObjectPool;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;

import java.io.FileInputStream;
import java.nio.ByteBuffer;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;

/**
 * Before making high-concurrency calls to the ASR service,
 * please configure the connection pool size through following environment
 * variables.
 *
 * DASHSCOPE_MAXIMUM_ASYNC_REQUESTS=2000
 * DASHSCOPE_MAXIMUM_ASYNC_REQUESTS_PER_HOST=2000
 * DASHSCOPE_CONNECTION_POOL_SIZE=2000
 *
 * The default is 32, and it is recommended to set it to 2 times the maximum
 * concurrent connections of a single server.
 */
public class RecognitionWithRealtimeApiConcurrently {
    public static void checkoutEnv(String envName, int defaultSize) {
        if (System.getenv(envName) != null) {
            System.out.println("[ENV CHECK]: " + envName + " "
                    + System.getenv(envName));
        } else {
            System.out.println("[ENV CHECK]: " + envName
                    + " Using Default which is " + defaultSize);
        }
    }

    public static void main(String[] args)
            throws NoApiKeyException, InterruptedException {
        // Check for connection pool env
        checkoutEnv("DASHSCOPE_CONNECTION_POOL_SIZE", 32);
        checkoutEnv("DASHSCOPE_MAXIMUM_ASYNC_REQUESTS", 32);
        checkoutEnv("DASHSCOPE_MAXIMUM_ASYNC_REQUESTS_PER_HOST", 32);
        checkoutEnv(RecognitionObjectPool.RECOGNITION_OBJECTPOOL_SIZE_ENV, RecognitionObjectPool.DEFAULT_OBJECT_POOL_SIZE);

        int threadNums = 3;
        String currentDir = System.getProperty("user.dir");
        // Please replace the path with your audio source
        Path[] filePaths = {
                Paths.get(currentDir, "asr_example.wav"),
                Paths.get(currentDir, "asr_example.wav"),
                Paths.get(currentDir, "asr_example.wav"),
        };
        // Use ThreadPool to run recognition tasks
        ExecutorService executorService = Executors.newFixedThreadPool(threadNums);
        for (int i = 0; i < threadNums; i++) {
            executorService.submit(new RealtimeRecognizeTask(filePaths));
        }
        executorService.shutdown();
        // wait for all tasks to complete
        executorService.awaitTermination(10, TimeUnit.MINUTES);
        System.exit(0);
    }
}

class RecognitionObjectFactory extends BasePooledObjectFactory<Recognition> {
    public RecognitionObjectFactory() {
        super();
    }

    @Override
    public Recognition create() throws Exception {
        return new Recognition();
    }

    @Override
    public PooledObject<Recognition> wrap(Recognition obj) {
        return new DefaultPooledObject<>(obj);
    }
}

class RecognitionObjectPool {
    public static GenericObjectPool<Recognition> recognitionGenericObjectPool;
    public static String RECOGNITION_OBJECTPOOL_SIZE_ENV =
            "RECOGNITION_OBJECTPOOL_SIZE";
    public static int DEFAULT_OBJECT_POOL_SIZE = 500;
    private static Lock lock = new java.util.concurrent.locks.ReentrantLock();

    public static int getObjectivePoolSize() {
        try {
            Integer n = Integer.parseInt(System.getenv(RECOGNITION_OBJECTPOOL_SIZE_ENV));
            return n;
        } catch (NumberFormatException e) {
            return DEFAULT_OBJECT_POOL_SIZE;
        }
    }

    public static GenericObjectPool<Recognition> getInstance() {
        lock.lock();
        if (recognitionGenericObjectPool == null) {
            // You can set the object pool size here. or in environment variable
            // RECOGNITION_OBJECTPOOL_SIZE It is recommended to set it to 1.5 to 2
            // times your server's maximum concurrent connections.
            int objectPoolSize = getObjectivePoolSize();
            System.out.println("RECOGNITION_OBJECTPOOL_SIZE: "
                    + objectPoolSize);
            RecognitionObjectFactory recognitionObjectFactory =
                    new RecognitionObjectFactory();
            GenericObjectPoolConfig<Recognition> config =
                    new GenericObjectPoolConfig<>();
            config.setMaxTotal(objectPoolSize);
            config.setMaxIdle(objectPoolSize);
            config.setMinIdle(objectPoolSize);
            recognitionGenericObjectPool =
                    new GenericObjectPool<>(recognitionObjectFactory, config);
        }
        lock.unlock();
        return recognitionGenericObjectPool;
    }
}

class RealtimeRecognizeTask implements Runnable {
    private static final Object lock = new Object();
    private Path[] filePaths;

    public RealtimeRecognizeTask(Path[] filePaths) {
        this.filePaths = filePaths;
    }

    /**
     * Set your DashScope API key. More information: <a
     * href="https://help.aliyun.com/document_detail/2712195.html">...</a> In
     * fact, if you have set DASHSCOPE_API_KEY in your environment variable, you
     * can ignore this, and the SDK will automatically get the api_key from the
     * environment variable
     * */
    private static String getDashScopeApiKey() throws NoApiKeyException {
        String dashScopeApiKey = null;
        try {
            ApiKey apiKey = new ApiKey();
            dashScopeApiKey =
                    ApiKey.getApiKey(null); // Retrieve from environment variable.
        } catch (NoApiKeyException e) {
            System.out.println("No API key found in environment.");
        }
        if (dashScopeApiKey == null) {
            // If you cannot set api_key in your environment variable,
            // you can set it here by code
            dashScopeApiKey = "your-dashscope-apikey";
        }
        return dashScopeApiKey;
    }

    public void runCallback() {
        int round = 0;
        for (Path filePath : filePaths) {
            // Create recognition params
            // you can customize the recognition parameters, like model, format,
            // sample_rate for more information, please refer to
            // https://help.aliyun.com/document_detail/2712536.html
            RecognitionParam param = null;
            try {
                param =
                        RecognitionParam.builder()
                                .model("paraformer-realtime-v2")
                                .format(
                                        "pcm") // 'pcm'、'wav'、'opus'、'speex'、'aac'、'amr', you
                                // can check the supported formats in the document
                                .sampleRate(16000) // supported 8000、16000
                                .apiKey(getDashScopeApiKey()) // use getDashScopeApiKey to get
                                // api key.
                                .build();
            } catch (NoApiKeyException e) {
                throw new RuntimeException(e);
            }

            Recognition recognizer = null;
            boolean hasInvalied = false;

            try {
                recognizer = RecognitionObjectPool.getInstance().borrowObject();
                CountDownLatch latch = new CountDownLatch(1);

                String threadName = Thread.currentThread().getName();

                // if recv onError
                final boolean[] hasError = {false};

                ResultCallback<RecognitionResult> callback =
                        new ResultCallback<RecognitionResult>() {
                            @Override
                            public void onEvent(RecognitionResult message) {
                                synchronized (lock) {
                                    if (message.isSentenceEnd()) {
                                        System.out.println("[process " + threadName
                                                + "] Fix:" + message.getSentence().getText());
                                    } else {
                                        System.out.println("[process " + threadName
                                                + "] Result: " + message.getSentence().getText());
                                    }
                                }
                            }

                            @Override
                            public void onComplete() {
                                System.out.println("[" + threadName + "] Recognition complete");
                                latch.countDown();
                            }

                            @Override
                            public void onError(Exception e) {
                                System.out.println("[" + threadName
                                        + "] RecognitionCallback error: " + e.getMessage());
                                latch.countDown();
                                hasError[0] = true;
                            }
                        };
                // Please replace the path with your audio file path
                System.out.println(
                        "[" + threadName + "] Input file_path is: " + filePath);
                FileInputStream fis = null;
                // Read file and send audio by chunks
                try {
                    fis = new FileInputStream(filePath.toFile());
                } catch (Exception e) {
                    System.out.println("Error when loading file: " + filePath);
                    e.printStackTrace();
                }
                // set param & callback
                recognizer.call(param, callback);
                if(round==0) {
                    Thread.sleep(65*1000);
                }
                round ++;

                // chunk size set to 100 ms for 16KHz sample rate
                byte[] buffer = new byte[3200];
                int bytesRead;
                // Loop to read chunks of the file
                while ((bytesRead = fis.read(buffer)) != -1) {
                    ByteBuffer byteBuffer;
                    if (bytesRead < buffer.length) {
                        byteBuffer = ByteBuffer.wrap(buffer, 0, bytesRead);
                    } else {
                        byteBuffer = ByteBuffer.wrap(buffer);
                    }
                    // Send the ByteBuffer to the recognition instance
                    recognizer.sendAudioFrame(byteBuffer);
                    Thread.sleep(100);
                    buffer = new byte[3200];
                }
                System.out.println(LocalDateTime.now());
                recognizer.stop();
                // wait for the recognition to complete
                try {
                    latch.await();
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }

                if (hasError[0] && !hasInvalied) {
                    recognizer.getDuplexApi().close(1000, "bye");
                    // invalidate the recognizer if onError
                    RecognitionObjectPool.getInstance().invalidateObject(recognizer);
                    RecognitionObjectPool.getInstance().addObject();
                    hasInvalied = true;
                }

            } catch (Exception e) {
                e.printStackTrace();
                if (!hasInvalied) {
                    recognizer.getDuplexApi().close(1000, "bye");
                    // invalidate the recognizer if exception occurs
                    try {
                        RecognitionObjectPool.getInstance().invalidateObject(recognizer);
                        RecognitionObjectPool.getInstance().addObject();
                    } catch (Exception ex) {
                        ex.printStackTrace();
                    }
                    hasInvalied = true;
                }
            } finally {
                if (recognizer != null && !hasInvalied) {
                    try {
                        // Return the recognition object to the pool if no error or exception.
                        RecognitionObjectPool.getInstance().returnObject(recognizer);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
        }
    }

    @Override
    public void run() {
        runCallback();
    }
}
```

### **异常处理**

在服务出现TaskFailed报错或SDK抛出异常时，本次任务使用的recognizer对象将不可复用。此时需要关闭ws连接，并在对象池中禁用该对象，同时加入一个新的对象以替代。具体操作为：

 

```java
// 关闭连接
recognizer.getDuplexApi().close(1000, "bye");
// 禁用对象
RecognitionObjectPool.getInstance().invalidateObject(recognizer);
// 添加新的对象到对象池
RecognitionObjectPool.getInstance().addObject();
```

需要注意，`invalidateObject`和`returnObject`方法不可以同时执行：

- `invalidateObject`方法：在出错时使用，禁用对象。
- `returnObject`方法：在未出错时使用，归还对象。

## 常见异常

**异常 1、 业务流量平稳，但是服务器 TCP 连接数持续上升**

**出错原因：**

**类型一：**

每一个 SDK 对象创建时都会申请一个连接。如果没有使用对象池，每一次任务结束后对象都被析构。此时这一个连接将进入无引用状态，需要等待 61s 秒后服务端报错连接超时才会真正断开，这会导致这个连接在 61 秒内不可复用。

在高并发场景下，新的任务在发现没有可复用连接时会创建新连接，会造成如下后果：

1. 连接数持续上升。
2. 由于连接数过多，服务器资源不足，服务器卡顿。
3. 连接池被打满、新任务由于启动时需要等待可用连接而阻塞。

**类型二：**

对象池配置的MaxIdle小于MaxTotal，导致在对象闲置时，超过MaxIdle的对象被销毁，从而造成连接泄漏。泄漏的连接需要等待61秒超时后断连，同类型一造成连接数持续上升。

**解决方法**：

对于类型一，使用对象池解决。

对于类型二，检查对象池配置参数，设置MaxIdle和MaxTotal相等，关闭对象池自动销毁策略解决。

异常 2、任务耗时比正常调用多 60 秒

同“**异常 1**”，连接池已经达到最大连接限制，新的任务需要等待无引用状态的连接 61 秒触发超时后才可以获得连接。

异常 3、服务启动时任务慢，之后慢慢恢复正常

**出错原因**：

在高并发调用时，同一个对象会复用同一个WebSocket连接，因此WebSocket连接只会在服务启动时创建。需要注意的是，任务启动阶段如果立刻开始较高并发调用，同时创建过多的WebSocket连接会导致阻塞。

**解决方法**：

启动服务后逐步提升并发量，或增加预热任务。

异常 4、服务端报错 Invalid action('run-task')! Please follow the protocol!

**出错原因**：

这是由于出现了客户端报错后，服务端不知道客户端出错，连接处于任务中状态。此时连接和对象被复用并开启下一个任务，导致流程错误，下一个任务失败。

**解决方法**：

在抛出异常后主动关闭 WebSocket 连接后归还对象池。

异常 5、业务流量平稳，调用量出现异常尖刺

**出错原因**：

同时创建过多 WebSocket 连接导致阻塞，但业务流量持续打进来，导致任务短时间积压，并且在阻塞后所有积压任务立刻调用。这会造成调用量尖刺，并且有可能造成瞬时超过账号的并发数限制导致部分任务失败、服务器卡顿等。

这种瞬间创建过多 WebSocket 的情况多发生于：

- 服务启动阶段
- 网络出现异常，大量 WebSocket 连接同时中断重连
- 某一时刻出现大量服务端报错，导致大量 WebSocket 重连。常见报错如并发数超过账号限制（“Requests rate limit exceeded, please try again later.”）。

**解决方法**：

1. 检查网络情况。
2. 排查尖刺前是否出现大量其他服务端报错。
3. 提高账号并发限制。
4. 调小对象池和连接池大小，通过对象池上限限制最大并发数。
5. 提升服务器配置或扩充机器数。

异常 6、随着并发数提升，所有任务都变慢

**解决方法**：

1. 检查是否已经达到网络带宽上限。
2. 检查实际并发数是否已经过高。