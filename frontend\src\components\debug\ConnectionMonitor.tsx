import React, { useState, useEffect } from 'react';
import { connectionTracker } from '../../utils/ConnectionTracker';

interface ConnectionMonitorProps {
  isVisible: boolean;
  onClose: () => void;
}

const ConnectionMonitor: React.FC<ConnectionMonitorProps> = ({ isVisible, onClose }) => {
  const [report, setReport] = useState<any>(null);
  const [autoRefresh, setAutoRefresh] = useState(true);

  useEffect(() => {
    if (!isVisible) return;

    const updateReport = () => {
      const newReport = connectionTracker.generateReport();
      setReport(newReport);
    };

    // 立即更新一次
    updateReport();

    // 自动刷新
    let interval: NodeJS.Timeout;
    if (autoRefresh) {
      interval = setInterval(updateReport, 2000); // 每2秒刷新
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isVisible, autoRefresh]);

  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[80vh] overflow-hidden">
        {/* 头部 */}
        <div className="bg-blue-600 text-white p-4 flex justify-between items-center">
          <h2 className="text-xl font-bold">🔍 连接监控面板</h2>
          <div className="flex items-center gap-4">
            <label className="flex items-center gap-2">
              <input
                type="checkbox"
                checked={autoRefresh}
                onChange={(e) => setAutoRefresh(e.target.checked)}
                className="rounded"
              />
              <span className="text-sm">自动刷新</span>
            </label>
            <button
              onClick={onClose}
              className="text-white hover:text-gray-200 text-xl"
            >
              ×
            </button>
          </div>
        </div>

        {/* 内容 */}
        <div className="p-6 overflow-y-auto max-h-[calc(80vh-80px)]">
          {report ? (
            <div className="space-y-6">
              {/* 概览 */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="bg-blue-50 p-4 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">{report.totalConnections}</div>
                  <div className="text-sm text-gray-600">总连接数</div>
                </div>
                <div className="bg-green-50 p-4 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">{report.activeConnections}</div>
                  <div className="text-sm text-gray-600">活跃连接</div>
                </div>
                <div className="bg-yellow-50 p-4 rounded-lg">
                  <div className="text-2xl font-bold text-yellow-600">{report.duplicateConnections.length}</div>
                  <div className="text-sm text-gray-600">重复连接</div>
                </div>
                <div className="bg-red-50 p-4 rounded-lg">
                  <div className="text-2xl font-bold text-red-600">{report.suspiciousConnections.length}</div>
                  <div className="text-sm text-gray-600">可疑连接</div>
                </div>
              </div>

              {/* 按类型统计 */}
              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="text-lg font-semibold mb-3">按类型统计</h3>
                <div className="grid grid-cols-3 gap-4">
                  {Object.entries(report.connectionsByType).map(([type, count]) => (
                    <div key={type} className="text-center">
                      <div className="text-xl font-bold">{count as number}</div>
                      <div className="text-sm text-gray-600">{type}</div>
                    </div>
                  ))}
                </div>
              </div>

              {/* 按来源统计 */}
              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="text-lg font-semibold mb-3">按来源统计</h3>
                <div className="space-y-2">
                  {Object.entries(report.connectionsBySource).map(([source, count]) => (
                    <div key={source} className="flex justify-between items-center">
                      <span className="text-sm font-mono">{source}</span>
                      <span className="font-bold">{count as number}</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* 重复连接警告 */}
              {report.duplicateConnections.length > 0 && (
                <div className="bg-yellow-50 border border-yellow-200 p-4 rounded-lg">
                  <h3 className="text-lg font-semibold text-yellow-800 mb-3">⚠️ 重复连接检测</h3>
                  <div className="space-y-2">
                    {report.duplicateConnections.map((conn: any, index: number) => (
                      <div key={index} className="text-sm font-mono bg-white p-2 rounded">
                        <div><strong>ID:</strong> {conn.id}</div>
                        <div><strong>类型:</strong> {conn.type}</div>
                        <div><strong>来源:</strong> {conn.source}</div>
                        <div><strong>时间:</strong> {new Date(conn.timestamp).toLocaleTimeString()}</div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* 可疑连接警告 */}
              {report.suspiciousConnections.length > 0 && (
                <div className="bg-red-50 border border-red-200 p-4 rounded-lg">
                  <h3 className="text-lg font-semibold text-red-800 mb-3">🚨 可疑连接检测</h3>
                  <div className="space-y-2">
                    {report.suspiciousConnections.map((conn: any, index: number) => (
                      <div key={index} className="text-sm font-mono bg-white p-2 rounded">
                        <div><strong>ID:</strong> {conn.id}</div>
                        <div><strong>类型:</strong> {conn.type}</div>
                        <div><strong>状态:</strong> {conn.status}</div>
                        <div><strong>时间:</strong> {new Date(conn.timestamp).toLocaleTimeString()}</div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* 连接时间线 */}
              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="text-lg font-semibold mb-3">连接时间线</h3>
                <div className="max-h-60 overflow-y-auto space-y-1">
                  {report.timeline.slice(-20).map((conn: any, index: number) => (
                    <div key={index} className="text-xs font-mono bg-white p-2 rounded flex justify-between">
                      <span>{new Date(conn.timestamp).toLocaleTimeString()}</span>
                      <span className={`px-2 py-1 rounded ${
                        conn.status === 'connected' ? 'bg-green-100 text-green-800' :
                        conn.status === 'error' ? 'bg-red-100 text-red-800' :
                        conn.status === 'disconnected' ? 'bg-gray-100 text-gray-800' :
                        'bg-yellow-100 text-yellow-800'
                      }`}>
                        {conn.status}
                      </span>
                      <span>{conn.type}</span>
                      <span className="truncate max-w-xs">{conn.id}</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* 操作按钮 */}
              <div className="flex gap-4">
                <button
                  onClick={() => connectionTracker.exportReport()}
                  className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
                >
                  导出完整报告到控制台
                </button>
                <button
                  onClick={() => connectionTracker.clearAll()}
                  className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
                >
                  清理所有追踪数据
                </button>
                <button
                  onClick={() => setReport(connectionTracker.generateReport())}
                  className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
                >
                  手动刷新
                </button>
              </div>
            </div>
          ) : (
            <div className="text-center py-8">
              <div className="text-gray-500">正在加载连接数据...</div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ConnectionMonitor;
