#!/usr/bin/env node

/**
 * 简化的阿里云短信服务测试脚本
 * 用于测试环境变量配置和基本功能
 */

const dotenv = require('dotenv');

// 加载环境变量
dotenv.config();

function testSmsConfiguration() {
  console.log('🚀 开始测试阿里云短信服务配置...\n');

  // 检查环境变量配置
  console.log('📋 检查环境变量配置:');
  console.log(`ACCESS_KEY_ID: ${process.env.ALIBABA_ACCESS_KEY_ID ? '已配置' : '未配置'}`);
  console.log(`ACCESS_KEY_SECRET: ${process.env.ALIBABA_ACCESS_KEY_SECRET ? '已配置' : '未配置'}`);
  console.log(`SMS_SIGN_NAME: ${process.env.ALIBABA_SMS_SIGN_NAME || '未配置'}`);
  console.log(`SMS_TEMPLATE_CODE: ${process.env.ALIBABA_SMS_TEMPLATE_CODE || '未配置'}\n`);

  if (!process.env.ALIBABA_ACCESS_KEY_ID || !process.env.ALIBABA_ACCESS_KEY_SECRET) {
    console.error('❌ 阿里云访问密钥未配置，请检查环境变量');
    return false;
  }

  if (!process.env.ALIBABA_SMS_TEMPLATE_CODE) {
    console.error('❌ 短信模板代码未配置，请检查环境变量');
    return false;
  }

  // 测试手机号验证功能
  console.log('📱 测试手机号验证功能:');
  
  function isValidPhoneNumber(phoneNumber) {
    // 中国大陆手机号正则表达式
    const phoneRegex = /^1[3-9]\d{9}$/;
    return phoneRegex.test(phoneNumber);
  }

  const testPhones = [
    '13800138000', // 有效
    '12345678901', // 无效
    '1380013800',  // 无效
    '15912345678'  // 有效
  ];

  testPhones.forEach(phone => {
    const isValid = isValidPhoneNumber(phone);
    console.log(`${phone}: ${isValid ? '✅ 有效' : '❌ 无效'}`);
  });

  console.log('\n✅ 基本配置检查完成');
  console.log('⚠️  实际短信发送测试需要启动完整的后端服务');
  console.log('⚠️  请确保已在阿里云控制台完成签名和模板的申请和审核');
  
  return true;
}

// 运行测试
try {
  const result = testSmsConfiguration();
  if (result) {
    console.log('\n🎉 配置测试通过');
    process.exit(0);
  } else {
    console.log('\n❌ 配置测试失败');
    process.exit(1);
  }
} catch (error) {
  console.error('💥 测试过程中发生错误:', error);
  process.exit(1);
}
