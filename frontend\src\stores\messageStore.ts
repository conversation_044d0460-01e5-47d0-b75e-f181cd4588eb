import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { unstable_batchedUpdates } from 'react-dom';

// 🔥 消息接口定义
export interface Message {
  id: string;
  content: string;
  type: 'interviewer' | 'ai-suggestion';
  timestamp: number;
}

// 🔥 面试会话状态接口
export interface InterviewSession {
  id: string;
  companyName: string;
  positionName: string;
  startTime: number;
  elapsedTime: number;
  messages: Message[];
  isActive: boolean;
}

// 🔥 消息批处理队列项
interface MessageQueueItem {
  message: Message;
  timestamp: number;
}

// 🔥 消息Store状态接口
interface MessageState {
  // 状态
  session: InterviewSession;
  messageQueue: MessageQueueItem[];
  isProcessingQueue: boolean;
  
  // 批处理配置
  batchConfig: {
    throttleMs: number;
    maxBatchSize: number;
    maxQueueSize: number;
  };

  // 性能监控
  performanceStats: {
    totalMessages: number;
    batchedUpdates: number;
    skippedUpdates: number;
    lastUpdateTime: number;
  };

  // Actions
  addMessage: (message: Message) => void;
  updateMessage: (messageId: string, updater: (msg: Message) => Message) => void;
  setMessages: (messages: Message[]) => void;
  clearMessages: () => void;
  
  // 批处理控制
  flushMessageQueue: () => void;
  setBatchConfig: (config: Partial<MessageState['batchConfig']>) => void;
  
  // 会话管理
  initializeSession: (sessionData: Partial<InterviewSession>) => void;
  updateSession: (updater: (session: InterviewSession) => InterviewSession) => void;
  
  // 性能监控
  getPerformanceStats: () => MessageState['performanceStats'];
  resetPerformanceStats: () => void;
}

// 🔥 默认会话配置
const defaultSession: InterviewSession = {
  id: `session-${Date.now()}`,
  companyName: '公司名称',
  positionName: '岗位名称',
  startTime: Date.now(),
  elapsedTime: 0,
  messages: [],
  isActive: true
};

// 🔥 默认批处理配置
const defaultBatchConfig = {
  throttleMs: 200,        // 200ms节流
  maxBatchSize: 10,       // 每批最多10条消息
  maxQueueSize: 100       // 队列最大100条消息
};

// 🔥 创建消息Store（使用subscribeWithSelector中间件支持选择器订阅）
export const useMessageStore = create<MessageState>()(
  subscribeWithSelector((set, get) => {
    let flushTimeout: NodeJS.Timeout | null = null;

    // 🔥 批处理刷新函数
    const flushMessageQueue = () => {
      const state = get();
      if (state.messageQueue.length === 0 || state.isProcessingQueue) {
        return;
      }

      console.log('🔄 Flushing message queue:', {
        queueLength: state.messageQueue.length,
        timestamp: Date.now()
      });

      set({ isProcessingQueue: true });

      // 🔥 使用React的批量更新
      unstable_batchedUpdates(() => {
        const { messageQueue, session, performanceStats } = get();
        const newMessages = messageQueue.map(item => item.message);
        
        set({
          session: {
            ...session,
            messages: [...session.messages, ...newMessages]
          },
          messageQueue: [],
          isProcessingQueue: false,
          performanceStats: {
            ...performanceStats,
            totalMessages: performanceStats.totalMessages + newMessages.length,
            batchedUpdates: performanceStats.batchedUpdates + 1,
            lastUpdateTime: Date.now()
          }
        });
      });

      // 清除定时器
      if (flushTimeout) {
        clearTimeout(flushTimeout);
        flushTimeout = null;
      }
    };

    return {
      // 初始状态
      session: defaultSession,
      messageQueue: [],
      isProcessingQueue: false,
      batchConfig: defaultBatchConfig,
      performanceStats: {
        totalMessages: 0,
        batchedUpdates: 0,
        skippedUpdates: 0,
        lastUpdateTime: Date.now()
      },

      // 🔥 添加消息（批处理）
      addMessage: (message: Message) => {
        const state = get();
        
        // 检查队列大小限制
        if (state.messageQueue.length >= state.batchConfig.maxQueueSize) {
          console.warn('⚠️ Message queue full, forcing flush');
          flushMessageQueue();
        }

        // 添加到队列
        set({
          messageQueue: [...state.messageQueue, { message, timestamp: Date.now() }]
        });

        // 清除之前的定时器
        if (flushTimeout) {
          clearTimeout(flushTimeout);
        }

        // 设置新的定时器
        flushTimeout = setTimeout(flushMessageQueue, state.batchConfig.throttleMs);

        console.log('📦 Message queued for batch processing:', {
          messageId: message.id,
          queueLength: state.messageQueue.length + 1,
          throttleMs: state.batchConfig.throttleMs
        });
      },

      // 🔥 更新消息
      updateMessage: (messageId: string, updater: (msg: Message) => Message) => {
        set((state) => ({
          session: {
            ...state.session,
            messages: state.session.messages.map(msg => 
              msg.id === messageId ? updater(msg) : msg
            )
          }
        }));
      },

      // 🔥 设置消息列表
      setMessages: (messages: Message[]) => {
        set((state) => ({
          session: {
            ...state.session,
            messages
          }
        }));
      },

      // 🔥 清空消息
      clearMessages: () => {
        set((state) => ({
          session: {
            ...state.session,
            messages: []
          },
          messageQueue: []
        }));
      },

      // 🔥 手动刷新队列
      flushMessageQueue,

      // 🔥 设置批处理配置
      setBatchConfig: (config) => {
        set((state) => ({
          batchConfig: { ...state.batchConfig, ...config }
        }));
      },

      // 🔥 初始化会话
      initializeSession: (sessionData) => {
        set((state) => ({
          session: { ...defaultSession, ...sessionData }
        }));
      },

      // 🔥 更新会话
      updateSession: (updater) => {
        set((state) => ({
          session: updater(state.session)
        }));
      },

      // 🔥 获取性能统计
      getPerformanceStats: () => get().performanceStats,

      // 🔥 重置性能统计
      resetPerformanceStats: () => {
        set((state) => ({
          performanceStats: {
            totalMessages: 0,
            batchedUpdates: 0,
            skippedUpdates: 0,
            lastUpdateTime: Date.now()
          }
        }));
      }
    };
  })
);

// 🔥 选择器Hook - 只订阅消息变化
export const useMessages = () => useMessageStore((state) => state.session.messages);

// 🔥 选择器Hook - 只订阅会话信息
export const useSessionInfo = () => useMessageStore((state) => ({
  id: state.session.id,
  companyName: state.session.companyName,
  positionName: state.session.positionName,
  startTime: state.session.startTime,
  elapsedTime: state.session.elapsedTime,
  isActive: state.session.isActive
}));

// 🔥 选择器Hook - 只订阅性能统计
export const usePerformanceStats = () => useMessageStore((state) => state.performanceStats);

// 🔥 Actions Hook - 获取所有操作函数
export const useMessageActions = () => useMessageStore((state) => ({
  addMessage: state.addMessage,
  updateMessage: state.updateMessage,
  setMessages: state.setMessages,
  clearMessages: state.clearMessages,
  flushMessageQueue: state.flushMessageQueue,
  setBatchConfig: state.setBatchConfig,
  initializeSession: state.initializeSession,
  updateSession: state.updateSession,
  getPerformanceStats: state.getPerformanceStats,
  resetPerformanceStats: state.resetPerformanceStats
}));
