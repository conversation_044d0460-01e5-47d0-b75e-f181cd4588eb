# Requirements Document

## Introduction

本需求文档旨在解决AI模拟面试系统中的核心逻辑问题，通过深度重构实现正确的面试流程。当前系统存在角色定位混乱、流程逻辑错误、消息类型系统设计缺陷等问题，导致用户体验不佳。本项目将通过状态机驱动的架构重构，实现AI面试官先提问、用户后回答的正确面试流程，同时确保与正式面试功能的完全隔离。

## Requirements

### Requirement 1: AI面试官角色定位修正

**User Story:** 作为一个求职者，我希望在AI模拟面试中，AI能够明确充当面试官角色，主动提问并引导面试流程，这样我就能获得真实的面试体验。

#### Acceptance Criteria

1. WHEN 系统处理模拟面试消息 THEN 系统 SHALL 只使用 'interviewer' 和 'user-answer' 两种消息类型
2. WHEN 系统初始化模拟面试会话 THEN 系统 SHALL 移除所有 'ai-suggestion' 相关字段和逻辑
3. WHEN AI生成面试问题 THEN 系统 SHALL 以面试官身份发送问题，显示为蓝色气泡
4. WHEN 用户提供回答 THEN 系统 SHALL 以求职者身份记录回答，显示为绿色气泡
5. IF 系统检测到正式面试模式 THEN 系统 SHALL 保持现有的AI助手角色逻辑不变

### Requirement 2: 正确的面试流程实现

**User Story:** 作为一个求职者，我希望进入模拟面试页面后，AI能够先主动提问，然后我再开始回答，这样符合真实面试的自然流程。

#### Acceptance Criteria

1. WHEN 用户进入模拟面试页面 THEN 系统 SHALL 在3秒内发送个性化的自我介绍引导问题
2. WHEN AI发送面试问题 THEN 系统 SHALL 在问题发送完成后自动启动ASR监听
3. WHEN 用户回答完毕（基于静音检测） THEN 系统 SHALL 自动发送回答内容给后端AI分析
4. WHEN AI收到用户回答 THEN 系统 SHALL 在5秒内生成并发送下一个相关问题
5. WHEN 面试进行中 THEN 系统 SHALL 维持"AI提问 → ASR启动 → 用户回答 → AI分析 → 生成新问题"的循环
6. IF 用户首次进入页面 THEN 系统 SHALL NOT 立即启动ASR监听

### Requirement 3: 个性化问题生成系统

**User Story:** 作为一个求职者，我希望AI能够根据我选择的岗位和公司信息生成个性化的面试问题，并且问题不重复，这样我能获得针对性的面试练习。

#### Acceptance Criteria

1. WHEN 生成第一个问题 THEN 系统 SHALL 包含用户选择的公司名称和岗位名称
2. WHEN 生成后续问题 THEN 系统 SHALL 基于用户的回答内容和面试进展动态生成
3. WHEN AI生成新问题 THEN 系统 SHALL 确保问题不与之前已问的问题重复
4. WHEN 问题生成完成 THEN 系统 SHALL 确保问题符合真实面试场景且有逻辑层次
5. IF 问题生成失败 THEN 系统 SHALL 使用预设的备用问题模板

### Requirement 4: ASR生命周期精确控制

**User Story:** 作为一个求职者，我希望语音识别能够在合适的时机启动和停止，不会在AI还没提问时就开始录音，这样避免混乱和误操作。

#### Acceptance Criteria

1. WHEN AI发送面试问题消息 THEN 系统 SHALL 在1秒内自动启动ASR监听
2. WHEN ASR检测到3秒以上静音 THEN 系统 SHALL 自动终止ASR并处理最终识别结果
3. WHEN 用户手动点击停止按钮 THEN 系统 SHALL 立即终止ASR并处理当前识别结果
4. WHEN ASR会话结束 THEN 系统 SHALL 清理所有ASR相关资源
5. IF 页面初始加载完成 THEN 系统 SHALL NOT 启动ASR直到收到AI问题
6. IF ASR服务失败 THEN 系统 SHALL 自动切换到备用ASR提供商

### Requirement 5: 消息类型系统重构

**User Story:** 作为一个系统开发者，我希望模拟面试和正式面试有完全独立的消息类型系统，这样能够避免功能混乱和维护困难。

#### Acceptance Criteria

1. WHEN 定义模拟面试消息接口 THEN 系统 SHALL 只包含 'interviewer' 和 'user-answer' 类型
2. WHEN 处理WebSocket消息 THEN 系统 SHALL 根据面试模式（mock/formal）进行正确路由
3. WHEN 创建面试官消息 THEN 系统 SHALL 包含 questionId, questionType, difficulty, expectedDuration 等字段
4. WHEN 创建用户回答消息 THEN 系统 SHALL 包含 questionId, isFinal, timestamp 等字段
5. IF 收到不匹配当前模式的消息类型 THEN 系统 SHALL 拒绝处理并记录错误日志

### Requirement 6: 状态机驱动的流程控制

**User Story:** 作为一个系统架构师，我希望使用状态机来管理复杂的面试流程，这样能够确保状态转换的可预测性和系统的稳定性。

#### Acceptance Criteria

1. WHEN 系统初始化 THEN 状态机 SHALL 设置为 'INITIALIZING' 状态
2. WHEN 会话开始 THEN 状态机 SHALL 转换到 'WAITING_FOR_AI_QUESTION' 状态
3. WHEN 收到AI问题 THEN 状态机 SHALL 转换到 'LISTENING_FOR_ANSWER' 状态
4. WHEN 用户回答完毕 THEN 状态机 SHALL 转换到 'PROCESSING_ANSWER' 状态
5. WHEN AI生成新问题 THEN 状态机 SHALL 转换回 'WAITING_FOR_AI_QUESTION' 状态
6. IF 发生错误 THEN 状态机 SHALL 转换到 'ERROR' 状态并提供恢复机制

### Requirement 7: 系统兼容性和隔离性

**User Story:** 作为一个产品经理，我希望模拟面试功能的优化不会影响现有的正式面试功能，确保两个功能模块完全独立运行。

#### Acceptance Criteria

1. WHEN 修改模拟面试逻辑 THEN 系统 SHALL 保持正式面试功能完全不变
2. WHEN 用户使用正式面试 THEN 系统 SHALL 继续提供AI建议功能
3. WHEN 系统运行时 THEN 模拟面试和正式面试的状态 SHALL 完全隔离
4. WHEN 进行WebSocket消息路由 THEN 系统 SHALL 根据模式正确分发消息
5. IF 一个模式出现错误 THEN 另一个模式 SHALL 不受任何影响

### Requirement 8: 性能和用户体验优化

**User Story:** 作为一个求职者，我希望面试过程流畅无卡顿，AI问题生成快速，语音识别响应及时，这样能够获得良好的面试体验。

#### Acceptance Criteria

1. WHEN AI生成问题 THEN 系统 SHALL 在3秒内完成并发送
2. WHEN ASR启动 THEN 系统 SHALL 在1秒内开始监听
3. WHEN 用户回答处理 THEN 系统 SHALL 在2秒内完成处理
4. WHEN WebSocket消息传输 THEN 系统 SHALL 保持延迟低于500ms
5. WHEN 长时间面试（30分钟+） THEN 系统 SHALL 保持稳定运行
6. IF 网络断开重连 THEN 系统 SHALL 自动恢复会话状态

### Requirement 9: 错误处理和边界情况

**User Story:** 作为一个求职者，我希望当系统出现异常时能够得到友好的提示和指导，而不是直接崩溃或无响应。

#### Acceptance Criteria

1. WHEN ASR服务失败 THEN 系统 SHALL 自动切换到备用ASR提供商
2. WHEN AI问题生成失败 THEN 系统 SHALL 使用预设的备用问题模板
3. WHEN WebSocket连接断开 THEN 系统 SHALL 自动重连并恢复会话状态
4. WHEN 用户中途退出 THEN 系统 SHALL 清理所有ASR资源和WebSocket连接
5. WHEN 发生任何错误 THEN 系统 SHALL 提供友好的错误提示而非技术错误信息
6. IF 系统检测到异常状态 THEN 系统 SHALL 记录详细日志用于问题排查

### Requirement 10: 测试和质量保证

**User Story:** 作为一个质量保证工程师，我希望系统有完整的测试覆盖，确保所有功能正常工作且不会出现回归问题。

#### Acceptance Criteria

1. WHEN 实施完成 THEN 系统 SHALL 通过所有功能测试用例
2. WHEN 进行兼容性测试 THEN 正式面试功能 SHALL 保持100%正常
3. WHEN 进行性能测试 THEN 系统 SHALL 满足所有性能指标要求
4. WHEN 进行边界情况测试 THEN 系统 SHALL 正确处理所有异常情况
5. WHEN 进行用户验收测试 THEN 用户体验 SHALL 达到预期标准
6. IF 发现任何问题 THEN 系统 SHALL 有快速回滚到修复前版本的能力