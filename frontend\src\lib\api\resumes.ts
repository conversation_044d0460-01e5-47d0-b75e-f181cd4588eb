const API_BASE_URL = process.env.NODE_ENV === 'production'
  ? 'https://mianshijun.xyz' // 生产环境 API URL (使用完整域名)
  : ''; // 开发环境使用相对路径，Vite 会代理到 http://localhost:3000

export interface Resume {
  id: string;
  fileName: string;
  filePath: string;
  fileType?: string | null;
  fileSize?: number | null;
  jobTitle?: string | null;
  uploadTimestamp: string;
  // userId: number; // 根据后端返回，通常不需要在前端展示
  // createdAt: string;
  // updatedAt: string;
}

export const getUserResumes = async (token: string): Promise<Resume[]> => {
  const response = await fetch(`${API_BASE_URL}/api/resumes`, {
    headers: { 'Authorization': `Bearer ${token}` },
  });
  if (!response.ok) throw new Error('Failed to fetch resumes');
  const result = await response.json();
  return result as Resume[];
};

export const uploadResumeMetadata = async (
  token: string,
  resumeData: {
    fileName: string;
    filePath: string; // 这是文件存储后得到的路径
    fileType?: string;
    fileSize?: number;
    jobTitle?: string;
  }
): Promise<Resume> => {
  const response = await fetch(`${API_BASE_URL}/api/resumes`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`,
    },
    body: JSON.stringify(resumeData),
  });
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Failed to upload resume metadata');
  }
  const result = await response.json();
  return result as Resume;
};

export const deleteResume = async (token: string, resumeId: string): Promise<void> => {
  const response = await fetch(`${API_BASE_URL}/api/resumes/${resumeId}`, { // ID 作为路径参数
    method: 'DELETE',
    headers: { 'Authorization': `Bearer ${token}` },
  });
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Failed to delete resume');
  }
};